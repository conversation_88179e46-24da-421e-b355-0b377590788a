# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip
require_relative '../node_modules/react-native-permissions/scripts/setup'

$RNFirebaseAsStaticFramework = true
platform :ios, min_ios_version_supported
prepare_react_native_project!
 
linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end


setup_permissions([
  # 'AppTrackingTransparency',
  # 'BluetoothPeripheral',
  # 'Calendars',
  'Camera',
  # 'Contacts',
  # 'FaceID',
  # 'LocationAccuracy',
  # 'LocationAlways',
  # 'LocationWhenInUse',
  # 'MediaLibrary',
  'Microphone',
  # 'Motion',
  # 'Notifications',
  'PhotoLibrary',
  # 'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'SpeechRecognition',
  # 'StoreKit'
])

target 'cargill' do
  config = use_native_modules!

  # NOTE: Do not remove, needed to keep WatermelonDB compiling:
  pod 'React-jsi', :path => '../node_modules/react-native/ReactCommon/jsi', :modular_headers => true

  # NOTE: This is required as of v0.23
  pod 'simdjson', path: '../node_modules/@nozbe/simdjson'

  pod 'react-native-blob-util', :path => '../node_modules/react-native-blob-util'

  # pod 'Permission-Microphone', :path => '../node_modules/react-native-permissions/ios/Microphone/Permission-Microphone.podspec'

  use_frameworks! :linkage => :static

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'cargillTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
    installer.pods_project.targets.each do |target|
      # target.build_configurations.each do |config|
      #   config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
      #   config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
      #   config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
      #   config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
      # end

      target.build_configurations.each do |config|
        config.build_settings['OTHER_SWIFT_FLAGS'] ||= ['$(inherited)', '-enable-experimental-feature', 'AccessLevelOnImport']
      end
    end
  end
  
end
