<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.cargill.de.internal</string>
		<string>com.cargill.de.internalprod</string>
		<string>com.cargill.de.prod</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>STG-DEDiscover</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.cargill.de.internal</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>cargill-deployment.folio3.com</string>
				<string>cargillcustomer-qa.oktapreview.com</string>
				<string>cargillcustomer-uat.oktapreview.com</string>
				<string>cargillcustomer.okta-emea.com</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CodePushDeploymentKey</key>
	<string>$(CODE_PUSH_DEPLOYMENT_KEY_IOS)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>mailto</string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>13.0.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>This allows us to use your camera to upload profile photo and media in notes. Your record wont be shared without your permission.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string></string>
	<key>NSMicrophoneUsageDescription</key>
	<string> Your record wont be shared without your permission.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This allows us to use your photo gallery to upload profile photo</string>
	<key>UIAppFonts</key>
	<array>
		<string>HelveticaNeue-Bold.ttf</string>
		<string>HelveticaNeue-Medium.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>Roboto-Medium.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>Helvetica Neue.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
