<svg width="63" height="63" viewBox="0 0 63 63" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_12867_48335)">
<circle cx="30.4585" cy="30.8281" r="24" fill="url(#paint0_linear_12867_48335)"/>
</g>
<defs>
<filter id="filter0_d_12867_48335" x="0.458496" y="0.828125" width="62" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12867_48335"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12867_48335" result="shape"/>
</filter>
<linearGradient id="paint0_linear_12867_48335" x1="30.4585" y1="6.82812" x2="30.4585" y2="54.8281" gradientUnits="userSpaceOnUse">
<stop stop-color="#A367DC"/>
<stop offset="1" stop-color="#6D488F"/>
</linearGradient>
</defs>
</svg>
