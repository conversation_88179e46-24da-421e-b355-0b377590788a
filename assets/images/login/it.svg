<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_4090_39368" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="30" height="30">
<circle cx="15" cy="15" r="15" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_4090_39368)">
<g clip-path="url(#clip0_4090_39368)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-5 0H35V30H-5V0Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-5 0H8.33125V30H-5V0Z" fill="#009246"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.6689 0H35.0002V30H21.6689V0Z" fill="#CE2B37"/>
</g>
</g>
<defs>
<clipPath id="clip0_4090_39368">
<rect width="40" height="30" fill="white" transform="translate(-5)"/>
</clipPath>
</defs>
</svg>
