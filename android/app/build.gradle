apply plugin: "com.android.application"
apply plugin: 'com.google.gms.google-services'
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

project.ext.envConfigFiles = [
    prodDebug: ".env.prod",
    prodRelease: ".env.prod",
    internalProdRelease: ".env.internalProd",
    // prodStoreRelease: ".env.prod",
    // internalStoreDebug: ".env",
    // internalStoreRelease: ".env",
    // internalStoreStoreRelease: ".env",
    cargillDebug: ".env",
    cargillRelease: ".env",
    // cargillStoreRelease: ".env",
    // stagingRelease: ".env.UAT",
    // stagingDebug: ".env.UAT",
    // qaRelease: ".env.QA",
    // qaDebug: ".env.QA"
]

apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
 
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
    // cliFile = file("../../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]
 
    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []
 
    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
     
    /* Autolinking */
    autolinkLibrariesWithApp()
}
 
/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US.  Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    namespace "com.cargill"
    defaultConfig {
        applicationId "com.cargill" 
        multiDexEnabled true 
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        resValue "string", "app_name", project.env.get("APP_NAME")
        versionCode project.env.get("ANDROID_VERSION_CODE").toInteger()
        versionName project.env.get("ANDROID_VERSION_NAME")
        resValue "string", "build_config_package", "com.cargill"
        manifestPlaceholders = [
            OKTA_SCHEME: project.env.get("OKTA_SCHEME"),
            appAuthRedirectScheme: project.env.get("AZURE_AUTH_ANDROID_REDIRECT_URL")
        ]
        vectorDrawables.useSupportLibrary = true
        flavorDimensions "default"

        externalNativeBuild {
            cmake {
                cppFlags "-fstack-protector-all", "-fpic", "-fstack-protector-strong"
            }
        }
    }

    productFlavors{
        prod{
            applicationId "com.cargill.de.prod"
        }
        internalProd{
            applicationId "com.cargill.de.internalprod"
        }
        cargill{
            applicationId "com.cargill.de.internal"
        }
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (project.hasProperty('PUBLIC_RELEASE_STORE_FILE')) {
                storeFile file(PUBLIC_RELEASE_STORE_FILE)
                storePassword PUBLIC_RELEASE_STORE_PASSWORD
                keyAlias PUBLIC_RELEASE_KEY_ALIAS
                keyPassword PUBLIC_RELEASE_KEY_PASSWORD
            }
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            
            resValue "string", "CodePushDeploymentKey", '' // In debug

        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"

            resValue "string", "CodePushDeploymentKey", project.env.get("CODE_PUSH_DEPLOYMENT_KEY_ANDROID")// release key should be changed based on env
        }
    }

     packagingOptions {
       pickFirst 'lib/x86/libc++_shared.so'
       pickFirst 'lib/x86_64/libjsc.so'
       pickFirst 'lib/arm64-v8a/libjsc.so'
       pickFirst 'lib/arm64-v8a/libc++_shared.so'
       pickFirst 'lib/x86_64/libc++_shared.so'
       pickFirst 'lib/armeabi-v7a/libc++_shared.so'
     }

//    configurations.all {
//        resolutionStrategy {
//            force 'com.google.android.gms:play-services-basement:18.2.0'
//            force 'com.google.android.gms:play-services-base:18.2.0'
//            force 'com.google.android.gms:play-services-tasks:18.2.0'
//        }
//    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    // implementation("com.facebook.fresco:fresco:3.4.0")
    implementation("com.facebook.fresco:nativeimagetranscoder:3.4.0")
    // For animated GIF support
    implementation 'com.facebook.fresco:animated-gif:3.4.0'

    // For WebP support, including animated WebP
    implementation 'com.facebook.fresco:animated-webp:3.4.0'
    implementation 'com.facebook.fresco:webpsupport:3.4.0'

    implementation "com.google.firebase:firebase-core:11.6.2"
    implementation "com.google.android.gms:play-services-gcm:11.6.2"

    implementation 'com.google.android.gms:play-services-basement:18.2.0'
    implementation 'com.google.android.gms:play-services-base:18.2.0'
    implementation 'com.google.android.gms:play-services-tasks:18.2.0'
    
    implementation project(':react-native-config')
    implementation project(':react-native-splash-screen')
    implementation project(':react-native-blob-util')

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

apply from: "../../node_modules/react-native-code-push/android/codepush.gradle"
