export default {
  GET_BCS_PEN_ANALYSIS_REQUEST: 'GET_BCS_PEN_ANALYSIS_REQUEST',
  GET_BCS_PEN_ANALYSIS_SUCCESS: 'GET_BCS_PEN_ANALYSIS_SUCCESS',
  GET_BCS_PEN_ANALYSIS_FAILURE: 'GET_BCS_PEN_ANALYSIS_FAILURE',

  SAVE_BCS_PEN_ANALYSIS_REQUEST: 'SAVE_BCS_PEN_ANALYSIS_REQUEST',
  SAVE_BCS_PEN_ANALYSIS_SUCCESS: 'SAVE_BCS_PEN_ANALYSIS_SUCCESS',
  SAVE_BCS_PEN_ANALYSIS_FAILURE: 'SAVE_BCS_PEN_ANALYSIS_FAILURE',
  RESET_SAVE_BCS_PEN_ANALYSIS_REQUEST: 'RESET_SAVE_BCS_PEN_ANALYSIS_REQUEST',

  SAVE_BCS_GOALS_REQUEST: 'SAVE_BCS_GOALS_REQUEST',
  SAVE_BCS_GOALS_SUCCESS: 'SAVE_BCS_GOALS_SUCCESS',
  SAVE_BCS_GOALS_FAILURE: 'SAVE_BCS_GOALS_FAILURE',
  RESET_SAVE_BCS_GOALS_REQUEST: 'RESET_SAVE_BCS_GOALS_REQUEST',

  SAVE_BCS_HERD_REQUEST: 'SAVE_BCS_HERD_REQUEST',
  SAVE_BCS_HERD_SUCCESS: 'SAVE_BCS_HERD_SUCCESS',
  SAVE_BCS_HERD_FAILURE: 'SAVE_BCS_HERD_FAILURE',

  UPDATE_BCS_HERD_REQUEST: 'UPDATE_BCS_HERD_REQUEST',
  UPDATE_BCS_HERD_SUCCESS: 'UPDATE_BCS_HERD_SUCCESS',
  UPDATE_BCS_HERD_FAILURE: 'UPDATE_BCS_HERD_FAILURE',
};
