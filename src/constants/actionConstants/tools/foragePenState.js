export default {
  GET_FORAGE_PENN_REQUEST: 'GET_FORAGE_PENN_REQUEST',
  GET_FORAGE_PENN_SUCCESS: 'GET_FORAGE_PENN_SUCCESS',
  GET_FORAGE_PENN_FAILURE: 'GET_FORAGE_PENN_FAILURE',

  SAVE_FORAGE_PENN_REQUEST: 'SAVE_FORAGE_PENN_REQUEST',
  SAVE_FORAGE_PENN_SUCCESS: 'SAVE_FORAGE_PENN_SUCCESS',
  SAVE_FORAGE_PENN_FAILURE: 'SAVE_FORAGE_PENN_FAILURE',

  CHANGE_FORAGE_PENN_SCORER_REQUEST: 'CHANGE_FORAGE_PENN_SCORER_REQUEST',
  CHANGE_FORAGE_PENN_SCORER_SUCCESS: 'CHANGE_FORAGE_PENN_SCORER_SUCCESS',
  CHANGE_FORAGE_PENN_SCORER_FAILURE: 'CHANGE_FORAGE_PENN_SCORER_FAILURE',

  SAVE_FORAGE_PENN_GOALS_REQUEST: 'SAVE_FORAGE_PENN_GOALS_REQUEST',
  SAVE_FORAGE_PENN_GOALS_SUCCESS: 'SAVE_FORAGE_PENN_GOALS_SUCCESS',
  SAVE_FORAGE_PENN_GOALS_FAILURE: 'SAVE_FORAGE_PENN_GOALS_FAILURE',
  RESET_FORAGE_PENN_GOALS_REQUEST: 'RESET_FORAGE_PENN_GOALS_REQUEST',

  GET_FORAGE_PENN_GOALS_REQUEST: 'GET_FORAGE_PENN_GOALS_REQUEST',
  GET_FORAGE_PENN_GOALS_SUCCESS: 'GET_FORAGE_PENN_GOALS_SUCCESS',
  GET_FORAGE_PENN_GOALS_FAILURE: 'GET_FORAGE_PENN_GOALS_FAILURE',
};
