export default {
  GET_SITE_FOR_METABOLIC_INCIDENCE_REQUEST:
    'GET_SITE_FOR_METABOLIC_INCIDENCE_REQUEST',
  GET_SITE_FOR_METABOLIC_INCIDENCE_SUCCESS:
    'GET_SITE_FOR_METABOLIC_INCIDENCE_SUCCESS',
  GET_SITE_FOR_METABOLIC_INCIDENCE_FAILURE:
    'GET_SITE_FOR_METABOLIC_INCIDENCE_FAILURE',

  GET_METABOLIC_INCIDENCE_REQUEST: 'GET_METABOLIC_INCIDENCE_REQUEST',
  GET_METABOLIC_INCIDENCE_SUCCESS: 'GET_METABOLIC_INCIDENCE_SUCCESS',
  GET_METABOLIC_INCIDENCE_FAILURE: 'GET_METABOLIC_INCIDENCE_FAILURE',
  RESET_GET_METABOLIC_INCIDENCE_REQUEST:
    'RESET_GET_METABOLIC_INCIDENCE_REQUEST',

  SAVE_METABOLIC_INCIDENCE_REQUEST: 'SAVE_METABOLIC_INCIDENCE_REQUEST',
  SAVE_METABOLIC_INCIDENCE_SUCCESS: 'SAVE_METABOLIC_INCIDENCE_SUCCESS',
  SAVE_METABOLIC_INCIDENCE_FAILURE: 'SAVE_METABOLIC_INCIDENCE_FAILURE',

  GET_METABOLIC_INCIDENCE_GOALS_REQUEST:
    'GET_METABOLIC_INCIDENCE_GOALS_REQUEST',
  GET_METABOLIC_INCIDENCE_GOALS_SUCCESS:
    'GET_METABOLIC_INCIDENCE_GOALS_SUCCESS',
  GET_METABOLIC_INCIDENCE_GOALS_FAILURE:
    'GET_METABOLIC_INCIDENCE_GOALS_FAILURE',
  RESET_GET_METABOLIC_INCIDENCE_GOALS_REQUEST:
    'RESET_GET_METABOLIC_INCIDENCE_GOALS_REQUEST',

  SAVE_METABOLIC_INCIDENCE_GOALS_REQUEST:
    'SAVE_METABOLIC_INCIDENCE_GOALS_REQUEST',
  SAVE_METABOLIC_INCIDENCE_GOALS_SUCCESS:
    'SAVE_METABOLIC_INCIDENCE_GOALS_SUCCESS',
  SAVE_METABOLIC_INCIDENCE_GOALS_FAILURE:
    'SAVE_METABOLIC_INCIDENCE_GOALS_FAILURE',
  RESET_SAVE_METABOLIC_INCIDENCE_GOALS_REQUEST:
    'RESET_SAVE_METABOLIC_INCIDENCE_GOALS_REQUEST',
};
