export default {
  GET_PILE_AND_BUNKER_REQUEST: 'GET_PILE_AND_BUNKER_REQUEST',
  GET_PILE_AND_BUNKER_SUCCESS: 'GET_PILE_AND_BUNKER_SUCCESS',
  GET_PILE_AND_BUNKER_FAILURE: 'GET_PILE_AND_BUNKER_FAILURE',
  RESET_GET_PILE_AND_BUNKER_REQUEST: 'RESET_GET_PILE_AND_BUNKER_REQUEST',

  ADD_INVENTORY_ITEM_REQUEST: 'ADD_INVENTORY_ITEM_REQUEST',
  ADD_INVENTORY_ITEM_SUCCESS: 'ADD_INVENTORY_ITEM_SUCCESS',
  ADD_INVENTORY_ITEM_FAILURE: 'ADD_INVENTORY_ITEM_FAILURE',
  RESET_ADD_INVENTORY_ITEM_REQUEST: 'RESET_ADD_INVENTORY_ITEM_REQUEST',

  UPDATE_INVENTORY_ITEM_REQUEST: 'UPDATE_INVENTORY_ITEM_REQUEST',
  UPDATE_INVENTORY_ITEM_SUCCESS: 'UPDATE_INVENTORY_ITEM_SUCCESS',
  UPDATE_INVENTORY_ITEM_FAILURE: 'UPDATE_INVENTORY_ITEM_FAILURE',
  RESET_UPDATE_INVENTORY_ITEM_REQUEST: 'RESET_UPDATE_INVENTORY_ITEM_REQUEST',

  DELETE_INVENTORY_ITEM_REQUEST: 'DELETE_INVENTORY_ITEM_REQUEST',
  DELETE_INVENTORY_ITEM_SUCCESS: 'DELETE_INVENTORY_ITEM_SUCCESS',
  DELETE_INVENTORY_ITEM_FAILURE: 'DELETE_INVENTORY_ITEM_FAILURE',
  RESET_DELETE_INVENTORY_ITEM_REQUEST: 'RESET_DELETE_INVENTORY_ITEM_REQUEST',

  RESTORE_PREVIOUS_SILOS_REQUEST: 'RESTORE_PREVIOUS_SILOS_REQUEST',
  RESTORE_PREVIOUS_SILOS_COMPLETED: 'RESTORE_PREVIOUS_SILOS_COMPLETED',
};
