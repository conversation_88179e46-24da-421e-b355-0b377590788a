export default {
  SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST: 'SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST',
  SAVE_LOCOMOTION_PEN_ANALYSIS_SUCCESS: 'SAVE_LOCOMOTION_PEN_ANALYSIS_SUCCESS',
  SAVE_LOCOMOTION_PEN_ANALYSIS_FAILURE: 'SAVE_LOCOMOTION_PEN_ANALYSIS_FAILURE',

  INITIALIZE_LOCOMOTION_SCORE_TOOL_REQUEST:
    'INITIALIZE_LOCOMOTION_SCORE_TOOL_REQUEST',
  INITIALIZE_LOCOMOTION_SCORE_TOOL_SUCCESS:
    'INITIALIZE_LOCOMOTION_SCORE_TOOL_SUCCESS',
  INITIALIZE_LOCOMOTION_SCORE_TOOL_FAILURE:
    'INITIALIZE_LOCOMOTION_SCORE_TOOL_FAILURE',

  SET_SELECTED_LOCOMOTION_PEN_REQUEST: 'SET_SELECTED_LOCOMOTION_PEN_REQUEST',

  UPDATE_SELECTED_PEN_DATA_REQUEST: 'UPDATE_SELECTED_PEN_DATA_REQUEST',

  UPDATE_SELECTED_PEN_FORM_DATA: 'UPDATE_SELECTED_PEN_FORM_DATA',

  CHANGE_LOCOMOTION_PEN: 'CHANGE_LOCOMOTION_PEN',

  UPDATE_LOCOMOTION_TOOL_DATA_REQUEST: 'UPDATE_LOCOMOTION_TOOL_DATA_REQUEST',

  UPDATE_LOCOMOTION_HERD_DATA: 'UPDATE_LOCOMOTION_HERD_DATA',

  UPDATE_ANIMAL_OBSERVE_COUNT_REQUEST: 'UPDATE_ANIMAL_OBSERVE_COUNT_REQUEST',

  //#region initialize pen as selected pen
  INITIALIZE_LOCOMOTION_SELECTED_PEN_REQUEST:
    'INITIALIZE_LOCOMOTION_SELECTED_PEN_REQUEST',

  INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_REQUEST:
    'INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_REQUEST',
  INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_SUCCESS:
    'INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_SUCCESS',
  INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_FAILURE:
    'INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_FAILURE',
};
