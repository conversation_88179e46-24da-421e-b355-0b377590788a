export default {
  GET_<PERSON>NURE_SCREENER_REQUEST: '<PERSON><PERSON><PERSON>_SCREENER_REQUEST',
  GET_MANURE_SCREENER_SUCCESS: 'MA<PERSON><PERSON>_SCREENER_SUCCESS',
  GET_MANURE_SCREENER_FAILURE: '<PERSON><PERSON><PERSON>_SCREENER_FAILURE',
  RESET_MANURE_SCREENER_REQUEST: 'RESET_MANURE_SCREENER_REQUEST',

  SAVE_MANURE_SCREENER_REQUEST: 'SAVE_MANURE_SCREENER_REQUEST',
  SAVE_MANURE_SCREENER_SUCCESS: 'SAVE_MANURE_SCREENER_SUCCESS',
  SAVE_MANURE_SCREENER_FAILURE: 'SAVE_MANURE_SCREENER_FAILURE',

  GET_MANURE_SCREENER_GOALS_REQUEST: 'GET_MANURE_SCREENER_GOALS_REQUEST',
  GET_MANURE_SCREENER_GOALS_SUCCESS: 'GET_MANURE_SCREENER_GOALS_SUCCESS',
  GET_MANURE_SCREENER_GOALS_FAILURE: 'GET_MANURE_SCREENER_GOALS_FAILURE',

  SAVE_MANURE_SCREENER_GOALS_REQUEST: 'SAVE_MANURE_SCREENER_GOALS_REQUEST',
  SAVE_MANURE_SCREENER_GOALS_SUCCESS: 'SAVE_MANURE_SCREENER_GOALS_SUCCESS',
  SAVE_MANURE_SCREENER_GOALS_FAILURE: 'SAVE_MANURE_SCREENER_GOALS_FAILURE',
  RESET_MANURE_SCREENER_GOALS_REQUEST: 'RESET_MANURE_SCREENER_GOALS_REQUEST',

  RESET_MANURE_SCREENER_TOOL: 'RESET_MANURE_SCREENER_TOOL',
};
