import { addColumns, createTable } from '@nozbe/watermelondb/Schema/migrations';

import { VISIT } from '../models/tableNames';

import { RofPriceListTableModel } from '../models/rofPrices';

export const version10Migrations = {
  toVersion: 10,
  steps: [
    addColumns({
      table: VISIT,
      columns: [{ name: 'returnOverFeed', type: 'string' }],
    }),
    createTable(RofPriceListTableModel),
  ],
};
