// modules
import { call, put, select, takeLatest } from 'redux-saga/effects';

// constants
import ANIMAL_ANALYSIS_ACTIONS from '../../../constants/actionConstants/tools/animalAnalysis';

// actions
import {
  getAnimalsFailure,
  getAnimalsSuccess,
  createAnimalSuccess,
  createAnimalFailure,
  updateAnimalSuccess,
  updateAnimalFailure,
  clearAnimalAnalysisFailure,
  clearAnimalAnalysisSuccess,
  deleteAnimalSuccess,
  deleteAnimalFailure,
} from '../../actions/tools/animalAnalysis';

// data managers
import {
  createOfflineAnimal,
  deleteOfflineAnimal,
  getAnimalsByVisit,
  resetAnimalAnalysis,
  updateOfflineAnimal,
} from '../../../database/dataManager/VisitManager';
import { extractUsedPensFromAnimalAnalysisTool } from '../../../helpers/animalHelper';
import { extendsUsedPensInVisitWithToolPens } from '../../../helpers/visitHelper';

const getSelectedVisit = state => state.visit.visit;

function* getAnimalsSaga(action) {
  try {
    let params = action.payload;
    let data = yield call(getAnimalsByVisit, params);
    yield put(getAnimalsSuccess({ data: data }));
  } catch (error) {
    yield put(getAnimalsFailure({ error: error.message }));
  }
}

function* clearAnimalAnalysisSaga(action) {
  try {
    let params = action.payload;
    let response = yield call(resetAnimalAnalysis, params);
    if (response) {
      yield put(clearAnimalAnalysisSuccess({}));
    }
  } catch (error) {
    yield put(clearAnimalAnalysisFailure({ error: error.message }));
  }
}

function* createAnimalSaga(action) {
  try {
    let model = action.payload;
    const visit = yield select(getSelectedVisit);

    const animalAnalysisUsedPens = yield call(
      extractUsedPensFromAnimalAnalysisTool,
      model,
      visit,
    );

    if (animalAnalysisUsedPens) {
      const updatedVisitUsedPens = yield call(
        extendsUsedPensInVisitWithToolPens,
        visit,
        animalAnalysisUsedPens,
      );

      updatedVisitUsedPens ? (model.usedPens = updatedVisitUsedPens) : null;
    }

    let response = yield call(createOfflineAnimal, model);
    if (response) {
      yield put(createAnimalSuccess({}));
    }
  } catch (error) {
    yield put(createAnimalFailure({ error: error.message }));
  }
}

function* updateAnimalSaga(action) {
  try {
    let model = action.payload;
    let response = yield call(updateOfflineAnimal, model);
    if (response) {
      yield put(updateAnimalSuccess({}));
    }
  } catch (error) {
    yield put(updateAnimalFailure({ error: error.message }));
  }
}

function* deleteAnimalSaga(action) {
  try {
    let model = action.payload;
    let response = yield call(deleteOfflineAnimal, model);
    if (response) {
      yield put(deleteAnimalSuccess({}));
    }
  } catch (error) {
    yield put(deleteAnimalFailure({ error: error.message }));
  }
}

function* animalAnalysisSaga() {
  yield takeLatest(ANIMAL_ANALYSIS_ACTIONS.GET_ANIMALS_REQUEST, getAnimalsSaga);

  yield takeLatest(
    ANIMAL_ANALYSIS_ACTIONS.CLEAR_ANIMAL_ANALYSIS_REQUEST,
    clearAnimalAnalysisSaga,
  );

  yield takeLatest(
    ANIMAL_ANALYSIS_ACTIONS.CREATE_ANIMAL_REQUEST,
    createAnimalSaga,
  );

  yield takeLatest(
    ANIMAL_ANALYSIS_ACTIONS.UPDATE_ANIMAL_REQUEST,
    updateAnimalSaga,
  );

  yield takeLatest(
    ANIMAL_ANALYSIS_ACTIONS.DELETE_ANIMAL_REQUEST,
    deleteAnimalSaga,
  );
}

export default animalAnalysisSaga;
