// modules
import { call, delay, put, select, takeLatest } from 'redux-saga/effects';

// constants
import CUD_CHEWING_ACTIONS from '../../../constants/actionConstants/tools/cudChewing';

// helpers
import { logEvent } from '../../../helpers/logHelper';
import { extendsUsedPensInVisitWithToolPens } from '../../../helpers/visitHelper';
import { extractUsedPensFromCudChewingTool } from '../../../helpers/cudChewingHelper';

// data manager
import {
  saveCudChewingPenAnalysisData,
  saveCudChewingHerdAnalysisData,
} from '../../../database/dataManager/VisitManager';

// actions
import { updateVisitFromCudChewing } from '../../actions/visit';

const getSelectedVisit = state => state.visit.visit;

function* saveCudChewingSaga(action) {
  try {
    if (action.payload) {
      const model = action.payload;

      const cudChewingUsedPens = yield call(
        extractUsedPensFromCudChewingTool,
        model?.penAnalysisData,
      );

      if (cudChewingUsedPens) {
        const visit = yield select(getSelectedVisit);

        const updatedVisitUsedPens = yield call(
          extendsUsedPensInVisitWithToolPens,
          visit,
          cudChewingUsedPens,
        );

        updatedVisitUsedPens ? (model.usedPens = updatedVisitUsedPens) : null;
      }

      yield call(saveCudChewingPenAnalysisData, model);
      yield put(updateVisitFromCudChewing());
    }
  } catch (error) {
    logEvent('sagas -> tool -> saveCudChewingSaga error', error);
  }
}

function* saveCudChewingHerdDataSaga(action) {
  try {
    if (action.payload) {
      const model = action.payload;
      yield delay(1200);
      yield call(saveCudChewingHerdAnalysisData, model);
      yield put(updateVisitFromCudChewing());
    }
  } catch (error) {
    logEvent('sagas -> tool -> saveCudChewingHerdDataSaga error', error);
  }
}

function* cudChewingSaga() {
  yield takeLatest(
    CUD_CHEWING_ACTIONS.SAVE_CUD_CHEWING_DATA,
    saveCudChewingSaga,
  );
  yield takeLatest(
    CUD_CHEWING_ACTIONS.SAVE_RUMEN_HERD_ANALYSIS_REQUEST,
    saveCudChewingHerdDataSaga,
  );
}

export default cudChewingSaga;
