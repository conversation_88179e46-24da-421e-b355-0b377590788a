// modules
import { call, put, select, takeLatest } from 'redux-saga/effects';

// constants
import BCS_ACTIONS from '../../../constants/actionConstants/tools/bcs';

// data managers
import {
  getBCSPenAnalysisByVisit,
  saveBCSGoalsByVisit,
  saveBCSHerdDataManager,
  saveBCSPenAnalysisByVisit,
  updateBCSHerdData,
} from '../../../database/dataManager/VisitManager';

// actions
import {
  getBCSPenAnalysisFailure,
  getBCSPenAnalysisSuccess,
  saveBCSGoalsFailure,
  saveBCSGoalsSuccess,
  saveBCSHerdFailure,
  saveBCSHerdSuccess,
  saveBCSPenAnalysisFailure,
  saveBCSPenAnalysisSuccess,
  updateBCSHerdFailure,
  updateBCSHerdSuccess,
} from '../../actions/tools/bcs';
import { updateVisitInProgressStatus } from '../../actions/visit';

// helpers
import { extractUsedPensFromBCSTool } from '../../../helpers/bcsHelper';
import { extendsUsedPensInVisitWithToolPens } from '../../../helpers/visitHelper';

const getSelectedVisit = state => state.visit.visit;

function* getBCSPenAnalysisSaga(action) {
  try {
    let params = action.payload;
    let data = yield call(getBCSPenAnalysisByVisit, params);
    yield put(getBCSPenAnalysisSuccess({ data: data }));
  } catch (error) {
    yield put(getBCSPenAnalysisFailure({ error: error.message }));
  }
}

function* saveBCSPenAnalysisSaga(action) {
  try {
    let model = action.payload;

    const bcsUsedPens = yield call(
      extractUsedPensFromBCSTool,
      model?.penAnalysis,
    );

    if (bcsUsedPens) {
      const visit = yield select(getSelectedVisit);

      const updatedVisitUsedPens = yield call(
        extendsUsedPensInVisitWithToolPens,
        visit,
        bcsUsedPens,
      );

      updatedVisitUsedPens ? (model.usedPens = updatedVisitUsedPens) : null;
    }

    let response = yield call(saveBCSPenAnalysisByVisit, model);
    if (response) {
      yield put(saveBCSPenAnalysisSuccess({}));
      yield put(updateVisitInProgressStatus());
    }
  } catch (error) {
    yield put(saveBCSPenAnalysisFailure({ error: error.message }));
  }
}

function* saveBCSGoalsSaga(action) {
  try {
    let model = action.payload;
    let response = yield call(saveBCSGoalsByVisit, model);
    if (response) {
      yield put(saveBCSGoalsSuccess({}));
    }
  } catch (error) {
    yield put(saveBCSGoalsFailure({ error: error.message }));
  }
}

function* saveBCSHerdDataSaga(action) {
  try {
    let model = action.payload;
    let response = yield call(saveBCSHerdDataManager, model);
    if (response) {
      yield put(saveBCSHerdSuccess({}));
    }
  } catch (error) {
    yield put(saveBCSHerdFailure({ error: error.message }));
  }
}

function* updateBCSHerdDataSaga(action) {
  try {
    let model = action.payload;
    let response = yield call(updateBCSHerdData, model);
    if (response) {
      yield put(updateBCSHerdSuccess({}));
    }
  } catch (error) {
    yield put(updateBCSHerdFailure({ error: error.message }));
  }
}

function* bcsSaga() {
  yield takeLatest(
    BCS_ACTIONS.GET_BCS_PEN_ANALYSIS_REQUEST,
    getBCSPenAnalysisSaga,
  );

  yield takeLatest(
    BCS_ACTIONS.SAVE_BCS_PEN_ANALYSIS_REQUEST,
    saveBCSPenAnalysisSaga,
  );

  yield takeLatest(BCS_ACTIONS.SAVE_BCS_GOALS_REQUEST, saveBCSGoalsSaga);

  yield takeLatest(BCS_ACTIONS.SAVE_BCS_HERD_REQUEST, saveBCSHerdDataSaga);

  yield takeLatest(BCS_ACTIONS.UPDATE_BCS_HERD_REQUEST, updateBCSHerdDataSaga);
}

export default bcsSaga;
