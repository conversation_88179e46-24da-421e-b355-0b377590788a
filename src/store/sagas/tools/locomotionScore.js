// modules
import { call, delay, put, select, takeLatest } from 'redux-saga/effects';

// constants
import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

// helpers
import {
  isLocomotionPenUsed,
  initializeNewLocomotionPen,
  initializeLocomotionToolData,
  insertPenInLocomotionToolData,
  extractUsedPensFromLocomotionScoreTool,
  initLocomotionSelectedPen,
  replaceAnimalAnalysisPenInLocomotionPens,
  initializeLocomotionHerdData,
} from '../../../helpers/locomotionHelper';
import { logEvent } from '../../../helpers/logHelper';
import { getParsedToolData } from '../../../helpers/genericHelper';
import { extendsUsedPensInVisitWithToolPens } from '../../../helpers/visitHelper';

// data manager
import { saveLocomotionPenAnalysisByVisit } from '../../../database/dataManager/VisitManager';

// actions
import {
  saveLocomotionPenAnalysisFailure,
  saveLocomotionPenAnalysisSuccess,
  initializeLocomotionScoreToolSuccess,
  initializeLocomotionScoreToolFailure,
  setSelectedLocomotionPenRequest,
  updateLocomotionToolDataRequest,
  initializeLocomotionHerdAnalysisSuccess,
  initializeLocomotionHerdAnalysisFailure,
} from '../../actions/tools/locomotionScore';
import { updateVisitInProgressStatus } from '../../actions/visit';

// selectors
const pensListSelector = state => state.tool.pensList;
const getSelectedVisitSelector = state => state.visit.visit;
const animalAnalysisSelector = state => state.visit.visit?.animalAnalysis;
const currentSiteSelector = state => state.site.visitSite;
const selectedPenSelector = state => state.locomotionScore.selectedPen;
const locomotionToolDataSelector = state =>
  state.locomotionScore.locomotionToolData;

function* saveLocomotionPenAnalysisSaga(action) {
  try {
    let model = action.payload;
    const locomotionUsedPens = yield call(
      extractUsedPensFromLocomotionScoreTool,
      model?.locomotionScoreData,
    );

    if (locomotionUsedPens) {
      const visit = yield select(getSelectedVisitSelector);

      const updatedVisitUsedPens = yield call(
        extendsUsedPensInVisitWithToolPens,
        visit,
        locomotionUsedPens,
      );

      updatedVisitUsedPens ? (model.usedPens = updatedVisitUsedPens) : null;
    }

    console.log('modelll--', model);
    let response = yield call(saveLocomotionPenAnalysisByVisit, model);
    if (response) {
      yield put(saveLocomotionPenAnalysisSuccess({}));
      yield put(updateVisitInProgressStatus());
    }
  } catch (error) {
    yield put(saveLocomotionPenAnalysisFailure({ error: error.message }));
  }
}

function* initializeLocomotionToolSaga() {
  try {
    const pensList = yield select(pensListSelector);
    const visit = yield select(getSelectedVisitSelector);
    const animalAnalysis = yield select(animalAnalysisSelector);
    const site = yield select(currentSiteSelector);

    const parsedLocomotionData = yield call(
      getParsedToolData,
      visit?.locomotionScore,
    );

    // loading already saved locomotion tool data
    if (parsedLocomotionData && Object.keys(parsedLocomotionData)?.length > 0) {
      yield put(initializeLocomotionScoreToolSuccess(parsedLocomotionData));
      return;
    }

    // Initialize locomotion tool data using the helper function
    // The function now generates static model data
    const initializedData = yield call(
      initializeLocomotionToolData,
      pensList,
      animalAnalysis,
      site,
    );

    yield put(initializeLocomotionScoreToolSuccess(initializedData));
  } catch (error) {
    console.log('initializeLocomotionToolSaga error:', error);
    yield put(initializeLocomotionScoreToolFailure({ error: error.message }));
    logEvent(
      'sagas -> locomotionScoreSaga -> initializeLocomotionToolSaga Error:',
      error,
    );
  }
}

function* changeLocomotionPenSaga(action) {
  try {
    const pen = action.payload;

    const animalAnalysis = yield select(animalAnalysisSelector);
    const locomotionData = yield select(locomotionToolDataSelector);

    const isPenUsed = yield call(isLocomotionPenUsed, pen, locomotionData);
    if (isPenUsed) {
      yield put(setSelectedLocomotionPenRequest(isPenUsed));
      return;
    }

    const newSelectedPen = yield call(
      initializeNewLocomotionPen,
      pen,
      animalAnalysis,
    );
    yield put(setSelectedLocomotionPenRequest(newSelectedPen));

    // const updatedLocomotionData = yield call(
    //   insertPenInLocomotionToolData,
    //   newSelectedPen,
    //   locomotionData,
    // );
    // yield put(updateLocomotionToolDataRequest(updatedLocomotionData));
  } catch (error) {
    console.log('initializeLocomotionToolSaga error:', error);
    logEvent(
      'sagas -> locomotionScoreSaga -> changeLocomotionPenSaga Error:',
      error,
    );
  }
}

function* updateAnimalsObserveCountSaga(action) {
  try {
    const animalsObservedPen = action.payload;

    const locomotionData = yield select(locomotionToolDataSelector);
    const updatedLocomotionData = yield call(
      replaceAnimalAnalysisPenInLocomotionPens,
      animalsObservedPen,
      locomotionData,
    );

    yield put(updateLocomotionToolDataRequest(updatedLocomotionData));
  } catch (error) {
    console.log('updateAnimalsObserveCountSaga error:', error);
    logEvent(
      'sagas -> locomotionScoreSaga -> updateAnimalsObserveCountSaga Error:',
      error,
    );
  }
}

// #region initialize locomotion pen analysis data
function* initLocomotionPenRequestSaga() {
  try {
    const pensList = yield select(pensListSelector);
    const locomotionData = yield select(locomotionToolDataSelector);
    const animalAnalysis = yield select(animalAnalysisSelector);

    const selectedPen = yield call(
      initLocomotionSelectedPen,
      pensList,
      locomotionData,
      animalAnalysis,
    );

    yield put(setSelectedLocomotionPenRequest(selectedPen));
  } catch (error) {
    console.log('initLocomotionPenRequestSaga error:', error);
    logEvent(
      'sagas -> locomotionScoreSaga -> initLocomotionPenRequestSaga Error:',
      error,
    );
  }
}

// #region initialize locomotion herd analysis data
function* initLocomotionHerdSaga() {
  try {
    const site = yield select(currentSiteSelector);
    const locomotionData = yield select(locomotionToolDataSelector);

    const herdData = yield call(
      initializeLocomotionHerdData,
      locomotionData,
      site,
    );
    console.log('herdData', herdData);

    yield delay(100);
    yield put(initializeLocomotionHerdAnalysisSuccess(herdData));
  } catch (error) {
    console.log('initLocomotionHerdSaga error:', error);
    logEvent(
      'sagas -> locomotionScoreSaga -> initLocomotionHerdSaga Error:',
      error,
    );
    yield put(initializeLocomotionHerdAnalysisFailure(error));
  }
}

function* locomotionScoreSaga() {
  yield takeLatest(
    LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_REQUEST,
    initializeLocomotionToolSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST,
    saveLocomotionPenAnalysisSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.CHANGE_LOCOMOTION_PEN,
    changeLocomotionPenSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.UPDATE_ANIMAL_OBSERVE_COUNT_REQUEST,
    updateAnimalsObserveCountSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SELECTED_PEN_REQUEST,
    initLocomotionPenRequestSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_REQUEST,
    initLocomotionHerdSaga,
  );
}

export default locomotionScoreSaga;
