import { takeLatest, call, put, select, takeEvery } from 'redux-saga/effects';

// actions
import TOOL_ACTIONS from '../../constants/actionConstants/tools';
import {
  getSitePensSuccess,
  getSitePensFailure,
  getRecentVisitsForToolSuccess,
  getRecentVisitsForToolFailure,
  downloadToolImageFailure,
  downloadToolExcelFailure,
  downloadToolExcelSuccess,
  downloadToolImageSuccess,
  getSiteSuccess,
  getSiteFailure,
  emailToolExcelSuccess,
  emailToolExcelFailure,
  emailToolImageSuccess,
  emailToolImageFailure,
  getPensInPenTimeBudgetSuccess,
  getPensInPenTimeBudgetFailure,
  getSitePensRequest,
} from '../actions/tool';

//services
import * as ToolService from '../../services/api/tools';
import { isOnline } from '../../services/netInfoService';

//managers
import * as VisitManager from '../../database/dataManager/VisitManager';
import * as PenManager from '../../database/dataManager/PenManager';
import * as SiteManager from '../../database/dataManager/SiteManager';

import { logEvent } from '../../helpers/logHelper';
import { refactorRecentVisitList } from '../../helpers/visitHelper';

import i18n from '../../localization/i18n';
import { extractPenDataFromPublishedVisitTools } from '../../helpers/toolHelper';
import { VISIT_STATUS } from '../../constants/AppConstants';

// selectors
const getSelectedPen = state => state.tool.selectedPen;
const getSelectedVisit = state => state.visit.visit;

function* getSitePens(action) {
  try {
    let params = action.payload;
    let data = yield call(PenManager.getPensBySite, params);

    const visitState = yield select(getSelectedVisit);
    if (
      params?.toolType &&
      visitState?.visitStatus === VISIT_STATUS.PUBLISHED
    ) {
      const toolType = params?.toolType;

      // check and extract tools data and merge it to pens.
      const toolPens = yield call(
        extractPenDataFromPublishedVisitTools,
        data,
        toolType,
        params?.[toolType],
      );

      const uniquePens = toolPens.map(item => {
        const isPenDuplicate = data.find(pen => pen.id === item.penId);
        if (!isPenDuplicate) {
          return item;
        } else {
          return isPenDuplicate;
        }
      });
      data = uniquePens;
    }

    if (data) {
      yield put(getSitePensSuccess({ data }));
    }
  } catch (error) {
    yield put(getSitePensFailure({ error: error.message }));
  }
}

// @TODO remove as not used
function* getSitePensInPenTimeBudget(action) {
  try {
    let params = action.payload;
    let data = yield call(PenManager.getPensBySiteInPenTimeBudget, params);
    yield put(getPensInPenTimeBudgetSuccess({ data: data }));
  } catch (error) {
    yield put(getPensInPenTimeBudgetFailure({ error: error.message }));
  }
}

function* getSite(action) {
  try {
    // let params = action.payload;
    const selectedVisit = yield select(getSelectedVisit);

    const model = {
      siteId: selectedVisit?.siteId,
      localSiteId: selectedVisit?.localSiteId,
    };

    let data = yield call(SiteManager.getBySiteId, model);
    yield put(getSiteSuccess({ data: data }));
  } catch (error) {
    yield put(getSiteFailure({ error: error.message }));
  }
}

function* downloadToolExcel(action) {
  try {
    const online = yield call(isOnline);
    if (online) {
      yield call(ToolService.downloadToolGraphExcel, {
        exportType: action.payload.exportType,
        model: action.payload.model,
      });
      yield put(downloadToolExcelSuccess());
    } else {
      yield put(
        downloadToolExcelFailure({ error: i18n.t('noInternetConnection') }),
      );
    }
  } catch (e) {
    logEvent('sagas -> tool -> downloadToolExcel fail', e);
    console.log('downloadToolExcel fail', e);
    yield put(downloadToolExcelFailure({ error: e.message }));
  }
}

function* downloadToolImage(action) {
  try {
    const online = yield call(isOnline);
    if (online) {
      yield call(ToolService.downloadToolGraphImage, {
        exportType: action.payload.exportType,
        model: action.payload.model,
      });

      yield put(downloadToolImageSuccess());
    } else {
      yield put(
        downloadToolImageFailure({ error: i18n.t('noInternetConnection') }),
      );
    }
  } catch (e) {
    logEvent('sagas -> tool -> downloadToolImage fail', e);
    console.log('downloadToolImage fail', e);
    yield put(downloadToolImageFailure({ error: e.message }));
  }
}

function* getRecentVisitsForTool(action) {
  try {
    let params = action.payload || {};
    let selectedPen = yield select(getSelectedPen);
    let selectedVisit = yield select(getSelectedVisit);
    let data = yield call(VisitManager.getRecentVisitRecordsForTools, params);
    //if graph screen is dependent on pen data, re-filter recent visits list to only keep those visits which have the same pen we are using while in graph screen
    //selectedPen object is only populated on tools whose graph is dependent on pen data. SelectedPen being populated means we have to re-filter from the list
    if (selectedPen && Object.keys(selectedPen).length > 0) {
      data = refactorRecentVisitList(
        data,
        action.payload.tool,
        selectedPen,
        selectedVisit,
        action?.payload?.chewingType?.type || null,
      );
    }
    yield put(
      getRecentVisitsForToolSuccess({ data, tool: action.payload.tool }),
    );
  } catch (error) {
    console.log('getRecentVisitsForToolFailure', error);
    yield put(getRecentVisitsForToolFailure({ error: error.message }));
  }
}

function* updatePenDimForTools(action) {
  try {
    let params = action.payload;
    let data = yield call(PenManager.updateDimOfPen, {
      penId: params?.penId,
      daysInMilk: params?.daysInMilk,
    });

    if (data) {
      yield put(
        getSitePensRequest({
          siteId: params?.siteId,
          localSiteId: params?.localSiteId,
        }),
      );
    }
  } catch (error) {
    console.log('ERROR updatePenDimScore', error);
    logEvent('sagas -> tool -> updatePenDimError', error);
  }
}

function* emailToolGraphExcel(action) {
  try {
    const online = yield call(isOnline);

    if (online) {
      yield call(ToolService.emailToolGraphExcel, {
        exportType: action.payload.exportType,
        model: action.payload.model,
      });
      yield put(emailToolExcelSuccess({}));
    } else {
      yield put(
        emailToolExcelFailure({ error: i18n.t('noInternetConnection') }),
      );
    }
  } catch (e) {
    logEvent('sagas -> tool -> downloadToolExcel fail', e);
    console.log('downloadToolExcel fail', e);
    yield put(emailToolExcelFailure({ error: e.message }));
  }
}

function* emailToolGraphImage(action) {
  try {
    const online = yield call(isOnline);

    if (online) {
      yield call(ToolService.emailToolGraphImage, {
        exportType: action.payload.exportType,
        model: action.payload.model,
      });
      yield put(emailToolImageSuccess({}));
    } else {
      yield put(
        emailToolImageFailure({ error: i18n.t('noInternetConnection') }),
      );
    }
  } catch (e) {
    logEvent('sagas -> tool -> downloadToolExcel fail', e);
    console.log('downloadToolExcel fail', e);
    yield put(emailToolImageFailure({ error: e.message }));
  }
}

function* toolSaga() {
  yield takeLatest(TOOL_ACTIONS.GET_SITE_PENS_REQUEST, getSitePens);
  yield takeLatest(TOOL_ACTIONS.GET_SITE_REQUEST, getSite);

  yield takeLatest(TOOL_ACTIONS.DOWNLOAD_TOOL_EXCEL_REQUEST, downloadToolExcel);
  yield takeLatest(TOOL_ACTIONS.DOWNLOAD_TOOL_IMAGE_REQUEST, downloadToolImage);

  yield takeEvery(
    TOOL_ACTIONS.GET_RECENT_VISITS_FOR_TOOL_REQUEST,
    getRecentVisitsForTool,
  );
  yield takeLatest(TOOL_ACTIONS.UPDATE_PEN_DIM_REQUEST, updatePenDimForTools);
  yield takeLatest(TOOL_ACTIONS.EMAIL_TOOL_EXCEL_REQUEST, emailToolGraphExcel);
  yield takeLatest(TOOL_ACTIONS.EMAIL_TOOL_IMAGE_REQUEST, emailToolGraphImage);

  // @TODO remove as not used
  yield takeLatest(
    TOOL_ACTIONS.GET_SITE_PENS_TIME_BUDGET_REQUEST,
    getSitePensInPenTimeBudget,
  );
}

export default toolSaga;
