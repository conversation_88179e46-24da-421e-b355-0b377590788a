import { failure, request, success } from '..';
import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

//#region save locomotion pen analysis
export const saveLocomotionPenAnalysisRequest = model =>
  request(LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST, model);
export const saveLocomotionPenAnalysisSuccess = data =>
  success(LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_SUCCESS, data);
export const saveLocomotionPenAnalysisFailure = error =>
  failure(LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_FAILURE, error);
//#end region

// #region initialize locomotion score tool
export const initializeLocomotionScoreToolRequest = payload =>
  request(LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_REQUEST, payload);
export const initializeLocomotionScoreToolSuccess = data =>
  success(LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_SUCCESS, data);
export const initializeLocomotionScoreToolFailure = error =>
  failure(LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_FAILURE, error);
// #end region

// #region selected pen actions
export const initializeLocomotionSelectedPenRequest = () =>
  request(LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SELECTED_PEN_REQUEST);

export const setSelectedLocomotionPenRequest = payload =>
  request(LOCOMOTION_ACTIONS.SET_SELECTED_LOCOMOTION_PEN_REQUEST, payload);

export const updateSelectedPenDataRequest = payload =>
  request(LOCOMOTION_ACTIONS.UPDATE_SELECTED_PEN_DATA_REQUEST, payload);

export const updateSelectedPenFormData = payload =>
  request(LOCOMOTION_ACTIONS.UPDATE_SELECTED_PEN_FORM_DATA, payload);

export const changeLocomotionPen = payload =>
  request(LOCOMOTION_ACTIONS.CHANGE_LOCOMOTION_PEN, payload);

// # region update locomotion tool data
export const updateLocomotionToolDataRequest = payload =>
  request(LOCOMOTION_ACTIONS.UPDATE_LOCOMOTION_TOOL_DATA_REQUEST, payload);

// #region update herd analysis data
export const updateLocomotionHerdAnalysisData = payload =>
  request(LOCOMOTION_ACTIONS.UPDATE_LOCOMOTION_HERD_DATA, payload);

// #region update locomotion animals observed from animal analysis
export const updateAnimalsObserveCountRequest = payload =>
  request(LOCOMOTION_ACTIONS.UPDATE_ANIMAL_OBSERVE_COUNT_REQUEST, payload);

// #region initialize locomotion herd data
export const initializeLocomotionHerdAnalysisRequest = () =>
  request(LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_REQUEST);
export const initializeLocomotionHerdAnalysisSuccess = data =>
  success(
    LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_SUCCESS,
    data,
  );
export const initializeLocomotionHerdAnalysisFailure = error =>
  failure(
    LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_FAILURE,
    error,
  );
// #end region
