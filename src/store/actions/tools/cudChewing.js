import { request, success } from '..';
import CUD_CHEWING_ACTIONS from '../../../constants/actionConstants/tools/cudChewing';

//#region initializing cud chewing data
export const initializeCudChewingToolData = payload => {
  return success(CUD_CHEWING_ACTIONS.INITIALIZE_CUD_CHEWING_DATA, payload);
};

export const setupDBCudChewingData = payload => {
  return success(CUD_CHEWING_ACTIONS.INITIALIZE_DB_CUD_CHEWING, payload);
};

export const updateGoalsData = payload => {
  return success(CUD_CHEWING_ACTIONS.UPDATE_CUD_CHEWING_GOALS, payload);
};

//#region save rumen health cud chewing data
export const pushUpdatedCudChewing = payload => {
  return success(CUD_CHEWING_ACTIONS.SAVE_CUD_CHEWING_DATA, payload);
};

export const addCudChewingPen = payload => {
  return success(CUD_CHEWING_ACTIONS.ADD_CUD_CHEWING_PEN, payload);
};

export const addCudChewsCow = payload => {
  return success(CUD_CHEWING_ACTIONS.ADD_CUD_CHEWS_COW, payload);
};

export const updateChewingCount = payload => {
  return success(CUD_CHEWING_ACTIONS.UPDATE_CHEWING_COUNT, payload);
};

export const updateChewsPens = payload => {
  return success(CUD_CHEWING_ACTIONS.UPDATE_CHEWS_COUNT, payload);
};

export const saveRumenHealthHerdAnalysisRequest = model => {
  return request(CUD_CHEWING_ACTIONS.SAVE_RUMEN_HERD_ANALYSIS_REQUEST, model);
};

//#end region
