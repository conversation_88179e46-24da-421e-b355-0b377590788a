import { failure, request, success } from '..';
import ANIMAL_ANALYSIS_ACTIONS from '../../../constants/actionConstants/tools/animalAnalysis';

//#region get animals
export const getAnimalsRequest = model => {
  return request(ANIMAL_ANALYSIS_ACTIONS.GET_ANIMALS_REQUEST, model);
};

export const getAnimalsSuccess = data => {
  return success(ANIMAL_ANALYSIS_ACTIONS.GET_ANIMALS_SUCCESS, data);
};

export const getAnimalsFailure = error => {
  return failure(ANIMAL_ANALYSIS_ACTIONS.GET_ANIMALS_FAILURE, error);
};
//#end region

//#region clear animal analysis
export const clearAnimalAnalysisRequest = model => {
  return request(ANIMAL_ANALYSIS_ACTIONS.CLEAR_ANIMAL_ANALYSIS_REQUEST, model);
};

export const clearAnimalAnalysisSuccess = model => {
  return success(ANIMAL_ANALYSIS_ACTIONS.CLEAR_ANIMAL_ANALYSIS_SUCCESS, model);
};

export const clearAnimalAnalysisFailure = error => {
  return failure(ANIMAL_ANALYSIS_ACTIONS.CLEAR_ANIMAL_ANALYSIS_FAILURE, error);
};

export const resetClearAnimalAnalysisRequest = () => {
  return request(
    ANIMAL_ANALYSIS_ACTIONS.RESET_CLEAR_ANIMAL_ANALYSIS_REQUEST,
    {},
  );
};
//#end region

//#region create animal
export const createAnimalRequest = model => {
  return request(ANIMAL_ANALYSIS_ACTIONS.CREATE_ANIMAL_REQUEST, model);
};

export const createAnimalSuccess = payload => {
  return success(ANIMAL_ANALYSIS_ACTIONS.CREATE_ANIMAL_SUCCESS, payload);
};

export const createAnimalFailure = error => {
  return failure(ANIMAL_ANALYSIS_ACTIONS.CREATE_ANIMAL_FAILURE, error);
};

export const resetCreateAnimalRequest = () => {
  return request(ANIMAL_ANALYSIS_ACTIONS.RESET_CREATE_ANIMAL_REQUEST, {});
};
//#end region

//#region update animal
export const updateAnimalRequest = model => {
  return request(ANIMAL_ANALYSIS_ACTIONS.UPDATE_ANIMAL_REQUEST, model);
};

export const updateAnimalSuccess = payload => {
  return success(ANIMAL_ANALYSIS_ACTIONS.UPDATE_ANIMAL_SUCCESS, payload);
};

export const updateAnimalFailure = error => {
  return failure(ANIMAL_ANALYSIS_ACTIONS.UPDATE_ANIMAL_FAILURE, error);
};

export const resetUpdateAnimalRequest = () => {
  return request(ANIMAL_ANALYSIS_ACTIONS.RESET_UPDATE_ANIMAL_REQUEST, {});
};
//#end region

//#region delete animal
export const deleteAnimalRequest = model => {
  return request(ANIMAL_ANALYSIS_ACTIONS.DELETE_ANIMAL_REQUEST, model);
};

export const deleteAnimalSuccess = () => {
  return success(ANIMAL_ANALYSIS_ACTIONS.DELETE_ANIMAL_SUCCESS, {});
};

export const deleteAnimalFailure = error => {
  return failure(ANIMAL_ANALYSIS_ACTIONS.DELETE_ANIMAL_FAILURE, error);
};

export const resetDeleteAnimalRequest = () => {
  return request(ANIMAL_ANALYSIS_ACTIONS.RESET_DELETE_ANIMAL_REQUEST, {});
};
//#end region
