import { failure, request, success } from '..';
import BCS_ACTIONS from '../../../constants/actionConstants/tools/bcs';

//#region get bcs pen analysis
export const getBCSPenAnalysisRequest = model => {
  return request(BCS_ACTIONS.GET_BCS_PEN_ANALYSIS_REQUEST, model);
};

export const getBCSPenAnalysisSuccess = data => {
  return success(BCS_ACTIONS.GET_BCS_PEN_ANALYSIS_SUCCESS, data);
};

export const getBCSPenAnalysisFailure = error => {
  return failure(BCS_ACTIONS.GET_BCS_PEN_ANALYSIS_FAILURE, error);
};
//#end region

//#region save bcs pen analysis
export const saveBCSPenAnalysisRequest = model => {
  return request(BCS_ACTIONS.SAVE_BCS_PEN_ANALYSIS_REQUEST, model);
};

export const saveBCSPenAnalysisSuccess = data => {
  return success(BCS_ACTIONS.SAVE_BCS_PEN_ANALYSIS_SUCCESS, data);
};

export const saveBCSPenAnalysisFailure = error => {
  return failure(BCS_ACTIONS.SAVE_BCS_PEN_ANALYSIS_FAILURE, error);
};

export const resetSaveBCSPenAnalysisRequest = () => {
  return request(BCS_ACTIONS.RESET_SAVE_BCS_PEN_ANALYSIS_REQUEST, {});
};
//#end region

//#region save bcs goals
export const saveBCSGoalsRequest = model => {
  return request(BCS_ACTIONS.SAVE_BCS_GOALS_REQUEST, model);
};

export const saveBCSGoalsSuccess = data => {
  return success(BCS_ACTIONS.SAVE_BCS_GOALS_SUCCESS, data);
};

export const saveBCSGoalsFailure = error => {
  return failure(BCS_ACTIONS.SAVE_BCS_GOALS_FAILURE, error);
};

export const resetSaveBCSGoalsRequest = () => {
  return request(BCS_ACTIONS.RESET_SAVE_BCS_GOALS_REQUEST, {});
};
//#end region

export const saveBCSHerdRequest = model => {
  return request(BCS_ACTIONS.SAVE_BCS_HERD_REQUEST, model);
};

export const saveBCSHerdSuccess = data => {
  return success(BCS_ACTIONS.SAVE_BCS_HERD_SUCCESS, data);
};

export const saveBCSHerdFailure = error => {
  return failure(BCS_ACTIONS.SAVE_BCS_HERD_FAILURE, error);
};

//update BCS herd request
export const updateBCSHerdRequest = model => {
  return request(BCS_ACTIONS.UPDATE_BCS_HERD_REQUEST, model);
};

export const updateBCSHerdSuccess = data => {
  return success(BCS_ACTIONS.UPDATE_BCS_HERD_SUCCESS, data);
};

export const updateBCSHerdFailure = error => {
  return failure(BCS_ACTIONS.UPDATE_BCS_HERD_FAILURE, error);
};
// #end region
