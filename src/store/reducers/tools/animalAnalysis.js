import ANIMAL_ANALYSIS_ACTIONS from '../../../constants/actionConstants/tools/animalAnalysis';

const initialState = {
  animalAnalysis: null,
  animalAnalysisLoading: false,
  resetAnimalAnalysisSuccess: false,
  createAnimalLoading: false,
  createAnimalSuccess: false,
  createAnimalError: false,
  createAnimalErrorMsg: null,
  updateAnimalLoading: false,
  updateAnimalSuccess: false,
  updateAnimalError: false,
  updateAnimalErrorMsg: null,
  deleteAnimalSuccess: false,
};

const animalAnalysisReducer = (state = initialState, action) => {
  switch (action.type) {
    //#region GET_ANIMALS
    case ANIMAL_ANALYSIS_ACTIONS.GET_ANIMALS_REQUEST:
      return {
        ...state,
        animalAnalysisLoading: true,
      };

    case ANIMAL_ANALYSIS_ACTIONS.GET_ANIMALS_SUCCESS:
      return {
        ...state,
        animalAnalysisLoading: false,
        animalAnalysis: { ...action.payload.data },
      };

    case ANIMAL_ANALYSIS_ACTIONS.GET_ANIMALS_FAILURE:
      return {
        ...state,
        animalAnalysisLoading: false,
      };
    //#end region

    //#region CLEAR_ANIMAL_ANALYSIS
    case ANIMAL_ANALYSIS_ACTIONS.CLEAR_ANIMAL_ANALYSIS_SUCCESS:
      return {
        ...state,
        resetAnimalAnalysisSuccess: true,
      };

    case ANIMAL_ANALYSIS_ACTIONS.RESET_CLEAR_ANIMAL_ANALYSIS_REQUEST:
      return {
        ...state,
        resetAnimalAnalysisSuccess: false,
      };
    //#end region

    //#region CREATE_ANIMAL
    case ANIMAL_ANALYSIS_ACTIONS.CREATE_ANIMAL_REQUEST:
      return {
        ...state,
        createAnimalLoading: true,
        createAnimalSuccess: false,
        createAnimalError: false,
        createAnimalErrorMsg: null,
      };

    case ANIMAL_ANALYSIS_ACTIONS.CREATE_ANIMAL_SUCCESS:
      return {
        ...state,
        createAnimalLoading: false,
        createAnimalSuccess: true,
        createAnimalError: false,
        createAnimalErrorMsg: null,
      };

    case ANIMAL_ANALYSIS_ACTIONS.CREATE_ANIMAL_FAILURE:
      return {
        ...state,
        createAnimalLoading: false,
        createAnimalSuccess: false,
        createAnimalError: true,
        createAnimalErrorMsg: action.payload.error,
      };

    case ANIMAL_ANALYSIS_ACTIONS.RESET_CREATE_ANIMAL_REQUEST:
      return {
        ...state,
        createAnimalLoading: false,
        createAnimalSuccess: false,
        createAnimalError: false,
        createAnimalErrorMsg: null,
      };
    //#end region

    //#region UPDATE_ANIMAL
    case ANIMAL_ANALYSIS_ACTIONS.UPDATE_ANIMAL_REQUEST:
      return {
        ...state,
        updateAnimalLoading: true,
        updateAnimalSuccess: false,
        updateAnimalError: false,
        updateAnimalErrorMsg: null,
      };

    case ANIMAL_ANALYSIS_ACTIONS.UPDATE_ANIMAL_SUCCESS:
      return {
        ...state,
        updateAnimalLoading: false,
        updateAnimalSuccess: true,
        updateAnimalError: false,
        updateAnimalErrorMsg: null,
      };

    case ANIMAL_ANALYSIS_ACTIONS.UPDATE_ANIMAL_FAILURE:
      return {
        ...state,
        updateAnimalLoading: false,
        updateAnimalSuccess: false,
        updateAnimalError: true,
        updateAnimalErrorMsg: action.payload.error,
      };

    case ANIMAL_ANALYSIS_ACTIONS.RESET_UPDATE_ANIMAL_REQUEST:
      return {
        ...state,
        updateAnimalLoading: false,
        updateAnimalSuccess: false,
        updateAnimalError: false,
        updateAnimalErrorMsg: null,
      };
    //#end region

    //#region DELETE_ANIMAL
    case ANIMAL_ANALYSIS_ACTIONS.DELETE_ANIMAL_REQUEST:
      return {
        ...state,
      };

    case ANIMAL_ANALYSIS_ACTIONS.DELETE_ANIMAL_SUCCESS:
      return {
        ...state,
        deleteAnimalSuccess: true,
      };

    case ANIMAL_ANALYSIS_ACTIONS.DELETE_ANIMAL_FAILURE:
      return {
        ...state,
      };

    case ANIMAL_ANALYSIS_ACTIONS.RESET_DELETE_ANIMAL_REQUEST:
      return {
        ...state,
        deleteAnimalSuccess: false,
      };
    //#end region

    default:
      return state;
  }
};

export default animalAnalysisReducer;
