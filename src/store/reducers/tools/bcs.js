import BCS_ACTIONS from '../../../constants/actionConstants/tools/bcs';

const initialState = {
  bcsPenAnalysisLoading: false,
  bcsPenAnalysis: null,
  saveBCSPenAnalysisLoading: false,
  saveBCSPenAnalysisSuccess: false,
  saveBCSPenAnalysisError: false,
  saveBCSPenAnalysisErrorMsg: null,
  saveBCSGoalsLoading: false,
  saveBCSGoalsSuccess: false,
  saveBCSGoalsError: false,
  saveBCSGoalsErrorMsg: null,
};

const bcsReducer = (state = initialState, action) => {
  switch (action.type) {
    //#region GET_BCS_PEN_ANALYSIS
    case BCS_ACTIONS.GET_BCS_PEN_ANALYSIS_REQUEST:
      return {
        ...state,
        bcsPenAnalysisLoading: true,
      };

    case BCS_ACTIONS.GET_BCS_PEN_ANALYSIS_SUCCESS:
      return {
        ...state,
        bcsPenAnalysisLoading: false,
        bcsPenAnalysis: action.payload.data ? { ...action.payload.data } : null,
      };

    case BCS_ACTIONS.GET_BCS_PEN_ANALYSIS_FAILURE:
      return {
        ...state,
        bcsPenAnalysisLoading: false,
      };
    //#end region

    //#region SAVE_BCS_PEN_ANALYSIS
    case BCS_ACTIONS.SAVE_BCS_PEN_ANALYSIS_REQUEST:
      return {
        ...state,
        saveBCSPenAnalysisLoading: true,
        saveBCSPenAnalysisSuccess: false,
        saveBCSPenAnalysisError: false,
        saveBCSPenAnalysisErrorMsg: null,
      };

    case BCS_ACTIONS.SAVE_BCS_PEN_ANALYSIS_SUCCESS:
      return {
        ...state,
        saveBCSPenAnalysisLoading: false,
        saveBCSPenAnalysisSuccess: true,
        saveBCSPenAnalysisError: false,
        saveBCSPenAnalysisErrorMsg: null,
      };

    case BCS_ACTIONS.SAVE_BCS_PEN_ANALYSIS_FAILURE:
      return {
        ...state,
        saveBCSPenAnalysisLoading: false,
        saveBCSPenAnalysisSuccess: false,
        saveBCSPenAnalysisError: true,
        saveBCSPenAnalysisErrorMsg: action.payload.error,
      };

    case BCS_ACTIONS.RESET_SAVE_BCS_PEN_ANALYSIS_REQUEST:
      return {
        ...state,
        saveBCSPenAnalysisLoading: false,
        saveBCSPenAnalysisSuccess: false,
        saveBCSPenAnalysisError: false,
        saveBCSPenAnalysisErrorMsg: null,
      };
    //#end region

    //#region SAVE_BCS_GOALS
    case BCS_ACTIONS.SAVE_BCS_GOALS_REQUEST:
      return {
        ...state,
        saveBCSGoalsLoading: true,
        saveBCSGoalsSuccess: false,
        saveBCSGoalsError: false,
        saveBCSGoalsErrorMsg: null,
      };

    case BCS_ACTIONS.SAVE_BCS_GOALS_SUCCESS:
      return {
        ...state,
        saveBCSGoalsLoading: false,
        saveBCSGoalsSuccess: true,
        saveBCSGoalsError: false,
        saveBCSGoalsErrorMsg: null,
      };

    case BCS_ACTIONS.SAVE_BCS_GOALS_FAILURE:
      return {
        ...state,
        saveBCSGoalsLoading: false,
        saveBCSGoalsSuccess: false,
        saveBCSGoalsError: true,
        saveBCSGoalsErrorMsg: action.payload.error,
      };

    case BCS_ACTIONS.RESET_SAVE_BCS_GOALS_REQUEST:
      return {
        ...state,
        saveBCSGoalsLoading: false,
        saveBCSGoalsSuccess: false,
        saveBCSGoalsError: false,
        saveBCSGoalsErrorMsg: null,
      };
    //#end region

    default:
      return state;
  }
};

export default bcsReducer;
