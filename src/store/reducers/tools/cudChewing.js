import CUD_CHEWING_ACTIONS from '../../../constants/actionConstants/tools/cudChewing';

const initialState = {
  cudChewingPenAnalysis: null,
};

const cudChewing = (state = initialState, action) => {
  switch (action.type) {
    case CUD_CHEWING_ACTIONS.INITIALIZE_CUD_CHEWING_DATA:
      return {
        ...state,
        cudChewingPenAnalysis: action.payload,
      };

    case CUD_CHEWING_ACTIONS.INITIALIZE_DB_CUD_CHEWING:
      return {
        ...state,
        cudChewingPenAnalysis: action.payload,
      };

    case CUD_CHEWING_ACTIONS.UPDATE_CUD_CHEWING_GOALS:
      return {
        ...state,
        cudChewingPenAnalysis: {
          ...state.cudChewingPenAnalysis,
          goals: action.payload,
        },
      };

    case CUD_CHEWING_ACTIONS.ADD_CUD_CHEWING_PEN:
      return {
        ...state,
        cudChewingPenAnalysis: {
          ...state.cudChewingPenAnalysis,
          pens: [...state.cudChewingPenAnalysis.pens, action.payload],
        },
      };

    case CUD_CHEWING_ACTIONS.ADD_CUD_CHEWS_COW:
      return {
        ...state,
        cudChewingPenAnalysis: {
          ...state.cudChewingPenAnalysis,
          pens: action.payload,
        },
      };

    case CUD_CHEWING_ACTIONS.UPDATE_CHEWING_COUNT:
      return {
        ...state,
        cudChewingPenAnalysis: {
          ...state.cudChewingPenAnalysis,
          pens: action.payload,
        },
      };

    case CUD_CHEWING_ACTIONS.UPDATE_CHEWS_COUNT:
      return {
        ...state,
        cudChewingPenAnalysis: {
          ...state.cudChewingPenAnalysis,
          pens: action.payload,
        },
      };

    default:
      return state;
  }
};

export default cudChewing;
