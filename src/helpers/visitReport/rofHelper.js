import { getParsedToolData } from '../genericHelper';
import { ROF_FIELDS } from '../../constants/FormConstants';
import { ENUM_CONSTANTS } from '../../constants/AppConstants';
import { getFormattedDate } from '../dateHelper';

import { createModelDataForROFGraphs } from '../rofHelper';
import i18n from '../../localization/i18n';
import { ROF_FORM_TYPES } from '../../constants/toolsConstants/ROFConstants';

const getEnumDisplayValue = (enumData, key) => {
  if (!enumData || !key) return key || '-';

  const enumItem = enumData.find(item => item.key === key);
  return enumItem.value || key;
};

export const getAvailableFormTypes = (analysis = {}) => {
  const rof = getParsedToolData(analysis?.visitDetails?.returnOverFeed);
  const formTypes = [];

  const analysisCategories = analysis?.tool?.analysisCategories || [];

  analysisCategories.forEach(category => {
    if (category.isSelected) {
      if (category.value === ROF_FORM_TYPES.TMR && rof?.tmr) {
        formTypes.push({
          key: ROF_FORM_TYPES.TMR,
          label: category.title || 'TMR',
        });
      } else if (
        category.value === ROF_FORM_TYPES.INDIVIDUAL_COW &&
        rof?.individualCow
      ) {
        formTypes.push({
          key: ROF_FORM_TYPES.INDIVIDUAL_COWS,
          label: category.title || 'Individual Cow',
        });
      }
    }
  });

  if (formTypes.length === 0) {
    if (rof?.tmr) {
      formTypes.push({ key: ROF_FORM_TYPES.TMR, label: 'TMR' });
    }

    if (rof?.individualCow) {
      formTypes.push({
        key: ROF_FORM_TYPES.INDIVIDUAL_COWS,
        label: 'Individual Cow',
      });
    }
  }

  return formTypes;
};

export const getHerdProfileTableData = (
  analysis = {},
  enums,
  formType = ROF_FORM_TYPES.TMR,
) => {
  const tableHeader = [];

  const rof = getParsedToolData(analysis?.visitDetails?.returnOverFeed);
  const herdProfile = rof?.[formType]?.herdProfile || {};
  const feeding = rof?.[formType]?.feeding || {};

  const tableData = [
    [
      ROF_FIELDS.BREED,
      herdProfile[ROF_FIELDS.BREED] === 'Other'
        ? herdProfile[ROF_FIELDS.OTHER_BREED_TYPE] || '-'
        : getEnumDisplayValue(
            enums?.[ENUM_CONSTANTS.BREED_RETURN_OVER_FEED],
            herdProfile[ROF_FIELDS.BREED],
          ),
    ],
    [
      ROF_FIELDS.FEEDING,
      getEnumDisplayValue(
        enums?.[ENUM_CONSTANTS.FEEDING],
        herdProfile[ROF_FIELDS.FEEDING_TYPE],
      ),
    ],
    [
      ROF_FIELDS.NUMBER_OF_TMR_GROUPS,
      herdProfile[ROF_FIELDS.NUMBER_OF_TMR_GROUPS] ?? '-',
    ],
    [
      ROF_FIELDS.SELECTED,
      getEnumDisplayValue(
        enums?.[ENUM_CONSTANTS.SUPPLEMENT_TYPES],
        herdProfile[ROF_FIELDS.TYPE_OF_SUPPLEMENT],
      ),
    ],
    [ROF_FIELDS.LACTATING_COWS, feeding[ROF_FIELDS.LACTATING_COWS] || '-'],
    [
      ROF_FIELDS.COOL_AID,
      herdProfile[ROF_FIELDS.COOL_AID] ? i18n.t('yes') : i18n.t('no'),
    ],
    [
      ROF_FIELDS.FORTISSA_FIT,
      herdProfile[ROF_FIELDS.FORTISSA_FIT] ? i18n.t('yes') : i18n.t('no'),
    ],
    [ROF_FIELDS.MUN, herdProfile[ROF_FIELDS.MUN] || '-'],
    [
      ROF_FIELDS.MILKING_PER_DAY,
      herdProfile[ROF_FIELDS.MILKING_PER_DAY] ?? '-',
    ],
    [ROF_FIELDS.DAYS_IN_MILK, feeding[ROF_FIELDS.DAYS_IN_MILK] || '-'],
  ];

  return { tableHeader, tableData };
};

// Helper function to create feeding section data
const createFeedingSectionData = (feedingItem, sectionType, enums) => {
  if (!feedingItem || Object.keys(feedingItem).length === 0) return [];

  let titleText = '';
  switch (sectionType) {
    case ROF_FIELDS.HOME_GROWN_FORAGES:
      titleText = `${i18n.t('homeGrownForages')} | ${getEnumDisplayValue(
        enums?.[ENUM_CONSTANTS.HOME_GROWN_FORAGE_TYPES],
        feedingItem.homeGrownForageType,
      )}`;
      break;
    case ROF_FIELDS.HOME_GROWN_GRAINS:
      titleText = `${i18n.t('homeGrownGrains')} | ${getEnumDisplayValue(
        enums?.[ENUM_CONSTANTS.HOME_GROWN_GRAIN_TYPES],
        feedingItem.homeGrownGrainsType,
      )}`;
      break;
    case ROF_FIELDS.PURCHASED_BULK_FEED:
      titleText = i18n.t('purchaseBulkFeed');
      break;
    case ROF_FIELDS.PURCHASED_BAG_FEED:
      titleText = i18n.t('purchaseBagsFeed');
      break;
  }

  const baseData = [['title', titleText]];

  if (sectionType === ROF_FIELDS.HOME_GROWN_FORAGES) {
    return [
      ...baseData,
      [ROF_FIELDS.FORAGE_NAME, feedingItem[ROF_FIELDS.FORAGE_NAME] || '-'],
      [
        ROF_FIELDS.TOTAL_HERD_PER_DAY,
        feedingItem[ROF_FIELDS.TOTAL_HERD_PER_DAY] || '-',
      ],
      [ROF_FIELDS.DRY_MATTER, feedingItem[ROF_FIELDS.DRY_MATTER] || '-'],
      [
        ROF_FIELDS.TOTAL_DRY_MATTER,
        feedingItem[ROF_FIELDS.TOTAL_DRY_MATTER] || '-',
      ],
      [ROF_FIELDS.PRICE_PER_TON, feedingItem[ROF_FIELDS.PRICE_PER_TON] || '-'],
    ];
  } else if (sectionType === ROF_FIELDS.HOME_GROWN_GRAINS) {
    return [
      ...baseData,
      [ROF_FIELDS.GRAINS_NAME, feedingItem[ROF_FIELDS.GRAINS_NAME] || '-'],
      [
        ROF_FIELDS.TOTAL_HERD_PER_DAY,
        feedingItem[ROF_FIELDS.TOTAL_HERD_PER_DAY] || '-',
      ],
      [ROF_FIELDS.DRY_MATTER, feedingItem[ROF_FIELDS.DRY_MATTER] || '-'],
      [
        ROF_FIELDS.TOTAL_DRY_MATTER,
        feedingItem[ROF_FIELDS.TOTAL_DRY_MATTER] || '-',
      ],
      [ROF_FIELDS.PRICE_PER_TON, feedingItem[ROF_FIELDS.PRICE_PER_TON] || '-'],
    ];
  } else if (sectionType === ROF_FIELDS.PURCHASED_BULK_FEED) {
    return [
      ...baseData,
      [ROF_FIELDS.FEED_NAME, feedingItem[ROF_FIELDS.FEED_NAME] || '-'],
      [
        ROF_FIELDS.TOTAL_DRY_MATTER,
        feedingItem[ROF_FIELDS.TOTAL_DRY_MATTER] || '-',
      ],
      [ROF_FIELDS.DRY_MATTER, feedingItem[ROF_FIELDS.DRY_MATTER] || '-'],
      [
        ROF_FIELDS.TOTAL_DRY_MATTER,
        feedingItem[ROF_FIELDS.TOTAL_DRY_MATTER] || '-',
      ],
      [ROF_FIELDS.PRICE_PER_TON, feedingItem[ROF_FIELDS.PRICE_PER_TON] || '-'],
    ];
  } else if (sectionType === ROF_FIELDS.PURCHASED_BAG_FEED) {
    return [
      ...baseData,
      [ROF_FIELDS.FEED_NAME, feedingItem[ROF_FIELDS.FEED_NAME] || '-'],
      [
        ROF_FIELDS.TOTAL_HERD_PER_DAY,
        feedingItem[ROF_FIELDS.TOTAL_HERD_PER_DAY] || '-',
      ],
      [ROF_FIELDS.DRY_MATTER, feedingItem[ROF_FIELDS.DRY_MATTER] || '-'],
      [
        ROF_FIELDS.TOTAL_DRY_MATTER,
        feedingItem[ROF_FIELDS.TOTAL_DRY_MATTER] || '-',
      ],
      [ROF_FIELDS.PRICE_PER_TON, feedingItem[ROF_FIELDS.PRICE_PER_TON] || '-'],
    ];
  }

  return [];
};

export const getAllFeedingSectionsData = (
  analysis = [],
  enums,
  formType = ROF_FORM_TYPES.TMR,
) => {
  const rof = getParsedToolData(analysis?.visitDetails?.returnOverFeed);
  const feeding = rof?.[formType]?.feeding || {};

  const allSections = [];

  // Process all homeGrownForages
  if (feeding?.homeGrownForages?.length > 0) {
    feeding.homeGrownForages.forEach(item => {
      const sectionData = createFeedingSectionData(
        item,
        ROF_FIELDS.HOME_GROWN_FORAGES,
        enums,
      );
      if (sectionData.length > 0) {
        allSections.push({
          type: ROF_FIELDS.HOME_GROWN_FORAGES,
          data: sectionData,
        });
      }
    });
  }

  // Process all homeGrownGrains
  if (feeding?.homeGrownGrains?.length > 0) {
    feeding.homeGrownGrains.forEach(item => {
      const sectionData = createFeedingSectionData(
        item,
        ROF_FIELDS.HOME_GROWN_GRAINS,
        enums,
      );
      if (sectionData.length > 0) {
        allSections.push({
          type: ROF_FIELDS.HOME_GROWN_GRAINS,
          data: sectionData,
        });
      }
    });
  }

  // Process all purchaseBulkFeed
  if (feeding?.purchaseBulkFeed?.length > 0) {
    feeding.purchaseBulkFeed.forEach(item => {
      const sectionData = createFeedingSectionData(
        item,
        ROF_FIELDS.PURCHASED_BULK_FEED,
        enums,
      );
      if (sectionData.length > 0) {
        allSections.push({
          type: ROF_FIELDS.PURCHASED_BULK_FEED,
          data: sectionData,
        });
      }
    });
  }

  // Process all purchasedBagFeed
  if (feeding?.purchaseBagsFeed?.length > 0) {
    feeding.purchaseBagsFeed.forEach(item => {
      const sectionData = createFeedingSectionData(
        item,
        ROF_FIELDS.PURCHASED_BAG_FEED,
        enums,
      );
      if (sectionData.length > 0) {
        allSections.push({
          type: ROF_FIELDS.PURCHASED_BAG_FEED,
          data: sectionData,
        });
      }
    });
  }

  return allSections;
};

// Function to organize feeding sections into pages
export const organizeFeedingSectionsIntoPages = allSections => {
  if (allSections.length === 0) return [];

  const pages = [];

  // Separate sections by type
  const homeGrownForages = allSections.filter(
    section => section.type === ROF_FIELDS.HOME_GROWN_FORAGES,
  );
  const homeGrownGrains = allSections.filter(
    section => section.type === ROF_FIELDS.HOME_GROWN_GRAINS,
  );
  const purchasedBulkFeed = allSections.filter(
    section => section.type === ROF_FIELDS.PURCHASED_BULK_FEED,
  );
  const purchasedBagFeed = allSections.filter(
    section => section.type === ROF_FIELDS.PURCHASED_BAG_FEED,
  );

  const firstPageForages = homeGrownForages.slice(0, 3);
  const firstPageGrains = homeGrownGrains.slice(0, 3);

  pages.push({
    leftColumn: firstPageForages,
    rightColumn: firstPageGrains,
    isFirstPage: true,
  });

  const remainingForages = homeGrownForages.slice(3);
  const remainingGrains = homeGrownGrains.slice(3);
  const hasRemainingData =
    remainingForages.length > 0 ||
    remainingGrains.length > 0 ||
    purchasedBulkFeed.length > 0 ||
    purchasedBagFeed.length > 0;

  if (hasRemainingData) {
    const secondPageLeft = [...remainingForages, ...purchasedBulkFeed];

    const secondPageRight = [...remainingGrains, ...purchasedBagFeed];

    pages.push({
      leftColumn: secondPageLeft,
      rightColumn: secondPageRight,
      isFirstPage: false,
    });
  }

  return pages;
};

export const getFeedingTableData = (
  analysis = [],
  enums,
  formType = ROF_FORM_TYPES.TMR,
) => {
  const rof = getParsedToolData(analysis?.visitDetails?.returnOverFeed);
  const feeding = rof?.[formType]?.feeding || {};

  const homeGrownForagesData =
    feeding?.homeGrownForages.length > 0 ? feeding?.homeGrownForages[0] : [];
  const homeGrownGrainsData =
    feeding?.homeGrownGrains.length > 0 ? feeding?.homeGrownGrains[0] : [];
  const purchaseBulkFeedData =
    feeding?.purchaseBulkFeed.length > 0 ? feeding?.purchaseBulkFeed[0] : [];
  const purchasedBagFeedData =
    feeding?.purchaseBagsFeed.length > 0 ? feeding?.purchaseBagsFeed[0] : [];

  const homeGrownForages =
    Object.keys(homeGrownForagesData).length > 0
      ? [
          [
            'title',
            `${i18n.t('homeGrownForages')} | ${getEnumDisplayValue(
              enums?.[ENUM_CONSTANTS.HOME_GROWN_FORAGE_TYPES],
              homeGrownForagesData.homeGrownForageType,
            )}`,
          ],
          [
            ROF_FIELDS.FORAGE_NAME,
            homeGrownForagesData[ROF_FIELDS.FORAGE_NAME],
          ],
          [
            ROF_FIELDS.TOTAL_HERD_PER_DAY,
            homeGrownForagesData[ROF_FIELDS.TOTAL_HERD_PER_DAY],
          ],
          [ROF_FIELDS.DRY_MATTER, homeGrownForagesData[ROF_FIELDS.DRY_MATTER]],
          [
            ROF_FIELDS.TOTAL_DRY_MATTER,
            homeGrownForagesData[ROF_FIELDS.TOTAL_DRY_MATTER],
          ],
          [
            ROF_FIELDS.PRICE_PER_TON,
            homeGrownForagesData[ROF_FIELDS.PRICE_PER_TON],
          ],
        ]
      : [];

  const homeGrownGrains =
    Object.keys(homeGrownGrainsData).length > 0
      ? [
          [
            'title',
            `${i18n.t('homeGrownGrains')} | ${getEnumDisplayValue(
              enums?.[ENUM_CONSTANTS.HOME_GROWN_GRAIN_TYPES],
              homeGrownGrainsData.homeGrownGrainsType,
            )}`,
          ],
          [ROF_FIELDS.GRAINS_NAME, homeGrownGrainsData[ROF_FIELDS.GRAINS_NAME]],
          [
            ROF_FIELDS.TOTAL_HERD_PER_DAY,
            homeGrownGrainsData[ROF_FIELDS.TOTAL_HERD_PER_DAY],
          ],
          [ROF_FIELDS.DRY_MATTER, homeGrownGrainsData[ROF_FIELDS.DRY_MATTER]],
          [
            ROF_FIELDS.TOTAL_DRY_MATTER,
            homeGrownGrainsData[ROF_FIELDS.TOTAL_DRY_MATTER],
          ],
          [
            ROF_FIELDS.PRICE_PER_TON,
            homeGrownGrainsData[ROF_FIELDS.PRICE_PER_TON],
          ],
        ]
      : [];

  const purchasedBulkFeed =
    Object.keys(purchaseBulkFeedData).length > 0
      ? [
          ['title', `${i18n.t('purchaseBulkFeed')}`],
          [ROF_FIELDS.FEED_NAME, purchaseBulkFeedData[ROF_FIELDS.FEED_NAME]],
          [
            ROF_FIELDS.TOTAL_DRY_MATTER,
            purchaseBulkFeedData[ROF_FIELDS.TOTAL_DRY_MATTER],
          ],
          [ROF_FIELDS.DRY_MATTER, purchaseBulkFeedData[ROF_FIELDS.DRY_MATTER]],
          [
            ROF_FIELDS.TOTAL_DRY_MATTER,
            purchaseBulkFeedData[ROF_FIELDS.TOTAL_DRY_MATTER],
          ],
          [
            ROF_FIELDS.PRICE_PER_TON,
            purchaseBulkFeedData[ROF_FIELDS.PRICE_PER_TON],
          ],
        ]
      : [];

  const purchasedBagFeed =
    Object.keys(purchasedBagFeedData).length > 0
      ? [
          ['title', `${i18n.t('purchaseBagsFeed')}`],
          [ROF_FIELDS.FEED_NAME, purchasedBagFeedData[ROF_FIELDS.FEED_NAME]],
          [
            ROF_FIELDS.TOTAL_HERD_PER_DAY,
            purchasedBagFeedData[ROF_FIELDS.TOTAL_HERD_PER_DAY] || '-',
          ],
          [
            ROF_FIELDS.DRY_MATTER,
            purchasedBagFeedData[ROF_FIELDS.DRY_MATTER] || '-',
          ],
          [
            ROF_FIELDS.TOTAL_DRY_MATTER,
            purchasedBagFeedData[ROF_FIELDS.TOTAL_DRY_MATTER] || '-',
          ],
          [
            ROF_FIELDS.PRICE_PER_TON,
            purchasedBagFeedData[ROF_FIELDS.PRICE_PER_TON] || '-',
          ],
        ]
      : [];

  return {
    homeGrownForages,
    homeGrownGrains,
    purchasedBulkFeed,
    purchasedBagFeed,
  };
};

export const getMilkProductionTableData = (
  analysis = {},
  formType = ROF_FORM_TYPES.TMR,
) => {
  const rof = getParsedToolData(analysis?.visitDetails?.returnOverFeed);
  const milk = rof?.[formType]?.milkProduction || {};

  const milkProductionSection = [
    [
      ROF_FIELDS.AVERAGE_MILK_PRODUCTION_ANIMALS_IN_TANK,
      parseFloat(milk?.averageMilkProductionKg ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.MILK_PRODUCTION_KG,
      parseFloat(milk?.milkProductionKg ?? 0).toFixed(2),
    ],
  ];

  const butterfatSection = [
    ['title', ROF_FIELDS.BUTTERFAT],
    [
      ROF_FIELDS.PRICE_PER_KG,
      parseFloat(milk?.butterfat?.pricePerKg ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.PRICE_PER_HL,
      parseFloat(milk?.butterfat?.pricePerHl ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.PRICE_PER_KG_PER_COW,
      parseFloat(milk?.butterfat?.pricePerKgPerCow ?? 0).toFixed(2),
    ],
  ];

  const secondColumnDataFirst = [
    [
      ROF_FIELDS.KG_OF_QUOTA_PER_DAY,
      parseFloat(milk?.kgOfQuotaPerDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY,
      parseFloat(milk?.incentiveDaysKgPerDay ?? 0).toFixed(2),
    ],
  ];

  const lactoseAndOtherSolidsSection = [
    ['title', ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS],
    [
      ROF_FIELDS.PRICE_PER_KG,
      parseFloat(milk?.lactoseAndOtherSolids?.pricePerKg ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.PRICE_PER_HL,
      parseFloat(milk?.lactoseAndOtherSolids?.pricePerHl ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.PRICE_PER_KG_PER_COW,
      parseFloat(milk?.lactoseAndOtherSolids?.pricePerKgPerCow ?? 0).toFixed(2),
    ],
  ];

  const thirdColumnDataFirst = [
    [
      ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY,
      parseFloat(milk?.totalQuotaKgPerDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY,
      parseFloat(milk?.currentQuotaUtilizationKgPerDay ?? 0).toFixed(2),
    ],
  ];

  const proteinSection = [
    ['title', ROF_FIELDS.PROTEIN],
    [
      ROF_FIELDS.PRICE_PER_KG,
      parseFloat(milk?.protein?.pricePerKg ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.PRICE_PER_HL,
      parseFloat(milk?.protein?.pricePerHl ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.PRICE_PER_KG_PER_COW,
      parseFloat(milk?.protein?.pricePerKgPerCow ?? 0).toFixed(2),
    ],
  ];

  const class2LactoseAndOtherSolidsSection = [
    ['title', ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS],
    [
      ROF_FIELDS.PRICE_PER_KG,
      parseFloat(milk?.class2LactoseAndOtherSolids?.pricePerKg ?? 0).toFixed(2),
    ],
  ];

  const class2ProteinSection = [
    ['title', ROF_FIELDS.CLASS2_PROTEIN],
    [
      ROF_FIELDS.PRICE_PER_KG,
      parseFloat(milk?.class2Protein?.pricePerKg ?? 0).toFixed(2),
    ],
  ];

  const deductionsSection = [
    ['title', ROF_FIELDS.DEDUCTIONS],
    [
      ROF_FIELDS.PRICE_PER_KG,
      parseFloat(milk?.deductions?.pricePerKg ?? 0).toFixed(2),
    ],
  ];

  return {
    milkProductionSection,
    butterfatSection,
    secondColumnDataFirst,
    lactoseAndOtherSolidsSection,
    thirdColumnDataFirst,
    proteinSection,
    class2LactoseAndOtherSolidsSection,
    class2ProteinSection,
    deductionsSection,
  };
};

export const getMilkProductionOutputsTableData = (
  analysis = {},
  formType = ROF_FORM_TYPES.TMR,
) => {
  const rof = getParsedToolData(analysis?.visitDetails?.returnOverFeed);
  const milkOutputs = rof?.[formType]?.milkProductionOutputs || {};

  const firstColumnSection = [
    [
      ROF_FIELDS.RATIO_SNF_PER_BUTTERFAT,
      parseFloat(milkOutputs?.ratioSNFPerButterfat ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.MAX_ALLOWED,
      parseFloat(milkOutputs?.maxAllowed ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_FAT_PROTEIN,
      parseFloat(milkOutputs?.totalFatProtein ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.DAIRY_EFFICIENCY,
      parseFloat(milkOutputs?.dairyEfficiency ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.COMPONENT_EFFICIENCY,
      parseFloat(milkOutputs?.componentEfficiency ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_REVENUE_PER_LITER,
      parseFloat(milkOutputs?.totalRevenuePerLiter ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.FEED_COST,
      parseFloat(milkOutputs?.feedCostPerLiter ?? 0).toFixed(2),
    ],
  ];

  const secondColumnSection = [
    [
      ROF_FIELDS.PURCHASED_FEED_COST,
      parseFloat(milkOutputs?.purchasedFeedCostPerLiter ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.CONCENTRATE_COST,
      parseFloat(milkOutputs?.concentrateCostPerLiter ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.CONCENTRATE_COST_PER_KG_BF,
      parseFloat(milkOutputs?.concentrateCostPerKgBF ?? 0).toFixed(2),
    ],
    [ROF_FIELDS.BF_REVENUE, parseFloat(milkOutputs?.bfRevenue ?? 0).toFixed(2)],
    [
      ROF_FIELDS.PROTEIN_REVENUE,
      parseFloat(milkOutputs?.proteinRevenue ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.OTHER_SOLIDS_REVENUE,
      parseFloat(milkOutputs?.otherSolidsRevenue ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.DEDUCTIONS_PRICE_PER_COW_PER_DAY,
      parseFloat(milkOutputs?.deductionsPricePerCowPerDay ?? 0).toFixed(2),
    ],
  ];

  const thirdColumnSection = [
    [
      ROF_FIELDS.SNF_NON_PAYMENT,
      parseFloat(milkOutputs?.snfNonPayment ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_REVENUE_KG_FAT,
      parseFloat(milkOutputs?.totalRevenuePricePerKgFat ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_REVENUE_COW_DAY,
      parseFloat(milkOutputs?.totalRevenueCowDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.UNDER_QUOTA_LOST_REVENUE,
      parseFloat(milkOutputs?.underQuotaLostRevenuePerMonth ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.ROF_KG_BUTTER_FAT,
      parseFloat(milkOutputs?.rofPerKgButterFat ?? 0).toFixed(2),
    ],
    [ROF_FIELDS.ROF, parseFloat(milkOutputs?.rof ?? 0).toFixed(2)],
  ];

  return {
    firstColumnSection,
    secondColumnSection,
    thirdColumnSection,
  };
};

export const getSummaryTablesData = (
  analysis = {},
  formType = ROF_FORM_TYPES.TMR,
) => {
  const rof = getParsedToolData(analysis?.visitDetails?.returnOverFeed);
  const summary = rof?.[formType]?.summary || {};

  const feedCostsData = summary?.feedCosts || {};
  const feedCostsSection = [
    [
      ROF_FIELDS.FORAGE_FEED_COST_PER_COW_PER_DAY,
      parseFloat(feedCostsData?.forageFeedCostPerCowPerDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.GRAINS_COST_PER_COW_PER_DAY,
      parseFloat(feedCostsData?.grainsCostPerCowPerDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_ON_FARM_FEED_COST_PER_COW_PER_DAY,
      parseFloat(feedCostsData?.totalOnFarmFeedCostPerCowPerDay ?? 0).toFixed(
        2,
      ),
    ],
    [
      ROF_FIELDS.PURCHASED_BULK_FEED_PER_COW_PER_DAY,
      parseFloat(feedCostsData?.purchasedBulkFeedPerCowPerDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.PURCHASED_BAGS_FEED_PER_COW_PER_DAY,
      parseFloat(feedCostsData?.purchasedBagsFeedPerCowPerDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_PURCHASED_COST_PER_COW_PER_DAY,
      parseFloat(feedCostsData?.totalPurchasedCostPerCowPerDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY,
      parseFloat(feedCostsData?.totalFeedCostPerCowPerDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_CONCENTRATE_COST_PER_COW_PER_DAY,
      parseFloat(feedCostsData?.totalConcentrateCostPerCowPerDay ?? 0).toFixed(
        2,
      ),
    ],
    [
      ROF_FIELDS.FEED_COST_PER_KG_OF_BF,
      parseFloat(feedCostsData?.feedCostPerKgOfBF ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.FEED_COST_PER_LITRE_OF_MILK,
      parseFloat(feedCostsData?.feedCostPerLitreOfMilk ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.FORAGE_PERCENTAGE,
      parseFloat(feedCostsData?.foragePercentage ?? 0).toFixed(2),
    ],
  ];

  const revenueData = summary?.revenue || {};
  const revenueSection = [
    [ROF_FIELDS.BF_REVENUE, parseFloat(revenueData?.bfRevenue ?? 0).toFixed(2)],
    [
      ROF_FIELDS.PROTEIN_REVENUE,
      parseFloat(revenueData?.proteinRevenue ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.OTHER_SOLIDS_REVENUE,
      parseFloat(revenueData?.otherSolidsRevenue ?? 0).toFixed(2),
    ],
    [ROF_FIELDS.SUBTOTAL, parseFloat(revenueData?.subtotal ?? 0).toFixed(2)],
    [
      ROF_FIELDS.DEDUCTIONS_PRICE_PER_COW_PER_DAY,
      parseFloat(revenueData?.deductionsPricePerCowPerDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.SNF_NON_PAYMENT,
      parseFloat(revenueData?.snfNonPayment ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_REVENUE_COW_DAY,
      parseFloat(revenueData?.totalRevenueCowDay ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_REVENUE_PER_KG_BUTTERFAT,
      parseFloat(revenueData?.totalRevenuePricePerKgButterFat ?? 0).toFixed(2),
    ],
    [
      ROF_FIELDS.TOTAL_REVENUE_PER_LITER,
      parseFloat(revenueData?.totalRevenuePerLiter ?? 0).toFixed(2),
    ],
  ];

  const currentReturnData = summary?.currentReturnOverFeedCosts || {};
  const currentReturnSection = [
    [
      ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY,
      parseFloat(
        currentReturnData?.returnOverFeedCostPerCowPerDay ?? 0,
      ).toFixed(2),
    ],
    [
      ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF,
      parseFloat(currentReturnData?.returnOverFeedCostPerKgOfBF ?? 0).toFixed(
        2,
      ),
    ],
    [
      ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE,
      parseFloat(currentReturnData?.returnOverFeedCostPerLitre ?? 0).toFixed(2),
    ],
  ];

  const previousReturnData = summary?.previousReturnOverFeedCosts || {};
  const previousReturnSection = [
    [
      ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY,
      parseFloat(
        previousReturnData?.returnOverFeedCostPerCowPerDay ?? 0,
      ).toFixed(2),
    ],
    [
      ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF,
      parseFloat(previousReturnData?.returnOverFeedCostPerKgOfBF ?? 0).toFixed(
        2,
      ),
    ],
    [
      ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE,
      parseFloat(previousReturnData?.returnOverFeedCostPerLitre ?? 0).toFixed(
        2,
      ),
    ],
  ];

  return {
    feedCostsSection,
    revenueSection,
    currentReturnSection,
    previousReturnSection,
  };
};

export const prepareROFVisitReportData = (
  tool = {},
  visitDetails = {},
  formType = ROF_FORM_TYPES.TMR,
  recentVisits = [],
  comparingRofVisits = [],
  enums = {},
) => {
  const analysis = { tool, visitDetails };

  const herdProfileData = getHerdProfileTableData(analysis, enums, formType);
  const feedingData = getFeedingTableData(analysis, enums, formType);

  // Get all feeding sections for pagination
  const allFeedingSections = getAllFeedingSectionsData(
    analysis,
    enums,
    formType,
  );
  const feedingPages = organizeFeedingSectionsIntoPages(allFeedingSections);

  const milkProductionData = getMilkProductionTableData(analysis, formType);
  const milkProductionOutputsData = getMilkProductionOutputsTableData(
    analysis,
    formType,
  );

  const summaryTablesData = getSummaryTablesData(analysis, formType);

  const formTypes = getAvailableFormTypes(analysis);

  const currentToolData = getParsedToolData(visitDetails?.returnOverFeed);

  const rawGraphData = createModelDataForROFGraphs(
    currentToolData,
    recentVisits,
    comparingRofVisits,
    formType,
  );

  const graphData =
    rawGraphData?.map(series => ({
      ...series,
      barColor: series.onScreeColor,
    })) || [];

  const graphLabels =
    graphData && graphData.length > 0 && graphData[0].dataPoints.length > 0
      ? graphData[0].dataPoints.map(point => point.x)
      : [];

  return {
    herdProfile: herdProfileData,
    feeding: feedingData,
    feedingPages: feedingPages,

    // MilkProductionPages data
    milkProduction: milkProductionData,
    milkProductionOutputs: milkProductionOutputsData,

    // SummaryTablesPage data
    summaryTables: summaryTablesData,

    // ROFGraphPage data
    graph: {
      data: graphData,
      labels: graphLabels,
      formTypes: formTypes,
    },
  };
};

export const createModelDataForVisitReportROFGraphs = (
  recentVisits = [],
  formTypes = [],
  comparingVisitIds = [],
) => {
  try {
    const filteredVisits = recentVisits?.filter(el =>
      comparingVisitIds?.includes(el.id || el.visitId),
    );

    let formattedRecentVisits = [];
    if (filteredVisits?.length > 0) {
      formattedRecentVisits = filteredVisits.map(visitObj => {
        const parsedVisitData = getParsedToolData(visitObj.returnOverFeed);

        return {
          rof: parsedVisitData || null,
          visitId: visitObj?.id,
          date: visitObj?.visitDate,
          mobileLastUpdatedTime: visitObj?.mobileLastUpdatedTime,
        };
      });
    }

    const graphData = [];
    if (formattedRecentVisits?.length > 0) {
      const rof = {
          dataPoints: [],
          barColor: '#7AD8DC',
        },
        totalFeedCost = {
          dataPoints: [],
          barColor: '#83BEF4',
        },
        totalRevenue = {
          dataPoints: [],
          barColor: '#ABA1E3',
        };

      const formKeys = {};
      formTypes.map(type => (formKeys[type?.key] = type?.key));

      formattedRecentVisits.map(item => {
        Object.keys(formKeys).map(formType => {
          const toolFormData = item.rof?.[formType];
          if (toolFormData) {
            const dateLabel = getFormattedDate(item.date, 'MM/dd');

            rof.dataPoints.push({
              x: dateLabel,
              y:
                toolFormData?.summary?.currentReturnOverFeedCosts
                  ?.returnOverFeedCostPerCowPerDay || 0,
            });

            totalFeedCost.dataPoints.push({
              x: dateLabel,
              y:
                toolFormData?.summary?.feedCosts?.totalFeedCostPerCowPerDay ||
                0,
            });

            totalRevenue.dataPoints.push({
              x: dateLabel,
              y: toolFormData?.summary?.revenue?.totalRevenueCowDay || 0,
            });
          }
        });
      });

      graphData.push(rof);
      graphData.push(totalFeedCost);
      graphData.push(totalRevenue);
    }

    return graphData;
  } catch (e) {
    console.log('createModelDataForVisitReportROFGraphs error:', e);
    return [];
  }
};
