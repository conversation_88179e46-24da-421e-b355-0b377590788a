import 'react-native-gesture-handler';
/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 * @flow strict-local
 */

import React from 'react';

import { Node, useEffect, useState, useRef } from 'react';
import { StatusBar, AppState, Platform } from 'react-native';
import { useDispatch } from 'react-redux';
import NetInfo from '@react-native-community/netinfo';

// orientation
import Orientation from 'react-native-orientation-locker';

//styling
import { NativeBaseProvider } from 'native-base';
import theme from '../constants/theme';
import colors from '../constants/theme/variables/customColor';

//store and actions
import { useSelector } from 'react-redux';

//localization
// import * as RNLocalize from 'react-native-localize';
import { setLanguage } from '../localization/i18n';

//splash
import SplashScreen from 'react-native-splash-screen';
import AppEntry from '../routes/Authentication';
import Loader from '../components/common/Loader';
import { showUpdateAlert } from '../components/common/Alerts';

import { autoPublishVisitsRequest } from '../store/actions/visit';
import { initializeFirebase } from '../services/crashlytics';

console.disableYellowBox = true;

const App: () => Node = () => {
  const authState = useSelector(state => state.authentication);
  const versioningState = useSelector(state => state.versioning);
  const dispatch = useDispatch();

  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  const [netInfo, setNetInfo] = useState(false);

  useEffect(() => {
    initializeFirebase();
  }, []);

  useEffect(() => {
    // Adding locale change event listener on app mount.
    // RNLocalize.addEventListener('change', setLanguage);
    if (authState.appInitialized) {
      if (authState.language) {
        setLanguage(authState.language);
      }

      // Hiding the splash screen as soon as the app is loaded
      setTimeout(() => {
        SplashScreen.hide();

        if (authState.isForceUpdateAvailable || authState.isUpdateAvailable) {
          showUpdateAlert(authState.isForceUpdateAvailable);
        }
      }, 1000);
    }
    if (authState.isUserLogin) {
      dispatch(autoPublishVisitsRequest());
    }
    Orientation.lockToPortrait();
    return () => {
      // Removing locale change event listener on app unmount.
      // RNLocalize.removeEventListener('change', setLanguage);
    };
  }, [authState]);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        if (Platform.OS === 'ios') {
          if (authState.isForceUpdateAvailable || authState.isUpdateAvailable) {
            showUpdateAlert(authState.isForceUpdateAvailable);
          }
        }
        setLanguage(authState.language);
      }

      appState.current = nextAppState;
      setAppStateVisible(appState.current);
    });

    return () => {
      subscription.remove();
    };
  }, [authState]);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      // get versioning config on getting online if forceupdate is false
      if (!versioningState.isUpdateAvailable && state.isConnected === true) {
        if (state.isConnected != netInfo) {
          // dispatch(syncLatestVersionConfigRequested());
          setNetInfo(state.isConnected);
        }
      } else {
        setNetInfo(state.isConnected);
      }
    });

    return unsubscribe;
  }, [versioningState]);

  return (
    <NativeBaseProvider theme={theme}>
      <StatusBar
        translucent={true}
        backgroundColor={colors.white}
        barStyle={'dark-content'}
      />
      <Loader />
      <AppEntry />
    </NativeBaseProvider>
  );
};

export default App;
