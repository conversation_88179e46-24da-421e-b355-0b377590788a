import React from 'react';
import { Provider } from 'react-redux';

import store from '../store';
import App from './App';

//constants
import ACTION_CONSTANTS from '../constants/actionConstants';

class Setup extends React.Component {
  componentDidMount() {
    store.dispatch({
      type: ACTION_CONSTANTS.GET_INITIALIZED_REQUEST,
    });
  }

  render() {
    return (
      <Provider store={store}>
        <App />
      </Provider>
    );
  }
}

export default Setup;
