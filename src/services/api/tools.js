import Config from 'react-native-config';

//services
import {
  saveFileFromURLWithPOSTMethod,
  shareFileFromURLWithPOSTMethod,
} from './media';

//constants
import APIConstants from '../../constants/APIConstants';
import { MEDIA_EXTENSIONS, MIME_TYPE } from '../../constants/AppConstants';
import { logEvent } from '../../helpers/logHelper';

export const downloadToolGraphExcel = async params => {
  try {
    let url =
      Config.API_URL +
      APIConstants.DOWNLOAD_TOOL_GRAPH_EXCEL(params.exportType);
    let filename = params.model.fileName;

    let destination = await saveFileFromURLWithPOSTMethod(
      url,
      filename,
      params.model,
      MEDIA_EXTENSIONS.XLSX,
      MIME_TYPE.XLSX,
    );
    return destination;
  } catch (error) {
    logEvent('services -> tools -> downloadToolGraphExcel error', error);
    console.log('downloadToolGraphExcel error', error);
    throw error;
  }
};

export const downloadToolGraphImage = async params => {
  try {
    let url =
      Config.API_URL +
      APIConstants.DOWNLOAD_TOOL_GRAPH_IMAGE(params.exportType);
    let filename = params.model.fileName;
    const isZip = params?.model?.multipleFiles;

    let destination = await saveFileFromURLWithPOSTMethod(
      url,
      filename,
      params.model,
      !!isZip ? MEDIA_EXTENSIONS.ZIP : MEDIA_EXTENSIONS.PNG,
      !!isZip ? MIME_TYPE.ZIP : MIME_TYPE.PNG,
    );
    return destination;
  } catch (error) {
    logEvent('services -> tools -> downloadToolGraphIMAGE error', error);
    console.log('downloadToolGraphIMAGE error', error);
    throw error;
  }
};

export const emailToolGraphExcel = async params => {
  try {
    let url =
      Config.API_URL +
      APIConstants.DOWNLOAD_TOOL_GRAPH_EXCEL(params.exportType);
    let filename = params.model.fileName;

    let destination = await shareFileFromURLWithPOSTMethod(
      url,
      filename,
      params.model,
      MEDIA_EXTENSIONS.XLSX,
      MIME_TYPE.XLSX,
    );
    return destination;
  } catch (error) {
    logEvent('services -> tools -> downloadToolGraphExcel error', error);
    console.log('downloadToolGraphExcel error', error);
    throw error;
  }
};

export const emailToolGraphImage = async params => {
  try {
    let url =
      Config.API_URL +
      APIConstants.DOWNLOAD_TOOL_GRAPH_IMAGE(params.exportType);
    let filename = params.model.fileName;
    const isZip = params?.model?.multipleFiles;

    let destination = await shareFileFromURLWithPOSTMethod(
      url,
      filename,
      params.model,
      !!isZip ? MEDIA_EXTENSIONS.ZIP : MEDIA_EXTENSIONS.PNG,
      !!isZip ? MIME_TYPE.ZIP : MIME_TYPE.PNG,
    );
    return destination;
  } catch (error) {
    logEvent('services -> tools -> downloadToolGraphExcel error', error);
    console.log('downloadToolGraphExcel error', error);
    throw error;
  }
};
