import { StyleSheet } from 'react-native';
import colors from '../../../../../../../constants/theme/variables/customColor';

import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';

export default StyleSheet.create({
  summaryHeader: {
    flexDirection: 'row',
    minHeight: normalize(40),
    marginTop: normalize(18),
    marginBottom: normalize(6),
    borderRadius: normalize(5),
    paddingVertical: normalize(8),
    marginHorizontal: normalize(20),
    paddingHorizontal: normalize(8),
    justifyContent: 'space-between',
    backgroundColor: colors.primaryMain,
  },
  col: {
    textAlign: 'center',
    color: colors.white,
    width: normalize(61),
    fontSize: normalize(12),
    fontWeight: '500',
    fontFamily: customFont.HelveticaNeueRegular,
  },

  container: {
    flexDirection: 'row',
    alignItems: 'center',
    height: normalize(48),
    marginVertical: normalize(6),
    backgroundColor: colors.grey10,
    marginHorizontal: normalize(20),
    paddingHorizontal: normalize(16),
  },
  rowItem: {
    flex: 1,
    fontSize: normalize(14),
    fontWeight: '500',
    fontFamily: customFont.HelveticaNeueRegular,
    textAlign: 'center',
  },
  scaleLabel: {
    flex: 1,
    fontWeight: '500',
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  infoTop: {
    // marginBottom: 30,
    position: 'absolute',
    left: normalize(100),
    top: normalize(10),
  },
  infoColumn: {
    flexDirection: 'row',
    marginHorizontal: normalize(20),
    marginTop: normalize(16),
  },
  penAnalysisTitle: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(14),
    color: colors.alphabetIndex,
    letterSpacing: normalize(0.15),
  },

  penAnalysisValue: {
    fontFamily: customFont.HelveticaNeueBold,
  },
});
