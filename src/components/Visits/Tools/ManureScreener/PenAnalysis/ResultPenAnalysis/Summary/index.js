// modules
import React, { useMemo } from 'react';
import { View, Text, FlatList } from 'react-native';

//redux
import { useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../../localization/i18n';

//component
import EmptyListComponent from '../../../../../../common/EmptyListComponent';

// styles
import styles from './styles';

//helpers
import { getSummaryData } from '../../../../../../../helpers/manureScreenerHelper';

//constants
import {
  MANURE_SCREENER_SUMMARY_COLUMN_HEADINGS,
  MANURE_SCREENER_SUMMARY_ROW_HEADINGS,
} from '../../../../../../../constants/toolsConstants/ManureScreenerConstants';
import { NO_RESULT_FOUND_ICON } from '../../../../../../../constants/AssetSVGConstants';
import MoreInfoPopOver from '../../../../RoboticMilkEvaluation/MoreInfoPopOver';
import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';

const SummaryResults = ({ penData, selectedPen }) => {
  //info

  const info = { key: [i18n.t('topInfo')] };

  //redux states
  const manureScreener = useSelector(state => state.manureScreener.mstState);
  const visitState = useSelector(state => state.visit?.visit);
  const { isEditable = false } = visitState;

  //labels
  const labels = [
    { scaleLabel: MANURE_SCREENER_SUMMARY_ROW_HEADINGS[0] },
    { scaleLabel: MANURE_SCREENER_SUMMARY_ROW_HEADINGS[1] },
    { scaleLabel: MANURE_SCREENER_SUMMARY_ROW_HEADINGS[2] },
  ];

  // data parsing
  const getSelectedPenData = useMemo(() => {
    let manureScreenerVisitState = visitState?.manureScreener;
    if (
      manureScreenerVisitState &&
      typeof manureScreenerVisitState === 'string'
    ) {
      manureScreenerVisitState = JSON.parse(manureScreenerVisitState);
    } else {
      manureScreenerVisitState = {};
    }

    //refactored to send goals from visit if they donot exist in tool's reducer
    return getSummaryData({
      labels,
      score: {
        ...penData,
        ...(manureScreener?.mstGoal
          ? manureScreener?.mstGoal
          : manureScreenerVisitState?.mstGoal),
      },
      isEditable,
    });
  }, [penData, selectedPen, manureScreener?.mstGoal]);

  // header to on list
  const summaryHeader = () => (
    <View style={styles.summaryHeader}>
      <Text style={styles.col}> </Text>
      <Text style={styles.col}>
        {MANURE_SCREENER_SUMMARY_COLUMN_HEADINGS[0]}
      </Text>
      <Text style={styles.col}>
        {MANURE_SCREENER_SUMMARY_COLUMN_HEADINGS[1]}
      </Text>
      <Text style={styles.col}>
        {MANURE_SCREENER_SUMMARY_COLUMN_HEADINGS[2]}
      </Text>
    </View>
  );

  const renderItem = ({ item }, index) => {
    return (
      <View style={styles.container} key={index}>
        <Text style={styles?.scaleLabel}>{item?.scaleLabel}</Text>
        {item?.scaleLabel == MANURE_SCREENER_SUMMARY_ROW_HEADINGS[0] && (
          <View style={styles.infoTop}>
            <MoreInfoPopOver infoText={info} robotType={'key'} />
          </View>
        )}
        <Text style={styles?.rowItem}>
          {convertInputNumbersToRegionalBasis(item?.onScreenPercent, 1)}
        </Text>
        <Text style={styles?.rowItem}>
          {convertInputNumbersToRegionalBasis(item?.goalMin, 1)}
        </Text>
        <Text style={styles?.rowItem}>
          {convertInputNumbersToRegionalBasis(item?.goalMax, 1)}
        </Text>
      </View>
    );
  };

  const EmptyComponent = () => (
    <EmptyListComponent
      title={i18n.t('noRecordFound')}
      description={i18n.t('noRecordFoundDescription')}
      image={<NO_RESULT_FOUND_ICON />}
    />
  );

  return (
    <>
      <View style={styles.infoColumn}>
        <Text style={styles.penAnalysisTitle}>
          {i18n.t('pen') || ''}:{' '}
          <Text style={styles.penAnalysisValue}>{selectedPen?.name || ''}</Text>
        </Text>
      </View>

      <FlatList
        data={getSelectedPenData}
        extraData={[getSelectedPenData]}
        renderItem={renderItem}
        ListHeaderComponent={summaryHeader}
        ListEmptyComponent={EmptyComponent}
        keyExtractor={item => item?.scoreCategory?.toString()}
      />
    </>
  );
};

export default SummaryResults;
