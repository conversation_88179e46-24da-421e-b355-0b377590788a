// modules
import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, ScrollView } from 'react-native';

//animation
import Animated, { SlideInRight, SlideOutRight } from 'react-native-reanimated';

// components
import SummaryResults from './Summary';
import ManurePenAnalysisGraph from './Graph';
import ToolBottomTabs from '../../../common/ToolBottomTabs';

// styles
import styles from './styles';

// constants
import {
  MANURE_SCREENER_RESULTS_TABS,
  MANURE_SCREENER_RESULTS_TYPES,
} from '../../../../../../constants/toolsConstants/ManureScreenerConstants';

import useExport from './Hooks/useExport';
import { useDispatch, useSelector } from 'react-redux';
import { getManureScreenerGoalsRequest } from '../../../../../../store/actions/tools/manureScreener';

const PenAnalysisResults = ({
  selectedPen,
  penData,
  manureScreenerTool,
  selectedVisits,
  onTabChange,
}) => {
  //api calling
  const dispatch = useDispatch();

  // custom hook
  const { downloadMSTGraph, onShareMSTData } = useExport();

  //local states
  const [selectedTab, setSelectedTab] = useState(
    MANURE_SCREENER_RESULTS_TABS[0],
  );

  //redux states
  const visitState = useSelector(state => state.visit);
  const { id } = visitState || {};

  //hooks

  useEffect(() => {
    dispatch(getManureScreenerGoalsRequest({ localVisitId: id }));
  }, []);

  return (
    <View style={styles.container}>
      {
        {
          [MANURE_SCREENER_RESULTS_TYPES.GRAPH]: (
            <ScrollView
              keyboardDismissMode="none"
              keyboardShouldPersistTaps="always"
              // onLayout={({ nativeEvent }) => setLayout(nativeEvent.layout)}
            >
              <ManurePenAnalysisGraph
                selectedPen={selectedPen}
                penData={penData}
                // layout={layout}
                // setLayout={setLayout}
                manureScreenerTool={manureScreenerTool}
                selectedVisits={selectedVisits}
                onShareClick={onShareMSTData}
                onDownloadPress={downloadMSTGraph}
              />
            </ScrollView>
          ),
          [MANURE_SCREENER_RESULTS_TYPES.SUMMARY]: (
            <SummaryResults
              penData={manureScreenerTool}
              selectedPen={selectedPen}
            />
          ),
        }[selectedTab?.key]
      }

      <View style={styles.tabsPosition}>
        <Animated.View
          // style={styles.container}
          entering={SlideInRight.duration(250)}
          exiting={SlideOutRight.duration(250)}>
          <ToolBottomTabs
            tabs={MANURE_SCREENER_RESULTS_TABS}
            selectedTab={selectedTab}
            onTabChange={tab => {
              setSelectedTab(tab);
              onTabChange(tab);
            }}
          />
        </Animated.View>
      </View>
    </View>
  );
};

export default PenAnalysisResults;
