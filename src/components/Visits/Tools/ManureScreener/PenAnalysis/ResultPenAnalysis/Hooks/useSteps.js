import { useState, useEffect } from 'react';

//redux
import { useDispatch, useSelector } from 'react-redux';

//helpers
import {
  calculatePensSum,
  getInitialGoalsValue,
  saveManureScreenerData,
} from '../../../../../../../helpers/manureScreenerHelper';

//actions
import { clearActiveCategoryTool } from '../../../../../../../store/actions/tool';
import { resetSaveActiveScreenRequest } from '../../../../../../../store/actions/toolSwitching';

//navigation
import { useNavigation } from '@react-navigation/native';
import {
  saveManureScreenerGoalsRequest,
  saveManureScreenerRequest,
} from '../../../../../../../store/actions/tools/manureScreener';
import { stringIsEmpty } from '../../../../../../../helpers/alphaNumericHelper';

const useSteps = (
  initialStep,
  totalToolSteps,
  isEditable = false,
  formRef,
  visitState,
  site,
  backToolListingStep,
  penList,
) => {
  //api calling
  const dispatch = useDispatch();
  //navigation
  const navigation = useNavigation();

  const manureScreenerState = useSelector(state => state.manureScreener);

  //local states
  const [currentStep, setCurrentStep] = useState(1);
  const [compareButtonVisible, setCompareButtonVisible] = useState(false);

  const [isDirty, setIsDirty] = useState(false);

  //hooks
  useEffect(() => {
    if (currentStep === initialStep) {
      dispatch(clearActiveCategoryTool());
    }
  }, [currentStep]);

  //handlers
  const onNextStepClick = values => {
    if (currentStep === totalToolSteps) {
      dispatch(resetSaveActiveScreenRequest()); //pressing 'done' button should clear reducer
      dispatch(clearActiveCategoryTool());
      setCurrentStep(1);
      setCompareButtonVisible(false);
      saveManureScreener && saveManureScreener(values, true);
    } else {
      const localSum = calculatePensSum(values);
      localSum && setCurrentStep(prevState => prevState + 1);
      setCompareButtonVisible(false);
      saveManureScreener && saveManureScreener(values, true);
    }
  };

  const onPrevStepClick = () => {
    if (currentStep === totalToolSteps) {
      setCurrentStep(prevState => prevState - 1);
      setCompareButtonVisible(false);
    } else if (currentStep === 2) {
      setCurrentStep(prevState => prevState - 1);
    }
  };

  const onToolListingClick = selectedStep => {
    if (selectedStep?.step == backToolListingStep) {
      navigation.goBack();
    } else if (selectedStep?.step === initialStep) {
      dispatch(resetSaveActiveScreenRequest()); //pressing 'done' button should clear reducer
      dispatch(clearActiveCategoryTool());
      setCompareButtonVisible(false);
      setCurrentStep(initialStep);
    }
  };

  const showCompareButton = value => {
    setCompareButtonVisible(value || false);
  };

  const onDropdownStepSelect = (selectedStep, values, isValid) => {
    if (!isValid) {
      return;
    }

    if (currentStep === selectedStep?.step) return;
    else if (selectedStep?.step === initialStep) {
      dispatch(clearActiveCategoryTool());
      setCurrentStep(prevState => (prevState = selectedStep?.step));
    } else if (selectedStep?.step === totalToolSteps) {
      const localSum = calculatePensSum(values);
      localSum && setCurrentStep(prevState => (prevState = selectedStep?.step));
      saveManureScreener(values, true);
    }
  };

  //save manure screener data in local db
  const saveManureScreener = (values, flag = f) => {
    const { isEditable } = visitState?.visit || {};
    if (!!penList?.length && isEditable && isDirty) {
      const data = saveManureScreenerData(values, penList);

      dispatch(
        saveManureScreenerRequest({
          localVisitId: visitState?.visit.id,
          pen: data,
          flag: flag,
        }),
      );

      if (
        manureScreenerState === null ||
        manureScreenerState?.mstState === null ||
        manureScreenerState?.mstState?.mstGoal === null ||
        stringIsEmpty(manureScreenerState || manureScreenerState?.mstState)
      ) {
        const pen = !!penList?.length
          ? penList.find(
              el => el.sv_id === values?.penId || el.id === values?.penId,
            )
          : [];

        let initialGoals = null;
        if (pen) {
          initialGoals = getInitialGoalsValue(pen);
        } else {
          const penPayload = {
            name: values?.penName,
            sv_id: values?.penId,
            id: values?.penId,
          };
          initialGoals = getInitialGoalsValue(penPayload);
        }

        dispatch(
          saveManureScreenerGoalsRequest({
            localVisitId: visitState?.visit.id,
            goals: { ...initialGoals },
          }),
        );
      }
    }
  };

  return [
    currentStep,
    compareButtonVisible,
    onNextStepClick,
    onPrevStepClick,
    onDropdownStepSelect,
    showCompareButton,
    saveManureScreener,
    onToolListingClick,
    isDirty,
    setIsDirty,
  ];
};

export default useSteps;
