// modules
import { useCallback } from 'react';

//redux
import { useDispatch } from 'react-redux';

// localization
import i18n from '../../../../../../../localization/i18n';

// constants
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
} from '../../../../../../../constants/AppConstants';
import { isOnline } from '../../../../../../../services/netInfoService';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../../../../../../store/actions/tool';
import { TOAST_TYPE } from '../../../../../../../constants/FormConstants';

import { showToast } from '../../../../../../common/CustomToast';

const useExport = () => {
  //api calling
  const dispatch = useDispatch();

  //download graph data
  const downloadMSTGraph = useCallback(
    async (model, type) => {
      if (await isOnline()) {
        if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
          dispatch(
            downloadToolExcelRequest({
              exportType: EXPORT_REPORT_TYPES.MANURE_SCREENER_TOOL,
              model,
            }),
          );
        } else {
          dispatch(
            downloadToolImageRequest({
              exportType: EXPORT_REPORT_TYPES.MANURE_SCREENER_TOOL,
              model,
            }),
          );
        }
      } else {
        showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
      }
    },
    [dispatch],
  );

  const onShareMSTData = useCallback(
    async (model, type, exportMethod) => {
      if (await isOnline()) {
        //share excel
        if (
          type === GRAPH_EXPORT_OPTIONS.EXCEL &&
          exportMethod == GRAPH_HEADER_OPTIONS.SHARE
        ) {
          dispatch(
            emailToolExcelRequest({
              exportType: EXPORT_REPORT_TYPES.MANURE_SCREENER_TOOL,
              model,
            }),
          );
          return;
        }
        // share image
        dispatch(
          emailToolImageRequest({
            exportType: EXPORT_REPORT_TYPES.MANURE_SCREENER_TOOL,
            model,
          }),
        );
      } else {
        showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
      }
    },
    [dispatch],
  );

  return {
    downloadMSTGraph,
    onShareMSTData,
  };
};

export default useExport;
