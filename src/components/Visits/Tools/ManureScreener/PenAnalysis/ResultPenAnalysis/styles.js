import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';
export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingLeft: normalize(10),
  },
  infoColumn: {
    flexDirection: 'column',
  },
  summaryParent: {
    flexDirection: 'column',
    marginHorizontal: normalize(20),
    marginTop: normalize(16),
    justifyContent: 'space-between',
    // gap: 10,
    gap: '53rem',
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: normalize(6),
  },
  statsTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    paddingBottom: normalize(7),
    color: colors.grey9,
  },
  summaryAverage: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  statsValue: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  leftMargin: {
    // marginLeft: normalize(20),
    // marginTop: normalize(10)
  },
  penAnalysisTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(20),
    color: colors.alphabetIndex,
    letterSpacing: normalize(0.15),
  },
  penAnalysisValue: {
    fontFamily: fonts.HelveticaNeueBold,
  },
  dataLineColor: colors.secondary2,

  yAxisLabel: {
    fill: colors.alphabetIndex,
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(11),
  },
  extraGraphWidth: normalize(20),
  reduceGraphHeight: normalize(90),

  graphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth + 30,
    },
    android: {
      width: Platform.deviceWidth + 20,
    },
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.39
          : Platform.deviceHeight * 0.46,
    },
    android: {
      height: normalize(Platform.deviceHeight / 2) - 50,
    },
  }),

  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight + 0)
          : Platform.deviceHeight - 30,
    },
    android: {
      width: normalize(Platform.deviceHeight) - 50,
    },
  }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 85)
          : normalize(Platform.deviceWidth - 100),
    },
    android: {
      height: normalize(Platform.deviceWidth - 120),
    },
  }),
  tabsPosition: {
    // position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    gap: '5rem',
    alignContent: 'auto',
  },
  domainPadding: {
    x: [0, 35],
  },
  axisStyles: {
    stroke: colors.grey1,
    strokeWidth: 1,
    strokeDasharray: '1, 5',
  },
  transparentValue: {
    fill: colors.transparent,
  },
  offsetX: RNPlatform.select({
    ios: Platform.deviceWidth - 32,
    android: Platform.deviceWidth - 40,
  }),
  offsetXLandscape: RNPlatform.select({
    ios:
      Platform.deviceHeight < 700
        ? Platform.deviceHeight - 33
        : Platform.deviceHeight - 80,
    android: normalize(Platform.deviceHeight - 75),
  }),
};
