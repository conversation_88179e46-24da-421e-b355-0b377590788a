// modules
import React, { useState, useEffect, useMemo } from 'react';
import { Safe<PERSON>reaView, ScrollView, Text, View } from 'react-native';

//redux
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// components
import ToolGraph from '../../../../common/ToolGraph';
import ManureScreenerBarGraph from '../../../../../../common/ManureScreenerGraph';

// localization
import i18n from '../../../../../../../localization/i18n';

// constants
import {
  DATE_FORMATS,
  VISIT_TABLE_FIELDS,
} from '../../../../../../../constants/AppConstants';
import { MANURE_SCREENER_GRAPH_LEGENDS } from '../../../../../../../constants/toolsConstants/ManureScreenerConstants';

// helpers
import { sortRecentVisitsForGraph } from '../../../../../../../helpers/toolHelper';
import { stringIsEmpty } from '../../../../../../../helpers/alphaNumericHelper';
import {
  exportManureScreenerGraph,
  getFormattedRecentVisits,
  getGraphLegends,
  parseManureScoreGraphData,
} from '../../../../../../../helpers/manureScreenerHelper';
import { getFormattedDate } from '../../../../../../../helpers/dateHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../../../store/actions/tool';

//USED BECAUSE MANURE SCREENER GRAPH EXPECTS PLACEHOLDER X AND Y DATA ELSE IT WILL PRINT 'false'
const DEFAULT_DATA = [
  {
    dataPoints: [
      {
        x: '09/27',
        y: 0,
      },
    ],
  },
];

const ManurePenAnalysisGraph = ({
  selectedPen,
  penData,
  manureScreenerTool,
  selectedVisits,
  onDownloadPress,
  onShareClick,
}) => {
  //api calling
  const dispatch = useDispatch();

  //redux states
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);
  const manureScreener = useSelector(state => state.manureScreener.mstState);

  //local states
  const [recentVisits, setRecentVisits] = useState([]);
  const [penGraphData, setPenGraphData] = useState([]);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  //data parsing
  const labels = useMemo(() => getGraphLegends(recentVisits), [recentVisits]);

  useEffect(() => {
    const { visit } = visitState || {};
    const siteId = !stringIsEmpty(visit?.siteId)
      ? visit?.siteId
      : visit?.localSiteId;
    const accountId = !stringIsEmpty(visit?.customerId)
      ? visit?.customerId
      : visit?.localCustomerId;

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visit.id,
        tool: VISIT_TABLE_FIELDS.MANURE_SCREENER_TOOL,
      }),
    );
  }, []);

  useEffect(() => {
    if (!toolState.recentVisitsLoading) {
      let data = getFormattedRecentVisits(toolState.recentVisits);
      data = sortRecentVisitsForGraph(data);
      setRecentVisits(data);
    }
  }, [toolState.recentVisitsLoading]);

  useEffect(() => {
    if (recentVisits?.length) {
      const visits = recentVisits?.filter(el =>
        selectedVisits?.includes(el.visitId),
      );
      const results = parseManureScoreGraphData(
        visits,
        manureScreenerTool,
        manureScreener?.mstGoal,
        selectedPen?.name,
      );
      setPenGraphData(results);
    }
  }, [recentVisits, selectedVisits, manureScreenerTool]);

  //CDEA-3245: if data for graph isn't properly formatted then set default data
  useEffect(() => {
    if (penGraphData?.length > 0) {
      //if dataPoints is empty then set default data
      if (penGraphData?.[0]?.dataPoints?.length === 0) {
        DEFAULT_DATA[0].dataPoints[0].x =
          labels?.[0] ||
          getFormattedDate(visitState?.visit?.visitDate, DATE_FORMATS.MM_dd);
        setPenGraphData(DEFAULT_DATA);
      }
    }
  }, [penGraphData]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const GraphComponent = props => {
    return (
      <>
        {penGraphData?.length > 0 && (
          <View>
            <ScrollView
              horizontal={true}
              showsHorizontalScrollIndicator={false}>
              <ManureScreenerBarGraph
                useTiltedLabels
                hasYOffset
                barWidth={12}
                barOffset={18}
                verticalTickCount={5}
                xAxisFormatter={t => {
                  return !!t?.length && t?.slice(0, 5);
                }}
                valuesFormatter={t => {
                  return !!t > 0 ? parseFloat(t) : '';
                }}
                yAxisFormatter={t => {
                  if (Math.round(t) == 0 || Math.round(t) == '0') {
                    return Math.round(t);
                  } else {
                    return Math.round(t) + '%';
                  }
                }}
                verticalAxisLabel={i18n.t('onScreen(%)')}
                labels={labels?.length ? labels : []}
                data={penGraphData || []}
                width={
                  landscapeModalVisible
                    ? styles.graphWidthLandscape(
                        penGraphData?.[0].dataPoints?.length,
                      ).width
                    : styles.graphWidth(penGraphData?.[0]?.dataPoints?.length)
                        .width
                }
                xDomainPadding={{
                  x: [
                    penGraphData?.[0].dataPoints?.length * 17,
                    penGraphData?.[0].dataPoints?.length * 15,
                  ],
                }}
                height={
                  landscapeModalVisible
                    ? styles.graphHeightLandscape.height
                    : styles.forageGraphHeight.height
                }
                customYAxisLabelStyle={styles.forageGraphYAxisLabel}
              />
            </ScrollView>

            {/* Legends */}
            <View style={styles.legendContainer(landscapeModalVisible)}>
              {MANURE_SCREENER_GRAPH_LEGENDS.map((legend, index) => {
                return (
                  <View
                    style={styles.legendItem}
                    key={`${legend.title}-${index}`}>
                    <View style={styles.legendCircle(legend.color)}></View>
                    <Text style={styles.legendText}>{legend.title}</Text>
                  </View>
                );
              })}
            </View>
          </View>
        )}
      </>
    );
  };

  const customGraphTitleComponent = (
    <View style={styles.infoColumn}>
      <Text style={styles.penAnalysisTitle}>
        {i18n.t('pen') || ''}:{' '}
        <Text style={styles.penAnalysisValue}>{selectedPen?.name || ''}</Text>
      </Text>
    </View>
  );

  return (
    <ToolGraph
      showExpandIcon
      handleExpandIconPress={onExpandIconPress}
      showDownloadIcon={!landscapeModalVisible}
      onDownloadPress={option => {
        const visits = recentVisits?.filter(el =>
          selectedVisits?.includes(el.visitId),
        );
        const result = exportManureScreenerGraph(
          visitState,
          visits,
          manureScreenerTool,
          manureScreener.mstGoal,
          selectedPen?.name,
        );
        onDownloadPress(result, option);
      }}
      onSharePress={(option, exportMethod) => {
        const visits = recentVisits?.filter(el =>
          selectedVisits?.includes(el.visitId),
        );
        const result = exportManureScreenerGraph(
          visitState,
          visits,
          manureScreenerTool,
          manureScreener.mstGoal,
          selectedPen?.name,
        );
        onShareClick(result, option, exportMethod);
      }}
      showShareIcon={!landscapeModalVisible}
      landscapeModalVisible={landscapeModalVisible}
      onExpandIconPress={onExpandIconPress}
      customGraphTitleComponent={customGraphTitleComponent}
      graphComponent={
        <SafeAreaView>
          <View style={styles.leftPadding}>
            <GraphComponent />
          </View>
        </SafeAreaView>
      }
    />
  );
};

export default ManurePenAnalysisGraph;
