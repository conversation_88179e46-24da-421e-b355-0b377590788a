import { StyleSheet } from 'react-native';
import colors from '../../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../../constants/theme/variables/platform';
import DeviceInfo from 'react-native-device-info';

export default StyleSheet.create({
  graphStyle: {
    marginVertical: normalize(8),
  },
  dotsProps: {
    r: '6',
    strokeWidth: '2',
    stroke: colors.white,
  },
  chartStyles: {
    margin: 0,
    padding: 0,
    marginRight: 0,
    marginLeft: 0,
    paddingRight: 0,
    paddingLeft: 0,
  },
  dotButton: {
    height: normalize(22),
    width: normalize(35),
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dotSelected: {
    backgroundColor: colors.white,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4.0,
    elevation: 8,
    borderRadius: 4,
  },
  dotNumber: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(10),
    lineHeight: normalize(12),
    fill: colors.secondary2,
    letterSpacing: normalize(0.5),
    color: colors.secondary2,
  },

  verticalLabels: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(10),
    lineHeight: normalize(12),
    letterSpacing: normalize(0.15),
    fill: colors.alphabetIndex,
  },
  horizontalLabels: {
    fontFamily: fonts.HelveticaNeueRegular,
    letterSpacing: normalize(0.15),
    fill: colors.alphabetIndex,
    fontSize: normalize(11),
    lineHeight: normalize(13),
  },
  graphOffset1: {
    offset: '0%',
    stopColor: colors.activeTabColor,
  },
  graphOffset2: {
    offset: '100%',
    stopColor: colors.white,
  },
  areaDataStyles: {
    stroke: colors.activeTabColor,
    fillOpacity: 0.3,
    strokeWidth: 3,
  },
  scatterDataStyles: {
    fill: colors.activeTabColor,
  },
  axisStyles: {
    stroke: colors.grey14,
    strokeWidth: 1,
    strokeDasharray: '5, 5',
  },

  lineGraphContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(12),
  },
  yLabelText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(11),
    lineHeight: normalize(13),
    letterSpacing: normalize(0.15),
    color: colors.alphabetIndex,
    transform: [{ rotate: '-90deg' }],
    marginRight: normalize(-10),
  },
  offsetX: RNPlatform.select({
    ios: Platform.deviceWidth - 20,
    android: Platform.deviceWidth - 40,
  }),
  offsetXLandscape: RNPlatform.select({
    ios:
      Platform.deviceHeight < 700
        ? Platform.deviceHeight - 33
        : Platform.deviceHeight - 80,
    android: normalize(Platform.deviceHeight - 75),
  }),

  graphWidth: dataLength => ({
    width: normalize(170 + dataLength * 80),
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.5
          : Platform.deviceHeight * 0.51,
    },
    android: {
      height: normalize(Platform.deviceHeight / 2),
    },
  }),
  graphWidthLandscape: dataLength =>
    RNPlatform.select({
      ios: {
        width:
          Platform.deviceHeight < 700
            ? Math.max(
                normalize(500 + dataLength * 90),
                normalize(Platform.deviceHeight - 40),
              )
            : Math.max(
                normalize(500 + dataLength * 90),
                normalize(Platform.deviceHeight - 120),
              ),
      },
      android: {
        width: Math.max(normalize(500 + dataLength * 90), normalize(720)),
      },
    }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 150)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.45)
          : normalize(Platform.deviceWidth - 180),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 400 : 162),
      ),
    },
  }),
  forageGraphHeight: {
    height: normalize(Platform.deviceHeight * 0.42),
  },
  legendContainer: landscapeModalVisible => ({
    flexDirection: 'row',
    marginTop: normalize(20),
    marginBottom: landscapeModalVisible ? normalize(12) : normalize(42),
    marginLeft: normalize(6),
    flexWrap: 'wrap',
  }),
  legendItem: {
    flexDirection: 'row',
    marginRight: normalize(20),
    alignItems: 'center',
    marginBottom: normalize(8),
    // marginBottom: !status ? normalize(8) : normalize(0),
    width: normalize(70),
  },

  legendItemBottomSpacing: {
    marginBottom: normalize(6),
  },
  legendCircle: backgroundColor => ({
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(50),
    backgroundColor: backgroundColor,
    marginRight: normalize(12),
  }),
  legendText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(15),
    color: colors.grey1,
    letterSpacing: 0.15,
  },

  infoColumn: {
    flexDirection: 'column',
  },
  leftPadding: {
    paddingLeft: normalize(18),
  },
  scrollViewStyles: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
  penAnalysisTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(12),
    color: colors.alphabetIndex,
    letterSpacing: normalize(0.15),
  },

  penAnalysisValue: {
    fontFamily: fonts.HelveticaNeueBold,
  },
  forageGraphYAxisLabel: { margin: normalize(-29) },
  legendInnerContainer: landscapeModalVisible =>
    !landscapeModalVisible
      ? {
          flexDirection: 'row',
          marginTop: normalize(7),
          marginBottom: normalize(48),
          justifyContent: 'space-between',
          flexWrap: 'wrap',
        }
      : {
          flex: 1,
          flexWrap: 'wrap',
          flexDirection: 'row',
          justifyContent: 'space-between',
        },
});
