import { StyleSheet } from 'react-native';
import colors from '../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default StyleSheet.create({
  flexOne: {
    flex: 1,
  },
  container: {
    // width: '100%',
    backgroundColor: colors.white,
    paddingHorizontal: normalize(20),
  },
  keyboardContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  dropdownTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: normalize(20),
  },
  dropdownText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: 0.15,
    marginRight: normalize(8),
  },

  manureScreenerNameText: {
    fontWeight: '400',
    color: colors.grey1,
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: normalize(0.2),
    fontFamily: customFont.HelveticaNeueRegular,
  },

  inputView: {
    width: normalize(98),
    height: normalize(48),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: colors.grey4,
  },
  input: {
    width: '100%',
    height: normalize(50),
    color: colors.black,
    textAlign: 'center',
  },
  customContainerStyles: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  customInputContainerStyle: {
    marginRight: normalize(-10),
    width: normalize(110),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
  },
  customFieldLabel: {
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    paddingLeft: normalize(5),
  },
  customTextAreaContainerStyle: {
    height: normalize(85),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
    paddingBottom: normalize(5),
    // paddingVertical: normalize(5),
  },
  customInputStyle: {
    height: normalize(78),
  },
  contentContainerStyle: {
    // paddingBottom: normalize(30),
  },
});
