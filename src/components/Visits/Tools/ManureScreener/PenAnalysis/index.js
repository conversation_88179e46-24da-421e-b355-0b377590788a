// modules
import React, { useState, useRef, useEffect } from 'react';

//react native
import { useIsFocused } from '@react-navigation/native';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TextInput,
} from 'react-native';

// styles
import styles from './styles';

//redux
import { useDispatch, useSelector } from 'react-redux';

//lodash
import _ from 'lodash';

// SVG_ICONS
import { CHEVRON_DOWN_BLUE_ICON } from '../../../../../constants/AssetSVGConstants';

//FORM CONSTANT
import {
  BOTTOM_SHEET_TYPE,
  CONTENT_TYPE,
  INPUT_TYPE,
  KEYBOARD_TYPE,
  MANURE_SCREENER_TOOL,
  MUNARE_SCREENING_FIELDS,
} from '../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../localization/i18n';

// Reusable Components
import FormInput from '../../../../common/FormInput';
import ResultPenAnalysis from './ResultPenAnalysis';
import NumberFormInput from '../../../../common/NumberFormInput';
import CustomBottomSheet from '../../../../common/CustomBottomSheet';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';

//colors
import { normalize } from '../../../../../constants/theme/variables/customFont';

//constants
import { NEXT_FIELD_TEXT } from '../../../../../constants/AppConstants';
import {
  FORM_INPUT_REFERENCE,
  MANURE_SCREENER_INPUT_VALIDATIONS,
} from '../../../../../constants/toolsConstants/ManureScreenerConstants';

//helpers
import {
  getFormattedCommaNumber,
  stringIsEmpty,
} from '../../../../../helpers/alphaNumericHelper';
import { saveSelectedPenInReducer } from '../../../../../helpers/visitHelper';
import {
  penExistsInPublishedVisit,
  saveManureScreenerData,
} from '../../../../../helpers/manureScreenerHelper';

//actions
import { saveManureScreenerRequest } from '../../../../../store/actions/tools/manureScreener';

const PenAnalysis = props => {
  const {
    currentStep,
    totalToolSteps,
    selectedVisits,
    openToolSheet,
    onTabChange,
    values,
    healthCurrentActivePen,
    handleBlur,
    handleChange,
    onPenChanged,
    pensList,
    dirty,
    setFieldValue,
    selectedPen,
    setSelectedPen,
    isDirty,
    setIsDirty,
    setPensSum,
  } = props;

  const dispatch = useDispatch();
  const isFocus = useIsFocused();

  //ref
  const viewRef = useRef(null);
  const inputReferences = useRef([]);
  const scrollViewRef = useRef();

  //local states

  const [showPenBottomSheet, setShowPenBottomSheet] = useState(false);
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  //redux states
  const visitState = useSelector(state => state.visit);
  const { isEditable = false } = visitState?.visit || false;

  const penAnalysisData = visitState?.visit?.manureScreener
    ? JSON.parse(visitState?.visit?.manureScreener)
    : [];

  //handlers

  useEffect(() => {
    setIsDirty(dirty);
  }, [dirty]);

  useEffect(() => {
    if (isEditable && dirty) {
      const data = saveManureScreenerData(values, pensList);
      dispatch(
        saveManureScreenerRequest({
          localVisitId: visitState?.visit.id,
          pen: data,
        }),
      );
    }
  }, [openToolSheet]);

  const openPenBottomSheet = () => {
    setShowPenBottomSheet(true);
  };

  const closePenBottomSheet = () => {
    setShowPenBottomSheet(false);
  };

  const debounce_fun = _.debounce(value => {
    setFieldValue([MUNARE_SCREENING_FIELDS.OBSERVATION], value);
  }, 1000);

  const onPenChange = item => {
    setPensSum(false);
    if (dirty) {
      const flag = true;
      const data = saveManureScreenerData(values, pensList);
      dispatch(
        saveManureScreenerRequest({
          localVisitId: visitState?.visit.id,
          pen: data,
          flag,
        }),
      );
    }
    saveSelectedPenInReducer(dispatch, item);
    setSelectedPen(item);
    onPenChanged(item);
  };

  const renderPenAnalysisResults = () => {
    const { id, visitDate, visitName } = visitState.visit;
    return (
      <ResultPenAnalysis
        selectedPen={selectedPen}
        penData={{
          visitId: id,
          date: visitDate,
          visitName,
        }}
        onTabChange={onTabChange}
        manureScreenerTool={{
          ...(values || {}),
          visitId: id,
          date: visitDate,
          visitName,
        }}
        selectedVisits={selectedVisits}
      />
    );
  };
  return (
    <>
      {currentStep === totalToolSteps ? (
        renderPenAnalysisResults()
      ) : (
        <KeyboardAvoidingView
          style={styles.keyboardContainer}
          behavior={Platform.OS == 'ios' ? 'padding' : null}>
          <CustomInputAccessoryView doneAction={action} type={type} />

          <ScrollView
            ref={scrollViewRef}
            keyboardDismissMode="none"
            keyboardShouldPersistTaps="always">
            <View
              style={styles.container}
              onTouchStart={() => Keyboard.dismiss()}>
              <View>
                <TouchableOpacity
                  style={styles.dropdownTextContainer}
                  onPress={openPenBottomSheet}>
                  <Text style={styles.dropdownText}>
                    {selectedPen?.name || ''}
                  </Text>
                  <CHEVRON_DOWN_BLUE_ICON
                    width={normalize(12)}
                    height={normalize(8)}
                  />
                </TouchableOpacity>
              </View>

              <View style={{ marginBottom: normalize(8) }}>
                <FormInput
                  customLabelStyle={styles.manureScreenerNameText}
                  label={i18n.t('manureScreenerName')}
                  type={INPUT_TYPE.TEXT}
                  placeholder={i18n.t('enterName')}
                  disabled={!isEditable}
                  blurOnSubmit={true}
                  onChange={handleChange(
                    `${MUNARE_SCREENING_FIELDS.MST_SCORE_NAME}`,
                  )}
                  onBlur={() => {
                    const value =
                      values?.[MUNARE_SCREENING_FIELDS.MST_SCORE_NAME]?.trim();
                    if (stringIsEmpty(value)) {
                      setFieldValue(
                        MUNARE_SCREENING_FIELDS.MST_SCORE_NAME,
                        MUNARE_SCREENING_FIELDS.DEFAULT_MANURE_NAME,
                      );
                    }
                    handleBlur(`${MUNARE_SCREENING_FIELDS.MST_SCORE_NAME}`);
                  }}
                  value={
                    values?.[
                      MUNARE_SCREENING_FIELDS.MST_SCORE_NAME
                    ]?.toString() || ''
                  }
                  onSubmitEditing={() => {
                    inputReferences?.current[
                      FORM_INPUT_REFERENCE.FIELD_TWO
                    ]?.focus();
                  }}
                  reference={input => {
                    inputReferences.current[FORM_INPUT_REFERENCE.FIELD_ONE] =
                      input;
                  }}
                  inputAccessoryViewID="customInputAccessoryView"
                  returnKeyType={NEXT_FIELD_TEXT.NEXT}
                  onFocus={() => {
                    setType(CONTENT_TYPE.TEXT);
                    setAction({
                      currentRef:
                        inputReferences?.current[
                          FORM_INPUT_REFERENCE.FIELD_TWO
                        ],
                    });
                  }}
                />
              </View>

              <View style={{ marginVertical: normalize(8) }}>
                <NumberFormInput
                  label={i18n.t('top(g)')}
                  disabled={!isEditable}
                  placeholder={i18n.t('twoNumberPlaceholder')}
                  minValue={MANURE_SCREENER_INPUT_VALIDATIONS.MIN}
                  maxValue={MANURE_SCREENER_INPUT_VALIDATIONS.MAX}
                  isInteger={true}
                  keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
                  onBlur={handleBlur(
                    `${MUNARE_SCREENING_FIELDS.TOP_SCALE_AMOUNT_IN_GRAMS}`,
                  )}
                  value={
                    penExistsInPublishedVisit(
                      isEditable,
                      penAnalysisData,
                      selectedPen,
                    )
                      ? // ? getFormattedCommaNumber(
                        //     values?.[
                        //       MUNARE_SCREENING_FIELDS.TOP_SCALE_AMOUNT_IN_GRAMS
                        //     ]?.toString(),
                        //   )

                        values?.[
                          MUNARE_SCREENING_FIELDS.TOP_SCALE_AMOUNT_IN_GRAMS
                        ]?.toString()
                      : '-'
                  }
                  onChange={handleChange(
                    `${MUNARE_SCREENING_FIELDS.TOP_SCALE_AMOUNT_IN_GRAMS}`,
                  )}
                  blurOnSubmit={false}
                  customLabelStyle={styles.customFieldLabel}
                  customInputContainerStyle={styles.customInputContainerStyle}
                  customContainerStyle={styles.customContainerStyles}
                  reference={input => {
                    inputReferences.current[FORM_INPUT_REFERENCE.FIELD_TWO] =
                      input;
                  }}
                  onSubmitEditing={() => {
                    inputReferences?.current[
                      FORM_INPUT_REFERENCE.FIELD_THREE
                    ]?.focus();
                  }}
                  inputAccessoryViewID="customInputAccessoryView"
                  returnKeyType={NEXT_FIELD_TEXT.NEXT}
                  onFocus={() => {
                    setType(CONTENT_TYPE.NUMBER);
                    setAction({
                      currentRef:
                        inputReferences?.current[
                          FORM_INPUT_REFERENCE.FIELD_THREE
                        ],
                    });
                  }}
                  hasCommas={true}
                />
              </View>
              <View style={{ marginVertical: normalize(8) }}>
                <NumberFormInput
                  label={i18n.t('middle(g)')}
                  disabled={!isEditable}
                  placeholder={i18n.t('twoNumberPlaceholder')}
                  minValue={MANURE_SCREENER_INPUT_VALIDATIONS.MIN}
                  maxValue={MANURE_SCREENER_INPUT_VALIDATIONS.MAX}
                  isInteger={true}
                  keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
                  value={
                    penExistsInPublishedVisit(
                      isEditable,
                      penAnalysisData,
                      selectedPen,
                    )
                      ? // ? getFormattedCommaNumber(
                        //     values?.[
                        //       MUNARE_SCREENING_FIELDS.MID_SCALE_AMOUNT_IN_GRAMS
                        //     ]?.toString(),
                        //   )

                        values?.[
                          MUNARE_SCREENING_FIELDS.MID_SCALE_AMOUNT_IN_GRAMS
                        ]?.toString()
                      : '-'
                  }
                  onChange={handleChange(
                    `${MUNARE_SCREENING_FIELDS.MID_SCALE_AMOUNT_IN_GRAMS}`,
                  )}
                  onBlur={handleBlur(
                    `${MUNARE_SCREENING_FIELDS.MID_SCALE_AMOUNT_IN_GRAMS}`,
                  )}
                  blurOnSubmit={false}
                  customLabelStyle={styles.customFieldLabel}
                  customInputContainerStyle={styles.customInputContainerStyle}
                  customContainerStyle={styles.customContainerStyles}
                  reference={input => {
                    inputReferences.current[FORM_INPUT_REFERENCE.FIELD_THREE] =
                      input;
                  }}
                  onSubmitEditing={() => {
                    inputReferences?.current[
                      FORM_INPUT_REFERENCE.FIELD_FOUR
                    ]?.focus();
                  }}
                  inputAccessoryViewID="customInputAccessoryView"
                  returnKeyType={NEXT_FIELD_TEXT.NEXT}
                  onFocus={() => {
                    setType(CONTENT_TYPE.NUMBER);
                    setAction({
                      currentRef:
                        inputReferences?.current[
                          FORM_INPUT_REFERENCE.FIELD_FOUR
                        ],
                    });
                  }}
                  hasCommas={true}
                />
              </View>
              <View style={{ marginVertical: normalize(8) }} ref={viewRef}>
                <NumberFormInput
                  label={i18n.t('bottom(g)')}
                  disabled={!isEditable}
                  placeholder={i18n.t('twoNumberPlaceholder')}
                  minValue={MANURE_SCREENER_INPUT_VALIDATIONS.MIN}
                  maxValue={MANURE_SCREENER_INPUT_VALIDATIONS.MAX}
                  decimalPoints={
                    MANURE_SCREENER_INPUT_VALIDATIONS.TWO_DECIMAL_POINTS
                  }
                  isInteger={false}
                  value={
                    penExistsInPublishedVisit(
                      isEditable,
                      penAnalysisData,
                      selectedPen,
                    )
                      ? // ? getFormattedCommaNumber(
                        //     values?.[
                        //       MUNARE_SCREENING_FIELDS.BOTTOM_SCALE_AMOUNT_IN_GRAMS
                        //     ]?.toString(),
                        //   )

                        values?.[
                          MUNARE_SCREENING_FIELDS.BOTTOM_SCALE_AMOUNT_IN_GRAMS
                        ]?.toString()
                      : '-'
                  }
                  onChange={handleChange(
                    `${MUNARE_SCREENING_FIELDS.BOTTOM_SCALE_AMOUNT_IN_GRAMS}`,
                  )}
                  onBlur={handleBlur(
                    `${MUNARE_SCREENING_FIELDS.BOTTOM_SCALE_AMOUNT_IN_GRAMS}`,
                  )}
                  blurOnSubmit={false}
                  customLabelStyle={styles.customFieldLabel}
                  customInputContainerStyle={styles.customInputContainerStyle}
                  customContainerStyle={styles.customContainerStyles}
                  reference={input => {
                    inputReferences.current[FORM_INPUT_REFERENCE.FIELD_FOUR] =
                      input;
                  }}
                  onSubmitEditing={() => {
                    inputReferences?.current[
                      FORM_INPUT_REFERENCE.FIELD_FIVE
                    ]?.focus();
                  }}
                  inputAccessoryViewID="customInputAccessoryView"
                  returnKeyType={NEXT_FIELD_TEXT.NEXT}
                  onFocus={() => {
                    setType(CONTENT_TYPE.NUMBER);
                    setAction({
                      currentRef:
                        inputReferences?.current[
                          FORM_INPUT_REFERENCE.FIELD_FIVE
                        ],
                    });
                  }}
                  hasCommas={true}
                />
              </View>
              <View style={{ marginVertical: normalize(8) }}>
                <FormInput
                  type={INPUT_TYPE.TEXT}
                  disabled={!isEditable}
                  scrollEnabled={true}
                  blurOnSubmit={true}
                  multiline={true}
                  placeholder={i18n.t('enterText')}
                  customLabelStyle={styles.customFieldLabel}
                  label={i18n.t('observation')}
                  customInputStyle={styles.customInputStyle}
                  customInputContainer={styles.customTextAreaContainerStyle}
                  defaultValue={
                    penExistsInPublishedVisit(
                      isEditable,
                      penAnalysisData,
                      selectedPen,
                    )
                      ? values?.[
                          MUNARE_SCREENING_FIELDS.OBSERVATION
                        ]?.toString()
                      : '-'
                  }
                  onChange={text => debounce_fun(text)}
                  onBlur={handleBlur(`${MUNARE_SCREENING_FIELDS.OBSERVATION}`)}
                  reference={input => {
                    inputReferences.current[FORM_INPUT_REFERENCE.FIELD_FIVE] =
                      input;
                  }}
                  inputAccessoryViewID="customInputAccessoryView"
                  returnKeyType={NEXT_FIELD_TEXT.DONE}
                  onFocus={() => {
                    setType(CONTENT_TYPE.NUMBER);
                    setAction({
                      dismiss: true,
                    });
                    Platform.OS === 'ios'
                      ? viewRef?.current?.measure((x, y) => {
                          scrollViewRef?.current?.scrollTo({
                            x: 0,
                            y,
                            animated: true,
                          });
                        })
                      : null;
                  }}
                />
              </View>
            </View>

            {showPenBottomSheet && (
              <CustomBottomSheet
                type={BOTTOM_SHEET_TYPE.SIMPLE}
                selectLabel={i18n.t('selectPen')}
                searchPlaceHolder={i18n.t('searchPen')}
                data={pensList || []}
                onChange={onPenChange}
                onClose={closePenBottomSheet}
              />
            )}
          </ScrollView>
        </KeyboardAvoidingView>
      )}
    </>
  );
};
export default PenAnalysis;
