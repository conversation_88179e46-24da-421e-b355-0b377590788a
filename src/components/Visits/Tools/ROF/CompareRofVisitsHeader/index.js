// modules
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// components
import ToolsInProgress from '../../common/ToolsInProgress';

// constants
import { TOTAL_ROF_STEPS } from '../../../../../constants/toolsConstants/ROFConstants';
import { RESULTS_TABS_TYPES } from '../../../../../constants/AppConstants';

// actions
import { changeComparingRofVisits } from '../../../../../store/actions/tools/rof';

const CompareRofVisitsHeader = ({ id, isEditable, currentStep }) => {
  const dispatch = useDispatch();

  const recentVisits = useSelector(state => state.tool.recentVisits);
  const activeResultTab = useSelector(state => state.rof.activeResultsTab);
  const comparingRofVisits = useSelector(state => state.rof.comparingRofVisits);

  useEffect(() => {
    let visitIds = [];
    if (recentVisits.length > 0) {
      recentVisits.map(visit => visitIds.push(visit.id));
    }
    dispatch(changeComparingRofVisits(visitIds));
  }, [recentVisits]);

  const onConfirmCompareVisits = selectedVisits => {
    dispatch(changeComparingRofVisits(selectedVisits));
  };

  if (activeResultTab?.key === RESULTS_TABS_TYPES.SUMMARY) {
    return null;
  }

  return (
    <ToolsInProgress
      currentVisit={id}
      isEditable={isEditable}
      currentStep={currentStep}
      compareModalData={recentVisits || []}
      selectedVisits={comparingRofVisits}
      onConfirmCompareModal={onConfirmCompareVisits}
      showCompareGraph={currentStep === TOTAL_ROF_STEPS}
      header={currentStep === TOTAL_ROF_STEPS ? false : true}
    />
  );
};

export default CompareRofVisitsHeader;
