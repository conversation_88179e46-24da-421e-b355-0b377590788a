// modules
import React from 'react';
import Animated, { SlideInLeft, SlideOutLeft } from 'react-native-reanimated';
import { useDispatch } from 'react-redux';

//components
import { ToolAnalysisItem } from '../../../common/ToolAnalysisTypes';

// styles
import styles from './styles';

// constants
import {
  INDIVIDUAL_COW_ANALYSIS_ICON,
  TMR_ANALYSIS_ICON,
} from '../../../../../../constants/AssetSVGConstants';
import { ROF_FORM_TYPES } from '../../../../../../constants/toolsConstants/ROFConstants';

// localization
import i18n from '../../../../../../localization/i18n';
import { setActiveCategoryToolRequest } from '../../../../../../store/actions/tool';

const ROFToolAnalysisTypes = ({ onStepChange }) => {
  const dispatch = useDispatch();

  // TODO: update function as props
  const handleToolSelect = tool => {
    dispatch(setActiveCategoryToolRequest(tool));
    onStepChange();
  };

  return (
    <Animated.View
      style={styles.container}
      entering={SlideInLeft.duration(250)}
      exiting={SlideOutLeft.duration(250)}>
      <ToolAnalysisItem
        analysisTitle={i18n.t('tmr')}
        onPress={() =>
          handleToolSelect({
            toolType: ROF_FORM_TYPES.TMR,
            toolName: i18n.t('tmr'),
          })
        }
        iconComponent={<TMR_ANALYSIS_ICON {...styles.tmrIcon} />}
      />
      <ToolAnalysisItem
        analysisTitle={i18n.t('individualCow')}
        onPress={() =>
          handleToolSelect({
            toolType: ROF_FORM_TYPES.INDIVIDUAL_COWS,
            toolName: i18n.t('individualCow'),
          })
        }
        iconComponent={
          <INDIVIDUAL_COW_ANALYSIS_ICON {...styles.individualCowIcon} />
        }
      />
    </Animated.View>
  );
};

export default ROFToolAnalysisTypes;
