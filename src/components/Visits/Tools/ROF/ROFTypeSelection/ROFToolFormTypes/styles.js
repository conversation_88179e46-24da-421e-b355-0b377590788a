import DeviceInfo from 'react-native-device-info';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  container: {
    flex: 1,
    paddingHorizontal: normalize(20),
    backgroundColor: colors.white,
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  analysisAvatar: {
    height: normalize(46),
    width: normalize(46),
    backgroundColor: colors.userAvatarBackground,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  analysisTitle: {
    flex: 1,
    fontSize: normalize(14),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.grey1,
    lineHeight: normalize(20),
    letterSpacing: 0.2,
  },
  rightIconStyles: {
    stroke: colors.alphabetIndex,
    strokeWidth: normalize(2),
    width: normalize(12),
    height: normalize(12),
  },
  tmrIcon: {
    width: normalize(20),
    height: normalize(20),
  },
  individualCowIcon: {
    width: normalize(25),
    height: normalize(25),
  },
};
