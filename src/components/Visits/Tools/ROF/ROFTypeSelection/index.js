// modules
import React from 'react';

// components
import ROFToolAnalysisTypes from './ROFToolFormTypes';
import ROFFormView from '..';

// constants
import { ROF_FORM_TYPES } from '../../../../../constants/toolsConstants/ROFConstants';

const ROFTypeSelection = ({
  selectedCategoryTool,
  currentStep,
  isEditable = false,
  onNextStepClick,
  saveROFData,
  formRef,
}) => {
  switch (selectedCategoryTool?.toolType || '') {
    case ROF_FORM_TYPES.TMR:
      return (
        <ROFFormView
          formRef={formRef}
          isEditable={isEditable}
          currentStep={currentStep}
          saveROFData={saveROFData}
          formType={ROF_FORM_TYPES.TMR}
        />
      );

    case ROF_FORM_TYPES.INDIVIDUAL_COWS:
      return (
        <ROFFormView
          formRef={formRef}
          isEditable={isEditable}
          currentStep={currentStep}
          saveROFData={saveROFData}
          formType={ROF_FORM_TYPES.INDIVIDUAL_COWS}
        />
      );

    default:
      return <ROFToolAnalysisTypes onStepChange={onNextStepClick} />;
  }
};

export default ROFTypeSelection;
