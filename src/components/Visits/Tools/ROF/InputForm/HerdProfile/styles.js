import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  formInputStyle: {
    width: normalize(150),
  },
  formLabelStyle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    maxWidth: normalize(220),
  },
  switchButtonContainer: {
    marginBottom: normalize(20),
  },
  switchContainerStyle: {
    height: normalize(37),
  },
  numberInputContainerStyles: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // borderWidth: 2,
    marginBottom: normalize(20),
  },
  numberInputStyle: {
    marginRight: normalize(-10),
    width: normalize(150),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
  },
  numberInputFieldLabel: {
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    paddingLeft: normalize(5),
    // textTransform: 'capitalize',
  },
};
