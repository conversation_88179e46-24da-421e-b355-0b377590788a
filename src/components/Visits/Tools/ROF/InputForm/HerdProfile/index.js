// modules
import React, { useRef } from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';
import { useFormikContext } from 'formik';

import InputRow from '../../../common/InputRow';
import SwitchButton from '../../../../../common/SwitchButton';
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';
import HorizontalSingleSelect from '../../../../../common/HorizontalSingleSelect';
import NumberFormInput from '../../../../../common/NumberFormInput';

import styles from './styles';

import i18n from '../../../../../../localization/i18n';

import {
  ENUM_CONSTANTS,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';
import {
  BOTTOM_SHEET_TYPE,
  ROF_FIELDS,
} from '../../../../../../constants/FormConstants';
import {
  ROF_DECIMAL_PLACES,
  ROF_INTEGER_MAX_VALUE,
  ROF_INTEGER_MIN_VALUE,
} from '../../../../../../constants/toolsConstants/ROFConstants';

import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';

const HerdProfile = props => {
  const inputRefs = useRef([]);
  let { isEditable, openFeedingAccordion } = props;
  let { handleChange, setFieldValue, values } = useFormikContext();

  const enumState = useSelector(state => state.enums.enum);

  return (
    <View style={styles.container}>
      <CustomBottomSheet
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        isSingleLineField
        label={i18n.t('breed')}
        required={true}
        disabled={!isEditable}
        value={values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.BREED]}
        infoText={i18n.t('selectOne')}
        selectLabel={i18n.t('select')}
        placeholder={i18n.t('select')}
        data={enumState?.[ENUM_CONSTANTS.BREED_RETURN_OVER_FEED]}
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.HERD_PROFILE}.${ROF_FIELDS.BREED}`,
            v.key,
          );
        }}
      />
      {/* text - only show when breed is selected as others*/}
      {values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.BREED] == 'Other' && (
        <InputRow
          title={i18n.t('other')}
          disabled={!isEditable}
          placeholder={i18n.t('enterValue')}
          value={values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.OTHER_BREED_TYPE]}
          onChange={handleChange(
            `${ROF_FIELDS.HERD_PROFILE}.${ROF_FIELDS.OTHER_BREED_TYPE}`,
          )}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onSubmitEditing={() => {
            setTimeout(() => inputRefs.current[0].focus(), 100);
          }}
          selectTextOnFocus={false}
          customContainerStyle={styles.customContainerStyle}
          customInputContainerStyle={styles.formInputStyle}
          textAlign={'flex-start'}
          isRequired
        />
      )}
      <CustomBottomSheet
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        isSingleLineField
        label={i18n.t('feeding')}
        placeholder={i18n.t('select')}
        disabled={!isEditable}
        value={values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.FEEDING_TYPE]}
        infoText={i18n.t('selectOne')}
        selectLabel={i18n.t('select')}
        data={enumState?.[ENUM_CONSTANTS.FEEDING]}
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.HERD_PROFILE}.${ROF_FIELDS.FEEDING_TYPE}`,
            v.key,
          );
        }}
      />
      {/* number */}
      <NumberFormInput
        label={i18n.t('numberOfTmrGroups')}
        disabled={!isEditable}
        placeholder={i18n.t('numberPlaceholder')}
        isInteger
        value={values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.NUMBER_OF_TMR_GROUPS]}
        onChange={handleChange(
          `${ROF_FIELDS.HERD_PROFILE}.${ROF_FIELDS.NUMBER_OF_TMR_GROUPS}`,
        )}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        blurOnSubmit={false}
        selectTextOnFocus={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[0] = reference)}
        onSubmitEditing={() => {
          setTimeout(() => inputRefs.current[1].focus(), 100);
        }}
      />
      <View style={styles.formInputView}>
        <HorizontalSingleSelect
          label={i18n.t('selectAnyFollowing')}
          customLabelStyle={styles.formLabelStyle}
          options={
            !stringIsEmpty(enumState)
              ? enumState?.[ENUM_CONSTANTS.SUPPLEMENT_TYPES]
              : []
          }
          value={values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.TYPE_OF_SUPPLEMENT]}
          onChange={handleChange(
            `${ROF_FIELDS.HERD_PROFILE}.${ROF_FIELDS.TYPE_OF_SUPPLEMENT}`,
          )}
          isEditable={!isEditable}
        />
      </View>
      <SwitchButton
        label={i18n.t('coolAid')}
        defaultSwitchState={
          values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.COOL_AID]
        }
        onChangeSelection={selectionState =>
          setFieldValue(
            `${ROF_FIELDS.HERD_PROFILE}.${ROF_FIELDS.COOL_AID}`,
            selectionState,
          )
        }
        disabled={!isEditable}
        containerStyles={styles.switchButtonContainer}
        customLabelStyle={styles.formLabelStyle}
        customSwitchContainerStyle={styles.switchContainerStyle}
      />
      <SwitchButton
        label={i18n.t('fortissaFit')}
        defaultSwitchState={
          values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.FORTISSA_FIT]
        }
        onChangeSelection={selectionState =>
          setFieldValue(
            `${ROF_FIELDS.HERD_PROFILE}.${ROF_FIELDS.FORTISSA_FIT}`,
            selectionState,
          )
        }
        disabled={!isEditable}
        containerStyles={styles.switchButtonContainer}
        customLabelStyle={styles.formLabelStyle}
        customSwitchContainerStyle={styles.switchContainerStyle}
      />
      <NumberFormInput
        label={i18n.t('MUN(mg/dL)')}
        disabled={!isEditable}
        placeholder={i18n.t('numberPlaceholder')}
        value={values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.MUN]}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        decimalPoints={ROF_DECIMAL_PLACES}
        onChange={handleChange(`${ROF_FIELDS.HERD_PROFILE}.${ROF_FIELDS.MUN}`)}
        blurOnSubmit={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[1] = reference)}
        onSubmitEditing={() => inputRefs.current[2].focus()}
      />
      <NumberFormInput
        label={i18n.t('milkingPerDay')}
        disabled={!isEditable}
        placeholder={i18n.t('numberPlaceholder')}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={9}
        isInteger
        value={values[ROF_FIELDS.HERD_PROFILE][ROF_FIELDS.MILKING_PER_DAY]}
        onChange={handleChange(
          `${ROF_FIELDS.HERD_PROFILE}.${ROF_FIELDS.MILKING_PER_DAY}`,
        )}
        blurOnSubmit={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[2] = reference)}
        onSubmitEditing={() => {
          openFeedingAccordion(true);
        }}
      />
    </View>
  );
};

export default HerdProfile;
