// modules
import React, { useRef, useEffect } from 'react';
import { View, Keyboard } from 'react-native';
import { useFormikContext } from 'formik';
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

// components
import NumberFormInput from '../../../../../common/NumberFormInput';

// localization
import i18n from '../../../../../../localization/i18n';

import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';

// constants
import { ROF_FIELDS } from '../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../constants/AppConstants';
import { getMilkProductionOutputsInitialFormValues } from '../../../../../../helpers/rofHelper';
import {
  ROF_INTEGER_MAX_VALUE,
  ROF_INTEGER_MIN_VALUE,
} from '../../../../../../constants/toolsConstants/ROFConstants';

const MilkProductionOutputs = props => {
  const inputRefs = useRef([]);

  let { isEditable, formType } = props;
  let { setFieldValue, values } = useFormikContext();
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  const milkProductionOutputsKeys = Object.keys(
    values[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS],
  );

  useEffect(() => {
    inputRefs.current[0].focus();
  }, []);

  return (
    <View style={styles.container}>
      {milkProductionOutputsKeys?.map(outputKey => {
        return (
          <NumberFormInput
            label={i18n
              .t(outputKey)
              .replaceAll('$', currencySymbol)
              .replaceAll('kg', weightUnit)}
            disabled={
              outputKey === ROF_FIELDS.MAX_ALLOWED && isEditable ? false : true
            }
            hasCommas
            value={values[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS][outputKey]}
            onChange={v => {
              if (outputKey === ROF_FIELDS.MAX_ALLOWED) {
                setFieldValue(
                  `${ROF_FIELDS.MILK_PRODUCTION_OUTPUTS}.${ROF_FIELDS.MAX_ALLOWED}`,
                  v,
                );
              }
            }}
            onBlur={() => {
              if (outputKey === ROF_FIELDS.MAX_ALLOWED) {
                getMilkProductionOutputsInitialFormValues(
                  values,
                  null,
                  formType,
                  setFieldValue,
                  true,
                );
              }
            }}
            minValue={
              outputKey === ROF_FIELDS.MAX_ALLOWED
                ? ROF_INTEGER_MIN_VALUE
                : null
            }
            maxValue={
              outputKey === ROF_FIELDS.MAX_ALLOWED
                ? ROF_INTEGER_MAX_VALUE
                : null
            }
            blurOnSubmit={true}
            selectTextOnFocus={false}
            customLabelStyle={styles.numberInputFieldLabel}
            customInputContainerStyle={styles.numberInputStyle}
            customContainerStyle={styles.numberInputContainerStyles}
            returnKeyType={NEXT_FIELD_TEXT.DONE}
            reference={reference => {
              if (outputKey === ROF_FIELDS.MAX_ALLOWED)
                inputRefs.current[0] = reference;
            }}
            onSubmitEditing={() => Keyboard.dismiss()}
          />
        );
      })}
    </View>
  );
};

export default MilkProductionOutputs;
