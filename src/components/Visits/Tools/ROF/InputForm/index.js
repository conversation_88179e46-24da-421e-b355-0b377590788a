// modules
import React, { useRef } from 'react';
import { Formik } from 'formik';
import { KeyboardAvoidingView, ScrollView } from 'native-base';
import { useDispatch, useSelector } from 'react-redux';

//components
import ToggleROFFormValidation from './ToggleROFFormValidation';
import ROFFormAccordionContainer from './ROFFormAccordionContainer';

// styles
import styles from './styles';

import { initializeROFFormData } from '../../../../../helpers/rofHelper';
import ROFValidationSchema from '../../../../../helpers/validation/rof';

import { saveROFToolDataRequest } from '../../../../../store/actions/tools/rof';

const ROFInputForm = props => {
  let dispatch = useDispatch();
  const siteData = useSelector(state => state.tool?.siteData);
  const visitData = useSelector(state => state.visit?.visit);
  const rofPriceList = useSelector(state => state.rof?.rofPriceList);
  const enumState = useSelector(state => state.enums.enum);
  const previousROFVisitData =
    useSelector(state => state.rof?.previousROFVisitData) || {};
  const unitOfMeasure = visitData?.unitOfMeasure || {};

  let { isEditable, formRef, formType } = props;

  let scrollViewRef = useRef();

  return (
    <Formik
      innerRef={formRef}
      initialValues={initializeROFFormData(
        formType,
        siteData,
        visitData,
        enumState,
        unitOfMeasure,
        isEditable,
        rofPriceList,
        previousROFVisitData,
      )}
      validateOnChange={true}
      enableReinitialize={true}
      validateOnMount={true}
      validationSchema={ROFValidationSchema}
      onSubmit={values => {
        dispatch(
          saveROFToolDataRequest({
            values: values,
            formType: formType,
            localVisitId: visitData?.id,
            unitOfMeasure,
          }),
        );
      }}>
      {formikProps => {
        return (
          <>
            <KeyboardAvoidingView style={styles.container} behavior="padding">
              <ScrollView
                keyboardDismissMode="on-drag"
                keyboardShouldPersistTaps="handled"
                contentContainerStyle={{ flexGrow: 1 }}
                ref={scrollViewRef}>
                <ToggleROFFormValidation />
                <ROFFormAccordionContainer
                  isEditable={isEditable}
                  scrollViewRef={scrollViewRef}
                  formType={formType}
                  unitOfMeasure={unitOfMeasure}
                />
              </ScrollView>
            </KeyboardAvoidingView>
          </>
        );
      }}
    </Formik>
  );
};

export default ROFInputForm;
