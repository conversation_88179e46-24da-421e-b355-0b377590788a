import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flexDirection: 'column',
  },
  numberInputContainerStyles: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: normalize(20),
  },
  numberInputStyle: {
    marginRight: normalize(-10),
    width: normalize(150),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
  },
  numberInputFieldLabel: {
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    paddingLeft: normalize(5),
    width: normalize(170),
  },
};
