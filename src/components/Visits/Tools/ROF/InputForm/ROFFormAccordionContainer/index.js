// modules
import React, { useState } from 'react';
import { View } from 'react-native';
import { useFormikContext } from 'formik';

//components
import ROFAccordions from './formAccordions';
import HerdProfile from '../HerdProfile';
import Feeding from '../Feeding';
import MilkProduction from '../MilkProduction';
import MilkProductionOutputs from '../MilkProductionOutputs';

// styles
import styles from './styles';

import { ROF_FORM_ACCORDIONS } from '../../../../../../constants/toolsConstants/ROFConstants';

import i18n from '../../../../../../localization/i18n';

import { getMilkProductionOutputsInitialFormValues } from '../../../../../../helpers/rofHelper';

const ROFFormAccordionContainer = props => {
  let { scrollViewRef, isEditable, formType, unitOfMeasure } = props;
  let { values, setFieldValue } = useFormikContext();

  const [selectedAccordion, setSelectedAccordion] = useState(
    ROF_FORM_ACCORDIONS.HERD_PROFILE,
  );

  let openFeedingAccordion = () => {
    scrollViewRef?.current?.scrollTo({
      x: 0,
      y: 70,
      animated: true,
    });
    setSelectedAccordion(ROF_FORM_ACCORDIONS.FEEDING);
  };

  let openMilkProductionAccordion = () => {
    scrollViewRef?.current?.scrollTo({
      x: 0,
      y: 120,
      animated: true,
    });

    setSelectedAccordion(ROF_FORM_ACCORDIONS.MILK_PRODUCTION);
  };

  let openMilkProductionOutputsAccordion = () => {
    scrollViewRef?.current?.scrollTo({
      x: 0,
      y: 180,
      animated: true,
    });

    setSelectedAccordion(ROF_FORM_ACCORDIONS.MILK_PRODUCTION_OUTPUTS);
    //calculate milk output formulae
    getMilkProductionOutputsInitialFormValues(
      values,
      unitOfMeasure,
      formType,
      setFieldValue,
      true,
    );
  };

  return (
    <View style={styles.formContainer}>
      <ROFAccordions
        isSelected={selectedAccordion === ROF_FORM_ACCORDIONS.HERD_PROFILE}
        onOpenAccordion={() => {
          setSelectedAccordion(ROF_FORM_ACCORDIONS.HERD_PROFILE);
        }}
        title={i18n.t('herdProfile')}
        content={
          <HerdProfile
            isEditable={isEditable}
            openFeedingAccordion={openFeedingAccordion}
          />
        }
      />
      <ROFAccordions
        isSelected={selectedAccordion === ROF_FORM_ACCORDIONS.FEEDING}
        onOpenAccordion={openFeedingAccordion}
        title={i18n.t('feeding')}
        content={
          <Feeding
            isEditable={isEditable}
            formType={formType}
            openMilkProductionAccordion={openMilkProductionAccordion}
          />
        }
      />
      <ROFAccordions
        isSelected={selectedAccordion === ROF_FORM_ACCORDIONS.MILK_PRODUCTION}
        onOpenAccordion={openMilkProductionAccordion}
        title={i18n.t('milkProduction')}
        content={
          <MilkProduction
            isEditable={isEditable}
            openMilkProductionOutputsAccordion={
              openMilkProductionOutputsAccordion
            }
          />
        }
      />
      <ROFAccordions
        isSelected={
          selectedAccordion === ROF_FORM_ACCORDIONS.MILK_PRODUCTION_OUTPUTS
        }
        onOpenAccordion={openMilkProductionOutputsAccordion}
        title={i18n.t('milkProductionOutputs')}
        content={
          <MilkProductionOutputs isEditable={isEditable} formType={formType} />
        }
      />
    </View>
  );
};

export default ROFFormAccordionContainer;
