// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

// styles
import styles from './styles';

// common component

import {
  ACCORDION_DOWN_ICON,
  ACCORDION_RIGHT_ICON,
} from '../../../../../../../constants/AssetSVGConstants';
import { normalize } from '../../../../../../../constants/theme/variables/customFont';

const ROFAccordions = props => {
  const { isSelected, onOpenAccordion, content, title } = props;

  return (
    <View style={styles.container}>
      <TouchableOpacity activeOpacity={0.9} onPress={onOpenAccordion}>
        <View style={styles.accordionContainer}>
          <Text style={styles.accordionTitle}>{title}</Text>
          {isSelected ? (
            <ACCORDION_DOWN_ICON width={normalize(14)} height={normalize(14)} />
          ) : (
            <ACCORDION_RIGHT_ICON
              width={normalize(14)}
              height={normalize(14)}
            />
          )}
        </View>
      </TouchableOpacity>

      {isSelected && <>{content}</>}
    </View>
  );
};

export default ROFAccordions;
