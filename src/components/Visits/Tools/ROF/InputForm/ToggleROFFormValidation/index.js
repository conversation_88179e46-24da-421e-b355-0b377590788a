// modules
import { useEffect } from 'react';
import { useFormikContext } from 'formik';
import { useDispatch, useSelector } from 'react-redux';

// actions
import { updateResultButton } from '../../../../../../store/actions/tools/rof';

const ToggleROFFormValidation = () => {
  const dispatch = useDispatch();

  const { isValid } = useFormikContext();

  const isFormValid = useSelector(state => state.rof?.isFormValid);

  useEffect(() => {
    if (isValid && !isFormValid) {
      dispatch(updateResultButton(true));
    } else if (!isValid && isFormValid) {
      dispatch(updateResultButton(false));
    }
  }, [isValid]);

  return null;
};

export default ToggleROFFormValidation;
