import React from 'react';
import { View, Text } from 'react-native';

import FormButton from '../../../../../../../common/FormButton';

import { BUTTON_TYPE } from '../../../../../../../../constants/FormConstants';

import i18n from '../../../../../../../../localization/i18n';
import styles from './styles';

const AddNewIngredientButton = props => {
  let { buttonText, addNewAccordionPress, isEditable } = props;

  return (
    <FormButton
      type={BUTTON_TYPE.PRIMARY}
      disabled={!isEditable}
      customButtonStyle={[
        styles.customButton,
        !isEditable && styles.disabledButton,
      ]}
      onPress={addNewAccordionPress}
      label={
        <View style={styles.labelContainer}>
          <Text style={[styles.plusIcon, !isEditable && styles.disabledText]}>
            {i18n.t('plusSign')}
          </Text>
          <Text
            style={[styles.createPenText, !isEditable && styles.disabledText]}>
            {buttonText}
          </Text>
        </View>
      }
    />
  );
};

export default AddNewIngredientButton;
