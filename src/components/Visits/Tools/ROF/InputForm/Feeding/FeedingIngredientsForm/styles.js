import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flexDirection: 'column',
  },
  ingredientContainer: {
    paddingHorizontal: normalize(10),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
    marginBottom: normalize(10),
  },
  formInputStyle: {
    width: normalize(150),
    marginTop: normalize(20),
  },
  categoryHeader: {
    marginVertical: normalize(13),
  },
  headerText: {
    fontSize: normalize(16),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.primaryMain,
    lineHeight: normalize(16),
    letterSpacing: 0.2,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dropdownWithDeleteContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  dropdownContainer: {
    flex: 1,
  },
  deleteButton: {
    backgroundColor: 'transparent',
    borderTopWidth: normalize(1),
    borderColor: colors.lightWhite,
  },
  deleteButtonText: {
    color: colors.error5,
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: normalize(0.2),
  },
  disabledDeleteButtonText: {
    color: colors.grey2,
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: normalize(0.2),
  },
  numberInputContainerStyles: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // borderWidth: 2,
    marginBottom: normalize(20),
  },
  numberInputStyle: {
    marginRight: normalize(-10),
    width: normalize(150),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
  },
  numberInputFieldLabel: {
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    paddingLeft: normalize(5),
    // textTransform: 'capitalize',
  },
  horizontalLine: {
    width: 100,
    backgroundColor: colors.grey1,
  },
};
