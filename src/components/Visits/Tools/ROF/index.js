// modules
import React from 'react';

import ROFInputForm from './InputForm';
import ROFResults from './Results';

import { ROF_STEPS } from '../../../../constants/toolsConstants/ROFConstants';

const ROFFormView = props => {
  let { isEditable, formRef, currentStep, formType, saveROFData } = props;

  switch (currentStep) {
    case ROF_STEPS[2].step:
      return <ROFResults formType={formType} />;

    default:
      return (
        <ROFInputForm
          formRef={formRef}
          isEditable={isEditable}
          currentStep={currentStep}
          saveROFData={saveROFData}
          formType={formType}
        />
      );
  }
};

export default ROFFormView;
