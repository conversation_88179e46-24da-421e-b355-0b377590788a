import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';
import DeviceInfo from 'react-native-device-info';
import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../constants/theme/variables/platform';

export default {
  container: {
    flexDirection: 'column',
  },
  accordionTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.15,
  },
  infoColumn: {
    flexDirection: 'column',
  },
  penAnalysisTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: normalize(0.15),
    fontWeight: '500',
  },
  legendContainer: landscapeModalVisible => ({
    flexDirection: 'row',
    marginTop: normalize(20),
    marginBottom: landscapeModalVisible ? normalize(12) : normalize(42),
    marginHorizontal: normalize(20),
    flexWrap: 'wrap',
  }),
  legendItem: {
    flexDirection: 'row',
    marginRight: normalize(20),
    alignItems: 'center',
    marginBottom: normalize(8),
  },
  legendCircle: backgroundColor => ({
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(50),
    backgroundColor: backgroundColor,
    marginRight: normalize(12),
  }),
  legendText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(15),
    color: colors.grey1,
    letterSpacing: 0.15,
  },
  graphWidth: dataLength => ({
    width: normalize(170 + dataLength * 80),
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.5
          : Platform.deviceHeight * 0.51,
    },
    android: {
      height: normalize(Platform.deviceHeight / 2),
    },
  }),
  graphWidthLandscape: dataLength =>
    RNPlatform.select({
      ios: {
        width:
          Platform.deviceHeight < 700
            ? Math.max(
                normalize(500 + dataLength * 90),
                normalize(Platform.deviceHeight - 40),
              )
            : Math.max(
                normalize(500 + dataLength * 90),
                normalize(Platform.deviceHeight - 120),
              ),
      },
      android: {
        width: Math.max(normalize(500 + dataLength * 90), normalize(720)),
      },
    }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 150)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.45)
          : normalize(Platform.deviceWidth - 180),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 400 : 162),
      ),
    },
  }),
  forageGraphHeight: {
    height: normalize(Platform.deviceHeight * 0.42),
  },
};
