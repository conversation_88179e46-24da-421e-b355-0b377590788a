// modules
import React from 'react';
import { View, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// components
import ROFGraph from './Graph';
import ROFSummaryAccordionContainer from './Summary';
import ToolBottomTabs from '../../common/ToolBottomTabs';

// constants
import {
  RESULTS_TABS_TYPES,
  RESULTS_TABS,
} from '../../../../../constants/AppConstants';

// actions
import { setActiveResultTab } from '../../../../../store/actions/tools/rof';

const ROFResults = props => {
  const dispatch = useDispatch();

  const activeTab = useSelector(state => state.rof.activeResultsTab);

  const _handleChangeResultsTab = tab => {
    dispatch(setActiveResultTab(tab));
  };

  return (
    <View style={styles.container}>
      <View style={styles.separator}></View>
      <ScrollView showsHorizontalScrollIndicator={false}>
        {
          {
            [RESULTS_TABS_TYPES.SUMMARY]: <ROFSummaryAccordionContainer />,
            [RESULTS_TABS_TYPES.GRAPH]: <ROFGraph formType={props.formType} />,
          }[activeTab?.key]
        }
      </ScrollView>
      <View style={styles.tabsContainer}>
        <ToolBottomTabs
          tabs={RESULTS_TABS}
          selectedTab={activeTab}
          onTabChange={_handleChangeResultsTab}
        />
      </View>
    </View>
  );
};

export default ROFResults;
