// modules
import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

// styles
import styles from './styles';

// common component

import {
  ACCORDION_DOWN_ICON,
  ACCORDION_RIGHT_ICON,
} from '../../../../../../../constants/AssetSVGConstants';
import { normalize } from '../../../../../../../constants/theme/variables/customFont';

const ROFSummaryAccordion = props => {
  const { content, title } = props;
  const [accordionSelected, setSelectedAccordion] = useState(false);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={() => setSelectedAccordion(!accordionSelected)}>
        <View style={styles.accordionContainer}>
          <Text style={styles.accordionTitle}>{title}</Text>
          {accordionSelected ? (
            <ACCORDION_DOWN_ICON width={normalize(14)} height={normalize(14)} />
          ) : (
            <ACCORDION_RIGHT_ICON
              width={normalize(14)}
              height={normalize(14)}
            />
          )}
        </View>
      </TouchableOpacity>

      {accordionSelected && <>{content}</>}
    </View>
  );
};

export default ROFSummaryAccordion;
