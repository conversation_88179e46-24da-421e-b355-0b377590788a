import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: normalize(30),
    alignItems: 'center',
    marginHorizontal: normalize(16.5),
    marginTop: normalize(8),
  },
  ingredientTextContainer: {
    flexDirection: 'column',
    height: normalize(80),
    marginHorizontal: normalize(16.5),
    marginTop: normalize(8),
  },
  ingredientContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: normalize(30),
    alignItems: 'center',
    marginHorizontal: normalize(6.5),
  },
  accordionText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    color: colors.grey1,
  },
  ingredientHeadingText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: 'bold',
    fontSize: normalize(12),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    color: colors.grey1,
  },
};
