// modules
import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

//components
import ROFSummaryAccordion from './ROFSummaryAccordion';
import ROFSummaryAccordionContent from './ROFSummaryAccordionContent';

// styles
import styles from './styles';

import i18n from '../../../../../../localization/i18n';

const ROFSummaryAccordionContainer = props => {
  const rofToolData = useSelector(state => state.rof?.rofToolData);
  const selectedCategoryTool = useSelector(
    state => state.tool.selectedCategoryTool,
  );
  let summaryData = rofToolData?.[selectedCategoryTool?.toolType]?.summary;

  return (
    <View>
      <View style={styles.formContainer}>
        {summaryData &&
          Object.entries(summaryData)?.map(([sectionKey, fields]) => {
            return (
              <ROFSummaryAccordion
                key={'_ROFSummaryAccordion-' + sectionKey}
                title={i18n.t(sectionKey)}
                content={<ROFSummaryAccordionContent data={fields} />}
              />
            );
          })}
      </View>
    </View>
  );
};

export default ROFSummaryAccordionContainer;
