import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flexDirection: 'column',
    flex: 1,
    backgroundColor: colors.white,
  },
  accordionTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.15,
  },
  tabsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
  },
  separator: {
    height: 1,
    backgroundColor: colors.grey8,
    width: '100%',
  },
};
