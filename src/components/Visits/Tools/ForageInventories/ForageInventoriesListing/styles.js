import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopWidth: 0.5,
    borderTopColor: colors.grey8,
  },
  inventoryContainer: {
    flexDirection: 'column',
    marginHorizontal: normalize(20),
    marginTop: normalize(16),
    marginBottom: normalize(16),
  },
  sectionTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  inventoryRow: { flexDirection: 'row', marginTop: normalize(16) },
  inventoryItem: {
    flex: 1,
    height: normalize(44),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
    borderRadius: normalize(4),
    alignItems: 'center',
    justifyContent: 'center',
  },
  inventoryItemMargin: {
    marginLeft: normalize(8),
    marginRight: normalize(8),
  },
  inventoryText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  singleInventoryRow: { flexDirection: 'row', marginTop: normalize(8) },
  marginTop: { marginTop: normalize(16) },
  flatList: {
    flex: 1,
    marginHorizontal: normalize(20),
    marginTop: normalize(16),
  },
  inventoryRowMargin: {
    marginBottom: normalize(8),
  },
  emptyListContainer: {
    flex: 1,
  },
};
