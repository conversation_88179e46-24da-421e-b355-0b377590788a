// modules
import React, { useState, useEffect, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// reusable components
import InventoryTabSelector from '../common/InventoryTabSelector';
import InventoryItemRow from '../common/InventoryItemRow';
import { showAlertMsg } from '../../../../common/Alerts';
import EmptyListComponent from '../../../../common/EmptyListComponent';
import InventoryListAndRestoreIcon from '../common/ListAndRestoreRow';

// constants
import ROUTE_CONSTANTS from '../../../../../constants/RouteConstants';
import { FORAGE_INVENTORIES_INVENTORY } from '../../../../../constants/AppConstants';
import { NO_RESULTS_ICON } from '../../../../../constants/AssetSVGConstants';

// actions
import {
  getPileAndBunkerRequest,
  deleteInventoryItemRequest,
  resetDeleteInventoryItemRequest,
} from '../../../../../store/actions/tools/pileBunker';
import { hideLoader, showLoader } from '../../../../../store/actions/view';

// helpers
import {
  getDefaultInventoryName,
  getInventoryNumber,
  getRouteBySelectedIndex,
} from '../../../../../helpers/forageInventoriesHelper';
import { stringIsEmpty } from '../../../../../helpers/alphaNumericHelper';

const ForageInventoriesListing = props => {
  const { screenDisabled } = props;
  const ref = useRef();
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [inventoryTypes, setInventoryTypes] = useState([]);
  const [allData, setAllData] = useState([]);
  const [list, setList] = useState([]);

  const dispatch = useDispatch();
  const navigation = useNavigation();

  const visitState = useSelector(state => state.visit?.visit);
  const pileBunkerState = useSelector(state => state.pileBunker);
  const enumState = useSelector(state => state.enums.enum);

  useEffect(() => {
    if (enumState?.feedStorageType) {
      if (enumState?.feedStorageType.length > 0) {
        setInventoryTypes(enumState?.feedStorageType);
      }
    }
    // Fetch Forage Inventories Tool Data
    getToolData();
  }, []);

  useEffect(() => {
    if (!pileBunkerState.pileAndBunkerLoading) {
      if (pileBunkerState.pileAndBunkerData) {
        const pileBunkerData = pileBunkerState.pileAndBunkerData;
        if (
          pileBunkerData.pileBunkers &&
          pileBunkerData.pileBunkers.length > 0
        ) {
          const inventoryList = pileBunkerData.pileBunkers.map(
            (item, index) => {
              return { data: item, dbIndex: index };
            },
          );
          setAllData(inventoryList);
        } else {
          setAllData([]);
        }
      }
    }
  }, [pileBunkerState.pileAndBunkerLoading]);

  useEffect(() => {
    const selectedInventoryKey = inventoryTypes[selectedTabIndex]?.key;
    const filteredData = allData.filter(
      item => item.data.isPileOrBunker === selectedInventoryKey,
    );
    const inventoryDetails = inventoryTypeDataCount(enumState?.feedStorageType);

    setList(filteredData);
    setInventoryTypes(inventoryDetails);
  }, [allData, selectedTabIndex]);

  useEffect(() => {
    if (!pileBunkerState.deleteInventoryItemLoading) {
      dispatch(hideLoader());
    }
  }, [pileBunkerState.deleteInventoryItemLoading]);

  useEffect(() => {
    if (pileBunkerState.deleteInventoryItemSuccess) {
      dispatch(resetDeleteInventoryItemRequest());
      // Fetch Forage Inventories Tool Data
      getToolData();
    }
  }, [pileBunkerState.deleteInventoryItemSuccess]);

  const getToolData = () =>
    dispatch(
      getPileAndBunkerRequest({
        localId: visitState?.id,
      }),
    );

  const openInventoryScreen = (route, index, editMode = false, data = null) => {
    const selectedInventory = inventoryTypes[index];

    let navObj = {
      editMode: editMode || false,
      screenDisabled: screenDisabled,
      defaultName: getDefaultInventoryName(
        selectedInventory?.value,
        getInventoryNumber(allData, selectedInventory?.key),
      ),
      selectedInventoryIndex: index,
    };

    if (editMode) {
      navObj.data = data;
      navObj.defaultName = data?.data?.name;
    }
    navigation.navigate(route, navObj);
  };

  const deleteInventory = dbIndex => {
    dispatch(showLoader());
    dispatch(
      deleteInventoryItemRequest({ dbIndex, localVisitId: visitState?.id }),
    );
  };

  const isItemSynced = item => {
    return item?.data?.id && !stringIsEmpty(item?.data?.id);
  };

  const renderItem = ({ item }) => {
    return (
      <View style={styles.inventoryRowMargin}>
        <InventoryItemRow
          name={item?.data?.name}
          disabled={screenDisabled || isItemSynced(item)}
          onPress={() => {
            openInventoryScreen(
              getRouteBySelectedIndex(selectedTabIndex),
              selectedTabIndex,
              true,
              item,
            );
          }}
          onDeletePress={() => {
            showAlertMsg('', i18n.t('deleteInventoryMessage'), [
              {
                text: i18n.t('no'),
                onPress: () => {},
              },
              {
                text: i18n.t('yes'),
                onPress: () => {
                  deleteInventory(item.dbIndex);
                },
              },
            ]);
          }}
        />
      </View>
    );
  };

  const onScroll = index => {
    if (ref && ref.current?.scrollTo) {
      ref?.current?.scrollTo({ animated: true, x: index * 80 });
    }
  };

  const inventoryTypeDataCount = inventoryDetail => {
    const inventoryCount = allData.reduce((count, item) => {
      const key = item.data.isPileOrBunker;
      count[key] = (count[key] || 0) + 1;
      return count;
    }, {});

    const updatedInventoryDetail = inventoryDetail.map(item => ({
      ...item,
      count: inventoryCount[item.key] || 0,
    }));

    return updatedInventoryDetail;
  };

  return (
    <>
      <View style={styles.container}>
        <View style={styles.inventoryContainer}>
          <Text style={styles.sectionTitle}>{i18n.t('addInventories')}</Text>
          <View style={styles.inventoryRow}>
            <TouchableOpacity
              style={styles.inventoryItem}
              disabled={screenDisabled}
              onPress={() => {
                openInventoryScreen(
                  ROUTE_CONSTANTS.FORAGE_INVENTORIES_PILE,
                  FORAGE_INVENTORIES_INVENTORY[0].id,
                );
                setSelectedTabIndex(0);
                onScroll(0);
              }}>
              <Text style={styles.inventoryText}>{i18n.t('pile')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.inventoryItem, styles.inventoryItemMargin]}
              disabled={screenDisabled}
              onPress={() => {
                openInventoryScreen(
                  ROUTE_CONSTANTS.FORAGE_INVENTORIES_BUNKER,
                  FORAGE_INVENTORIES_INVENTORY[1].id,
                );
                setSelectedTabIndex(1);
                onScroll(1);
              }}>
              <Text style={styles.inventoryText}>{i18n.t('bunker')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.inventoryItem}
              disabled={screenDisabled}
              onPress={() => {
                openInventoryScreen(
                  ROUTE_CONSTANTS.FORAGE_INVENTORIES_BAG,
                  FORAGE_INVENTORIES_INVENTORY[4].id,
                );
                setSelectedTabIndex(4);
                onScroll(4);
              }}>
              <Text style={styles.inventoryText}>{i18n.t('bag')}</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.singleInventoryRow}>
            <TouchableOpacity
              style={styles.inventoryItem}
              disabled={screenDisabled}
              onPress={() => {
                // navigateToTopUnloadingSilo()
                openInventoryScreen(
                  ROUTE_CONSTANTS.TOP_UNLOADING_SILO,
                  FORAGE_INVENTORIES_INVENTORY[2].id,
                );
                setSelectedTabIndex(2);
                onScroll(2);
              }}>
              <Text style={styles.inventoryText}>
                {i18n.t('topUnloadingSilo')}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={styles.singleInventoryRow}>
            <TouchableOpacity
              style={styles.inventoryItem}
              disabled={screenDisabled}
              onPress={() => {
                // navigateToBottomUnloadingSilo()
                openInventoryScreen(
                  ROUTE_CONSTANTS.BOTTOM_UNLOADING_SILO,
                  FORAGE_INVENTORIES_INVENTORY[3].id,
                );
                setSelectedTabIndex(3);
                onScroll(3);
              }}>
              <Text style={styles.inventoryText}>
                {i18n.t('bottomUnloadingSilo')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <InventoryListAndRestoreIcon screenDisabled={screenDisabled} />

        <View style={styles.marginTop}>
          <InventoryTabSelector
            ref={ref}
            selectedIndex={selectedTabIndex}
            item={inventoryTypes?.map(item => item)}
            onTabChange={index => {
              setSelectedTabIndex(index);
            }}
          />
        </View>

        {list && list?.length > 0 ? (
          <FlatList
            style={styles.flatList}
            data={list?.length > 0 ? list : []}
            showsVerticalScrollIndicator={false}
            renderItem={renderItem}
          />
        ) : (
          <View style={styles.emptyListContainer}>
            <EmptyListComponent
              title={i18n.t('noRecordFound')}
              description={i18n.t('tryAddingNewOne')}
              image={<NO_RESULTS_ICON />}
            />
          </View>
        )}
      </View>
    </>
  );
};

export default ForageInventoriesListing;
