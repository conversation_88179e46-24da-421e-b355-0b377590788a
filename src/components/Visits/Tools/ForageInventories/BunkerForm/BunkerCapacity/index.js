// modules
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import InputRow from '../../common/InputRow';
import CalculatedRow from '../../common/CalculatedRow';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  BLACK_INFO_ICON,
  BUNKER_ENGLISH_IMAGE,
} from '../../../../../../constants/AssetSVGConstants';
import ROUTE_CONSTANTS from '../../../../../../constants/RouteConstants';
import {
  FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES,
  FORAGE_INVENTORIES_INPUT_MAX_LIMIT,
  FORAGE_INVENTORIES_INPUT_MIN_LIMIT,
  FORAGE_INVENTORIES_DRY_MATTER_MIN_LIMIT,
  FORAGE_INVENTORIES_DRY_MATTER_MAX_LIMIT,
  BUNKER_SLOPE_GOAL_VALUE,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';
import {
  CONTENT_TYPE,
  FORAGE_INVENTORIES_BUNKER_FIELDS,
} from '../../../../../../constants/FormConstants';
import { FORAGE_INVENTORIES_TYPES } from '../../../../../../constants/toolsConstants/ForageInventories';

// helpers
import {
  getAltDensityText,
  getAltDistanceText,
  getDensityUnit,
  getDistanceUnit,
  getLabelWithUnit,
  getUnitOfMeasure,
  getBunkerTonsDM,
  getBunkerTonsAF,
  getBunkerSlope,
  getBunkerSilageAFDensity,
  getFormImageByLocale,
} from '../../../../../../helpers/forageInventoriesHelper';
import { getFormattedCommaNumber } from '../../../../../../helpers/alphaNumericHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const BunkerCapacity = props => {
  const { screenDisabled, values, references, handleChange } = props;
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const navigation = useNavigation();

  const userData = useSelector(
    state => state?.userPreferences?.userPreferences,
  );
  const selectedUnit = getUnitOfMeasure(userData);
  const distanceUnit = getDistanceUnit(userData);
  const densityUnit = getDensityUnit(userData);

  const openDensityConverterScreen = () => {
    navigation.navigate(ROUTE_CONSTANTS.DENSITY_CONVERTER);
  };

  return (
    <View style={styles.container}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.imageContainer}>
        {/* <BUNKER_ENGLISH_IMAGE {...styles.bunkerImage} /> */}
        {getFormImageByLocale(FORAGE_INVENTORIES_TYPES.BUNKER)}
        <Text
          style={[
            styles.imageText,
            getBunkerSlope(values, true) > BUNKER_SLOPE_GOAL_VALUE
              ? styles.greenText
              : styles.redText,
          ]}>
          {`${i18n.t('slope')} ${convertInputNumbersToRegionalBasis(
            getBunkerSlope(values, true).toFixed(1),
            1,
          )} ${i18n.t('to1')}`}
          <Text style={styles.imageText}>
            {` (${i18n.t('bunkerSlopeGoal')})`}
          </Text>
        </Text>
        <Text style={styles.imageText}>
          {`${i18n.t('silageAFDensity')} ${convertInputNumbersToRegionalBasis(
            getBunkerSilageAFDensity(values, true).toFixed(1),
            1,
          )}`}
        </Text>
      </View>

      <View style={styles.bunkerDimensionRow}>
        <Text style={styles.bunkerDimensionText}>
          {i18n.t('bunkerDimension')}
        </Text>
        <TouchableOpacity onPress={openDensityConverterScreen}>
          <BLACK_INFO_ICON {...styles.bigIcon} fill={styles.grey1} />
        </TouchableOpacity>
      </View>

      {/* Height */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('height'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[FORAGE_INVENTORIES_BUNKER_FIELDS.HEIGHT]?.toString() || '',
          // )}
          value={
            values[FORAGE_INVENTORIES_BUNKER_FIELDS.HEIGHT]?.toString() || ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_BUNKER_FIELDS.HEIGHT)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BUNKER_FIELDS.HEIGHT] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_BUNKER_FIELDS.TOP_WIDTH]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_BUNKER_FIELDS.HEIGHT],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[FORAGE_INVENTORIES_BUNKER_FIELDS.TOP_WIDTH],
            });
          }}
        />
      </View>

      {/* Top Width */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('topWidth'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[FORAGE_INVENTORIES_BUNKER_FIELDS.TOP_WIDTH]?.toString() ||
          //     '',
          // )}
          value={
            values[FORAGE_INVENTORIES_BUNKER_FIELDS.TOP_WIDTH]?.toString() || ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_BUNKER_FIELDS.TOP_WIDTH)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BUNKER_FIELDS.TOP_WIDTH] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_WIDTH]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_BUNKER_FIELDS.TOP_WIDTH],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_WIDTH],
            });
          }}
        />
      </View>

      {/* Bottom Width */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('bottomWidth'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_WIDTH]?.toString() ||
          //     '',
          // )}
          value={
            values[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_WIDTH]?.toString() ||
            ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_WIDTH)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_WIDTH] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_LENGTH]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_WIDTH],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_LENGTH],
            });
          }}
        />
      </View>

      {/* Bottom Length */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('bottomLength'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[
          //     FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_LENGTH
          //   ]?.toString() || '',
          // )}
          value={
            values[
              FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_LENGTH
            ]?.toString() || ''
          }
          onChange={handleChange(
            FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_LENGTH,
          )}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_LENGTH] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_BUNKER_FIELDS.DRY_MATTER]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_BUNKER_FIELDS.BOTTOM_LENGTH],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[FORAGE_INVENTORIES_BUNKER_FIELDS.DRY_MATTER],
            });
          }}
        />
      </View>

      <View style={styles.divider}></View>

      {/* Dry Matter % */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <InputRow
          title={i18n.t('dryMatterPercent')}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_DRY_MATTER_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_DRY_MATTER_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={
            values[FORAGE_INVENTORIES_BUNKER_FIELDS.DRY_MATTER]?.toString() ||
            ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_BUNKER_FIELDS.DRY_MATTER)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BUNKER_FIELDS.DRY_MATTER] = ref)
          }
          onSubmitEditing={() => {
            references[
              FORAGE_INVENTORIES_BUNKER_FIELDS.SILAGE_DM_DENSITY
            ]?.focus();
          }}
          blurOnSubmit={false}
          hideAltText={true}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[FORAGE_INVENTORIES_BUNKER_FIELDS.SILAGE_DM_DENSITY],
            });
          }}
        />
      </View>

      {/* Silage DM Density */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('silageDMDensity'), densityUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[
          //     FORAGE_INVENTORIES_BUNKER_FIELDS.SILAGE_DM_DENSITY
          //   ]?.toString() || '',
          // )}
          value={
            values[
              FORAGE_INVENTORIES_BUNKER_FIELDS.SILAGE_DM_DENSITY
            ]?.toString() || ''
          }
          onChange={handleChange(
            FORAGE_INVENTORIES_BUNKER_FIELDS.SILAGE_DM_DENSITY,
          )}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BUNKER_FIELDS.SILAGE_DM_DENSITY] =
              ref)
          }
          onSubmitEditing={() => {
            Keyboard?.dismiss();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDensityText(
            values[FORAGE_INVENTORIES_BUNKER_FIELDS.SILAGE_DM_DENSITY],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              dismiss: true,
            });
          }}
        />
      </View>

      <Text style={styles.bunkerDimensionText}>{i18n.t('capacity')}</Text>

      {/* Tons DM */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <CalculatedRow
          title={i18n.t('tonsDM')}
          value={convertInputNumbersToRegionalBasis(
            getBunkerTonsDM(values, selectedUnit, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>

      {/* Tons AF */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={i18n.t('tonsAF')}
          value={convertInputNumbersToRegionalBasis(
            getBunkerTonsAF(values, selectedUnit, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>
    </View>
  );
};

export default BunkerCapacity;
