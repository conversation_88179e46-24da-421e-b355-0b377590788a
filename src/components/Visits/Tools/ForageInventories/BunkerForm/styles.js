import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    paddingTop: normalize(16),
  },
  formInputView: {
    marginBottom: normalize(16),
  },
  customFieldLabel: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(13),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginBottom: normalize(8),
  },
  customInputStyle: {
    width: '100%',
    paddingHorizontal: normalize(16),
  },
};
