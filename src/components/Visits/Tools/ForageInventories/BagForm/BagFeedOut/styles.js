import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    paddingTop: normalize(16),
  },
  headingText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  formInputView: {
    marginBottom: normalize(16),
  },
  marginTop: { marginTop: normalize(16) },
};
