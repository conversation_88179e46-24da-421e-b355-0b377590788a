// modules
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import InputRow from '../../common/InputRow';
import CalculatedRow from '../../common/CalculatedRow';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import { BLACK_INFO_ICON } from '../../../../../../constants/AssetSVGConstants';
import ROUTE_CONSTANTS from '../../../../../../constants/RouteConstants';
import {
  FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES,
  FORAGE_INVENTORIES_INPUT_MAX_LIMIT,
  FORAGE_INVENTORIES_INPUT_MIN_LIMIT,
  FORAGE_INVENTORIES_DRY_MATTER_MIN_LIMIT,
  FORAGE_INVENTORIES_DRY_MATTER_MAX_LIMIT,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';
import {
  CONTENT_TYPE,
  FORAGE_INVENTORIES_BAG_FIELDS,
} from '../../../../../../constants/FormConstants';
import { FORAGE_INVENTORIES_TYPES } from '../../../../../../constants/toolsConstants/ForageInventories';

// helpers
import {
  getAltDensityText,
  getAltDistanceText,
  getDensityUnit,
  getDistanceUnit,
  getLabelWithUnit,
  getUnitOfMeasure,
  getBagTonsDM,
  getBagTonsAF,
  getBagSilageAFDensity,
  getFormImageByLocale,
} from '../../../../../../helpers/forageInventoriesHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const BagCapacity = props => {
  const { screenDisabled, values, references, handleChange } = props;
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const navigation = useNavigation();

  const userData = useSelector(
    state => state?.userPreferences?.userPreferences,
  );
  const selectedUnit = getUnitOfMeasure(userData);
  const distanceUnit = getDistanceUnit(userData);
  const densityUnit = getDensityUnit(userData);

  const openDensityConverterScreen = () => {
    navigation.navigate(ROUTE_CONSTANTS.DENSITY_CONVERTER);
  };

  return (
    <View style={styles.container}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.imageContainer}>
        {/* <BAG_ENGLISH_IMAGE {...styles.bagImage} /> */}
        <View style={styles.imageContainer}>
          {getFormImageByLocale(FORAGE_INVENTORIES_TYPES.BAG, styles.bagImage)}
        </View>
        <Text style={styles.imageText}>
          {`${i18n.t('silageAFDensity')} ${convertInputNumbersToRegionalBasis(
            getBagSilageAFDensity(values, true).toFixed(1),
            1,
          )}`}
        </Text>
      </View>

      <View style={styles.bagDimensionRow}>
        <Text style={styles.bagDimensionText}>{i18n.t('bagDimension')}</Text>
        <TouchableOpacity onPress={openDensityConverterScreen}>
          <BLACK_INFO_ICON {...styles.bigIcon} fill={styles.grey1} />
        </TouchableOpacity>
      </View>

      {/* Length */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('length'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={values[FORAGE_INVENTORIES_BAG_FIELDS.LENGTH]?.toString() || ''}
          onChange={handleChange(FORAGE_INVENTORIES_BAG_FIELDS.LENGTH)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BAG_FIELDS.LENGTH] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_BAG_FIELDS.DIAMETER]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_BAG_FIELDS.LENGTH],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: references[FORAGE_INVENTORIES_BAG_FIELDS.DIAMETER],
            });
          }}
        />
      </View>

      {/* Diameter */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('diameter'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={
            values[FORAGE_INVENTORIES_BAG_FIELDS.DIAMETER]?.toString() || ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_BAG_FIELDS.DIAMETER)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BAG_FIELDS.DIAMETER] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_BAG_FIELDS.DRY_MATTER]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_BAG_FIELDS.DIAMETER],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: references[FORAGE_INVENTORIES_BAG_FIELDS.DRY_MATTER],
            });
          }}
        />
      </View>

      <View style={styles.divider}></View>

      {/* Dry Matter % */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <InputRow
          title={i18n.t('dryMatterPercent')}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_DRY_MATTER_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_DRY_MATTER_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={
            values[FORAGE_INVENTORIES_BAG_FIELDS.DRY_MATTER]?.toString() || ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_BAG_FIELDS.DRY_MATTER)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BAG_FIELDS.DRY_MATTER] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_BAG_FIELDS.DM_DENSITY]?.focus();
          }}
          blurOnSubmit={false}
          hideAltText={true}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: references[FORAGE_INVENTORIES_BAG_FIELDS.DM_DENSITY],
            });
          }}
        />
      </View>

      {/* DM Density */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('dmDensity'), densityUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={
            values[FORAGE_INVENTORIES_BAG_FIELDS.DM_DENSITY]?.toString() || ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_BAG_FIELDS.DM_DENSITY)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BAG_FIELDS.DM_DENSITY] = ref)
          }
          onSubmitEditing={() => {
            Keyboard?.dismiss();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDensityText(
            values[FORAGE_INVENTORIES_BAG_FIELDS.DM_DENSITY],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              dismiss: true,
            });
          }}
        />
      </View>

      <Text style={styles.bagDimensionText}>{i18n.t('capacity')}</Text>

      {/* Tons DM */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <CalculatedRow
          title={i18n.t('tonsDM')}
          value={convertInputNumbersToRegionalBasis(
            getBagTonsDM(values, selectedUnit, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>

      {/* Tons AF */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={i18n.t('tonsAF')}
          value={convertInputNumbersToRegionalBasis(
            getBagTonsAF(values, selectedUnit, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>
    </View>
  );
};

export default BagCapacity;
