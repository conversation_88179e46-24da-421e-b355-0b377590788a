// modules
import React, { useState, useRef } from 'react';
import { View } from 'react-native';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// common component
import FormInput from '../../../../common/FormInput';
import MenuTabs from '../common/MenuTabs';
import BagCapacity from './BagCapacity';
import BagFeedOut from './BagFeedOut';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';

// constants
import colors from '../../../../../constants/theme/variables/customColor';
import {
  CONTENT_TYPE,
  FORAGE_INVENTORIES_BAG_FIELDS,
  INPUT_TYPE,
} from '../../../../../constants/FormConstants';
import {
  FORAGE_INVENTORIES_TABS,
  NEXT_FIELD_TEXT,
} from '../../../../../constants/AppConstants';

// helpers
import { getBagReferencesObject } from '../../../../../helpers/forageInventoriesHelper';
import { stringIsEmpty } from '../../../../../helpers/alphaNumericHelper';

const BagForm = props => {
  const { screenDisabled, values, defaultName, setFieldValue, handleChange } =
    props;

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const [selectedTab, setSelectedTab] = useState(
    FORAGE_INVENTORIES_TABS.CAPACITY,
  );

  const references = getBagReferencesObject();
  for (const key in references) {
    references[key] = useRef();
  }

  return (
    <View style={styles.container}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      {/* Bag Name */}
      <View style={styles.formInputView}>
        <FormInput
          label={i18n.t('bagName')}
          type={INPUT_TYPE.TEXT}
          placeholder={i18n.t('namePlaceholder')}
          placeholderColor={colors.alphabetIndex}
          disabled={screenDisabled}
          value={values[FORAGE_INVENTORIES_BAG_FIELDS.BAG_NAME]}
          onChange={value => {
            setFieldValue(FORAGE_INVENTORIES_BAG_FIELDS.BAG_NAME, value);
          }}
          reference={ref =>
            (references[FORAGE_INVENTORIES_BAG_FIELDS.BAG_NAME] = ref)
          }
          onSubmitEditing={() => {
            if (selectedTab === FORAGE_INVENTORIES_TABS.CAPACITY) {
              references[FORAGE_INVENTORIES_BAG_FIELDS.LENGTH]?.focus();
            } else {
              references[FORAGE_INVENTORIES_BAG_FIELDS.FEEDING_RATE]?.focus();
            }
          }}
          customLabelStyle={styles.customFieldLabel}
          customInputContainer={styles.customInputStyle}
          // inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.TEXT);
            setAction({
              currentRef:
                selectedTab === FORAGE_INVENTORIES_TABS.CAPACITY
                  ? references[FORAGE_INVENTORIES_BAG_FIELDS.LENGTH]
                  : references[FORAGE_INVENTORIES_BAG_FIELDS.FEEDING_RATE],
            });
          }}
          onBlur={() => {
            if (stringIsEmpty(values[FORAGE_INVENTORIES_BAG_FIELDS.BAG_NAME])) {
              setFieldValue(
                FORAGE_INVENTORIES_BAG_FIELDS.BAG_NAME,
                defaultName,
              );
            }
          }}
        />
      </View>

      <MenuTabs
        selectedTab={selectedTab}
        labels={[
          { key: FORAGE_INVENTORIES_TABS.CAPACITY, value: i18n.t('capacity') },
          { key: FORAGE_INVENTORIES_TABS.FEED_OUT, value: i18n.t('feedOut') },
        ]}
        onTabChange={tab => {
          setSelectedTab(tab);
        }}
      />

      {selectedTab === FORAGE_INVENTORIES_TABS.CAPACITY ? (
        <BagCapacity
          values={values}
          handleChange={handleChange}
          references={references}
          screenDisabled={screenDisabled}
        />
      ) : (
        <BagFeedOut
          values={values}
          handleChange={handleChange}
          setFieldValue={setFieldValue}
          references={references}
          screenDisabled={screenDisabled}
        />
      )}
    </View>
  );
};

export default BagForm;
