// modules
import React, { useState } from 'react';
import { View, Text, Keyboard } from 'react-native';
import { useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import InputRow from '../../common/InputRow';
import CalculatedRow from '../../common/CalculatedRow';
import DatePicker from '../../common/DatePicker';
import CustomCalenderDatePicker from '../../../../../common/CustomCalendarDatePicker';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES,
  FORAGE_INVENTORIES_INPUT_MIN_LIMIT,
  FORAGE_INVENTORIES_INPUT_MAX_LIMIT,
  UNIT_OF_MEASURE,
  DATE_FORMATS,
  NEXT_FIELD_TEXT,
  FORAGE_INVENTORIES_FEEDOUT_MAX_LIMIT,
} from '../../../../../../constants/AppConstants';
import {
  CONTENT_TYPE,
  FORAGE_INVENTORIES_PILE_FIELDS,
} from '../../../../../../constants/FormConstants';

// helpers
import {
  getLabelWithUnit,
  getPile3InchesPerDay,
  getPile6InchesPerDay,
  getPileDM,
  getPileEndDate,
  getPileFeedoutSurfaceArea,
  getPileInchesPerDay,
  getPileTonsPerDay,
  getUnitOfMeasure,
} from '../../../../../../helpers/forageInventoriesHelper';
import { dateHelper } from '../../../../../../helpers/dateHelper';
import { getFormattedCommaNumber } from '../../../../../../helpers/alphaNumericHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const PileFeedOut = props => {
  const { screenDisabled, values, references, handleChange, setFieldValue } =
    props;

  const [dateModalVisible, setDateModalVisible] = useState(false);
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const userData = useSelector(
    state => state?.userPreferences?.userPreferences,
  );
  const selectedUnit = getUnitOfMeasure(userData);

  const setStartDate = date => {
    setDateModalVisible(false);
    setFieldValue(
      FORAGE_INVENTORIES_PILE_FIELDS.START_DATE,
      dateHelper.getUnixTimestamp(date?.toISOString()),
    );
  };

  return (
    <View style={styles.container}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <Text style={styles.headingText}>{i18n.t('feedoutRateInformation')}</Text>

      {/* Feeding Rate */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <InputRow
          title={i18n.t('feedingRate')}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[FORAGE_INVENTORIES_PILE_FIELDS.FEEDING_RATE]?.toString() ||
          //     '',
          // )}
          value={
            values[FORAGE_INVENTORIES_PILE_FIELDS.FEEDING_RATE]?.toString() ||
            ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_PILE_FIELDS.FEEDING_RATE)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_PILE_FIELDS.FEEDING_RATE] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_PILE_FIELDS.COWS_TO_BE_FED]?.focus();
          }}
          blurOnSubmit={false}
          hideAltText={true}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[FORAGE_INVENTORIES_PILE_FIELDS.COWS_TO_BE_FED],
            });
          }}
          // hasCommas={true}
        />
      </View>

      {/* Cows to be fed */}
      <View style={styles.formInputView}>
        <InputRow
          title={i18n.t('cowsToBeFed')}
          disabled={screenDisabled}
          placeholder={i18n.t('numberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_FEEDOUT_MAX_LIMIT}
          isInteger={true}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[FORAGE_INVENTORIES_PILE_FIELDS.COWS_TO_BE_FED]?.toString() ||
          //     '',
          // )}
          value={
            values[FORAGE_INVENTORIES_PILE_FIELDS.COWS_TO_BE_FED]?.toString() ||
            ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_PILE_FIELDS.COWS_TO_BE_FED)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_PILE_FIELDS.COWS_TO_BE_FED] = ref)
          }
          onSubmitEditing={() => {
            Keyboard?.dismiss();
          }}
          blurOnSubmit={false}
          hideAltText
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              dismiss: true,
            });
          }}
          hasCommas={true}
        />
      </View>

      {/* DM in 1 foot/meter */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={
            selectedUnit === UNIT_OF_MEASURE.IMPERIAL
              ? i18n.t('lbsDMIn1Foot')
              : i18n.t('kgsDMIn1M')
          }
          value={convertInputNumbersToRegionalBasis(
            getPileDM(values, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>

      {/* Feedout surface area */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={
            selectedUnit === UNIT_OF_MEASURE.IMPERIAL
              ? getLabelWithUnit(
                  i18n.t('feedOutSurfaceArea'),
                  i18n.t('footSquare'),
                )
              : getLabelWithUnit(
                  i18n.t('feedOutSurfaceArea'),
                  i18n.t('meterSquare'),
                )
          }
          value={convertInputNumbersToRegionalBasis(
            getPileFeedoutSurfaceArea(values, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>

      {/* Inches/Cm per day */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={
            selectedUnit === UNIT_OF_MEASURE.IMPERIAL
              ? i18n.t('inchesPerDay')
              : i18n.t('cmPerDay')
          }
          value={convertInputNumbersToRegionalBasis(
            getPileInchesPerDay(values, selectedUnit, true)
              .toFixed(1)
              .toString(),
            1,
          )}
        />
      </View>

      {/* Tons per day */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={i18n.t('tonsPerDay')}
          value={convertInputNumbersToRegionalBasis(
            getPileTonsPerDay(values, selectedUnit, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>

      <Text style={styles.headingText}>{i18n.t('cowsDayNeeded')}</Text>

      {/* At 3 Inches per day */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <CalculatedRow
          title={
            selectedUnit === UNIT_OF_MEASURE.IMPERIAL
              ? i18n.t('at3InchesPerDay')
              : i18n.t('at7cmPerDay')
          }
          value={getPile3InchesPerDay(values, selectedUnit, true)
            .toFixed(0)
            .toString()}
        />
      </View>

      {/* At 6 Inches per day */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={
            selectedUnit === UNIT_OF_MEASURE.IMPERIAL
              ? i18n.t('at6InchesPerDay')
              : i18n.t('at15cmPerDay')
          }
          value={getPile6InchesPerDay(values, selectedUnit, true)
            .toFixed(0)
            .toString()}
        />
      </View>

      <Text style={styles.headingText}>{i18n.t('days')}</Text>

      {/* Start Date */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <DatePicker
          title={i18n.t('startDate')}
          disabled={screenDisabled}
          openDatePicker={() => {
            setDateModalVisible(true);
          }}
          value={values[FORAGE_INVENTORIES_PILE_FIELDS.START_DATE]}
        />
      </View>

      {/* End Date */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={i18n.t('endDate')}
          value={
            getPileEndDate(values, selectedUnit, true)
              ? dateHelper.getFormattedDate(
                  getPileEndDate(values, selectedUnit, true),
                  DATE_FORMATS.MM_dd_yyyy,
                )
              : '-'
          }
        />
      </View>

      <CustomCalenderDatePicker
        isVisible={dateModalVisible}
        closeModal={() => setDateModalVisible(false)}
        defaultDate={values[FORAGE_INVENTORIES_PILE_FIELDS.START_DATE]}
        onSubmit={setStartDate}
      />
    </View>
  );
};

export default PileFeedOut;
