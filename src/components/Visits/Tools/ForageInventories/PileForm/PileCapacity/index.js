// modules
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import InputRow from '../../common/InputRow';
import CalculatedRow from '../../common/CalculatedRow';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  BLACK_INFO_ICON,
  PILE_ENGLISH_IMAGE,
} from '../../../../../../constants/AssetSVGConstants';
import ROUTE_CONSTANTS from '../../../../../../constants/RouteConstants';
import {
  FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES,
  FORAGE_INVENTORIES_INPUT_MAX_LIMIT,
  FORAGE_INVENTORIES_INPUT_MIN_LIMIT,
  FORAGE_INVENTORIES_DRY_MATTER_MIN_LIMIT,
  FORAGE_INVENTORIES_DRY_MATTER_MAX_LIMIT,
  PILE_SLOPE_GOAL_VALUE,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';
import {
  CONTENT_TYPE,
  FORAGE_INVENTORIES_PILE_FIELDS,
} from '../../../../../../constants/FormConstants';
import { FORAGE_INVENTORIES_TYPES } from '../../../../../../constants/toolsConstants/ForageInventories';

// helpers
import {
  getAltDensityText,
  getAltDistanceText,
  getDensityUnit,
  getDistanceUnit,
  getLabelWithUnit,
  getPileTopLength,
  getPileTonsAF,
  getPileTonsDM,
  getUnitOfMeasure,
  getPileSilageAFDensity,
  getPileSlope,
  getFormImageByLocale,
} from '../../../../../../helpers/forageInventoriesHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const PileCapacity = props => {
  const { screenDisabled, values, references, handleChange } = props;
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const navigation = useNavigation();

  const userData = useSelector(
    state => state?.userPreferences?.userPreferences,
  );
  const selectedUnit = getUnitOfMeasure(userData);
  const distanceUnit = getDistanceUnit(userData);
  const densityUnit = getDensityUnit(userData);

  const openDensityConverterScreen = () => {
    navigation.navigate(ROUTE_CONSTANTS.DENSITY_CONVERTER);
  };

  return (
    <View style={styles.container}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.imageContainer}>
        {/* <PILE_ENGLISH_IMAGE {...styles.pileImage} /> */}
        {getFormImageByLocale(FORAGE_INVENTORIES_TYPES.PILE, styles.pileImage)}
        <Text
          style={[
            styles.imageText,
            getPileSlope(values, true) > PILE_SLOPE_GOAL_VALUE
              ? styles.greenText
              : styles.redText,
          ]}>
          {`${i18n.t('slope')} ${convertInputNumbersToRegionalBasis(
            getPileSlope(values, true).toFixed(1),
            1,
          )} ${i18n.t('to1')}`}
          <Text style={styles.imageText}>
            {` (${i18n.t('pileSlopeGoal')})`}
          </Text>
        </Text>
        <Text style={styles.imageText}>
          {`${i18n.t('silageAFDensity')} ${convertInputNumbersToRegionalBasis(
            getPileSilageAFDensity(values, true).toFixed(1),
            1,
          )} (${i18n.t('pileSilageAFDensityGoal')})`}
        </Text>
      </View>

      <View style={styles.pileDimensionRow}>
        <Text style={styles.pileDimensionText}>{i18n.t('pileDimension')}</Text>
        <TouchableOpacity onPress={openDensityConverterScreen}>
          <BLACK_INFO_ICON {...styles.bigIcon} fill={styles.grey1} />
        </TouchableOpacity>
      </View>

      {/* Height */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('height'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[FORAGE_INVENTORIES_PILE_FIELDS.HEIGHT]?.toString() || '',
          // )}
          value={
            values[FORAGE_INVENTORIES_PILE_FIELDS.HEIGHT]?.toString() || ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_PILE_FIELDS.HEIGHT)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_PILE_FIELDS.HEIGHT] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_PILE_FIELDS.TOP_WIDTH]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_PILE_FIELDS.HEIGHT],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: references[FORAGE_INVENTORIES_PILE_FIELDS.TOP_WIDTH],
            });
          }}
        />
      </View>

      {/* Top Width */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('topWidth'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[FORAGE_INVENTORIES_PILE_FIELDS.TOP_WIDTH]?.toString() || '',
          // )}
          value={
            values[FORAGE_INVENTORIES_PILE_FIELDS.TOP_WIDTH]?.toString() || ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_PILE_FIELDS.TOP_WIDTH)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_PILE_FIELDS.TOP_WIDTH] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_WIDTH]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_PILE_FIELDS.TOP_WIDTH],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_WIDTH],
            });
          }}
        />
      </View>

      {/* Bottom Width */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('bottomWidth'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_WIDTH]?.toString() ||
          //     '',
          // )}
          value={
            values[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_WIDTH]?.toString() ||
            ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_WIDTH)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_WIDTH] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_LENGTH]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_WIDTH],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_LENGTH],
            });
          }}
        />
      </View>

      {/* Bottom Length */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('bottomLength'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_LENGTH]?.toString() ||
          //     '',
          // )}
          value={
            values[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_LENGTH]?.toString() ||
            ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_LENGTH)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_LENGTH] = ref)
          }
          onSubmitEditing={() => {
            references[FORAGE_INVENTORIES_PILE_FIELDS.DRY_MATTER]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_PILE_FIELDS.BOTTOM_LENGTH],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: references[FORAGE_INVENTORIES_PILE_FIELDS.DRY_MATTER],
            });
          }}
        />
      </View>

      {/* Top Length */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={getLabelWithUnit(i18n.t('topLength'), distanceUnit)}
          value={convertInputNumbersToRegionalBasis(
            getPileTopLength(values, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>

      <View style={styles.divider}></View>

      {/* Dry Matter % */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <InputRow
          title={i18n.t('dryMatterPercent')}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_DRY_MATTER_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_DRY_MATTER_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={
            values[FORAGE_INVENTORIES_PILE_FIELDS.DRY_MATTER]?.toString() || ''
          }
          onChange={handleChange(FORAGE_INVENTORIES_PILE_FIELDS.DRY_MATTER)}
          reference={ref =>
            (references[FORAGE_INVENTORIES_PILE_FIELDS.DRY_MATTER] = ref)
          }
          onSubmitEditing={() => {
            references[
              FORAGE_INVENTORIES_PILE_FIELDS.SILAGE_DM_DENSITY
            ]?.focus();
          }}
          blurOnSubmit={false}
          hideAltText={true}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[FORAGE_INVENTORIES_PILE_FIELDS.SILAGE_DM_DENSITY],
            });
          }}
        />
      </View>

      {/* Silage DM Density */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('silageDMDensity'), densityUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[
          //     FORAGE_INVENTORIES_PILE_FIELDS.SILAGE_DM_DENSITY
          //   ]?.toString() || '',
          // )}
          value={
            values[
              FORAGE_INVENTORIES_PILE_FIELDS.SILAGE_DM_DENSITY
            ]?.toString() || ''
          }
          onChange={handleChange(
            FORAGE_INVENTORIES_PILE_FIELDS.SILAGE_DM_DENSITY,
          )}
          reference={ref =>
            (references[FORAGE_INVENTORIES_PILE_FIELDS.SILAGE_DM_DENSITY] = ref)
          }
          onSubmitEditing={() => {
            Keyboard?.dismiss();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDensityText(
            values[FORAGE_INVENTORIES_PILE_FIELDS.SILAGE_DM_DENSITY],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              dismiss: true,
            });
          }}
        />
      </View>

      <Text style={styles.pileDimensionText}>{i18n.t('capacity')}</Text>

      {/* Tons DM */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <CalculatedRow
          title={i18n.t('tonsDM')}
          value={convertInputNumbersToRegionalBasis(
            getPileTonsDM(values, selectedUnit, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>

      {/* Tons AF */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={i18n.t('tonsAF')}
          value={convertInputNumbersToRegionalBasis(
            getPileTonsAF(values, selectedUnit, true).toFixed(1).toString(),
            1,
          )}
        />
      </View>
    </View>
  );
};

export default PileCapacity;
