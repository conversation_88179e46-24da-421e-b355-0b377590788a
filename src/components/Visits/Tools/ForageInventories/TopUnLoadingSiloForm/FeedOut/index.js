// modules
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

import InputRow from '../../common/InputRow';
import CalculatedRow from '../../common/CalculatedRow';
import DatePicker from '../../common/DatePicker';
import CustomCalenderDatePicker from '../../../../../common/CustomCalendarDatePicker';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput/index';

import {
  DATE_FORMATS,
  FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES,
  FORAGE_INVENTORIES_FEEDOUT_MAX_LIMIT,
  FORAGE_INVENTORIES_INPUT_MIN_LIMIT,
  FORAGE_INVENTORIES_INPUT_MAX_LIMIT,
  UNIT_OF_MEASURE,
} from '../../../../../../constants/AppConstants';
import { FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS } from '../../../../../../constants/FormConstants';
import { CONTENT_TYPE } from '../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../constants/AppConstants';

import {
  getLabelWithUnit,
  getTopUnloadingAt3InchesDay,
  getTopUnloadingAt6InchesDay,
  getTopUnloadingEndDate,
  getTopUnloadingFeedOutSurfaceArea,
  getTopUnloadingInchesCmPerDay,
  getTopUnloadingLbsDmInFoot,
  getTopUnloadingTonsPerDay,
  getUnitOfMeasure,
} from '../../../../../../helpers/forageInventoriesHelper';
import { dateHelper } from '../../../../../../helpers/dateHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const TopUnloadingSiloFeedOut = props => {
  const { screenDisabled, setFieldValue, values, references, handleChange } =
    props;
  const navigation = useNavigation();

  const [dateModalVisible, setDateModalVisible] = useState(false);
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const userData = useSelector(
    state => state?.userPreferences?.userPreferences,
  );
  const selectedUnit = getUnitOfMeasure(userData);

  const setStartDate = date => {
    setDateModalVisible(false);
    setFieldValue(
      FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.START_DATE,
      dateHelper.getUnixTimestamp(date?.toISOString()),
    );
  };

  return (
    <View style={styles.container}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.pileDimensionRow}>
        <Text style={styles.pileDimensionText}>
          {i18n.t('feedOutRateInformation')}
        </Text>
      </View>

      {/* Filled height */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('feedingRate'))}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[
          //     FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.FEEDING_RATE
          //   ]?.toString() || '',
          // )}
          value={
            values[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.FEEDING_RATE
            ]?.toString() || ''
          }
          onChange={handleChange(
            FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.FEEDING_RATE,
          )}
          reference={ref =>
            (references[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.FEEDING_RATE
            ] = ref)
          }
          onSubmitEditing={() => {
            references[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.COWS_TO_BE_FED
            ]?.focus();
          }}
          blurOnSubmit={false}
          hideAltText
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[
                  FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.COWS_TO_BE_FED
                ],
            });
          }}
          // hasCommas={true}
        />
      </View>

      {/* Height of silage left in silo */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('cowsToBeFed'))}
          disabled={screenDisabled}
          placeholder={i18n.t('numberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_FEEDOUT_MAX_LIMIT}
          isInteger={true}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          // value={getFormattedCommaNumber(
          //   values[
          //     FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.COWS_TO_BE_FED
          //   ]?.toString() || '',
          // )}
          value={
            values[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.COWS_TO_BE_FED
            ]?.toString() || ''
          }
          onChange={handleChange(
            FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.COWS_TO_BE_FED,
          )}
          reference={ref =>
            (references[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.COWS_TO_BE_FED
            ] = ref)
          }
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
          blurOnSubmit={false}
          hideAltText
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              dismiss: true,
            });
          }}
          hasCommas={true}
        />
      </View>

      {/* lbs DM in 1 foot Or Kgs DM in 1 m. */}
      <View style={[styles.formInputView]}>
        <CalculatedRow
          title={
            selectedUnit === UNIT_OF_MEASURE.IMPERIAL
              ? i18n.t('lbsDMIn1Foot')
              : i18n.t('kgsDMIn1M')
          }
          value={convertInputNumbersToRegionalBasis(
            getTopUnloadingLbsDmInFoot(values, selectedUnit, true)
              .toFixed(0)
              .toString(),
            0,
          )}
        />
      </View>

      <View style={[styles.formInputView, styles.marginTop]}>
        <CalculatedRow
          title={getLabelWithUnit(
            i18n.t('feedOutSurfaceArea'),
            selectedUnit === UNIT_OF_MEASURE.IMPERIAL ? 'ft^2' : 'm^2',
          )}
          value={convertInputNumbersToRegionalBasis(
            getTopUnloadingFeedOutSurfaceArea(values, true)
              .toFixed(1)
              .toString(),
            1,
          )}
        />
      </View>

      <View style={[styles.formInputView, styles.marginTop]}>
        <CalculatedRow
          // title={getLabelWithUnit(i18n.t('inchesCmPerDay'))}
          title={
            selectedUnit == UNIT_OF_MEASURE.IMPERIAL
              ? i18n.t('inchesPerDay')
              : i18n.t('cmPerDay')
          }
          value={convertInputNumbersToRegionalBasis(
            getTopUnloadingInchesCmPerDay(values, selectedUnit, true)
              .toFixed(1)
              .toString(),
            1,
          )}
        />
      </View>

      <View style={[styles.formInputView, styles.marginTop]}>
        <CalculatedRow
          title={getLabelWithUnit(i18n.t('tonsPerDay'))}
          value={convertInputNumbersToRegionalBasis(
            getTopUnloadingTonsPerDay(values, selectedUnit, true)
              .toFixed(1)
              .toString(),
            1,
          )}
        />
      </View>

      <Text style={[styles.pileDimensionText, styles.marginVertical]}>
        {i18n.t('cowsDayNeeded')}
      </Text>

      {/* Tons DM */}
      <View style={[styles.formInputView]}>
        <CalculatedRow
          title={
            selectedUnit == UNIT_OF_MEASURE.IMPERIAL
              ? i18n.t('at3InchesDay')
              : i18n.t('at7CmDay')
          }
          value={convertInputNumbersToRegionalBasis(
            getTopUnloadingAt3InchesDay(values, selectedUnit, true)
              .toFixed(0)
              .toString(),
          )}
        />
      </View>

      {/* Tons AF */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={
            selectedUnit == UNIT_OF_MEASURE.IMPERIAL
              ? i18n.t('at6InchesDay')
              : i18n.t('at15CmPerDay')
          }
          value={convertInputNumbersToRegionalBasis(
            getTopUnloadingAt6InchesDay(values, selectedUnit, true)
              .toFixed(0)
              .toString(),
          )}
        />
      </View>
      <Text style={[styles.pileDimensionText, styles.marginVertical]}>
        {i18n.t('days')}
      </Text>

      {/* Start Date */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <DatePicker
          title={i18n.t('startDate')}
          disabled={screenDisabled}
          openDatePicker={() => {
            setDateModalVisible(true);
          }}
          value={
            values[FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.START_DATE]
          }
        />
      </View>

      {/* Tons AF */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={i18n.t('endDate')}
          value={
            getTopUnloadingEndDate(values, selectedUnit, true)
              ? dateHelper.getFormattedDate(
                  getTopUnloadingEndDate(values, selectedUnit, true),
                  DATE_FORMATS.MM_dd_yyyy,
                )
              : '-'
          }
        />
      </View>

      <CustomCalenderDatePicker
        isVisible={dateModalVisible}
        closeModal={() => setDateModalVisible(false)}
        defaultDate={
          values[FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.START_DATE]
        }
        onSubmit={setStartDate}
      />
    </View>
  );
};

export default TopUnloadingSiloFeedOut;
