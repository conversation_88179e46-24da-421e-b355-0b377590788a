import { StyleSheet } from 'react-native';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../constants/theme/variables/customColor';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sectionTitle: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: customColor.grey1,
  },
  titleHorizontalMargin: {
    marginHorizontal: normalize(20),
  },
  loaderColor: customColor.primaryMain,
  popoverStyle: {
    borderWidth: 1,
    borderColor: customColor.popoverBorderColor,
    backgroundColor: customColor.white,
    marginRight: normalize(20),
  },
  arrowStyles: {
    backgroundColor: customColor.white,
    borderColor: customColor.popoverBorderColor,
  },
  popoverBodyStyle: {
    backgroundColor: customColor.white,
    shadowColor: customColor.popoverShadowColor,
    shadowOffset: {
      width: -2,
      height: 15,
    },
    shadowOpacity: 0.35,
    shadowRadius: 20,
    elevation: 7,
  },
});

export default styles;
