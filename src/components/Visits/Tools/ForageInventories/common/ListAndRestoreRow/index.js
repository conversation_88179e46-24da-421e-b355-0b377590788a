// modules
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Popover } from 'native-base';

// localization
import i18n from '../../../../../../localization/i18n';

// actions
import { restorePreviousSilosRequest } from '../../../../../../store/actions/tools/pileBunker';

// styling constants
import styles from './styles';

// icons
import { ARROWS_ROTATE } from '../../../../../../constants/AssetSVGConstants';

/**
 * @description
 * Component for inventory list heading and restore previous visit silos button
 * restore icon with button to refetch last visit silos, only applicable to (TopUploadingSilo) and (BottomUnloadingSilo)
 *
 * @param {Number} selectedTabIndex active selected tab index
 * @param {Boolean} screenDisabled is tool editable
 *
 * @returns {React.Component} react component
 */
const InventoryListAndRestoreIcon = ({ screenDisabled }) => {
  const dispatch = useDispatch();

  // reducer states
  const visitState = useSelector(state => state.visit?.visit);
  const isSilosLoading = useSelector(
    state => state.pileBunker.restoreSiloDataLoading,
  );

  // local component state
  const [isOpen, setIsOpen] = useState(false);

  /**
   * @description
   * function to handle restore previous silos button press listener
   */
  const _handlePressRestoreSilos = () =>
    dispatch(restorePreviousSilosRequest());

  /**
   * @description
   * function to show popover on long press on restore previous button silo to demonstrate button function
   */
  const _handleLongPressRestoreIcon = () => setIsOpen(true);

  /**
   * @description
   * function to show or hide popover for long press button
   */
  const _handlePressPopover = () => setIsOpen(!isOpen);

  /**
   * @description
   * condition for showing and hiding button of restore previous silos
   * conditions on which button is not visible on screen,
   * 1. if visit already have any pile and bunker data in it, in visit if the pileBunker key is not empty.
   * 2. if visit is editable, do not show button is visit status is published.
   * 3. do not show button is selected tabs are not TopUploadingSilo and BottomUnloadingSilo.
   */
  const showRestoreButton =
    visitState?.pileAndBunker || screenDisabled ? false : true;

  /**
   * @description
   * condition to show restore button icon or loading indicator if it is press
   */
  const loadingView = isSilosLoading ? (
    <ActivityIndicator
      color={styles.loaderColor}
      style={styles.titleHorizontalMargin}
    />
  ) : (
    <ARROWS_ROTATE style={[styles.titleHorizontalMargin]} />
  );

  return (
    <View style={styles.container}>
      <Text style={[styles.sectionTitle, styles.titleHorizontalMargin]}>
        {i18n.t('inventoryList')}
      </Text>

      {showRestoreButton && (
        <Popover
          isOpen={isOpen}
          onClose={_handlePressPopover}
          trigger={triggerProps => (
            <TouchableOpacity
              {...triggerProps}
              disabled={isSilosLoading}
              onPress={_handlePressRestoreSilos}
              onLongPress={_handleLongPressRestoreIcon}>
              {loadingView}
            </TouchableOpacity>
          )}>
          <Popover.Content style={styles.popoverStyle} overflow={'visible'}>
            <Popover.Arrow style={styles.arrowStyles} />
            <Popover.Body
              style={styles.popoverBodyStyle}
              rounded={'md'}
              shadow={'0'}>
              <Text style={styles.sectionTitle}>
                {i18n.t('restorePreviousVisitSilo')}
              </Text>
            </Popover.Body>
          </Popover.Content>
        </Popover>
      )}
    </View>
  );
};

export default InventoryListAndRestoreIcon;
