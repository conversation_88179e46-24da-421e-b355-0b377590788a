// modules
import React, { forwardRef } from 'react';
import { View, TouchableOpacity, Text, ScrollView } from 'react-native';

// styles
import styles from './styles';
import { separateStringIntoCamelCase, toCamelCase } from '../../../../../../helpers/alphaNumericHelper';
import i18n from '../../../../../../localization/i18n';

const InventoryTabSelector = forwardRef((props, ref) => {
  const { selectedIndex, onTabChange, item } = props;

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal={true}
        ref={ref}
        showsHorizontalScrollIndicator={false}
        style={styles.scrollView}>
        {item.map((val, index) => {
          const { value, count } = val;
          return (
            <TouchableOpacity
              key={index}
              style={styles.button}
              onPress={() => {
                onTabChange(index);
              }}>
              <View
                style={
                  index === selectedIndex
                    ? styles.activeTabStyle
                    : styles.inactiveTabStyle
                }>
                <Text
                  style={
                    index === selectedIndex
                      ? styles.activeLabelStyle
                      : styles.inactiveLabelStyle
                  }>
                  {/* {separateStringIntoCamelCase(value)} */}
                  {i18n.t(`${toCamelCase(item[index]?.key)}`)}
                  {`(${count})`}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
});

export default InventoryTabSelector;
