import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    width: '100%',
    flexDirection: 'row',
    borderBottomWidth: normalize(1),
    borderBottomColor: colors.inventoryTabsBorder,
    backgroundColor: colors.white,
  },
  scrollView: { paddingLeft: normalize(8), marginRight: normalize(8) },
  button: {
    height: normalize(30),
    marginHorizontal: normalize(12),
  },
  inactiveTabStyle: {
    height: normalize(30),
    backgroundColor: colors.white,
    paddingHorizontal: normalize(8),
    alignItems: 'center',
  },
  activeTabStyle: {
    height: normalize(30),
    alignItems: 'center',
    backgroundColor: colors.primaryMainLight,
    borderBottomWidth: normalize(2),
    borderBottomColor: colors.primaryMain,
    paddingHorizontal: normalize(14),
    alignItems: 'center',
    justifyContent: 'center',
  },
  inactiveLabelStyle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(13),
    lineHeight: normalize(22),
    color: colors.inventoryTabsInactiveLabel,
  },
  activeLabelStyle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(13),
    lineHeight: normalize(22),
    color: colors.primaryMain,
  },
};
