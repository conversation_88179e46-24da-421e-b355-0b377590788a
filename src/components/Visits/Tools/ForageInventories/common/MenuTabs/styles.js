import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    width: '100%',
    flexDirection: 'row',
    borderRadius: normalize(8),
    borderWidth: normalize(1),
    borderColor: colors.popoverShadowColor,
  },
  button: {
    height: normalize(40),
    flex: 1,
  },
  inactiveTabStyle: {
    height: normalize(40),
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTabStyle: {
    height: normalize(40),
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primaryMain,
  },
  inactiveLabelStyle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(24),
    color: colors.popoverTextColor,
  },
  activeLabelStyle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(24),
    color: colors.white,
  },
  leftTabSelected: {
    borderTopLeftRadius: normalize(8),
    borderBottomLeftRadius: normalize(8),
  },
  rightTabSelected: {
    borderTopRightRadius: normalize(8),
    borderBottomRightRadius: normalize(8),
  },
};
