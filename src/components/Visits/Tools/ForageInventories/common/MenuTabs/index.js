// modules
import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';

// styles
import styles from './styles';

const MenuTabs = props => {
  const { selectedTab, labels, onTabChange } = props;

  return (
    <View style={styles.container}>
      {labels.map((label, index) => {
        return (
          <TouchableOpacity
            key={label.key}
            style={styles.button}
            onPress={() => {
              onTabChange(label.key);
            }}>
            <View
              style={[
                label.key === selectedTab
                  ? styles.activeTabStyle
                  : styles.inactiveTabStyle,
                label.key === selectedTab && index === 0
                  ? styles.leftTabSelected
                  : label.key === selectedTab && index === labels.length - 1
                  ? styles.rightTabSelected
                  : null,
              ]}>
              <Text
                style={
                  label.key === selectedTab
                    ? styles.activeLabelStyle
                    : styles.inactiveLabelStyle
                }>
                {label.value}
              </Text>
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default MenuTabs;
