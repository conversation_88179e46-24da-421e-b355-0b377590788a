// modules
import React from 'react';
import { View, Text } from 'react-native';

// styles
import styles from './styles';

const CalculatedRow = props => {
  const { title, value } = props;

  return (
    <View style={styles.inputRow}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.valueContainer}>
        <Text style={styles.valueText}>{value}</Text>
      </View>
    </View>
  );
};

export default CalculatedRow;
