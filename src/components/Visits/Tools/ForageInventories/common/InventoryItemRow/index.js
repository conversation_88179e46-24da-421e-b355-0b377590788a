// modules
import React from 'react';
import { TouchableOpacity, Text } from 'react-native';

// styles
import styles from './styles';

// constants
import { RED_DELETE_ICON } from '../../../../../../constants/AssetSVGConstants';
import { normalize } from '../../../../../../constants/theme/variables/customFont';

const InventoryItemRow = props => {
  const { name, disabled, onPress, onDeletePress } = props;

  return (
    <TouchableOpacity style={styles.row} onPress={onPress}>
      <Text style={styles.title}>{name}</Text>
      <TouchableOpacity onPress={onDeletePress}>
        {!disabled && (
          <RED_DELETE_ICON width={normalize(16)} height={normalize(16)} />
        )}
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

export default InventoryItemRow;
