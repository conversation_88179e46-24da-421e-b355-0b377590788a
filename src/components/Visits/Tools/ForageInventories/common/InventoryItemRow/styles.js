import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  row: {
    height: normalize(56),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: normalize(1),
    borderColor: colors.todayBackGroundColor,
    borderRadius: normalize(8),
    paddingLeft: normalize(12),
    paddingRight: normalize(20),
  },
  title: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
};
