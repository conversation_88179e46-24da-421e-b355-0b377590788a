// modules
import React from 'react';
import { View, Text } from 'react-native';

// styles
import styles from './styles';

// common component
import NumberFormInput from '../../../../../common/NumberFormInput';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';
import { KEYBOARD_TYPE } from '../../../../../../constants/FormConstants';

const InputRow = props => {
  const {
    title,
    disabled,
    placeholder,
    minValue,
    maxValue,
    isInteger,
    decimalPoints,
    value,
    onChange,
    reference,
    onSubmitEditing,
    blurOnSubmit,
    altUnitText,
    hideAltText,
    inputAccessoryViewID,
    onFocus,
    returnKeyType,
    hasCommas = false,
  } = props;

  return (
    <View style={styles.container}>
      <View style={[styles.inputRow, hideAltText ? styles.centerRow : null]}>
        <Text style={styles.title}>{title}</Text>
        <NumberFormInput
          disabled={disabled}
          placeholder={convertInputNumbersToRegionalBasis(
            placeholder,
            isInteger ? 0 : decimalPoints,
            hasCommas,
          )}
          minValue={minValue}
          maxValue={maxValue}
          isInteger={isInteger}
          decimalPoints={isInteger ? 0 : decimalPoints}
          hideLabel={true}
          hideUnit={true}
          value={value}
          onChange={onChange}
          reference={reference}
          onSubmitEditing={onSubmitEditing}
          blurOnSubmit={blurOnSubmit}
          customInputContainerStyle={styles.customInputContainer}
          inputAccessoryViewID={inputAccessoryViewID}
          onFocus={onFocus}
          returnKeyType={returnKeyType}
          hasCommas={hasCommas}
          keyboardType={
            isInteger ? KEYBOARD_TYPE.NUMBER_PAD : KEYBOARD_TYPE.DECIMAL
          }
        />
      </View>
      {!hideAltText && (
        <Text style={styles.alterateUnitText}>{altUnitText}</Text>
      )}
    </View>
  );
};

export default InputRow;
