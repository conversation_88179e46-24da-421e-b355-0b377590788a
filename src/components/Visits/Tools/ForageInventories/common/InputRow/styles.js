import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: { flexDirection: 'column' },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  centerRow: {
    alignItems: 'center',
  },
  title: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    maxWidth: normalize(220),
  },
  customInputContainer: {
    width: normalize(106),
    height: normalize(48),
    marginTop: 0,
    paddingTop: 0,
  },
  alterateUnitText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontStyle: 'italic',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    alignSelf: 'flex-end',
    marginTop: normalize(8),
    width: normalize(106),
  },
};
