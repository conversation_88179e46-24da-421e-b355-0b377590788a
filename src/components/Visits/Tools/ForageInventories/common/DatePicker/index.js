// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../localization/i18n';

// constants
import { FORAGE_INVENTORIES_CALENDAR_ICON } from '../../../../../../constants/AssetSVGConstants';
import { DATE_FORMATS } from '../../../../../../constants/AppConstants';

// helpers
import { dateHelper } from '../../../../../../helpers/dateHelper';

const DatePicker = props => {
  const { title, disabled, openDatePicker, placeholder, value } = props;

  return (
    <View style={styles.inputRow}>
      <Text style={styles.title}>{title}</Text>
      {disabled ? (
        <Text style={[styles.dateText, styles.disabledTextWidth]}>
          {value
            ? dateHelper.getFormattedDate(value, DATE_FORMATS.MM_dd_yyyy)
            : '-'}
        </Text>
      ) : (
        <TouchableOpacity onPress={openDatePicker}>
          <View style={styles.datePickerContainer}>
            {value ? (
              <Text style={styles.dateText}>
                {dateHelper.getFormattedDate(value, DATE_FORMATS.MM_dd_yyyy)}
              </Text>
            ) : (
              <Text style={styles.placeholderText}>
                {placeholder || i18n.t('select')}
              </Text>
            )}
            <FORAGE_INVENTORIES_CALENDAR_ICON />
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default DatePicker;
