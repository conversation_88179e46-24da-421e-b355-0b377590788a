import fonts, {
    normalize,
  } from '../../../../../../constants/theme/variables/customFont';
  import colors from '../../../../../../constants/theme/variables/customColor';
  
  export default {
    container: {
      flex: 1,
      paddingTop: normalize(16),
    },
    imageContainer: {
      width: '100%',
      height: normalize(269),
      borderWidth: normalize(1),
      borderColor: colors.todayBackGroundColor,
      borderRadius: normalize(8),
    },
    pileDimensionRow: {
      flexDirection: 'row',
      marginTop: normalize(20),
      marginBottom: normalize(20),
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingRight: normalize(4),
    },
    pileDimensionText: {
      fontFamily: fonts.HelveticaNeueRegular,
      fontWeight: '700',
      fontSize: normalize(14),
      lineHeight: normalize(16),
      letterSpacing: 0.2,
      color: colors.grey1,
    },
    bigIcon: {
      width: normalize(16),
      height: normalize(16),
    },
    grey1: colors.grey1,
    formInputView: {
      marginBottom: normalize(16),
    },
    divider: {
      height: normalize(1),
      backgroundColor: colors.grey8,
    },
    marginTop: { marginTop: normalize(16) },
    marginVertical: { marginVertical: normalize(20) ,marginTop:normalize(6),},
  };
  