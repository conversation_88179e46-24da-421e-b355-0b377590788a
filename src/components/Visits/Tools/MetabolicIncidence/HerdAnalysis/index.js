// modules
import React, { useState, useEffect, useRef, forwardRef } from 'react';
import { View, Keyboard, KeyboardAvoidingView, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Formik } from 'formik';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// reusable components
import HerdAnalysisForm from './HerdAnalysisForm';
import HerdAnalysisResults from './HerdAnalysisResults';
import StepsFooter from '../../common/ToolStepsFooter';

// constants
import {
  METABOLIC_INCIDENCE_FIELDS,
  TOAST_TYPE,
} from '../../../../../constants/FormConstants';
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
  UNIT_OF_MEASURE,
} from '../../../../../constants/AppConstants';
import { normalize } from '../../../../../constants/theme/variables/customFont';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../../../../store/actions/tool';
import {
  getMetabolicIncidenceRequest,
  getSiteForMetabolicIncidenceRequest,
  resetGetMetabolicIncidenceRequest,
  saveMetabolicIncidenceRequest,
} from '../../../../../store/actions/tools/metabolicIncidence';
import { updateToolSiteRequest } from '../../../../../store/actions/site';

// helpers
import {
  createMetabolicIncidenceObject,
  getDefaultFormValues,
  getReferencesObject,
  getUpdatedSiteData,
  populateMetabolicIncidenceData,
} from '../../../../../helpers/metabolicIncidenceHelper';
import metabolicIncidenceValidationSchema from '../../../../../helpers/validation/metabolicIncidence';
import {
  convertDenominatorWeightToImperial,
  convertDenominatorWeightToMetric,
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../helpers/appSettingsHelper';

//services
import { isOnline } from '../../../../../services/netInfoService';

import { showToast } from '../../../../common/CustomToast';
import {
  convertNumberToString,
  convertStringToNumber,
} from '../../../../../helpers/alphaNumericHelper';

const HerdAnalysis = forwardRef((props, ref) => {
  const isFormRef = ref;
  let scrollViewRef = useRef();

  const {
    currentStep,
    totalSteps,
    selectedVisits,
    screenDisabled,
    showCompareButton,
    onPrevStepClick,
    onNextStepClick,
    currentActiveTool,
    onDropdownStepSelect,
    bottomSheetSteps,
    toolAlert,
  } = props;
  const visitState = useSelector(state => state.visit);
  const unitOfMeasure = visitState?.visit?.unitOfMeasure || {};
  const [formValues, setFormValues] = useState(
    getDefaultFormValues(unitOfMeasure),
  );
  const [currency, setCurrency] = useState('');
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const dispatch = useDispatch();

  const metabolicIncidenceState = useSelector(
    state => state.metabolicIncidence,
  );
  const { currencies } = useSelector(state => state?.enums?.enum);

  const getSiteData = () => {
    const { siteId, localSiteId } = visitState?.visit || {};
    dispatch(
      getSiteForMetabolicIncidenceRequest({
        siteId: siteId,
        localSiteId: localSiteId,
      }),
    );
  };

  const getToolData = () => {
    const { id } = visitState.visit || {};
    dispatch(
      getMetabolicIncidenceRequest({
        localId: id,
      }),
    );
  };

  useEffect(() => {
    const currencySymbol = getCurrencyForTools(
      currencies,
      visitState.visit.selectedCurrency,
    );
    setCurrency(currencySymbol);
    // Fetch Current Site Data
    getSiteData();
    // Fetch Metabolic Incidence Tool Data
    getToolData();
  }, []);

  useEffect(() => {
    if (!metabolicIncidenceState.metabolicIncidenceLoading) {
      if (metabolicIncidenceState?.metabolicIncidence) {
        const toolData = populateMetabolicIncidenceData(
          metabolicIncidenceState.metabolicIncidence,
          screenDisabled,
          unitOfMeasure,
          true,
        );

        setFormValues(prevFormValues => {
          return { ...prevFormValues, ...toolData };
        });
      }
    }
  }, [metabolicIncidenceState.metabolicIncidenceLoading]);

  useEffect(() => {
    if (!metabolicIncidenceState.siteLoading && !screenDisabled) {
      if (metabolicIncidenceState.site) {
        setFormValues(prevFormValues => {
          return {
            ...prevFormValues,
            [METABOLIC_INCIDENCE_FIELDS.MILK_PRICE]: convertNumberToString(
              unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
                ? convertDenominatorWeightToImperial(
                    metabolicIncidenceState.site.currentMilkPrice,
                    3,
                  )
                : metabolicIncidenceState.site.currentMilkPrice,
            ),
          };
        });
      }
    }
  }, [metabolicIncidenceState.siteLoading]);

  const references = getReferencesObject();
  for (const key in references) {
    references[key] = useRef();
  }

  const downloadHerdAnalysisData = async (model, type) => {
    if (await isOnline()) {
      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.METABOLIC_INCIDENCE_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType: EXPORT_REPORT_TYPES.METABOLIC_INCIDENCE_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareHerdAnalysisData = async (model, type, exportMethod) => {
    if (await isOnline()) {
      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.METABOLIC_INCIDENCE_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.METABOLIC_INCIDENCE_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const renderHerdAnalysisResults = values => {
    return (
      <HerdAnalysisResults
        milkPrice={values[METABOLIC_INCIDENCE_FIELDS.MILK_PRICE]}
        toolData={values}
        currency={currency}
        selectedVisits={selectedVisits}
        onShareHerdAnalysisData={onShareHerdAnalysisData}
        onDownloadPress={downloadHerdAnalysisData}
        showCompareButton={showCompareButton}
      />
    );
  };

  const onSavePress = values => {
    if (!screenDisabled) {
      if (isFormRef?.current?.isValid) {
        const { id } = visitState.visit || {};
        const obj = createMetabolicIncidenceObject(
          values,
          unitOfMeasure,
          metabolicIncidenceState?.metabolicIncidence,
        );
        dispatch(
          saveMetabolicIncidenceRequest({
            toolData: obj,
            localVisitId: id,
          }),
        );
        const oldValue = convertStringToNumber(
          metabolicIncidenceState?.site?.currentMilkPrice,
        );
        const newValue =
          unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
            ? convertDenominatorWeightToMetric(
                convertStringToNumber(values?.milkPrice),
              )
            : convertStringToNumber(values?.milkPrice);

        if (oldValue != newValue) {
          const tempValues = { ...values, milkPrice: newValue };

          const siteData = getUpdatedSiteData(
            tempValues,
            metabolicIncidenceState?.site,
          );

          dispatch(updateToolSiteRequest(siteData));
        }

        dispatch(resetGetMetabolicIncidenceRequest());
      }
    }
  };

  const onResultPress = values => {
    if (currentStep === totalSteps - 1) {
      onSavePress(values);
    }
    onNextStepClick();
  };

  return (
    <>
      <Formik
        innerRef={isFormRef}
        initialValues={formValues}
        enableReinitialize={true}
        validateOnMount={true}
        validationSchema={metabolicIncidenceValidationSchema}
        onSubmit={onSavePress}>
        {({
          values,
          errors,
          touched,
          dirty,
          isValid,
          handleChange,
          handleBlur,
          handleSubmit,
        }) => {
          return (
            <>
              {currentStep === totalSteps ? (
                renderHerdAnalysisResults(values)
              ) : (
                <KeyboardAvoidingView
                  style={styles.container}
                  behavior="padding">
                  <ScrollView
                    keyboardDismissMode="on-drag"
                    keyboardShouldPersistTaps="handled"
                    ref={scrollViewRef}>
                    <View
                      // onTouchStart={() => Keyboard.dismiss()}
                      style={styles.metabolicIncidenceFormContainer}>
                      <HerdAnalysisForm
                        screenDisabled={screenDisabled}
                        values={values}
                        errors={errors}
                        touched={touched}
                        references={references}
                        currency={currency}
                        weightUnit={weightUnit}
                        handleChange={handleChange}
                        handleBlur={handleBlur}
                        scrollAbove={() => {
                          scrollViewRef?.current?.scrollTo({
                            x: 0,
                            y: normalize(490),
                            animated: true,
                          });
                        }}
                      />
                    </View>
                  </ScrollView>
                </KeyboardAvoidingView>
              )}
              {!!toolAlert && <View style={styles.alert}>{toolAlert}</View>}
              <StepsFooter
                totalSteps={totalSteps}
                currentStep={currentStep}
                onLeftArrowClick={onPrevStepClick}
                onResultPress={() => onResultPress(values)}
                currentActiveTool={currentActiveTool}
                onDropdownStepSelect={step =>
                  onDropdownStepSelect(step, isValid, values, onSavePress)
                }
                bottomSheetSteps={bottomSheetSteps}
                isValid={isValid}
                herdAnalysis={true}
              />
            </>
          );
        }}
      </Formik>
    </>
  );
});

export default HerdAnalysis;
