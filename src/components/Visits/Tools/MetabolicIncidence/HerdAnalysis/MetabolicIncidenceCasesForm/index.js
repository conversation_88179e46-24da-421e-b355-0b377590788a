// modules
import React from 'react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import NumberFormInput from '../../../../../common/NumberFormInput';

// constants
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
  METABOLIC_INCIDENCE_FIELDS,
} from '../../../../../../constants/FormConstants';
import {
  METABOLIC_INCIDENCE_CASES_MAX_LIMIT,
  METABOLIC_INCIDENCE_CASES_MIN_LIMIT,
  NEXT_FIELD_TEXT,
  TEXT_INPUT_ALIGNMENT,
} from '../../../../../../constants/AppConstants';
import {
  ACCORDION_DOWN_ICON,
  ACCORDION_RIGHT_ICON,
} from '../../../../../../constants/AssetSVGConstants';
import { normalize } from '../../../../../../constants/theme/variables/customFont';
import { getFormattedCommaNumber } from '../../../../../../helpers/alphaNumericHelper';

const MetabolicIncidenceCasesForm = props => {
  const {
    screenDisabled,
    isSelected,
    onOpenAccordion,
    values,
    errors,
    touched,
    references,
    handleChange,
    handleBlur,
    setType,
    setAction,
  } = props;

  return (
    <View style={styles.container}>
      <TouchableOpacity activeOpacity={0.9} onPress={onOpenAccordion}>
        <View style={styles.accordionContainer}>
          <Text style={styles.accordionTitle}>
            {i18n.t('metabolicIncidenceCases')}
          </Text>
          {isSelected ? (
            <ACCORDION_DOWN_ICON width={normalize(14)} height={normalize(14)} />
          ) : (
            <ACCORDION_RIGHT_ICON
              width={normalize(14)}
              height={normalize(14)}
            />
          )}
        </View>
      </TouchableOpacity>

      {isSelected && (
        <>
          <Text style={styles.sectionContent}>
            {i18n.t('metabolicIncidenceCasesContent')}
          </Text>

          {/* Total Fresh Cows/Evaluation */}
          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('totalFreshCowsEvaluation')}
              required
              disabled={screenDisabled}
              placeholder={i18n.t('numberPlaceholder')}
              minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
              maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
              // value={getFormattedCommaNumber(
              //   values[
              //     METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_EVALUATION
              //   ]?.toString() || '',
              // )}
              value={
                values[
                  METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_EVALUATION
                ]?.toString() || ''
              }
              error={
                touched[
                  METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_EVALUATION
                ] &&
                errors[METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_EVALUATION]
              }
              onChange={handleChange(
                METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_EVALUATION,
              )}
              forceOnBlur
              onBlur={handleBlur(
                METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_EVALUATION,
              )}
              reference={ref =>
                (references[
                  METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_EVALUATION
                ] = ref)
              }
              onSubmitEditing={() => {
                references[
                  METABOLIC_INCIDENCE_FIELDS.RETAINED_PLACENTA
                ]?.focus();
              }}
              blurOnSubmit={false}
              customLabelStyle={styles.customFieldLabel}
              customInputContainerStyle={styles.customInputContainer}
              hasCommas={true}
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef:
                    references[METABOLIC_INCIDENCE_FIELDS.RETAINED_PLACENTA],
                });
              }}
            />
          </View>

          {/* Retained Placenta */}
          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('retainedPlacenta')}
              disabled={screenDisabled}
              placeholder={i18n.t('numberPlaceholder')}
              minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
              maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
              // value={getFormattedCommaNumber(
              //   values[
              //     METABOLIC_INCIDENCE_FIELDS.RETAINED_PLACENTA
              //   ]?.toString() || '',
              // )}
              value={
                values[
                  METABOLIC_INCIDENCE_FIELDS.RETAINED_PLACENTA
                ]?.toString() || ''
              }
              onChange={handleChange(
                METABOLIC_INCIDENCE_FIELDS.RETAINED_PLACENTA,
              )}
              reference={ref =>
                (references[METABOLIC_INCIDENCE_FIELDS.RETAINED_PLACENTA] = ref)
              }
              onSubmitEditing={() => {
                references[METABOLIC_INCIDENCE_FIELDS.METRITIS]?.focus();
              }}
              blurOnSubmit={false}
              customLabelStyle={styles.customFieldLabel}
              customInputContainerStyle={styles.customInputContainer}
              hasCommas={true}
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: references[METABOLIC_INCIDENCE_FIELDS.METRITIS],
                });
              }}
            />
          </View>

          {/* Metritis */}
          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('metritis')}
              disabled={screenDisabled}
              placeholder={i18n.t('numberPlaceholder')}
              minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
              maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
              // value={getFormattedCommaNumber(
              //   values[METABOLIC_INCIDENCE_FIELDS.METRITIS]?.toString() || '',
              // )}
              value={
                values[METABOLIC_INCIDENCE_FIELDS.METRITIS]?.toString() || ''
              }
              onChange={handleChange(METABOLIC_INCIDENCE_FIELDS.METRITIS)}
              reference={ref =>
                (references[METABOLIC_INCIDENCE_FIELDS.METRITIS] = ref)
              }
              onSubmitEditing={() => {
                references[
                  METABOLIC_INCIDENCE_FIELDS.DISPLACED_ABOMASUM
                ]?.focus();
              }}
              blurOnSubmit={false}
              customLabelStyle={styles.customFieldLabel}
              customInputContainerStyle={styles.customInputContainer}
              hasCommas={true}
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef:
                    references[METABOLIC_INCIDENCE_FIELDS.DISPLACED_ABOMASUM],
                });
              }}
            />
          </View>

          {/* Displaced Abomasum */}
          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('displacedAbomasum')}
              disabled={screenDisabled}
              placeholder={i18n.t('numberPlaceholder')}
              minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
              maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
              // value={getFormattedCommaNumber(
              //   values[
              //     METABOLIC_INCIDENCE_FIELDS.DISPLACED_ABOMASUM
              //   ]?.toString() || '',
              // )}
              value={
                values[
                  METABOLIC_INCIDENCE_FIELDS.DISPLACED_ABOMASUM
                ]?.toString() || ''
              }
              onChange={handleChange(
                METABOLIC_INCIDENCE_FIELDS.DISPLACED_ABOMASUM,
              )}
              reference={ref =>
                (references[METABOLIC_INCIDENCE_FIELDS.DISPLACED_ABOMASUM] =
                  ref)
              }
              onSubmitEditing={() => {
                references[METABOLIC_INCIDENCE_FIELDS.KETOSIS]?.focus();
              }}
              blurOnSubmit={false}
              customLabelStyle={styles.customFieldLabel}
              customInputContainerStyle={styles.customInputContainer}
              hasCommas={true}
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: references[METABOLIC_INCIDENCE_FIELDS.KETOSIS],
                });
              }}
            />
          </View>

          {/* Ketosis */}
          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('ketosis')}
              disabled={screenDisabled}
              placeholder={i18n.t('numberPlaceholder')}
              minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
              maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
              // value={getFormattedCommaNumber(
              //   values[METABOLIC_INCIDENCE_FIELDS.KETOSIS]?.toString() || '',
              // )}
              value={
                values[METABOLIC_INCIDENCE_FIELDS.KETOSIS]?.toString() || ''
              }
              onChange={handleChange(METABOLIC_INCIDENCE_FIELDS.KETOSIS)}
              reference={ref =>
                (references[METABOLIC_INCIDENCE_FIELDS.KETOSIS] = ref)
              }
              onSubmitEditing={() => {
                references[METABOLIC_INCIDENCE_FIELDS.MILK_FEVER]?.focus();
              }}
              blurOnSubmit={false}
              customLabelStyle={styles.customFieldLabel}
              customInputContainerStyle={styles.customInputContainer}
              hasCommas={true}
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: references[METABOLIC_INCIDENCE_FIELDS.MILK_FEVER],
                });
              }}
            />
          </View>

          {/* Milk Fever */}
          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('milkFever')}
              disabled={screenDisabled}
              placeholder={i18n.t('numberPlaceholder')}
              minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
              maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
              // value={getFormattedCommaNumber(
              //   values[METABOLIC_INCIDENCE_FIELDS.MILK_FEVER]?.toString() || '',
              // )}
              value={
                values[METABOLIC_INCIDENCE_FIELDS.MILK_FEVER]?.toString() || ''
              }
              onChange={handleChange(METABOLIC_INCIDENCE_FIELDS.MILK_FEVER)}
              reference={ref =>
                (references[METABOLIC_INCIDENCE_FIELDS.MILK_FEVER] = ref)
              }
              onSubmitEditing={() => {
                references[METABOLIC_INCIDENCE_FIELDS.DYSTOCIA]?.focus();
              }}
              blurOnSubmit={false}
              customLabelStyle={styles.customFieldLabel}
              customInputContainerStyle={styles.customInputContainer}
              hasCommas={true}
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: references[METABOLIC_INCIDENCE_FIELDS.DYSTOCIA],
                });
              }}
            />
          </View>

          {/* Dystocia */}
          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('dystocia')}
              disabled={screenDisabled}
              placeholder={i18n.t('numberPlaceholder')}
              minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
              maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
              // value={getFormattedCommaNumber(
              //   values[METABOLIC_INCIDENCE_FIELDS.DYSTOCIA]?.toString() || '',
              // )}
              value={
                values[METABOLIC_INCIDENCE_FIELDS.DYSTOCIA]?.toString() || ''
              }
              onChange={handleChange(METABOLIC_INCIDENCE_FIELDS.DYSTOCIA)}
              reference={ref =>
                (references[METABOLIC_INCIDENCE_FIELDS.DYSTOCIA] = ref)
              }
              onSubmitEditing={() => {
                references[METABOLIC_INCIDENCE_FIELDS.DEATH_LOSS]?.focus();
              }}
              blurOnSubmit={false}
              customLabelStyle={styles.customFieldLabel}
              customInputContainerStyle={styles.customInputContainer}
              hasCommas={true}
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: references[METABOLIC_INCIDENCE_FIELDS.DEATH_LOSS],
                });
              }}
            />
          </View>

          {/* Death Loss */}
          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('deathLoss')}
              disabled={screenDisabled}
              placeholder={i18n.t('numberPlaceholder')}
              minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
              maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
              // value={getFormattedCommaNumber(
              //   values[METABOLIC_INCIDENCE_FIELDS.DEATH_LOSS]?.toString() || '',
              // )}
              value={
                values[METABOLIC_INCIDENCE_FIELDS.DEATH_LOSS]?.toString() || ''
              }
              onChange={handleChange(METABOLIC_INCIDENCE_FIELDS.DEATH_LOSS)}
              reference={ref =>
                (references[METABOLIC_INCIDENCE_FIELDS.DEATH_LOSS] = ref)
              }
              onSubmitEditing={() => {
                Keyboard.dismiss();
              }}
              blurOnSubmit={false}
              customLabelStyle={styles.customFieldLabel}
              customInputContainerStyle={styles.customInputContainer}
              hasCommas={true}
              returnKeyType={NEXT_FIELD_TEXT.DONE}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({ dismiss: true });
              }}
            />
          </View>
        </>
      )}
    </View>
  );
};

export default MetabolicIncidenceCasesForm;
