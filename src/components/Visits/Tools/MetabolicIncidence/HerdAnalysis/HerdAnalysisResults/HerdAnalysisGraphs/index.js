// modules
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import CustomBottomSheet from '../../../../../../common/CustomBottomSheet';
import ToolGraph from '../../../../common/ToolGraph';
import CategoryBarGraph from '../../../../../../common/CategoryBarGraph';

// localization
import i18n from '../../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import { CHEVRON_DOWN_BLUE_ICON } from '../../../../../../../constants/AssetSVGConstants';
import { BOTTOM_SHEET_TYPE } from '../../../../../../../constants/FormConstants';
import { normalize } from '../../../../../../../constants/theme/variables/customFont';
import {
  METABOLIC_DISORDER_GRAPH_COLORS,
  METABOLIC_INCIDENCE_GRAPHS,
  METABOLIC_INCIDENCE_GRAPH_TYPES,
  VISIT_TABLE_FIELDS,
} from '../../../../../../../constants/AppConstants';
import colors from '../../../../../../../constants/theme/variables/customColor';

// helpers
import { stringIsEmpty } from '../../../../../../../helpers/alphaNumericHelper';
import {
  formatVisitsForDisorderGraph,
  getDisorderGraphLegend,
  getFormattedRecentVisits,
  getGraphLegends,
  getMetabolicDisorderGraphValue,
  getMetabolicIncidenceGraphGoal,
  getMetabolicIncidenceGraphIncidencePercent,
  mapDataForIncidencePercentExport,
  mapDataForMetabolicDisorderExport,
} from '../../../../../../../helpers/metabolicIncidenceHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../../../store/actions/tool';

const HerdAnalysisGraphs = ({
  milkPrice,
  toolData,
  goalsData,
  currency,
  selectedVisits,
  onDownloadPress,
  showCompareButton,
  onShareHerdAnalysisData,
}) => {
  const dispatch = useDispatch();
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);

  const [selectedGraph, setSelectedGraph] = useState(
    METABOLIC_INCIDENCE_GRAPHS[0],
  );
  const [showGraphBottomSheet, setShowGraphBottomSheet] = useState(false);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  const [recentVisits, setRecentVisits] = useState([]);
  const [metabolicDisorderVisits, setMetabolicDisorderVisits] = useState([]);

  useEffect(() => {
    const visit = visitState.visit || {};
    const siteId = !stringIsEmpty(visit.siteId)
      ? visit.siteId
      : visit.localSiteId;
    const accountId = !stringIsEmpty(visit.customerId)
      ? visit.customerId
      : visit.localCustomerId;

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visit?.id,
        tool: VISIT_TABLE_FIELDS.METABOLIC_INCIDENCE,
      }),
    );
  }, []);

  useEffect(() => {
    if (!toolState.recentVisitsLoading) {
      const data = getFormattedRecentVisits(toolState?.recentVisits);
      setRecentVisits(data);
    }
  }, [toolState.recentVisitsLoading]);

  useEffect(() => {
    const visit = visitState?.visit || {};
    const selectedRecentVisits = formatVisitsForDisorderGraph(
      visit,
      toolData,
      goalsData,
      recentVisits,
      selectedVisits,
    );
    setMetabolicDisorderVisits(selectedRecentVisits);
  }, [recentVisits, selectedVisits, toolData, goalsData]);

  const openGraphBottomSheet = () => {
    setShowGraphBottomSheet(true);
  };

  const closeGraphBottomSheet = () => {
    setShowGraphBottomSheet(false);
  };

  const onGraphChange = item => {
    if (item?.id === METABOLIC_INCIDENCE_GRAPH_TYPES.METABOLIC_DISORDER) {
      showCompareButton(true);
    } else {
      showCompareButton(false);
    }
    setSelectedGraph(item);
    closeGraphBottomSheet();
  };

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const renderMetabolicIncidencePercentGraph = () => {
    const legendItems = [
      {
        color: colors.goalPercentBar,
        title: `${i18n.t('goal')} (${i18n.t('%')})`,
      },
      {
        color: colors.metabolicIncidencePercentBar,
        title: `${i18n.t('incidence')} (${i18n.t('%')})`,
      },
    ];

    const labels = getGraphLegends();
    const data = [
      {
        dataPoints: getMetabolicIncidenceGraphGoal(goalsData),
        barColor: colors.goalPercentBar,
      },
      {
        dataPoints: getMetabolicIncidenceGraphIncidencePercent(toolData),
        barColor: colors.metabolicIncidencePercentBar,
      },
    ];
    return (
      <View>
        <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
          <CategoryBarGraph
            barWidth={16}
            verticalTickCount={5}
            xAxisFormatter={t => {
              return typeof t == 'string' ? t?.replace(' ', '\n') : t;
            }}
            yAxisFormatter={t => {
              return t + '%';
            }}
            valuesFormatter={t => {
              return t + '%';
            }}
            verticalAxisLabel={i18n.t('metabolicIncidencePercent')}
            labels={labels || []}
            data={data || []}
            width={
              landscapeModalVisible
                ? styles.graphWidthLandscape.width
                : styles.graphWidth.width
            }
            height={
              landscapeModalVisible
                ? styles.graphHeightLandscape.height
                : styles.graphHeight.height
            }
            customYAxisLabelStyle={styles.incidenceGraphYAxisLabel}
          />
        </ScrollView>
        <View style={styles.legendContainer(landscapeModalVisible)}>
          {legendItems.map((legend, index) => {
            return (
              <View style={styles.legendItem} key={`${legend.title}-${index}`}>
                <View style={styles.legendCircle(legend.color)}></View>
                <Text style={styles.legendText}>{legend.title}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderMetabolicDisorderGraph = () => {
    const legendItems = metabolicDisorderVisits.map((visit, index) => {
      return {
        color: METABOLIC_DISORDER_GRAPH_COLORS[index],
        title: getDisorderGraphLegend(visit),
      };
    });

    const labels = getGraphLegends();
    const data = metabolicDisorderVisits.map((visit, index) => {
      return {
        dataPoints: getMetabolicDisorderGraphValue(
          visit?.toolData,
          visit?.goalsData,
          visit?.toolData?.milkPrice,
          true,
        ),
        barColor: METABOLIC_DISORDER_GRAPH_COLORS[index],
      };
    });

    return (
      <View>
        <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
          <CategoryBarGraph
            useTiltedLabels
            barWidth={9}
            barOffset={18}
            verticalTickCount={5}
            xAxisFormatter={t => {
              return typeof t == 'string' ? t?.replace(' ', '\n') : t;
            }}
            yAxisFormatter={t => {
              return Math.round(t);
            }}
            valuesFormatter={t => {
              return Math.round(t);
            }}
            verticalAxisLabel={`${i18n.t(
              'metabolicDisorderCostPerCow',
            )} (${currency})`}
            customBarLabelStyle={styles.disorderGraphLabels}
            xDomainPadding={{ x: [17, 15] }}
            labels={labels || []}
            data={data || []}
            width={
              landscapeModalVisible
                ? styles.disorderGraphWidthLandscape(data.length).width
                : styles.disorderGraphWidth(data.length).width
            }
            height={
              landscapeModalVisible
                ? styles.graphHeightLandscape.height
                : styles.disorderGraphHeight.height
            }
            customYAxisLabelStyle={styles.disorderGraphYAxisLabel}
          />
        </ScrollView>
        <View style={styles.legendContainer(landscapeModalVisible)}>
          {legendItems?.map((legend, index) => {
            return (
              <View
                style={[styles.legendItem, styles.legendItemBottomSpacing]}
                key={`${legend.title}-${index}`}>
                <View style={styles.legendCircle(legend.color)}></View>
                <Text style={styles.legendText}>{legend.title}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <ToolGraph
          showExpandIcon
          onDownloadPress={option => {
            if (
              selectedGraph?.id ===
              METABOLIC_INCIDENCE_GRAPH_TYPES.METABOLIC_INCIDENCE_PERCENT
            ) {
              const model = mapDataForIncidencePercentExport(
                visitState.visit,
                getMetabolicIncidenceGraphIncidencePercent(toolData),
                getMetabolicIncidenceGraphGoal(goalsData),
              );
              onDownloadPress(model, option);
            } else {
              const model = mapDataForMetabolicDisorderExport(
                visitState.visit,
                metabolicDisorderVisits,
                milkPrice,
                `${i18n.t('metabolicDisorderCostPerCow')} (${currency})`,
                true,
              );
              onDownloadPress(model, option);
            }
          }}
          onSharePress={(option, exportMethod) => {
            if (
              selectedGraph?.id ===
              METABOLIC_INCIDENCE_GRAPH_TYPES.METABOLIC_INCIDENCE_PERCENT
            ) {
              const model = mapDataForIncidencePercentExport(
                visitState.visit,
                getMetabolicIncidenceGraphIncidencePercent(toolData),
                getMetabolicIncidenceGraphGoal(goalsData),
              );
              onShareHerdAnalysisData(model, option, exportMethod);
            } else {
              const model = mapDataForMetabolicDisorderExport(
                visitState?.visit,
                metabolicDisorderVisits,
                milkPrice,
                `${i18n.t('metabolicDisorderCostPerCow')} (${currency})`,
                true,
              );
              onShareHerdAnalysisData(model, option, exportMethod);
            }
          }}
          handleExpandIconPress={onExpandIconPress}
          showDownloadIcon={!landscapeModalVisible}
          showShareIcon={!landscapeModalVisible}
          landscapeModalVisible={landscapeModalVisible}
          customGraphTitleComponent={
            <View style={styles.infoColumn}>
              {!landscapeModalVisible ? (
                <TouchableOpacity
                  activeOpacity={0.8}
                  style={styles.dropdownTextContainer}
                  onPress={openGraphBottomSheet}>
                  <Text
                    style={[styles.dropdownText, styles.textLimit]}
                    noOfLines={1}>
                    {selectedGraph?.name || ''}
                  </Text>
                  <CHEVRON_DOWN_BLUE_ICON
                    width={normalize(12)}
                    height={normalize(8)}
                  />
                </TouchableOpacity>
              ) : (
                <View style={styles.dropdownTextContainer}>
                  <Text
                    style={[styles.dropdownText, styles.textLimit]}
                    noOfLines={1}>
                    {selectedGraph?.name || ''}
                  </Text>
                </View>
              )}
            </View>
          }
          graphComponent={
            <SafeAreaView>
              <View style={styles.leftPadding}>
                {selectedGraph?.id ===
                METABOLIC_INCIDENCE_GRAPH_TYPES.METABOLIC_INCIDENCE_PERCENT
                  ? renderMetabolicIncidencePercentGraph()
                  : renderMetabolicDisorderGraph()}
              </View>
            </SafeAreaView>
          }
        />
      </ScrollView>

      {showGraphBottomSheet && (
        <CustomBottomSheet
          type={BOTTOM_SHEET_TYPE.SIMPLE}
          selectLabel={i18n.t('selectGraph')}
          data={METABOLIC_INCIDENCE_GRAPHS}
          disableSearch
          onChange={onGraphChange}
          onClose={closeGraphBottomSheet}
          customContainerStyle={styles.scaleBottomSheetContainer}
        />
      )}
    </View>
  );
};

export default HerdAnalysisGraphs;
