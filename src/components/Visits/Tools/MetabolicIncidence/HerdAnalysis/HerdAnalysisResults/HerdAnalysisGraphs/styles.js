import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../../constants/theme/variables/platform';
import colors from '../../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import DeviceInfo from 'react-native-device-info';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  infoColumn: {
    flexDirection: 'column',
  },
  dropdownTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dropdownText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: 0.15,
    marginRight: normalize(8),
  },
  textLimit: {
    maxWidth: normalize(200),
  },
  scaleBottomSheetContainer: {
    height: normalize(312),
  },
  leftPadding: { paddingLeft: normalize(18) },
  legendContainer: landscapeModalVisible => ({
    flexDirection: 'row',
    marginTop: normalize(24),
    marginBottom: landscapeModalVisible ? normalize(12) : normalize(42),
    marginLeft: normalize(6),
    flexWrap: 'wrap',
  }),
  legendItem: {
    flexDirection: 'row',
    marginRight: normalize(26),
    alignItems: 'center',
  },
  legendItemBottomSpacing: {
    marginBottom: normalize(6),
  },
  legendCircle: backgroundColor => ({
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(50),
    backgroundColor: backgroundColor,
    marginRight: normalize(12),
  }),
  legendText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(15),
    color: colors.grey1,
    letterSpacing: 0.15,
  },
  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight - 40)
          : normalize(Platform.deviceHeight - 120),
    },
    android: {
      width: DeviceInfo.isTablet()
        ? normalize(Platform.deviceHeight - 340)
        : normalize(720),
    },
  }),
  graphWidth: {
    width: normalize(700),
  },
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 120)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.45)
          : normalize(Platform.deviceWidth - 110),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 340 : 120),
      ),
    },
  }),
  graphHeight: {
    height: normalize(Platform.deviceHeight * 0.45),
  },
  disorderGraphHeight: {
    height: normalize(Platform.deviceHeight * 0.4),
  },
  disorderGraphWidthLandscape: dataLength =>
    RNPlatform.select({
      ios: {
        width:
          Platform.deviceHeight < 700
            ? Math.max(
                normalize(500 + dataLength * 90),
                normalize(Platform.deviceHeight - 40),
              )
            : Math.max(
                normalize(500 + dataLength * 90),
                normalize(Platform.deviceHeight - 120),
              ),
      },
      android: {
        width: Math.max(
          normalize(500 + dataLength * 90),
          DeviceInfo.isTablet()
            ? normalize(Platform.deviceHeight - 360)
            : normalize(720),
        ),
      },
    }),
  disorderGraphWidth: dataLength => ({
    width: normalize(500 + dataLength * 90),
  }),
  disorderGraphLabels: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(9),
    lineHeight: normalize(11),
    fill: colors.primaryMain,
    padding: normalize(-1),
  },
  incidenceGraphYAxisLabel: {
    marginLeft:
      RNPlatform.OS === 'ios'
        ? Platform.deviceHeight < 700
          ? normalize(-50)
          : normalize(-50)
        : normalize(-40),
    marginRight:
      RNPlatform.OS === 'ios'
        ? Platform.deviceHeight < 700
          ? normalize(-60)
          : normalize(-60)
        : normalize(-70),
  },
  disorderGraphYAxisLabel: {
    margin: normalize(-65),
    marginLeft:
      RNPlatform.OS === 'ios'
        ? Platform.deviceHeight < 700
          ? -80
          : -80
        : normalize(-80),
    marginRight:
      RNPlatform.OS === 'ios'
        ? Platform.deviceHeight < 700
          ? -100
          : -90
        : normalize(-90),
    lineHeight: RNPlatform.OS === 'ios' ? normalize(-100) : normalize(16),
  },
};
