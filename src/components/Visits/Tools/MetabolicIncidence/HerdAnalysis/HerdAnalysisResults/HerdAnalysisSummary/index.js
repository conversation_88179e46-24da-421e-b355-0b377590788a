// modules
import React, { useEffect, useState } from 'react';
import { View, Text } from 'react-native';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../../localization/i18n';

// reusable components
import SummaryTable from '../../../../common/SummaryTable';

// helpers
import {
  getAnnualImpactSummary,
  getMetabolicIncidenceSummary,
  getTotalAnnualImpactSummary,
} from '../../../../../../../helpers/metabolicIncidenceHelper';

const HerdAnalysisSummary = props => {
  const { milkPrice = 0, toolData = {}, goalsData = {}, currency } = props;

  const [metabolicIncidenceSummary, setMetabolicIncidenceSummary] = useState(
    [],
  );
  const [annualImpactSummary, setAnnualImpactSummary] = useState([]);
  const [annualTotalImpactSummary, setAnnualTotalImpactSummary] = useState([]);

  useEffect(() => {
    // Metabolic Incidence %
    const metabolicIncidenceSummaryData = getMetabolicIncidenceSummary(
      toolData,
      goalsData,
    );
    setMetabolicIncidenceSummary(metabolicIncidenceSummaryData);

    // Annual Economic Impact
    const annualImpactSummaryData = getAnnualImpactSummary(
      toolData,
      goalsData,
      milkPrice,
      true,
    );
    setAnnualImpactSummary(annualImpactSummaryData);

    // Annual Economic Impact - Total
    const annualTotalImpactSummaryData = getTotalAnnualImpactSummary(
      toolData,
      goalsData,
      milkPrice,
      true,
    );
    setAnnualTotalImpactSummary(annualTotalImpactSummaryData);
  }, [milkPrice, toolData, goalsData]);

  return (
    <View style={styles.container}>
      <Text style={styles.titleText}>
        {i18n.t('metabolicIncidencePercent')}
      </Text>
      <SummaryTable
        tableKey={'metabolicIncidenceSummary'}
        headerData={[
          i18n.t('case'),
          `${i18n.t('incidence')} (${i18n.t('%')})`,
          `${i18n.t('goal')} (${i18n.t('%')})`,
          i18n.t('difference'),
        ]}
        data={metabolicIncidenceSummary}
      />

      <Text style={[styles.titleText, styles.titleMarginTop]}>
        {i18n.t('annualEconomicImpact')}
      </Text>
      <SummaryTable
        tableKey={'annualImpactSummary'}
        headerData={[
          i18n.t('case'),
          `${i18n.t('milkLossValue')} (${currency})`,
          `${i18n.t('increasedDaysOpen')} (${currency})`,
          `${i18n.t('treatmentCost')} (${currency})`,
        ]}
        data={annualImpactSummary}
      />

      <Text style={[styles.titleText, styles.titleMarginTop]}>
        {i18n.t('annualEconomicImpactTotal')}
      </Text>
      <SummaryTable
        tableKey={'annualTotalImpactSummary'}
        headerData={[
          i18n.t('case'),
          `${i18n.t('totalCost')} (${currency})`,
          `${i18n.t('currentCostPerCow')} (${currency})`,
        ]}
        data={annualTotalImpactSummary}
      />
    </View>
  );
};

export default HerdAnalysisSummary;
