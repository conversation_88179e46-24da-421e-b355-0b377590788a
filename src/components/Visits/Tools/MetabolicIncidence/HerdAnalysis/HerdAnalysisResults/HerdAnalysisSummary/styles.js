import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flexDirection: 'column',
    marginTop: normalize(16),
    marginHorizontal: normalize(21),
    marginBottom: normalize(30),
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.primaryMain,
  },
  titleMarginTop: {
    marginTop: normalize(24),
  },
};
