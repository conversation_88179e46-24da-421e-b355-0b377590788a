// modules
import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, TouchableOpacity, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../localization/i18n';

// reusable components
import HerdAnalysisSummary from './HerdAnalysisSummary';
import HerdAnalysisGraphs from './HerdAnalysisGraphs';

// constants
import { TOOL_RESULTS_TABS } from '../../../../../../constants/AppConstants';

// actions
import { getMetabolicIncidenceGoalsRequest } from '../../../../../../store/actions/tools/metabolicIncidence';

// helpers
import { getDefaultGoalValues } from '../../../../../../helpers/metabolicIncidenceHelper';

const HerdAnalysisResults = props => {
  const {
    milkPrice,
    toolData,
    currency,
    selectedVisits,
    onDownloadPress,
    onShareHerdAnalysisData,
    showCompareButton,
  } = props;
  const [selectedTab, setSelectedTab] = useState(TOOL_RESULTS_TABS.SUMMARY);
  const [goalsData, setGoalsData] = useState(getDefaultGoalValues(true));

  let scrollViewRef = useRef();
  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit);
  const metabolicIncidenceState = useSelector(
    state => state.metabolicIncidence,
  );

  const getGoalsData = () => {
    const { id } = visitState.visit || {};
    dispatch(
      getMetabolicIncidenceGoalsRequest({
        localId: id,
      }),
    );
  };

  useEffect(() => {
    getGoalsData();
  }, []);

  useEffect(() => {
    if (!metabolicIncidenceState.goalsLoading) {
      if (metabolicIncidenceState?.goals) {
        setGoalsData(metabolicIncidenceState.goals);
      } else {
        setGoalsData(getDefaultGoalValues(true));
      }
    }
  }, [metabolicIncidenceState?.goalsLoading]);

  const scrollToTop = () => {
    scrollViewRef?.current?.scrollTo({ x: 0, y: 0 });
  };

  return (
    <>
      <View style={styles.container}>
        <ScrollView
          keyboardDismissMode="none"
          keyboardShouldPersistTaps="always"
          ref={scrollViewRef}>
          {selectedTab === TOOL_RESULTS_TABS.SUMMARY ? (
            <HerdAnalysisSummary
              milkPrice={milkPrice}
              toolData={toolData}
              goalsData={goalsData}
              currency={currency}
            />
          ) : (
            <HerdAnalysisGraphs
              milkPrice={milkPrice}
              toolData={toolData}
              goalsData={goalsData}
              currency={currency}
              selectedVisits={selectedVisits}
              onDownloadPress={onDownloadPress}
              showCompareButton={showCompareButton}
              onShareHerdAnalysisData={onShareHerdAnalysisData}
            />
          )}
        </ScrollView>
        <View style={styles.tabsRow}>
          <TouchableOpacity
            activeOpacity={0.7}
            style={
              selectedTab === TOOL_RESULTS_TABS.SUMMARY
                ? styles.selectedTabContainer
                : null
            }
            onPress={() => {
              setSelectedTab(TOOL_RESULTS_TABS.SUMMARY);
              showCompareButton(false);
              scrollToTop();
            }}>
            <Text
              style={[
                styles.tabItemText,
                selectedTab === TOOL_RESULTS_TABS.SUMMARY
                  ? styles.selectedTabItemText
                  : styles.unSelectedTabItemText,
              ]}>
              {i18n.t('summary')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.7}
            style={[
              styles.tabMarginLeft,
              selectedTab === TOOL_RESULTS_TABS.GRAPH
                ? styles.selectedTabContainer
                : null,
            ]}
            onPress={() => {
              setSelectedTab(TOOL_RESULTS_TABS.GRAPH);
              scrollToTop();
            }}>
            <Text
              style={[
                styles.tabItemText,
                selectedTab === TOOL_RESULTS_TABS.GRAPH
                  ? styles.selectedTabItemText
                  : styles.unSelectedTabItemText,
              ]}>
              {i18n.t('graph')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
};

export default HerdAnalysisResults;
