import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  herdFormContainer: {
    flex: 1,
  },
  formHeading: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(26),
    letterSpacing: 0.2,
    color: colors.primaryMain,
  },
  requiredLabelRow: {
    flexDirection: 'row',
    marginTop: normalize(4),
  },
  starIcon: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.stericColor,
  },
  requiredLabel: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontStyle: 'italic',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginLeft: normalize(6),
    marginBottom: normalize(20),
  },
  formInputView: {
    marginBottom: normalize(15),
  },
  customFieldLabel: {
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    // textTransform: 'capitalize',
    marginBottom: normalize(8),
  },
  customInputContainer: {
    width: '100%',
    paddingHorizontal: normalize(16),
  },
};
