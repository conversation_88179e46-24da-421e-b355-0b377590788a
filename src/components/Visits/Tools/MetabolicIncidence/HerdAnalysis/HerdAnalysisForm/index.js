// modules
import React, { useState } from 'react';
import { View, Text } from 'react-native';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import MetabolicIncidenceCasesForm from '../MetabolicIncidenceCasesForm';
import PerformanceTreatmentCostsForm from '../PerformanceTreatmentCostsForm';
import NumberFormInput from '../../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  METABOLIC_INCIDENCE_CASES_MAX_LIMIT,
  METABOLIC_INCIDENCE_CASES_MIN_LIMIT,
  METABOLIC_INCIDENCE_FORM_ACCORDIONS,
  METABOLIC_INCIDENCE_MILK_PRICE_MAX_LIMIT,
  METABOLIC_INCIDENCE_MILK_PRICE_MIN_LIMIT,
  METABOLIC_INCIDENCE_REPLACEMENT_COW_COST_MAX_LIMIT,
  METABOLIC_INCIDENCE_TOTAL_FRESH_COWS_MAX_LIMIT,
  METABOLIC_INCIDENCE_TOTAL_FRESH_COWS_MIN_LIMIT,
  NEXT_FIELD_TEXT,
  TEXT_INPUT_ALIGNMENT,
} from '../../../../../../constants/AppConstants';
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
  METABOLIC_INCIDENCE_FIELDS,
} from '../../../../../../constants/FormConstants';

// helpers
import {
  getFormattedCommaNumber,
  stringIsEmpty,
} from '../../../../../../helpers/alphaNumericHelper';

const HerdAnalysisForm = props => {
  const {
    screenDisabled,
    currency,
    weightUnit,
    values,
    errors,
    touched,
    references,
    handleChange,
    handleBlur,
    scrollAbove,
  } = props;

  const [selectedAccordion, setSelectedAccordion] = useState(
    METABOLIC_INCIDENCE_FORM_ACCORDIONS.METABOLIC_INCIDENCE_CASES,
  );

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  return (
    <View style={styles.herdFormContainer}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <Text style={styles.formHeading}>{i18n.t('herdLevelInformation')}</Text>
      <View style={styles.requiredLabelRow}>
        <Text style={styles.starIcon}>{i18n.t('starSign')}</Text>
        <Text style={styles.requiredLabel}>{i18n.t('requiredFieldMsg')}</Text>
      </View>

      {/* Total Fresh Cows/Year */}
      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('totalFreshCowsPerYear')}
          required
          disabled={screenDisabled}
          placeholder={i18n.t('numberPlaceholder')}
          minValue={METABOLIC_INCIDENCE_TOTAL_FRESH_COWS_MIN_LIMIT}
          maxValue={METABOLIC_INCIDENCE_TOTAL_FRESH_COWS_MAX_LIMIT}
          isInteger={true}
          keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
          hasCommas={true}
          // skipCommaReplacementValidation={true}
          textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
          // value={getFormattedCommaNumber(
          //   values[METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_PER_YEAR],
          // )}
          value={values[METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_PER_YEAR]}
          error={
            touched[METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_PER_YEAR] &&
            errors[METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_PER_YEAR]
          }
          onChange={handleChange(
            METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_PER_YEAR,
          )}
          forceOnBlur
          onBlur={handleBlur(
            METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_PER_YEAR,
          )}
          reference={ref =>
            (references[METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_PER_YEAR] =
              ref)
          }
          onSubmitEditing={() => {
            references[METABOLIC_INCIDENCE_FIELDS.MILK_PRICE]?.focus();
          }}
          blurOnSubmit={false}
          customLabelStyle={styles.customFieldLabel}
          customInputContainerStyle={styles.customInputContainer}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: references[METABOLIC_INCIDENCE_FIELDS.MILK_PRICE],
            });
          }}
        />
      </View>

      {/* Milk Price */}
      <View style={styles.formInputView}>
        <NumberFormInput
          label={`${i18n.t('milkPrice')} (${currency}/${weightUnit})`}
          required
          disabled={screenDisabled}
          placeholder={currency + i18n.t('decimalNumberPlaceholder')}
          minValue={METABOLIC_INCIDENCE_MILK_PRICE_MIN_LIMIT}
          maxValue={METABOLIC_INCIDENCE_MILK_PRICE_MAX_LIMIT}
          maxLength={12}
          isInteger={false}
          hasCommas={true}
          // skipCommaReplacementValidation={true}
          decimalPoints={3}
          textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
          currency={
            !stringIsEmpty(
              values[METABOLIC_INCIDENCE_FIELDS.MILK_PRICE]?.toString(),
            )
              ? currency
              : null
          }
          // value={getFormattedCommaNumber(
          //   values[METABOLIC_INCIDENCE_FIELDS.MILK_PRICE]?.toString() || '',
          // )}
          value={
            values[METABOLIC_INCIDENCE_FIELDS.MILK_PRICE]?.toString() || ''
          }
          error={
            touched[METABOLIC_INCIDENCE_FIELDS.MILK_PRICE] &&
            errors[METABOLIC_INCIDENCE_FIELDS.MILK_PRICE]
          }
          onChange={handleChange(METABOLIC_INCIDENCE_FIELDS.MILK_PRICE)}
          forceOnBlur
          onBlur={handleBlur(METABOLIC_INCIDENCE_FIELDS.MILK_PRICE)}
          reference={ref =>
            (references[METABOLIC_INCIDENCE_FIELDS.MILK_PRICE] = ref)
          }
          onSubmitEditing={() => {
            references[
              METABOLIC_INCIDENCE_FIELDS.REPLACEMENT_COW_COST
            ]?.focus();
          }}
          blurOnSubmit={false}
          customLabelStyle={styles.customFieldLabel}
          customInputContainerStyle={styles.customInputContainer}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[METABOLIC_INCIDENCE_FIELDS.REPLACEMENT_COW_COST],
            });
          }}
        />
      </View>

      {/* Replacement Cow Cost */}
      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('replacementCowCost')}
          disabled={screenDisabled}
          placeholder={currency + i18n.t('decimalNumberPlaceholder')}
          minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
          maxValue={METABOLIC_INCIDENCE_REPLACEMENT_COW_COST_MAX_LIMIT}
          maxLength={11}
          isInteger={false}
          decimalPoints={2}
          textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
          currency={
            !stringIsEmpty(
              values[
                METABOLIC_INCIDENCE_FIELDS.REPLACEMENT_COW_COST
              ]?.toString(),
            )
              ? currency
              : null
          }
          // value={getFormattedCommaNumber(
          //   values[
          //     METABOLIC_INCIDENCE_FIELDS.REPLACEMENT_COW_COST
          //   ]?.toString() || '',
          // )}
          value={
            values[
              METABOLIC_INCIDENCE_FIELDS.REPLACEMENT_COW_COST
            ]?.toString() || ''
          }
          onChange={handleChange(
            METABOLIC_INCIDENCE_FIELDS.REPLACEMENT_COW_COST,
          )}
          reference={ref =>
            (references[METABOLIC_INCIDENCE_FIELDS.REPLACEMENT_COW_COST] = ref)
          }
          onSubmitEditing={() => {
            references[
              METABOLIC_INCIDENCE_FIELDS.COST_OF_EXTRA_DAYS_OPEN
            ]?.focus();
          }}
          blurOnSubmit={false}
          customLabelStyle={styles.customFieldLabel}
          customInputContainerStyle={styles.customInputContainer}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[METABOLIC_INCIDENCE_FIELDS.COST_OF_EXTRA_DAYS_OPEN],
            });
          }}
          hasCommas={true}
          // skipCommaReplacementValidation={true}
        />
      </View>

      {/* Cost of Extra Days Open */}
      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('costOfExtraDaysOpen')}
          disabled={screenDisabled}
          placeholder={currency + i18n.t('decimalNumberPlaceholder')}
          minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
          maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
          isInteger={false}
          decimalPoints={2}
          textAlign={TEXT_INPUT_ALIGNMENT.LEFT}
          currency={
            !stringIsEmpty(
              values[
                METABOLIC_INCIDENCE_FIELDS.COST_OF_EXTRA_DAYS_OPEN
              ]?.toString(),
            )
              ? currency
              : null
          }
          // value={getFormattedCommaNumber(
          //   values[
          //     METABOLIC_INCIDENCE_FIELDS.COST_OF_EXTRA_DAYS_OPEN
          //   ]?.toString() || '',
          // )}
          value={
            values[
              METABOLIC_INCIDENCE_FIELDS.COST_OF_EXTRA_DAYS_OPEN
            ]?.toString() || ''
          }
          onChange={handleChange(
            METABOLIC_INCIDENCE_FIELDS.COST_OF_EXTRA_DAYS_OPEN,
          )}
          reference={ref =>
            (references[METABOLIC_INCIDENCE_FIELDS.COST_OF_EXTRA_DAYS_OPEN] =
              ref)
          }
          onSubmitEditing={() => {
            if (
              selectedAccordion ===
              METABOLIC_INCIDENCE_FORM_ACCORDIONS.METABOLIC_INCIDENCE_CASES
            ) {
              references[
                METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_EVALUATION
              ]?.focus();
            } else {
              references[
                METABOLIC_INCIDENCE_FIELDS.RETAINED_PLACENTA_MILK_PER_COW
              ]?.focus();
            }
          }}
          blurOnSubmit={false}
          customLabelStyle={styles.customFieldLabel}
          customInputContainerStyle={styles.customInputContainer}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                selectedAccordion ===
                METABOLIC_INCIDENCE_FORM_ACCORDIONS.METABOLIC_INCIDENCE_CASES
                  ? references[
                      METABOLIC_INCIDENCE_FIELDS.TOTAL_FRESH_COWS_EVALUATION
                    ]
                  : references[
                      METABOLIC_INCIDENCE_FIELDS.RETAINED_PLACENTA_MILK_PER_COW
                    ],
            });
          }}
          hasCommas={true}
          // skipCommaReplacementValidation={true}
        />
      </View>

      <MetabolicIncidenceCasesForm
        screenDisabled={screenDisabled}
        isSelected={
          selectedAccordion ===
          METABOLIC_INCIDENCE_FORM_ACCORDIONS.METABOLIC_INCIDENCE_CASES
        }
        onOpenAccordion={() => {
          setSelectedAccordion(
            METABOLIC_INCIDENCE_FORM_ACCORDIONS.METABOLIC_INCIDENCE_CASES,
          );
        }}
        values={values}
        errors={errors}
        touched={touched}
        references={references}
        handleChange={handleChange}
        handleBlur={handleBlur}
        setType={setType}
        setAction={setAction}
      />

      <PerformanceTreatmentCostsForm
        screenDisabled={screenDisabled}
        isSelected={
          selectedAccordion ===
          METABOLIC_INCIDENCE_FORM_ACCORDIONS.PERFORMANCE_AND_TREATMENT_COSTS
        }
        onOpenAccordion={() => {
          setSelectedAccordion(
            METABOLIC_INCIDENCE_FORM_ACCORDIONS.PERFORMANCE_AND_TREATMENT_COSTS,
          );
          scrollAbove();
        }}
        currency={currency}
        weightUnit={weightUnit}
        values={values}
        references={references}
        handleChange={handleChange}
        setType={setType}
        setAction={setAction}
      />
    </View>
  );
};

export default HerdAnalysisForm;
