// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import MetabolicCaseSection from '../MetabolicCaseSection';

// constants
import { METABOLIC_INCIDENCE_CASES } from '../../../../../../constants/AppConstants';
import {
  ACCORDION_DOWN_ICON,
  ACCORDION_RIGHT_ICON,
} from '../../../../../../constants/AssetSVGConstants';
import { normalize } from '../../../../../../constants/theme/variables/customFont';

const PerformanceTreatmentCostsForm = props => {
  const {
    screenDisabled,
    isSelected,
    onOpenAccordion,
    currency,
    weightUnit,
    values,
    references,
    handleChange,
    setType,
    setAction,
  } = props;

  const getNextCaseType = index => {
    if (index === METABOLIC_INCIDENCE_CASES.length - 1) {
      return null;
    } else {
      return METABOLIC_INCIDENCE_CASES[index + 1].caseType;
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity activeOpacity={0.9} onPress={onOpenAccordion}>
        <View style={styles.accordionContainer}>
          <Text style={styles.accordionTitle}>
            {i18n.t('performanceAndTreatmentCosts')}
          </Text>
          {isSelected ? (
            <ACCORDION_DOWN_ICON width={normalize(14)} height={normalize(14)} />
          ) : (
            <ACCORDION_RIGHT_ICON
              width={normalize(14)}
              height={normalize(14)}
            />
          )}
        </View>
      </TouchableOpacity>

      {isSelected && (
        <>
          <Text style={styles.sectionContent}>
            {i18n.t('performanceAndTreatmentCostsContent')}
          </Text>
          {METABOLIC_INCIDENCE_CASES.map((item, index) => {
            return (
              <MetabolicCaseSection
                key={item.caseType}
                caseType={item.caseType}
                nextCaseType={getNextCaseType(index)}
                title={item.title}
                screenDisabled={screenDisabled}
                currency={currency}
                weightUnit={weightUnit}
                values={values}
                references={references}
                handleChange={handleChange}
                setType={setType}
                setAction={setAction}
                index={index}
              />
            );
          })}
        </>
      )}
    </View>
  );
};

export default PerformanceTreatmentCostsForm;
