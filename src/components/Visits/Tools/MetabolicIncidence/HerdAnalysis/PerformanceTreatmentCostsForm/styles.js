import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: { flexDirection: 'column' },
  accordionContainer: {
    width: '100%',
    height: normalize(40),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.primaryMain,
    borderRadius: normalize(4),
    paddingLeft: normalize(8),
    paddingRight: normalize(11),
  },
  accordionTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.15,
    color: colors.white,
  },
  sectionContent: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(12),
    lineHeight: normalize(17),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginVertical: normalize(15),
  },
};
