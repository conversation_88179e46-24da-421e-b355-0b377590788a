import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: { flexDirection: 'column' },
  title: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    marginBottom: normalize(16),
  },
  formInputView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: normalize(16),
  },
  fieldLabel: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  customInputContainer: {
    width: normalize(106),
    marginTop: 0,
    paddingTop: 0,
  },
  customFieldLabel: {
    marginBottom: normalize(0),
  },
};
