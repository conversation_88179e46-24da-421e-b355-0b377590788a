// modules
import React from 'react';
import { View, Text, Keyboard } from 'react-native';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import NumberFormInput from '../../../../../common/NumberFormInput';

// constants
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
  METABOLIC_INCIDENCE_FIELDS,
} from '../../../../../../constants/FormConstants';
import {
  METABOLIC_INCIDENCE_CASES,
  METABOLIC_INCIDENCE_CASES_MAX_LIMIT,
  METABOLIC_INCIDENCE_CASES_MIN_LIMIT,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';

// helpers
import {
  getFormattedCommaNumber,
  stringIsEmpty,
} from '../../../../../../helpers/alphaNumericHelper';

const MetabolicCaseSection = props => {
  const {
    caseType,
    nextCaseType,
    title,
    screenDisabled,
    currency,
    weightUnit,
    values,
    references,
    handleChange,
    setType,
    setAction,
    index,
  } = props;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>

      {/* Milk/Cow */}
      <View style={styles.formInputView}>
        <Text style={styles.fieldLabel}>{`${i18n.t(
          'milkPerCow',
        )} (${weightUnit})`}</Text>
        <NumberFormInput
          disabled={screenDisabled}
          placeholder={i18n.t('decimalNumberPlaceholder')}
          minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
          maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
          isInteger={false}
          decimalPoints={2}
          hideLabel={true}
          hideUnit={true}
          // value={getFormattedCommaNumber(
          //   values[
          //     METABOLIC_INCIDENCE_FIELDS[`${caseType}_MILK_PER_COW`]
          //   ]?.toString() || '',
          // )}
          value={
            values[
              METABOLIC_INCIDENCE_FIELDS[`${caseType}_MILK_PER_COW`]
            ]?.toString() || ''
          }
          onChange={handleChange(
            METABOLIC_INCIDENCE_FIELDS[`${caseType}_MILK_PER_COW`],
          )}
          reference={ref =>
            (references[
              METABOLIC_INCIDENCE_FIELDS[`${caseType}_MILK_PER_COW`]
            ] = ref)
          }
          onSubmitEditing={() => {
            references[
              METABOLIC_INCIDENCE_FIELDS[`${caseType}_DAYS_OPEN`]
            ]?.focus();
          }}
          blurOnSubmit={false}
          customInputContainerStyle={styles.customInputContainer}
          hasCommas={true}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[METABOLIC_INCIDENCE_FIELDS[`${caseType}_DAYS_OPEN`]],
            });
          }}
        />
      </View>

      {/* Days Open */}
      <View style={styles.formInputView}>
        <Text style={styles.fieldLabel}>{i18n.t('daysOpen')}</Text>
        <NumberFormInput
          disabled={screenDisabled}
          placeholder={i18n.t('numberPlaceholder')}
          minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
          maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
          isInteger={true}
          keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
          hideLabel={true}
          hideUnit={true}
          // value={getFormattedCommaNumber(
          //   values[
          //     METABOLIC_INCIDENCE_FIELDS[`${caseType}_DAYS_OPEN`]
          //   ]?.toString() || '',
          // )}
          value={
            values[
              METABOLIC_INCIDENCE_FIELDS[`${caseType}_DAYS_OPEN`]
            ]?.toString() || ''
          }
          onChange={handleChange(
            METABOLIC_INCIDENCE_FIELDS[`${caseType}_DAYS_OPEN`],
          )}
          reference={ref =>
            (references[METABOLIC_INCIDENCE_FIELDS[`${caseType}_DAYS_OPEN`]] =
              ref)
          }
          onSubmitEditing={() => {
            references[
              METABOLIC_INCIDENCE_FIELDS[`${caseType}_TREATMENT_DEFAULT`]
            ]?.focus();
          }}
          blurOnSubmit={false}
          customInputContainerStyle={styles.customInputContainer}
          hasCommas={true}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[
                  METABOLIC_INCIDENCE_FIELDS[`${caseType}_TREATMENT_DEFAULT`]
                ],
            });
          }}
        />
      </View>

      {/* Treatment Default */}
      <View style={styles.formInputView}>
        <Text style={styles.fieldLabel}>{`${i18n.t(
          'treatmentDefault',
        )} (${currency})`}</Text>
        <NumberFormInput
          disabled={screenDisabled}
          placeholder={currency + i18n.t('decimalNumberPlaceholder')}
          minValue={METABOLIC_INCIDENCE_CASES_MIN_LIMIT}
          maxValue={METABOLIC_INCIDENCE_CASES_MAX_LIMIT}
          isInteger={false}
          decimalPoints={2}
          hideLabel={true}
          hideUnit={true}
          currency={
            !stringIsEmpty(
              values[
                METABOLIC_INCIDENCE_FIELDS[`${caseType}_TREATMENT_DEFAULT`]
              ]?.toString(),
            )
              ? currency
              : null
          }
          // value={getFormattedCommaNumber(
          //   values[
          //     METABOLIC_INCIDENCE_FIELDS[`${caseType}_TREATMENT_DEFAULT`]
          //   ]?.toString() || '',
          // )}
          value={
            values[
              METABOLIC_INCIDENCE_FIELDS[`${caseType}_TREATMENT_DEFAULT`]
            ]?.toString() || ''
          }
          onChange={handleChange(
            METABOLIC_INCIDENCE_FIELDS[`${caseType}_TREATMENT_DEFAULT`],
          )}
          reference={ref =>
            (references[
              METABOLIC_INCIDENCE_FIELDS[`${caseType}_TREATMENT_DEFAULT`]
            ] = ref)
          }
          onSubmitEditing={() => {
            if (nextCaseType) {
              references[
                METABOLIC_INCIDENCE_FIELDS[`${nextCaseType}_MILK_PER_COW`]
              ]?.focus();
            } else {
              Keyboard.dismiss();
            }
          }}
          blurOnSubmit={false}
          customInputContainerStyle={styles.customInputContainer}
          hasCommas={true}
          returnKeyType={
            METABOLIC_INCIDENCE_CASES?.length - 1 !== index
              ? NEXT_FIELD_TEXT.NEXT
              : NEXT_FIELD_TEXT.DONE
          }
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            if (index === METABOLIC_INCIDENCE_CASES?.length - 1) {
              setAction({
                dismiss: true,
              });
              return;
            }
            if (nextCaseType) {
              setAction({
                currentRef:
                  references[
                    METABOLIC_INCIDENCE_FIELDS[`${nextCaseType}_MILK_PER_COW`]
                  ],
              });
            }
          }}
        />
      </View>
    </View>
  );
};

export default MetabolicCaseSection;
