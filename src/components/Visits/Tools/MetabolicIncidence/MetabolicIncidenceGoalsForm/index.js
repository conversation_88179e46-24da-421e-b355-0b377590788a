// modules
import React, { useRef, useState } from 'react';
import { View, Text, Keyboard } from 'react-native';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// reusable components
import NumberFormInput from '../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  METABOLIC_INCIDENCE_CASES,
  NEXT_FIELD_TEXT,
  PERCENTAGE_MAX_LIMIT,
  PERCENTAGE_MIN_LIMIT,
} from '../../../../../constants/AppConstants';
import { CONTENT_TYPE } from '../../../../../constants/FormConstants';

const MetabolicIncidenceGoalsForm = props => {
  const { values, handleChange, screenDisabled } = props;
  const [type, setType] = useState(CONTENT_TYPE.NUMBER);
  const [action, setAction] = useState(() => {});

  const references = new Array(METABOLIC_INCIDENCE_CASES.length);
  for (let i = 0; i < references.length; i++) {
    references[i] = useRef();
  }

  const renderListItem = (item, index) => {
    return (
      <View style={styles.goalsTableRow}>
        <Text style={styles.goalsTableRowText}>{item.title}</Text>
        <View style={styles.goalPercentInputContainer}>
          <NumberFormInput
            disabled={screenDisabled}
            placeholder={i18n.t('numberPlaceholder')}
            minValue={PERCENTAGE_MIN_LIMIT}
            maxValue={PERCENTAGE_MAX_LIMIT}
            isInteger={false}
            decimalPoints={1}
            hideLabel={true}
            hideUnit={true}
            value={values[`${item.dbKey}Goal`]?.toString() || ''}
            onChange={handleChange(`${item.dbKey}Goal`)}
            reference={ref => (references[index] = ref)}
            onSubmitEditing={() => {
              if (index + 1 < references.length) {
                references[index + 1]?.focus();
              } else {
                Keyboard.dismiss();
              }
            }}
            blurOnSubmit={false}
            customInputContainerStyle={styles.customInputContainer}
            inputAccessoryViewID="customInputAccessoryView"
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({
                currentRef: references[index + 1],
                dismiss: index + 1 < references.length ? false : true,
              });
            }}
          />
        </View>
      </View>
    );
  };

  return (
    <View style={styles.goalsFormContainer}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <Text style={styles.titleText}>
        {i18n.t('metabolicIncidencePercent')}
      </Text>
      <View style={styles.goalsTableHeader}>
        <Text style={styles.goalsTableHeaderText}>{i18n.t('case')}</Text>
        <View style={styles.goalPercentHeaderContainer}>
          <Text style={styles.goalsTableHeaderText}>{`${i18n.t(
            'goal',
          )} (${i18n.t('%')})`}</Text>
        </View>
      </View>
      {METABOLIC_INCIDENCE_CASES.map((item, index) => {
        return renderListItem(item, index);
      })}
    </View>
  );
};

export default MetabolicIncidenceGoalsForm;
