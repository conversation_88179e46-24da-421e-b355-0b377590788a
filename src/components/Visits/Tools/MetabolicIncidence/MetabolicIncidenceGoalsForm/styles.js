import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  goalsFormContainer: {
    flexDirection: 'column',
    marginTop: normalize(12),
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    marginBottom: normalize(16),
  },
  goalsTableHeader: {
    flexDirection: 'row',
    width: '100%',
    height: normalize(40),
    borderWidth: normalize(1),
    borderColor: colors.lightWhite,
    backgroundColor: colors.grey7,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    marginBottom: normalize(8),
  },
  goalPercentHeaderContainer: {
    width: normalize(106),
    alignItems: 'center',
  },
  goalsTableHeaderText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.grey1,
  },
  goalsTableRow: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
  },
  goalsTableRowText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  goalPercentInputContainer: {
    width: normalize(106),
    marginVertical: normalize(8),
    alignItems: 'center',
  },
  customInputContainer: {
    width: '100%',
    marginTop: 0,
    paddingTop: 0,
  },
};
