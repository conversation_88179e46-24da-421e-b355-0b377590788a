// modules
import React from 'react';
import { View, Text } from 'react-native';

// styles
import styles from './styles';

// common component
import NumberFormInput from '../../../../common/NumberFormInput';

import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';

import {
  INPUT_TYPE,
  KEYBOARD_TYPE,
} from '../../../../../constants/FormConstants';

import i18n from '../../../../../localization/i18n';
import FormInput from '../../../../common/FormInput';
import { GENERAL_INPUT_MAX_LIMIT } from '../../../../../constants/AppConstants';

const InputRow = props => {
  const {
    title,
    disabled,
    placeholder,
    minValue,
    maxValue,
    isInteger,
    decimalPoints,
    value,
    onChange,
    reference,
    onSubmitEditing,
    blurOnSubmit,
    alternateText,
    hideAltText,
    inputAccessoryViewID,
    onFocus,
    returnKeyType,
    hasCommas = false,
    customContainerStyle,
    customInputContainerStyle,
    customTitleStyle,
    textAlign,
    isRequired,
    isNumberField = false,
    onBlur,
    selectTextOnFocus,
  } = props;

  return (
    <View style={[styles.container, customContainerStyle]}>
      <View style={[styles.inputRow, hideAltText ? styles.centerRow : null]}>
        <View style={styles.titleContainerStyle}>
          <Text style={[styles.title, customTitleStyle]}>{title}</Text>
          {isRequired && (
            <Text style={styles.starSignNewStyle}>{i18n.t('starSign')}</Text>
          )}
        </View>
        {isNumberField ? (
          <NumberFormInput
            disabled={disabled}
            placeholder={convertInputNumbersToRegionalBasis(
              placeholder,
              isInteger ? 0 : decimalPoints,
              hasCommas,
            )}
            minValue={minValue}
            maxValue={maxValue}
            isInteger={isInteger}
            decimalPoints={isInteger ? 0 : decimalPoints}
            hideLabel={true}
            hideUnit={true}
            value={value}
            onChange={onChange}
            reference={reference}
            onSubmitEditing={onSubmitEditing}
            blurOnSubmit={blurOnSubmit}
            customInputContainerStyle={[
              styles.inputContainerStyle,
              customInputContainerStyle,
            ]}
            inputAccessoryViewID={inputAccessoryViewID}
            onFocus={onFocus}
            returnKeyType={returnKeyType}
            hasCommas={hasCommas}
            keyboardType={
              isInteger ? KEYBOARD_TYPE.NUMBER_PAD : KEYBOARD_TYPE.DECIMAL
            }
            textAlign={textAlign}
            selectTextOnFocus={selectTextOnFocus}
          />
        ) : (
          <FormInput
            type={INPUT_TYPE.TEXT}
            required={isRequired}
            placeholder={placeholder || i18n.t('inputPlaceholder')}
            maxLength={GENERAL_INPUT_MAX_LIMIT}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            reference={reference}
            onSubmitEditing={onSubmitEditing}
            blurOnSubmit={blurOnSubmit}
            returnKeyType={returnKeyType}
            inputAccessoryViewID={inputAccessoryViewID}
            onFocus={onFocus}
            disabled={disabled}
            customInputContainer={customInputContainerStyle}
            textAlign={textAlign}
          />
        )}
      </View>
      {!hideAltText && (
        <Text style={styles.alternateText}>{alternateText}</Text>
      )}
    </View>
  );
};

export default InputRow;
