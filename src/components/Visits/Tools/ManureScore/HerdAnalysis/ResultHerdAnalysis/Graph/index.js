// modules
import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView } from 'react-native';
import { useSelector } from 'react-redux';

// components
import ToolGraph from '../../../../common/ToolGraph';
import ManureHerdAnalysisGraph from '../../../../../../common/ManureHerdAnalysisGraph';

// localization
import i18n from '../../../../../../../localization/i18n';

// styles
import styles from './styles';

// helpers
import {
  getManureAvgForLactationStage,
  parsePenLactationStages,
} from '../../../../../../../helpers/manureScoreHelper';
import {
  getDomain,
  getFormattedLactationStage,
} from '../../../../../../../helpers/toolHelper';
import { convertStringToNumber } from '../../../../../../../helpers/alphaNumericHelper';
import { ScrollView } from 'native-base';

const ManureHerdGraph = ({
  goalsData,
  penData,
  onDownloadPress,
  onShareClick,
}) => {
  //local states
  const [avgManureData, setAvgManureData] = useState([]);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  //redux state
  const enumState = useSelector(state => state.enums);

  useEffect(() => {
    let dict = parsePenLactationStages(penData, enumState);
    const goalsArray = [...goalsData];
    const lactationStages = enumState?.enum
      ? [
          ...goalsArray.map(goal =>
            getFormattedLactationStage(enumState, goal?.lactationStage),
          ),
        ]
      : [];
    const avgManureData = lactationStages?.map(stage => {
      if (Object.keys(dict)?.includes(stage)) {
        return getManureAvgForLactationStage(dict[stage]);
      }
      return null;
    });
    setAvgManureData(avgManureData);
  }, [penData, goalsData]);

  const getLeftAxisDomain = () => {
    const goals = [...goalsData?.map(e => e?.goal)];
    const minGoalsData = [...goalsData.map(goal => goal.goalMin)];
    const maxGoalsData = [...goalsData.map(goal => goal.goalMax)];
    const avgManureData = [...avgManureData];
    let domain = getDomain([
      ...goals,
      ...minGoalsData,
      ...maxGoalsData,
      ...avgManureData,
    ]);
    domain[1] = 5;
    return domain;
  };

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  return (
    <View style={styles.container}>
      <ToolGraph
        showExpandIcon
        handleExpandIconPress={onExpandIconPress}
        showDownloadIcon={!landscapeModalVisible}
        showShareIcon={!landscapeModalVisible}
        landscapeModalVisible={landscapeModalVisible}
        onSharePress={(option, exportMethod) =>
          onShareClick(
            {
              avgManureData: avgManureData,
              goalsData: [...goalsData?.map(e => e?.goal)],
              minGoalsData: [...goalsData.map(goal => goal.goalMin)],
              maxGoalsData: [...goalsData.map(goal => goal.goalMax)],
            },
            option,
            exportMethod,
            [...goalsData?.map(goal => goal?.lactationStage)],
          )
        }
        onDownloadPress={option =>
          onDownloadPress(
            {
              avgManureData: avgManureData,
              goalsData: [...goalsData?.map(e => e?.goal)],
              minGoalsData: [
                ...goalsData.map(goal => convertStringToNumber(goal.goalMin)),
              ],
              maxGoalsData: [
                ...goalsData.map(goal => convertStringToNumber(goal.goalMax)),
              ],
            },
            option,
            [...goalsData?.map(goal => goal?.lactationStage)],
          )
        }
        customGraphTitleComponent={<View></View>}
        graphComponent={
          <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
            <SafeAreaView>
              <View>
                <ManureHerdAnalysisGraph
                  labels={
                    enumState?.enum
                      ? [
                          ...goalsData.map(goal =>
                            getFormattedLactationStage(
                              enumState,
                              goal.lactationStage,
                            ),
                          ),
                        ]
                      : []
                  }
                  // goals={[...goalsData?.map(e => e?.goal)]}
                  minGoalsData={[
                    ...goalsData?.map(goal =>
                      convertStringToNumber(goal.goalMin),
                    ),
                  ]}
                  maxGoalsData={[
                    ...goalsData?.map(goal =>
                      convertStringToNumber(goal.goalMax),
                    ),
                  ]}
                  avgManureData={avgManureData?.map(e => e && e?.toFixed(2))}
                  width={
                    landscapeModalVisible
                      ? styles.graphWidthLandscape.width
                      : styles.graphWidth.width
                  }
                  height={
                    landscapeModalVisible
                      ? styles.graphHeightLandscape.height
                      : styles.graphHeight.height
                  }
                  xAxisLabel={i18n.t('lactationStages')}
                  leftYAxisLabel={i18n.t('manureScore')}
                  leftAxisDomain={getLeftAxisDomain()}
                  customXAxisLabelStyles={styles.customXAxisLabelStyles}
                  customRightYAxisTickStyle={styles.rightAxisTicks}
                  xAxisFormatter={t => {
                    return typeof t == 'string' ? t?.replace(' ', '\n') : t;
                  }}
                />
              </View>
              <View style={styles.legendRow}>
                <View style={styles.legendItem}>
                  <View style={styles.manureLegend}></View>
                  <Text style={styles.legendText}>
                    {i18n.t('avgManureScore')}
                  </Text>
                </View>
                {/* <View style={styles.legendItem}>
                  <View style={styles.manureGoalLegend}></View>
                  <Text style={styles.legendText}>{i18n.t('goal')}</Text>
                </View> */}
                <View style={styles.legendItem}>
                  <View style={styles.minLegend}></View>
                  <Text style={styles.legendText}>{i18n.t('min')}</Text>
                </View>
                <View style={styles.legendItem}>
                  <View style={styles.maxLegend}></View>
                  <Text style={styles.legendText}>{i18n.t('max')}</Text>
                </View>
              </View>
            </SafeAreaView>
          </ScrollView>
        }
      />
    </View>
  );
};

export default ManureHerdGraph;
