// modules
import React from 'react';
import { View, Text, FlatList } from 'react-native';

// localization
import i18n from '../../../../../../../localization/i18n';

//component
import EmptyListComponent from '../../../../../../../components/common/EmptyListComponent';

//constants
import { NO_RESULT_FOUND_ICON } from '../../../../../../../constants/AssetSVGConstants';

// styles
import styles from './styles';
import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';

const SummaryResults = ({ penData }) => {
  // header to on list
  const summaryHeader = () => (
    <View style={styles.summaryHeader}>
      <Text style={styles.pen}>{i18n.t('pen')}</Text>
      <Text style={styles.manureScore}>{i18n.t('manureScore')}</Text>
      <Text style={styles.dim}>{i18n.t('DIM')}</Text>
    </View>
  );

  const renderItem = (item, index) => {
    return (
      <View style={styles.container} key={index}>
        <Text style={styles.penName}>{item?.name}</Text>
        <Text style={styles.avg}>
          {convertInputNumbersToRegionalBasis(
            parseFloat(item?.avgManureScore),
            2,
          )}
        </Text>
        <Text style={styles.milk}>
          {convertInputNumbersToRegionalBasis(item?.daysInMilk ?? '-')}
        </Text>
      </View>
    );
  };

  const EmptyComponent = () => (
    <EmptyListComponent
      title={i18n.t('noRecordFound')}
      description={i18n.t('noRecordFoundDescription')}
      image={<NO_RESULT_FOUND_ICON />}
    />
  );

  return (
    <View style={styles.flexOne}>
      <FlatList
        keyExtractor={item => item?.id?.toString()}
        data={penData || []}
        style={styles.marginBottom}
        ListHeaderComponent={summaryHeader}
        ListEmptyComponent={EmptyComponent}
        renderItem={({ item }) => renderItem(item)}
      />
    </View>
  );
};

export default SummaryResults;
