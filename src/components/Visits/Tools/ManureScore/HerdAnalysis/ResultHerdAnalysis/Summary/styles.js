import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../../constants/theme/variables/platform';
import colors from '../../../../../../../constants/theme/variables/customColor';

import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';

export default {
  summaryHeader: {
    flexDirection: 'row',
    // height: normalize(30),
    marginHorizontal: normalize(20),
    marginTop: normalize(20),
    paddingVertical: normalize(8),
    paddingHorizontal: normalize(8),
    borderRadius: normalize(5),
    backgroundColor: colors.primaryMain,
  },
  pen: {
    flex: 2,
    color: colors.white,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  manureScore: {
    flex: 1.5,
    textAlign: 'center',
    color: colors.white,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueRegular,
  },

  dim: {
    flex: 1,
    textAlign: 'center',
    color: colors.white,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueRegular,
  },

  container: {
    flexDirection: 'row',
    marginHorizontal: normalize(20),
    marginTop: normalize(8),
    // height: normalize(30),
    padding: normalize(8),
    backgroundColor: colors.grey10,
  },
  penName: {
    flex: 2,
    fontSize: normalize(12),
    lineHeight: normalize(14),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  avg: {
    flex: 1.5,
    textAlign: 'center',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  milk: {
    flex: 1,
    textAlign: 'center',
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  flexOne: {
    flex: 1,
  },
  marginBottom: { marginBottom: 60 },
};
