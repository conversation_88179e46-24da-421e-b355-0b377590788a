import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../constants/theme/variables/platform';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  legendRow: {
    flexDirection: 'row',
    marginHorizontal: normalize(20),
    marginTop: normalize(16),
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: normalize(16),
  },
  milkLegend: {
    width: normalize(10),
    height: normalize(10),
    backgroundColor: colors.purple,
    borderRadius: normalize(42),
  },
  bcsAvgLegend: {
    width: normalize(10),
    height: normalize(10),
    backgroundColor: colors.secondary2,
    borderRadius: normalize(42),
  },
  minLegend: {
    width: normalize(10),
    height: normalize(10),
    borderWidth: normalize(1),
    borderStyle: 'dashed',
    borderColor: colors.minBCSGoalColor,
    borderRadius: normalize(52),
  },
  maxLegend: {
    width: normalize(10),
    height: normalize(10),
    borderWidth: normalize(1),
    borderStyle: 'dashed',
    borderColor: colors.error4,
    borderRadius: normalize(52),
  },
  legendText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(10),
    lineHeight: normalize(12),
    color: colors.grey1,
    marginLeft: normalize(6),
  },
  dataLineColor: colors.secondary2,
  minGoalLineColor: colors.minBCSGoalColor,
  maxGoalLineColor: colors.error4,
  rightAxisTicks: {
    fill: colors.purple,
  },
  customXAxisLabelStyles: {
    padding: normalize(37),
  },
  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight) - 50
          : Platform.deviceHeight - 60,
    },
    android: {
      width: normalize(Platform.deviceHeight),
    },
  }),
  graphWidth: {
    width: Platform.deviceWidth,
  },
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 100)
          : normalize(Platform.deviceWidth - 110),
    },
    android: {
      height: normalize(Platform.deviceWidth - 120),
    },
  }),
  graphHeight: {
    height: normalize(Platform.deviceHeight * 0.45),
  },
  tabsPosition: {
  position: 'absolute',
  bottom: 0,  
  left: 0,
  right: 0,
  }
};
