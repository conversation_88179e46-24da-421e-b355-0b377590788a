// modules
import React, { useState } from 'react';
import { View, useWindowDimensions, ScrollView } from 'react-native';
import { useSelector } from 'react-redux';
import Animated, { SlideInRight, SlideOutRight } from 'react-native-reanimated';

// components
import ManureHerdGraph from '../ResultHerdAnalysis/Graph';
import SummaryResults from '../ResultHerdAnalysis/Summary';
import ToolBottomTabs from '../../../common/ToolBottomTabs';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import {
  RESULTS_TABS_TYPES,
  RESULTS_TABS,
} from '../../../../../../constants/AppConstants';

const HerdAnalysisResults = ({
  goalsData,
  penData,
  onDownloadPress,
  onShareClick,
  onTabChange,
}) => {
  const [selectedTab, setSelectedTab] = useState(RESULTS_TABS[0]);

  return (
    <View style={styles.container}>
      {
        {
          [RESULTS_TABS_TYPES.GRAPH]: (
            <ScrollView showsVerticalScrollIndicator={false}>
              <ManureHerdGraph
                penData={penData}
                goalsData={goalsData}
                onShareClick={onShareClick}
                onDownloadPress={onDownloadPress}
              />
            </ScrollView>
          ),
          [RESULTS_TABS_TYPES.SUMMARY]: <SummaryResults penData={penData} />,
        }[selectedTab?.key]
      }

      <View style={styles.tabsPosition}>
        <Animated.View
          style={styles.container}
          entering={SlideInRight.duration(250)}
          exiting={SlideOutRight.duration(250)}>
          <ToolBottomTabs
            tabs={RESULTS_TABS}
            selectedTab={selectedTab}
            onTabChange={tab => {
              setSelectedTab(tab);
              onTabChange(tab);
            }}
          />
        </Animated.View>
      </View>
    </View>
  );
};

export default HerdAnalysisResults;
