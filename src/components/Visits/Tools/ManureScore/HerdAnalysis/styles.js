import { Platform } from 'react-native';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  flexOne: { flex: 1 },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  flatlist: {
    width: '100%',
    marginTop: normalize(14),
  },
  listItemRow: {
    flexDirection: 'column',
    paddingVertical: normalize(12),
    marginHorizontal: normalize(20),
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(24),
    color: colors.grey1,
  },
  penNameText: {
    fontWeight: '700',
  },
  fieldsRow: {
    flexDirection: 'row',
    marginTop: normalize(12),
  },
  marginRight: {
    marginRight: normalize(19),
  },
  customInputStyle: {
    width: '100%',
  },
  center: { 
    justifyContent: 'center',
    marginTop: normalize(25)
  },
  dim: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    color: colors.alphabetIndex
  },
  keyboardVerticalOffset:
  Platform.deviceHeight < 700 ? normalize(260) : normalize(225),

  separator: {
    height: 8,
    width: "100%",
    backgroundColor: colors.grey7,
  },
  alert: {
    backgroundColor: colors.white
  }
};
