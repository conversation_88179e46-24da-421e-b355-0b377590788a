// modules
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  FlatList,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  Text,
} from 'react-native';

//redux
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// reusable components
import HerdAnalysisResults from './ResultHerdAnalysis';
import { showToast } from '../../../../common/CustomToast';
import NumberFormInput from '../../../../common/NumberFormInput';
import ToolAlert from '../../common/ToolAlert';
import EmptyListComponent from '../../../../common/EmptyListComponent';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  BCS_HERD_ANALYSIS_FIELDS,
  CONTENT_TYPE,
  KEYBOARD_TYPE,
  TOAST_TYPE,
} from '../../../../../constants/FormConstants';
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
  MAX_VALUE,
  MIN_VALUE,
  NEXT_FIELD_TEXT,
  TOOL_ANALYSIS_TYPES,
  TOOL_TYPES,
} from '../../../../../constants/AppConstants';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../../../../store/actions/tool';
import {
  resetUpdatePenRequest,
  updatePenRequest,
} from '../../../../../store/actions/pen';
import { hideManureToolToastRequest } from '../../../../../store/actions/userPreferences';

// helpers
import {
  onUpdatedManureScoreObj,
  saveManureHerdData,
  setManureScorePenData,
  shouldEnableResultsButton,
} from '../../../../../helpers/manureScoreHelper';
import {
  mapGraphDataForHerdAnalysisExport,
  parsePensData,
} from '../../../../../helpers/manureScoreHelper';

//services
import { isOnline } from '../../../../../services/netInfoService';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import {
  saveManureScoreHerdAnalysisRequest,
  updateManureScoreHerdAnalysisRequest,
} from '../../../../../store/actions/manureScore';

//lodash
import _ from 'lodash';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';

const HerdAnalysis = props => {
  const {
    penList,
    screenDisabled,
    currentStep,
    totalSteps,
    refreshPensData,
    goalsData,
    onTabChange,
    isDirty,
    setIsDirty,
    enableResults,
    setEnableResults,
    penData,
    setPenData,
    isDirtyHerd,
    setIsDirtyHerd,
  } = props;

  //local states
  // const [penData, setPenData] = useState([]); //moved to root screen
  const [initialPenData, setInitialPenData] = useState([]);
  const [refresh, setRefresh] = useState(false);
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const dispatch = useDispatch();

  const isFocus = useIsFocused();

  //redux states
  const visitState = useSelector(state => state.visit);
  const penState = useSelector(state => state.pen);
  const userPreferencesState = useSelector(state => state.userPreferences);

  const {
    id,
    rumenHealthManureScore = [],
    isEditable = false,
  } = visitState?.visit || {};

  const populatePenData = pens => {
    const { rumenHealthManureScore = [], isEditable = false } =
      visitState?.visit || {};
    const rumenScorePens = setManureScorePenData(rumenHealthManureScore);
    const data = parsePensData(pens, rumenScorePens, isEditable);
    setPenData(data);
    setInitialPenData(data);
  };

  useEffect(() => {
    populatePenData(penList);

    if (isEditable && !!penList?.length && (isDirtyHerd || isDirty)) {
      const pens = saveManureHerdData(penList, rumenHealthManureScore);
      let obj = {
        isEditable,
        pens: [...pens],
        localVisitId: id,
      };
      dispatch(saveManureScoreHerdAnalysisRequest(obj));
    }
  }, [penList, visitState.visit, refresh]);

  useEffect(() => {
    if (penState.updateSuccess) {
      dispatch(resetUpdatePenRequest());
      refreshPensData();
    }
  }, [penState.updateSuccess]);

  useEffect(() => {
    if (penState.updateError) {
      showToast(
        TOAST_TYPE.ERROR,
        penState.updateError || i18n.t('somethingWentWrongError'),
      );
    }
  }, [penState.updateError]);

  //disables results button if no pens exist
  useEffect(() => {
    if (!enableResults) {
      setEnableResults(
        shouldEnableResultsButton(TOOL_ANALYSIS_TYPES.HERD_ANALYSIS, penData),
      );
    }
  }, [penData]);

  const onDIMChange = (val, index) => {
    let tempPenData = [...penData];
    tempPenData[index] = { ...penData[index], daysInMilk: val };
    setPenData(tempPenData);
    setIsDirtyHerd(true);
    setIsDirty(true);
  };

  const onCloseToast = () => {
    dispatch(hideManureToolToastRequest());
  };

  // update pen fields on submit
  const updatePen = pen => {
    const payload = {
      ...pen,
      daysInMilk:
        pen?.daysInMilk == '' || pen?.daysInMilk == null
          ? null
          : parseInt(pen?.daysInMilk),
      milk: parseFloat(pen?.milk) || 0,
      localId: pen.id,
      updated: true,
    };
    delete payload?.avgManureScore;
    dispatch(updatePenRequest(payload));
    isEditable &&
      dispatch(
        updateManureScoreHerdAnalysisRequest({
          ...payload,
          isEditable,
          localVisitId: id,
        }),
      );
  };

  // set dim/milk data and open alert
  const onBlurInput = (item, inputKey) => {
    const penFilteredList = initialPenData.filter(
      initialPen => initialPen.id === item.id,
    );
    if (penFilteredList && penFilteredList.length > 0) {
      const initialPenObj = penFilteredList[0];
      if (inputKey === BCS_HERD_ANALYSIS_FIELDS.DIM) {
        if (item.daysInMilk === null && initialPenObj.daysInMilk === null) {
          return;
        }
        if (
          item.daysInMilk != null &&
          initialPenObj.daysInMilk != null &&
          item.daysInMilk.toString() === initialPenObj.daysInMilk.toString()
        ) {
          return;
        }
      } else if (inputKey === BCS_HERD_ANALYSIS_FIELDS.MILK) {
        if (item.milk == initialPenObj.milk) {
          return;
        }
      }
    }

    updatePen(item);
  };

  const getToolToast = () => {
    const {
      userPreferences: { defaultValues },
    } = userPreferencesState || {};
    const { [TOOL_TYPES.RUMEN_HEALTH_MANURE_SCORE]: rumenToast } =
      defaultValues || false;
    return !screenDisabled ? rumenToast : false;
  };

  const DIMRef = useRef([]);

  const renderItem = ({ item, index }) => {
    return (
      <View style={styles.listItemRow}>
        <Text style={styles.titleText}>
          {`${i18n.t('pen')}: `}
          <Text style={styles.penNameText}>{item?.name}</Text>
          {` (${i18n.t('manureScore')}: ${convertInputNumbersToRegionalBasis(
            item?.avgManureScore,
            2,
          )})`}
        </Text>
        <View style={styles.fieldsRow}>
          <View style={[styles.flexOne, styles.marginRight]}>
            <NumberFormInput
              disabled={screenDisabled}
              keyboardType={
                Platform.OS === 'ios'
                  ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
                  : KEYBOARD_TYPE.NUMBER_PAD
              }
              unit={''}
              minValue={MIN_VALUE}
              maxValue={MAX_VALUE}
              isNegative={true}
              isInteger={true}
              placeholder={i18n.t('numberPlaceholder')}
              value={item?.daysInMilk?.toString() || ''}
              customInputContainerStyle={styles.customInputStyle}
              onChange={val => onDIMChange(val, index)}
              skipBlurValidation={true}
              onBlur={() => onBlurInput(item, BCS_HERD_ANALYSIS_FIELDS.DIM)}
              reference={e => {
                DIMRef.current[index] = e;
              }}
              onSubmitEditing={() => {
                if (index === penData?.length - 1) {
                  Keyboard?.dismiss();
                  return;
                }
                listRef?.scrollToIndex({ animated: true, index: index });
                DIMRef?.current[index + 1]?.focus();
              }}
              isEditable={!screenDisabled}
              blurOnSubmit={false}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={
                index === penData?.length - 1
                  ? NEXT_FIELD_TEXT.DONE
                  : NEXT_FIELD_TEXT.NEXT
              }
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: DIMRef?.current[index + 1],
                  dismiss: index === penData?.length - 1 ? true : false,
                });
              }}
            />
          </View>
          <View style={[styles.flexOne, styles.center]}>
            <Text style={styles.dim}>{i18n.t('DIM')}</Text>
          </View>
        </View>
      </View>
    );
  };

  const downloadHerdAnalysisData = async (graphData, type, lactationStages) => {
    if (await isOnline()) {
      const model = mapGraphDataForHerdAnalysisExport(
        visitState.visit,
        graphData,
        lactationStages,
      );

      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.RUMEN_HEALTH_MS_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType:
              EXPORT_REPORT_TYPES.RUMEN_HEALTH_MS_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareEmailPress = async (
    graphData,
    type,
    exportMethod,
    lactationStages,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForHerdAnalysisExport(
        visitState.visit,
        graphData,
        lactationStages,
        true,
      );

      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.RUMEN_HEALTH_MS_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.RUMEN_HEALTH_MS_HERD_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const renderHerdAnalysisResults = () => {
    return (
      <HerdAnalysisResults
        penData={penData || []}
        goalsData={goalsData}
        onTabChange={onTabChange}
        onShareClick={onShareEmailPress}
        onDownloadPress={downloadHerdAnalysisData}
      />
    );
  };

  // add separator
  const ItemSeparator = () => <View style={styles.separator} />;
  let listRef = useRef();

  const emptyComponent = () => {
    if (!isEditable) {
      return (
        <EmptyListComponent
          title={i18n.t('noResultShow')}
          description={i18n.t('noPensToShow')}
        />
      );
    }
    return (
      <EmptyListComponent
        title={i18n.t('noPensAdded')}
        description={i18n.t('addPensForHerdData')}
      />
    );
  };

  return (
    <>
      {currentStep === totalSteps ? (
        renderHerdAnalysisResults()
      ) : (
        <>
          <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : null}>
            <CustomInputAccessoryView doneAction={action} type={type} />
            <FlatList
              data={penData || []}
              keyExtractor={({ item }) => item?.id}
              showsVerticalScrollIndicator={false}
              renderItem={renderItem}
              ListEmptyComponent={emptyComponent}
              ItemSeparatorComponent={ItemSeparator}
              removeClippedSubviews={false}
              ref={ref => (listRef = ref)}
            />
          </KeyboardAvoidingView>
          {!!getToolToast() && (
            <View style={styles.alert}>
              <ToolAlert onCloseToast={onCloseToast} />
            </View>
          )}
        </>
      )}
    </>
  );
};
export default HerdAnalysis;
