// modules
import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
//victory-graph
import { VictoryAxis } from 'victory-native';

//redux
import { useDispatch, useSelector } from 'react-redux';

// components
import CustomLineGraph from '../../../../../../common/LineGraph';
import ToolGraph from '../../../../common/ToolGraph';

// localization
import i18n from '../../../../../../../localization/i18n';

// styles
import styles from '../styles';

// constants
import { VISIT_TABLE_FIELDS } from '../../../../../../../constants/AppConstants';

// helpers
import { sortRecentVisitsForGraph } from '../../../../../../../helpers/toolHelper';
import { stringIsEmpty } from '../../../../../../../helpers/alphaNumericHelper';

import {
  getManureScoreAvg,
  getManureScoreStdDeviation,
  getTotalCowsCountManure,
  setPenAnalysisGraphData,
} from '../../../../../../../helpers/manureScoreHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../../../store/actions/tool';
import { getManureScoreDataOfPenFromRecentVisits } from '../../../../../../../helpers/manureScoreHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';

const ManurePenAnalysisGraph = ({
  selectedPen,
  penData,
  manureScore,
  selectedVisits,
  onDownloadPress,
  onShareClick,
  layout,
}) => {
  const dispatch = useDispatch();

  //redux states
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);

  //local states
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);
  const [recentVisits, setRecentVisits] = useState([]);
  const [penGraphData, setPenGraphData] = useState([]);

  useEffect(() => {
    const visit = visitState.visit || {};
    const siteId = !stringIsEmpty(visit.siteId)
      ? visit.siteId
      : visit.localSiteId;
    const accountId = !stringIsEmpty(visit.customerId)
      ? visit.customerId
      : visit.localCustomerId;

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visit.id,
        tool: VISIT_TABLE_FIELDS.RUMEN_HEALTH_MANURE_SCORE,
      }),
    );
  }, []);

  useEffect(() => {
    if (!toolState.recentVisitsLoading) {
      let data = getManureScoreDataOfPenFromRecentVisits(
        toolState.recentVisits,
        selectedPen,
      );
      setRecentVisits(data);
    }
  }, [toolState.recentVisitsLoading]);

  useEffect(() => {
    let selectedRecentVisits = recentVisits.slice(1);
    selectedRecentVisits = selectedRecentVisits.filter(visit =>
      selectedVisits?.includes(visit.visitId),
    );

    selectedRecentVisits.push(manureScore);
    selectedRecentVisits = sortRecentVisitsForGraph(selectedRecentVisits);
    setPenGraphData(setPenAnalysisGraphData(selectedRecentVisits));
  }, [penData, recentVisits, selectedVisits]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const getCurrentValue = (pen = []) => {
    const data = pen[0]?.data;
    const { length = 0 } = data || [];
    return data?.[length - 1]?.x || null;
  };

  const customGraphTitleComponent = (
    <View style={styles.infoColumn}>
      {/* <View style={styles.statsRow}> */}
      <Text style={styles.statsTitle}>
        {`${i18n.t('avgManureScore')}: `}
        <Text style={styles.statsValue}>
          {convertInputNumbersToRegionalBasis(
            getManureScoreAvg(penData)?.toFixed(2),
            2,
            true,
          )}
        </Text>
      </Text>
      <Text style={[styles.statsTitle, styles.leftMargin]}>
        {`${i18n.t('std')}: `}
        <Text style={styles.statsValue}>
          {convertInputNumbersToRegionalBasis(
            getManureScoreStdDeviation(penData)?.toFixed(2),
            2,
            true,
          )}
        </Text>
      </Text>
      {/* </View> */}
    </View>
  );

  const graphComponent = (
    <View>
      <View style={styles.textContainer}>
        <Text style={styles.penAnalysisTitle}>
          {i18n.t('pen') || ''}:{' '}
          <Text style={styles.penAnalysisValue}>{selectedPen?.name || ''}</Text>
        </Text>
        <Text style={styles.penAnalysisTitle}>
          {i18n.t('animalsObserved') || ''}:{' '}
          <Text style={styles.penAnalysisValue}>
            {getTotalCowsCountManure(manureScore)}
          </Text>
        </Text>
      </View>

      <CustomLineGraph
        verticalAxisDomain
        showVerticalYAxis={true}
        verticalAxisLabel={`${i18n.t('category')}`}
        showVictory={true}
        animate={false}
        data={penGraphData}
        width={
          landscapeModalVisible
            ? styles.graphWidthLandscape.width
            : styles.graphWidth.width
        }
        height={
          landscapeModalVisible
            ? styles.graphHeightLandscape.height
            : styles.graphHeight.height
        }
        showLabelsValue={true}
        showXAxis={
          <VictoryAxis
            dependentAxis
            axisValue={getCurrentValue(penGraphData)}
            tickFormat={t => ''}
            domainPadding={!landscapeModalVisible ? styles.domainPadding : null}
            style={{
              axis: styles.axisStyles,
            }}
          />
        }
      />
    </View>
  );

  return (
    <ToolGraph
      showExpandIcon
      handleExpandIconPress={onExpandIconPress}
      showDownloadIcon={!landscapeModalVisible}
      onDownloadPress={option =>
        onDownloadPress(penGraphData, option, selectedPen, penData)
      }
      onSharePress={(option, exportMethod) =>
        onShareClick(penGraphData, option, selectedPen, penData, exportMethod)
      }
      showShareIcon={!landscapeModalVisible}
      landscapeModalVisible={landscapeModalVisible}
      onExpandIconPress={onExpandIconPress}
      customGraphTitleComponent={customGraphTitleComponent}
      graphComponent={layout && graphComponent}
    />
  );
};

export default ManurePenAnalysisGraph;
