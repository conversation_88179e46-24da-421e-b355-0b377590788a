// modules
import React, { useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

import { isOnline } from '../../../../../../services/netInfoService';

//animation
import Animated, { SlideInRight, SlideOutRight } from 'react-native-reanimated';

// components
import SummaryResults from './Summary';
import ManurePenAnalysisGraph from './Graph';
import ToolBottomTabs from '../../../common/ToolBottomTabs';
import { showToast } from '../../../../../common/CustomToast';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import {
  RESULTS_TABS_TYPES,
  RESULTS_TABS,
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
} from '../../../../../../constants/AppConstants';
import { TOAST_TYPE } from '../../../../../../constants/FormConstants';

//helpers
import {
  getManureScoreAvg,
  getManureScoreStdDeviation,
  mapGraphDataForPenAnalysisExport,
} from '../../../../../../helpers/manureScoreHelper';

//actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../../../../../store/actions/tool';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const PenAnalysisResults = ({
  selectedPen,
  penData,
  manureScore,
  selectedVisits,
  onTabChange,
  isEditable,
  pensAnalysisData,
}) => {
  //api calling
  const dispatch = useDispatch();

  //local states
  const [layout, setLayout] = useState(null);
  const [selectedTab, setSelectedTab] = useState(RESULTS_TABS[0]);

  //redux states
  const visitState = useSelector(state => state.visit);

  const downloadPenAnalysisData = async (
    graphData,
    type,
    selectedPen,
    penData,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForPenAnalysisExport(
        visitState.visit,
        graphData,
        selectedPen?.name,
        penData,
      );
      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.RUMEN_HEALTH_MS_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType: EXPORT_REPORT_TYPES.RUMEN_HEALTH_MS_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareEmailPress = async (
    graphData,
    type,
    selectedPen,
    penData,
    exportMethod,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForPenAnalysisExport(
        visitState.visit,
        graphData,
        selectedPen?.name,
        penData,
      );

      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.RUMEN_HEALTH_MS_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.RUMEN_HEALTH_MS_PEN_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const SummaryAverageData = (
    <View style={styles.summaryParent}>
      <View style={styles.summaryRow}>
        <Text style={styles.summaryAverage}>
          {`${i18n.t('avgManureScore')}: `}
          <Text style={styles.statsValue}>
            {convertInputNumbersToRegionalBasis(getManureScoreAvg(penData)?.toFixed(2), 2)}
          </Text>
        </Text>

        <Text style={styles.penAnalysisTitle}>
          {i18n.t('pen') || ''}:{' '}
          <Text style={styles.penAnalysisValue}>{selectedPen?.name || ''}</Text>
        </Text>
      </View>

      <Text style={[styles.statsTitle]}>
        {`${i18n.t('std')}: `}
        <Text style={styles.statsValue}>
          {convertInputNumbersToRegionalBasis(getManureScoreStdDeviation(penData)?.toFixed(2), 2)}
        </Text>
      </Text>
    </View>
  );

  return (
    <>
      {
        {
          [RESULTS_TABS_TYPES.GRAPH]: (
            <ScrollView
              style={styles.container}
              onLayout={({ nativeEvent }) => setLayout(nativeEvent.layout)}>
              <ManurePenAnalysisGraph
                selectedPen={selectedPen}
                penData={penData}
                layout={layout}
                setLayout={setLayout}
                manureScore={manureScore}
                selectedVisits={selectedVisits}
                onShareClick={onShareEmailPress}
                onDownloadPress={downloadPenAnalysisData}
              />
            </ScrollView>
          ),
          [RESULTS_TABS_TYPES.SUMMARY]: (
            <SummaryResults
              penData={penData || {}}
              customGraphTitleComponent={SummaryAverageData}
              isEditable={isEditable}
              pensAnalysisData={pensAnalysisData}
              selectedPen={selectedPen}
            />
          ),
        }[selectedTab?.key]
      }

      <View style={styles.tabsPosition}>
        <Animated.View
          style={styles.container}
          entering={SlideInRight.duration(250)}
          exiting={SlideOutRight.duration(250)}>
          <ToolBottomTabs
            tabs={RESULTS_TABS}
            selectedTab={selectedTab}
            onTabChange={tab => {
              setSelectedTab(tab);
              onTabChange(tab);
            }}
          />
        </Animated.View>
      </View>
    </>
  );
};

export default PenAnalysisResults;
