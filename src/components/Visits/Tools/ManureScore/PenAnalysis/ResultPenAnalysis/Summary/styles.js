import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../../constants/theme/variables/platform';
import colors from '../../../../../../../constants/theme/variables/customColor';

import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';

export default {
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // height: normalize(40),
    marginHorizontal: normalize(20),
    marginTop: normalize(30),
    paddingVertical: normalize(8),
    paddingHorizontal: normalize(8),
    borderRadius: normalize(5),
    backgroundColor: colors.primaryMain,
    alignItems: 'center',
  },
  pen: {
    flex: 1,
    color: colors.white,
    textAlign: 'center',
  },
  manureScore: {
    flex: 1,
    textAlign: 'center',
    color: colors.white,
  },
  dim: {
    flex: 1,
    textAlign: 'center',
    color: colors.white,
  },

  container: {
    flexDirection: 'row',
    marginHorizontal: normalize(20),
    marginTop: normalize(5),
    // height: normalize(30),
    padding: normalize(8),
    backgroundColor: colors.grey10,
  },
  penName: {
    flex: 1,
    textAlign: 'center',
    fontSize: normalize(13),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  avg: {
    flex: 1,
    textAlign: 'center',
    fontSize: normalize(13),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  milk: {
    flex: 1,
    textAlign: 'center',
    fontSize: normalize(13),
    fontFamily: customFont.HelveticaNeueRegular,
  },
};
