// modules
import React from 'react';
import { View, Text, FlatList } from 'react-native';

// localization
import i18n from '../../../../../../../localization/i18n';

//component
import EmptyListComponent from '../../../../../../../components/common/EmptyListComponent';

//constants
import { NO_RESULT_FOUND_ICON } from '../../../../../../../constants/AssetSVGConstants';

// styles
import styles from './styles';

//helpers
import { setCategory } from '../../../../../../../helpers/manureScoreHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';

const SummaryResults = ({
  penData,
  customGraphTitleComponent,
  isEditable,
  selectedPen,
  pensAnalysisData,
}) => {
  const { items = [] } = penData;

  //parse data
  const values = setCategory(items, isEditable, selectedPen, pensAnalysisData);

  // header to on list
  const summaryHeader = () => (
    <View style={styles.summaryHeader}>
      <Text style={styles.pen}>{i18n.t('category')}</Text>
      <Text style={styles.manureScore}>{i18n.t('penPercent')}</Text>
      <Text style={styles.dim} numberOfLines={2}>
        {i18n.t('animalsObs')}
      </Text>
    </View>
  );

  const renderItem = (item, index) => {
    return (
      <View style={styles.container} key={index}>
        <Text style={styles?.penName}>{item?.scoreCategory}</Text>
        <Text style={styles?.avg}>{convertInputNumbersToRegionalBasis(item?.percentOfPen, 2)}</Text>
        <Text style={styles?.milk}>{convertInputNumbersToRegionalBasis(item?.animalNumbers)}</Text>
      </View>
    );
  };

  const EmptyComponent = () => (
    <EmptyListComponent
      title={i18n.t('noRecordFound')}
      description={i18n.t('noRecordFoundDescription')}
      image={<NO_RESULT_FOUND_ICON />}
    />
  );

  return (
    <>
      {customGraphTitleComponent}
      <FlatList
        keyExtractor={item => item?.scoreCategory?.toString()}
        data={values || []}
        ListHeaderComponent={summaryHeader}
        ListEmptyComponent={EmptyComponent}
        renderItem={({ item }) => renderItem(item)}
      />
    </>
  );
};

export default SummaryResults;
