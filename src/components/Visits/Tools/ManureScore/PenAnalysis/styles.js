import colors from '../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default {
  flexOne: {
    flex: 1,
  },
  container: {
    width: '100%',
    backgroundColor: colors.white,
  },
  keyboardContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  dropdownTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: normalize(20),
    paddingHorizontal: normalize(15),
  },
  dropdownText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: 0.15,
    marginRight: normalize(8),
  },
  listView: {
    width: '100%',
    paddingVertical: normalize(15),
    backgroundColor: colors.grey7,
    borderTopColor: colors.lightWhite,
    borderTopWidth: normalize(1),
    borderBottomColor: colors.lightWhite,
    borderBottomWidth: normalize(1),
    alignItems: 'center',
  },
  catName: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  listChildView: {
    flexDirection: 'row',
    padding: normalize(10),
    paddingVertical: normalize(10),
    backgroundColor: colors.white,
  },
  animalsInPen: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    padding: normalize(5),
  },
  listHeaderText: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  listValue: {
    flexDirection: 'row',
    height: normalize(80),
    justifyCenter: 'center',
    alignItems: 'center',
  },
  listViewText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  animalObservedView: {
    flex: 3,
    padding: normalize(5),
    alignItems: 'center',
  },
  listHeaderView: {
    height: normalize(35),
  },
  penSetupContainer: {
    borderTopWidth: normalize(1),
    borderTopColor: colors.lightWhite,
    flex: 1,
    paddingHorizontal: normalize(10),
    paddingVertical: normalize(20),
  },
  penSetupView: {
    flex: 1,
    paddingHorizontal: normalize(15),
  },
  penSetupText: {
    color: colors.primaryMain,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  penViewChildContainer: {
    flex: 1,
    paddingVertical: normalize(5),
    marginTop: normalize(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: normalize(10),
  },
  milkLossViewHeader: {
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
  penChildText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  inputView: {
    width: normalize(98),
    height: normalize(48),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: colors.grey4,
  },
  input: {
    width: '100%',
    height: normalize(50),
    color: colors.black,
    textAlign: 'center',
  },
  penSetupChildContainer: {},
  milkLossView: {
    width: normalize(98),
    height: normalize(48),
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexDirection: 'row',
    paddingRight: normalize(2),
    justifyCenter: 'center',
  },
  milkLossText: {
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
    color: colors.grey1,
    width: '100%',
    textAlign: 'center',
  },
  kgsUnit: {
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueRegular,
    color: colors.grey1,
  },

  accordionView: {
    borderTopLeftRadius: normalize(8),
    borderTopRightRadius: normalize(8),
    borderRadius: normalize(8),
    borderColor: colors.accordionBorder,
    borderWidth: normalize(1),
    backgroundColor: colors.white,
    marginHorizontal: normalize(15),
    marginBottom: normalize(20),
  },
  accordionButton: {
    height: normalize(60),
    borderRadius: normalize(8),
    backgroundColor: colors.grey5,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: normalize(20),
  },
  accordionOpen: {
    borderRadius: normalize(0),
    borderTopLeftRadius: normalize(8),
    borderTopRightRadius: normalize(8),
  },
  accordionClose: {
    borderRadius: normalize(8),
  },
  accordionText: {
    paddingHorizontal: normalize(10),
    fontSize: normalize(14),
    fontFamily: customFont.HelveticaNeueMedium,
    color: colors.grey1,
  },
  tableContainer: {
    flex: 1,
    paddingHorizontal: normalize(20),
  },
  tableHeaderViewContainer: {
    flex: 1,
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingHorizontal: normalize(10),
    paddingVertical: normalize(15),
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightWhite,
  },
  tableHeaderViewChildContainer: {
    flex: 1,
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingHorizontal: normalize(10),
    paddingVertical: normalize(10),
    alignItems: 'center',
  },
  tableHeaderText: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueBold,
    width: '100%',
  },
  tableHeaderRightText: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueBold,
    width: '100%',
    textAlign: 'center',
  },
  tableLeftRowText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueRegular,
    width: '100%',
  },
  tableRightRowText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueRegular,
    width: '100%',
    alignItems: 'center',
    textAlign: 'center',
  },
  tabletableHeaderTextLeftView: {
    flex: 1,
    alignItems: 'flex-start',
    marginLeft: normalize(20),
  },
  tableRightView: {
    flex: 1,
    marginRight: normalize(0),
  },
  tableLeftView: {
    flex: 1,
    marginRight: normalize(20),
    marginLeft: normalize(20),
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  tableHeader: {
    flexDirection: 'row',
    height: normalize(40),
    backgroundColor: colors.grey12,
    paddingHorizontal: normalize(20),
    paddingRight: normalize(35),
    borderWidth: normalize(1),
    borderColor: colors.grey13,
    alignItems: 'center',
  },
  headerCategoryText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    color: colors.grey1,
  },
  listItemText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(16),
    lineHeight: normalize(19),
    letterSpacing: 0.15,
    color: colors.grey1,
  },
  listItemRow: {
    flexDirection: 'row',
    paddingVertical: normalize(5),
    paddingHorizontal: normalize(20),
    paddingRight: normalize(35),
    alignItems: 'center',
  },
  flexTwo: { flex: 2 },
};
