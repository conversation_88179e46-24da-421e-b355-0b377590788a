// modules
import { useIsFocused } from '@react-navigation/native';
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  ScrollView,
  KeyboardAvoidingView,
  Keyboard,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// SVG_ICONS
import { CHEVRON_DOWN_BLUE_ICON } from '../../../../../constants/AssetSVGConstants';

// FORM CONSTANT
import {
  BOTTOM_SHEET_TYPE,
  CONTENT_TYPE,
} from '../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../localization/i18n';

// Reusable Components
import CircularCounter from '../../../../common/CircularCounter';
import CustomBottomSheet from '../../../../common/CustomBottomSheet';
import PenAnalysisGraph from './ResultPenAnalysis';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';

// styles
import styles from './styles';
import { normalize } from '../../../../../constants/theme/variables/customFont';

// helpers
import { stringIsEmpty } from '../../../../../helpers/alphaNumericHelper';
import {
  getSelectedPen,
  allPenAnalysis,
  pensInPercent,
  onUpdatedManureScoreObj,
  penExistsInPublishedVisit,
  shouldEnableResultsButton,
  getTotalCowsCountManure,
} from '../../../../../helpers/manureScoreHelper';
import { dateHelper } from '../../../../../helpers/dateHelper';
import {
  pickPenInReducerFromPensList,
  saveSelectedPenInReducer,
} from '../../../../../helpers/visitHelper';

// actions
import {
  getPenByIdRequest,
  resetUpdatePenRequest,
} from '../../../../../store/actions/pen';
import {
  getSitePensRequest,
  setSelectedPenRequest,
} from '../../../../../store/actions/tool';
import { saveManureScorePenAnalysisRequest } from '../../../../../store/actions/manureScore';
import { logEvent } from '../../../../../helpers/logHelper';
import {
  NEXT_FIELD_TEXT,
  TOOL_ANALYSIS_TYPES,
  VISIT_TABLE_FIELDS,
} from '../../../../../constants/AppConstants';

//lodash
import _ from 'lodash';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';
import { clearForageAuditScorecardReducer } from '../../../../../store/actions/tools/forageAuditScorecard';

const PenAnalysis = props => {
  const {
    currentStep,
    totalToolSteps,
    selectedVisits,
    openToolSheet,
    onTabChange,
    healthCurrentActivePen,
    isDirty,
    setIsDirty,
    setEnableResults,
    activePenAnalysis,
    setActivePenAnalysis,
  } = props;
  const dispatch = useDispatch();
  const isFocus = useIsFocused();

  //ref
  const counterRef = useRef([]);

  //local states
  const [pensList, setPensList] = useState([]);
  const [selectedPen, setSelectedPen] = useState(null);
  const [showPenBottomSheet, setShowPenBottomSheet] = useState(false);
  const [initialStateUpdate, setInitialStateUpdate] = useState(false);
  const [totalCowsCount, setTotalCowsCount] = useState(0);

  // const [activePenAnalysis, setActivePenAnalysis] = useState({});
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  //redux states
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);
  const penState = useSelector(state => state.pen);
  const { isEditable = false } = visitState?.visit || {};

  const pensAnalysisData = visitState?.visit?.rumenHealthManureScore
    ? JSON.parse(visitState?.visit?.rumenHealthManureScore)
    : {};

  useEffect(() => {
    if (toolState?.pensList?.length > 0) {
      setPensList(toolState?.pensList);
      // very first time page load 1 pen already selected
      // setSelectedPen(toolState?.pensList[0]);
      if (healthCurrentActivePen) {
        pickPenInReducerFromPensList(
          toolState?.pensList,
          healthCurrentActivePen,
          setSelectedPen,
        );
      } else {
        setSelectedPen(toolState?.pensList[0]);
      }
    }
  }, [toolState.pensList]);

  useEffect(() => {
    const { siteId, localSiteId, rumenHealthManureScore } =
      visitState?.visit || {};
    const payload = {
      rumenHealthManureScore,
      siteId,
      localSiteId,
      toolType: VISIT_TABLE_FIELDS.RUMEN_HEALTH_MANURE_SCORE,
    };
    // Fetch site  data from database
    dispatch(getSitePensRequest(payload));
  }, []);

  useEffect(() => {
    if (
      !stringIsEmpty(penState?.pen) &&
      Object.keys(penState?.pen).length > 0 &&
      initialStateUpdate
    ) {
      saveToolData(true);
    }
  }, [openToolSheet, currentStep]);

  useEffect(() => {
    if (selectedPen?.id) {
      // Fetch pen data from database
      dispatch(getPenByIdRequest({ id: selectedPen?.id }));
    }
    //saves selected pen in reducer on pen change
    dispatch(setSelectedPenRequest(selectedPen));
  }, [selectedPen?.id]);

  useEffect(() => {
    debounce_fun(activePenAnalysis);
    setTotalCowsCount(getTotalCowsCountManure(activePenAnalysis));

    return () => saveToolData();
  }, [isFocus, activePenAnalysis]);

  //disables results button if pen's values sum to 0
  const debounce_fun = _.debounce(penAnalysisArray => {
    setEnableResults(
      shouldEnableResultsButton(
        TOOL_ANALYSIS_TYPES.PEN_ANALYSIS,
        penAnalysisArray,
      ),
    );
  }, 1000);

  useEffect(() => {
    initialStateFormPenAnalysis();
  }, [penState.pen, selectedPen]);

  useEffect(() => {
    if (penState.updateSuccess) {
      dispatch(resetUpdatePenRequest());
    }
  }, [penState.updateSuccess]);

  const initialStateFormPenAnalysis = () => {
    if (
      (!stringIsEmpty(penState?.pen) &&
        Object.keys(penState?.pen).length > 0) ||
      (!stringIsEmpty(selectedPen) && Object.keys(selectedPen?.id).length > 0)
    ) {
      const pen = penState?.pen || selectedPen;
      let isPen = allPenAnalysis(visitState?.visit);
      setActivePenAnalysis(getSelectedPen(isPen, pen));
      setInitialStateUpdate(true);
    }
  };

  const saveToolData = (isForce = false) => {
    if (isDirty) {
      const { id } = visitState?.visit;
      let obj = {
        visitData: visitState?.visit,
        data: activePenAnalysis,
        localVisitId: id,
      };
      if (isEditable && !!pensList?.length) {
        let onSaveManureScoreData = onUpdatedManureScoreObj(obj);
        dispatch(
          saveManureScorePenAnalysisRequest({
            manureScoreData: onSaveManureScoreData,
            localVisitId: id,
            updated_at: dateHelper.getUnixTimestamp(new Date()),
            mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
          }),
        );
      }
    }
  };

  // Animal observed click decrement btn when pen%  animals in per pen and animal milk loss calculated at run time
  const onDecrementClick = index => {
    setIsDirty(true);
    let categoryListState = [...activePenAnalysis?.manureScores?.items];
    categoryListState[index].animalNumbers -= 1;
    onCategoriesListStateUpdate(categoryListState, index);
    setTotalCowsCount(totalCowsCount - 1);
  };

  // Animal observed click increment btn when pen%  animals in per pen and animal milk loss calculated at run time
  const onIncrementClick = index => {
    setIsDirty(true);
    let categoryListState = [...activePenAnalysis?.manureScores?.items];
    categoryListState[index].animalNumbers += 1;
    onCategoriesListStateUpdate(categoryListState, index);
    setTotalCowsCount(totalCowsCount + 1);
  };

  const onAnimalCountChange = (index, value) => {
    setIsDirty(true);
    let categoryListState = [...activePenAnalysis?.manureScores?.items];
    let _categoryCount = categoryListState[index].animalNumbers; //used below
    categoryListState[index].animalNumbers = parseInt(value);
    onCategoriesListStateUpdate(categoryListState, index);
    let _totalCowsCount = totalCowsCount - _categoryCount;
    _totalCowsCount += parseInt(value);
    setTotalCowsCount(_totalCowsCount);
  };

  const onCategoriesListStateUpdate = (categoryListState, index) => {
    try {
      let temp = activePenAnalysis;
      temp.manureScores = { ...activePenAnalysis.manureScores };
      temp.manureScores.items = [...categoryListState];
      setActivePenAnalysis({ ...temp });
    } catch (error) {
      logEvent(
        'components -> ManureScore -> PenAnalysis -> onCategoriesListStateUpdate Exception',
        error,
      );
      console.log('onCategoriesListStateUpdate Exception', error);
    }
  };

  const openPenBottomSheet = () => {
    setShowPenBottomSheet(true);
  };

  const closePenBottomSheet = () => {
    setShowPenBottomSheet(false);
  };

  const onPenChange = item => {
    saveSelectedPenInReducer(dispatch, item);
    saveToolData(true);
    setIsDirty(false);
    setEnableResults(false);
    setSelectedPen(item);
    closePenBottomSheet();
    initialStateFormPenAnalysis();
  };

  const renderPenAnalysisResults = () => {
    const { id, visitDate } = visitState?.visit || {};
    return (
      <PenAnalysisGraph
        selectedPen={selectedPen || {}}
        penData={{
          ...activePenAnalysis?.manureScores,
          visitId: id,
          date: visitDate,
        }}
        onTabChange={onTabChange}
        manureScore={{ ...activePenAnalysis, visitId: id, date: visitDate }}
        selectedVisits={selectedVisits}
        isEditable={isEditable}
        pensAnalysisData={pensAnalysisData}
      />
    );
  };

  return (
    <>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior="padding"
        // keyboardVerticalOffset={30}
      >
        <CustomInputAccessoryView doneAction={action} type={type} />
        {currentStep === totalToolSteps ? (
          renderPenAnalysisResults()
        ) : (
          <ScrollView
            style={styles.flexOne}
            keyboardDismissMode="on-drag"
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}>
            <View style={styles.container}>
              <View>
                <TouchableOpacity
                  style={styles.dropdownTextContainer}
                  onPress={openPenBottomSheet}>
                  <Text style={styles.dropdownText}>
                    {selectedPen?.name || ''}
                  </Text>
                  <CHEVRON_DOWN_BLUE_ICON
                    width={normalize(12)}
                    height={normalize(8)}
                  />
                </TouchableOpacity>
              </View>
              <View style={styles.flexOne}>
                <View style={styles.tableHeader}>
                  <Text style={[styles.headerCategoryText, styles.flexOne]}>
                    {i18n.t('category')}
                  </Text>
                  <Text style={[styles.headerCategoryText, styles.flexOne]}>
                    {i18n.t('penPercent')}
                  </Text>
                  <Text
                    style={[
                      styles.headerCategoryText,
                      styles.flexTwo,
                      { textAlign: 'center' },
                    ]}>
                    {`${i18n.t('animalsObserved')}: ${totalCowsCount}`}
                  </Text>
                </View>
              </View>
              {Object.keys(activePenAnalysis).length > 0 &&
                activePenAnalysis?.manureScores?.items?.map((item, index) => {
                  return (
                    <View style={styles.listItemRow} key={index}>
                      <Text style={[styles.listItemText, styles.flexOne]}>
                        {item?.scoreCategory?.toFixed(1)}
                      </Text>

                      <Text style={[styles.listItemText, styles.flexOne]}>
                        {convertInputNumbersToRegionalBasis(
                          pensInPercent(
                            index,
                            activePenAnalysis?.manureScores?.items,
                          ),
                          2,
                        )}
                      </Text>

                      <View style={[styles.flexTwo, { width: 167 }]}>
                        <CircularCounter
                          disabled={!isEditable}
                          count={
                            penExistsInPublishedVisit(
                              isEditable,
                              pensAnalysisData,
                              selectedPen,
                            )
                              ? activePenAnalysis?.manureScores?.items[index]
                                  ?.animalNumbers || 0
                              : '-'
                          }
                          onDecrementClick={() => onDecrementClick(index)}
                          onIncrementClick={() => onIncrementClick(index)}
                          onChangeText={count =>
                            onAnimalCountChange(index, count)
                          }
                          reference={e => {
                            counterRef.current[index] = e;
                          }}
                          onSubmitEditing={() => {
                            if (
                              activePenAnalysis?.manureScores?.items?.length -
                                1 ===
                              index
                            ) {
                              Keyboard.dismiss();
                              return;
                            }
                            counterRef?.current[index + 1]?.focus();
                          }}
                          showInput={true}
                          inputAccessoryViewID="customInputAccessoryView"
                          onFocus={() => {
                            setType(CONTENT_TYPE.NUMBER);
                            setAction({
                              currentRef: counterRef?.current[index + 1],
                              dismiss:
                                activePenAnalysis?.manureScores?.items?.length -
                                  1 ===
                                index
                                  ? true
                                  : false,
                            });
                          }}
                          returnKeyType={
                            activePenAnalysis?.manureScores?.items?.length -
                              1 ===
                            index
                              ? NEXT_FIELD_TEXT.DONE
                              : NEXT_FIELD_TEXT.NEXT
                          }
                        />
                      </View>
                    </View>
                  );
                })}
            </View>
            {showPenBottomSheet && (
              <CustomBottomSheet
                type={BOTTOM_SHEET_TYPE.SIMPLE}
                selectLabel={i18n.t('selectPen')}
                searchPlaceHolder={i18n.t('searchPen')}
                data={pensList || []}
                onChange={onPenChange}
                onClose={closePenBottomSheet}
              />
            )}
          </ScrollView>
        )}
      </KeyboardAvoidingView>
    </>
  );
};
export default PenAnalysis;
