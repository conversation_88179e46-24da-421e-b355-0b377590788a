// modules
import React, { useRef, useState } from 'react';
import { View, Text, Keyboard, Platform } from 'react-native';
import { useSelector } from 'react-redux';

//components
import MilkPickup from './MilkPickup';
import MilkUreaMeasure from './MilkUreaMeasure';
import NumberFormInput from '../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput/index';

//constant
import {
  LEFT_TEXT_ALIGNMENT,
  MILK_SOLD_EVALUATION_DAYS_AS_FED_INTAKE_MAX_VALUE,
  DAYS_IN_MILK_MAX_VALUE,
  DAYS_IN_MILK_MIN_VALUE,
  MILK_SOLD_EVALUATION_DAYS_NEL_DAIRY_MAX_VALUE,
  MILK_SOLD_EVALUATION_DECIMAL_PLACES,
  MILK_SOLD_EVALUATION_DRY_MATTER_INTAKE_MAX_VALUE,
  MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE,
  ANIMALS_IN_TANK_MAX_VALUE,
  MILK_SOLD_EVALUATION_ONE_DECIMAL_PLACES,
  MILK_SOLD_EVALUATION_TOTAL_MILK_PRICE_MAX_VALUE,
  MILK_SOLD_EVALUATION_TWO_DECIMAL_PLACES,
  NEXT_FIELD_TEXT,
  RATION_COST_MAX_VALUE,
  LACTATING_ANIMALS_MIN_VALUE,
  LACTATING_ANIMALS_MAX_VALUE,
} from '../../../../../constants/AppConstants';
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
  MILK_SOLID_EVALUATION_FIELDS,
} from '../../../../../constants/FormConstants';
import {
  MILK_SOLD_EVALUATION_DAYS_IN_MILK_MAX_VALUE,
  MILK_SOLD_EVALUATION_DAYS_IN_MILK_MIN_VALUE,
} from '../../../../../constants/toolsConstants/MilkSoldEvaluationConstants';

// localization
import i18n from '../../../../../localization/i18n';

//styles
import styles from './styles';

// helpers
import { getCurrencyForTools } from '../../../../../helpers/appSettingsHelper';

const HerdInformation = ({
  milkProcessor,
  values,
  errors,
  touched,
  setFieldValue,
  handleChange,
  isEditable = false,
  updateUserPreferences,
  userData,
  weightUnit,
  addExtraScrollForDIMInput,
}) => {
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );
  const authState = useSelector(state => state.authentication.user);

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  let currentMilkPrice = useRef();
  let lactatingAnimal = useRef();
  let animalsInTank = useRef();
  let dryMatterIntake = useRef();
  let daysInMilk = useRef();
  let asFedIntake = useRef();
  let NELDairy = useRef();
  let rationCostPerAnimal = useRef();

  const updateMilkPickUpUserPreferences = (label, value) => {
    setFieldValue(label, value);
    let selectedPreferences = { ...userData };
    selectedPreferences.defaultMilkPickup = value;
    updateUserPreferences(selectedPreferences);
  };

  const updateMilkUreaUserPreferences = (label, value) => {
    setFieldValue(label, value);
    let selectedPreferences = { ...userData };
    selectedPreferences.defaultMilkUreaMeasure = value;
    updateUserPreferences(selectedPreferences);
  };

  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  return (
    <View style={styles.parent}>
      <View style={styles.addHerdInformationForm}>
        {/* Add pickup component */}
        <View style={styles.pickupContainer}>{milkProcessor}</View>

        <CustomInputAccessoryView doneAction={action} type={type} />

        <View style={styles.flex1}>
          <View style={styles.container}>
            <Text style={styles.formHeading}>{i18n.t('herdLevelInfo')}</Text>
            <View style={styles.requiredLabelRow}>
              <Text style={styles.starIcon}>{i18n.t('starSign')}</Text>
              <Text style={styles.requiredLabel}>
                {i18n.t('requiredFieldMsg')}
              </Text>
            </View>
          </View>

          <View style={styles.formInputView}>
            <NumberFormInput
              label={`${i18n.t(
                'currentMilkPrice',
              )} (${currencySymbol}/${weightUnit})`}
              required
              placeholder={i18n.t('numberPlaceholder')}
              value={values[MILK_SOLID_EVALUATION_FIELDS.CURRENT_MILK_PRICE]}
              error={
                touched[MILK_SOLID_EVALUATION_FIELDS.CURRENT_MILK_PRICE] &&
                errors[MILK_SOLID_EVALUATION_FIELDS.CURRENT_MILK_PRICE]
              }
              decimalPoints={MILK_SOLD_EVALUATION_DECIMAL_PLACES}
              isInteger={false}
              minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
              maxValue={MILK_SOLD_EVALUATION_TOTAL_MILK_PRICE_MAX_VALUE}
              onChange={handleChange(
                MILK_SOLID_EVALUATION_FIELDS.CURRENT_MILK_PRICE,
              )}
              textAlign={LEFT_TEXT_ALIGNMENT}
              customInputContainerStyle={styles.customInputStyle}
              reference={input => {
                currentMilkPrice = input;
              }}
              onSubmitEditing={() => {
                lactatingAnimal?.focus();
              }}
              blurOnSubmit={false}
              disabled={!isEditable}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: lactatingAnimal,
                });
              }}
              hasCommas={true}
            />
          </View>

          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('lactatingAnimals')}
              required
              placeholder={i18n.t('numberPlaceholder')}
              // value={getFormattedCommaNumber(
              //   values[MILK_SOLID_EVALUATION_FIELDS.LACTATING_ANIMALS],
              // )}
              value={values[MILK_SOLID_EVALUATION_FIELDS.LACTATING_ANIMALS]}
              error={
                touched[MILK_SOLID_EVALUATION_FIELDS.LACTATING_ANIMALS] &&
                errors[MILK_SOLID_EVALUATION_FIELDS.LACTATING_ANIMALS]
              }
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              minValue={LACTATING_ANIMALS_MIN_VALUE}
              maxValue={LACTATING_ANIMALS_MAX_VALUE}
              onChange={handleChange(
                MILK_SOLID_EVALUATION_FIELDS.LACTATING_ANIMALS,
              )}
              customInputContainerStyle={styles.customInputStyle}
              textAlign={LEFT_TEXT_ALIGNMENT}
              reference={input => {
                lactatingAnimal = input;
              }}
              onSubmitEditing={() => {
                animalsInTank?.focus();
              }}
              blurOnSubmit={false}
              disabled={!isEditable}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: animalsInTank,
                });
              }}
              hasCommas={true}
            />
          </View>

          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('animalsInTank')}
              required
              placeholder={i18n.t('numberPlaceholder')}
              // value={getFormattedCommaNumber(
              //   values[MILK_SOLID_EVALUATION_FIELDS.ANIMALS_IN_TANK],
              // )}
              value={values[MILK_SOLID_EVALUATION_FIELDS.ANIMALS_IN_TANK]}
              error={
                touched[MILK_SOLID_EVALUATION_FIELDS.ANIMALS_IN_TANK] &&
                errors[MILK_SOLID_EVALUATION_FIELDS.ANIMALS_IN_TANK]
              }
              isInteger={true}
              keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
              minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
              maxValue={ANIMALS_IN_TANK_MAX_VALUE}
              customInputContainerStyle={styles.customInputStyle}
              onChange={handleChange(
                MILK_SOLID_EVALUATION_FIELDS.ANIMALS_IN_TANK,
              )}
              textAlign={LEFT_TEXT_ALIGNMENT}
              reference={input => {
                animalsInTank = input;
              }}
              onSubmitEditing={() => {
                dryMatterIntake?.focus();
              }}
              blurOnSubmit={false}
              disabled={!isEditable}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: dryMatterIntake,
                });
              }}
              hasCommas={true}
            />
          </View>

          <View style={styles.formInputView}>
            <MilkPickup
              values={values}
              errors={errors}
              touched={touched}
              isEditable={isEditable}
              handleChange={(label, value) =>
                updateMilkPickUpUserPreferences(label, value)
              }
            />
          </View>

          <View style={styles.formInputView}>
            <NumberFormInput
              label={`${i18n.t('dryMatterIntake')} (${weightUnit})`}
              placeholder={i18n.t('numberPlaceholder')}
              required
              value={values[MILK_SOLID_EVALUATION_FIELDS.DRY_MATTER_INTAKE]}
              error={
                touched[MILK_SOLID_EVALUATION_FIELDS.DRY_MATTER_INTAKE] &&
                errors[MILK_SOLID_EVALUATION_FIELDS.DRY_MATTER_INTAKE]
              }
              decimalPoints={MILK_SOLD_EVALUATION_ONE_DECIMAL_PLACES}
              isInteger={false}
              minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
              maxValue={MILK_SOLD_EVALUATION_DRY_MATTER_INTAKE_MAX_VALUE}
              customInputContainerStyle={styles.customInputStyle}
              onChange={handleChange(
                MILK_SOLID_EVALUATION_FIELDS.DRY_MATTER_INTAKE,
              )}
              textAlign={LEFT_TEXT_ALIGNMENT}
              reference={input => {
                dryMatterIntake = input;
              }}
              onSubmitEditing={() => {
                daysInMilk?.focus();
              }}
              blurOnSubmit={false}
              disabled={!isEditable}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: daysInMilk,
                });
              }}
              onBlur={() => {
                addExtraScrollForDIMInput(daysInMilk);
              }}
            />
          </View>

          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('daysInMilk')}
              placeholder={i18n.t('numberPlaceholder')}
              required
              value={values[MILK_SOLID_EVALUATION_FIELDS.DAYS_IN_MILK]}
              error={
                touched[MILK_SOLID_EVALUATION_FIELDS.DAYS_IN_MILK] &&
                errors[MILK_SOLID_EVALUATION_FIELDS.DAYS_IN_MILK]
              }
              isInteger={true}
              keyboardType={
                Platform.OS === 'ios'
                  ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
                  : KEYBOARD_TYPE.NUMBER_PAD
              }
              minValue={DAYS_IN_MILK_MIN_VALUE}
              maxValue={DAYS_IN_MILK_MAX_VALUE}
              textAlign={LEFT_TEXT_ALIGNMENT}
              customInputContainerStyle={styles.customInputStyle}
              onChange={handleChange(MILK_SOLID_EVALUATION_FIELDS.DAYS_IN_MILK)}
              reference={input => {
                daysInMilk = input;
              }}
              isNegative={true}
              onSubmitEditing={() => {
                asFedIntake?.focus();
              }}
              blurOnSubmit={false}
              disabled={!isEditable}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: asFedIntake,
                });
              }}
            />
          </View>

          <View style={styles.formInputView}>
            <MilkUreaMeasure
              values={values}
              errors={errors}
              touched={touched}
              isEditable={isEditable}
              handleChange={(label, value) =>
                updateMilkUreaUserPreferences(label, value)
              }
            />
          </View>

          <View style={styles.formInputView}>
            <NumberFormInput
              label={`${i18n.t('asFedIntake')} (${weightUnit})`}
              placeholder={i18n.t('numberPlaceholder')}
              value={values[MILK_SOLID_EVALUATION_FIELDS.AS_FED_INTAKE]}
              error={
                touched[MILK_SOLID_EVALUATION_FIELDS.AS_FED_INTAKE] &&
                errors[MILK_SOLID_EVALUATION_FIELDS.AS_FED_INTAKE]
              }
              decimalPoints={MILK_SOLD_EVALUATION_ONE_DECIMAL_PLACES}
              isInteger={false}
              minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
              maxValue={MILK_SOLD_EVALUATION_DAYS_AS_FED_INTAKE_MAX_VALUE}
              textAlign={LEFT_TEXT_ALIGNMENT}
              customInputContainerStyle={styles.customInputStyle}
              onChange={handleChange(
                MILK_SOLID_EVALUATION_FIELDS.AS_FED_INTAKE,
              )}
              reference={input => {
                asFedIntake = input;
              }}
              onSubmitEditing={() => {
                NELDairy?.focus();
              }}
              blurOnSubmit={false}
              disabled={!isEditable}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: NELDairy,
                });
              }}
            />
          </View>

          <View style={styles.formInputView}>
            <NumberFormInput
              label={`${i18n.t('NELDairy(Kg)')} (${i18n.t(
                'Mcal',
              )}/${weightUnit})`}
              placeholder={i18n.t('numberPlaceholder')}
              value={values[MILK_SOLID_EVALUATION_FIELDS.NEL_DAIRY]}
              error={
                touched[MILK_SOLID_EVALUATION_FIELDS.NEL_DAIRY] &&
                errors[MILK_SOLID_EVALUATION_FIELDS.NEL_DAIRY]
              }
              decimalPoints={MILK_SOLD_EVALUATION_DECIMAL_PLACES}
              isInteger={false}
              minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
              maxValue={MILK_SOLD_EVALUATION_DAYS_NEL_DAIRY_MAX_VALUE}
              textAlign={LEFT_TEXT_ALIGNMENT}
              customInputContainerStyle={styles.customInputStyle}
              onChange={handleChange(MILK_SOLID_EVALUATION_FIELDS.NEL_DAIRY)}
              reference={input => {
                NELDairy = input;
              }}
              onSubmitEditing={() => {
                rationCostPerAnimal?.focus();
              }}
              blurOnSubmit={false}
              disabled={!isEditable}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: rationCostPerAnimal,
                });
              }}
            />
          </View>

          <View style={styles.formInputView}>
            <NumberFormInput
              label={`${i18n.t(
                'rationCostPerAnimalWithCurrency($)',
              )} (${currencySymbol})`}
              placeholder={i18n.t('numberPlaceholder')}
              // value={getFormattedCommaNumber(
              //   values[MILK_SOLID_EVALUATION_FIELDS.RATION_COST],
              // )}
              value={values[MILK_SOLID_EVALUATION_FIELDS.RATION_COST]}
              error={
                touched[MILK_SOLID_EVALUATION_FIELDS.RATION_COST] &&
                errors[MILK_SOLID_EVALUATION_FIELDS.RATION_COST]
              }
              decimalPoints={MILK_SOLD_EVALUATION_TWO_DECIMAL_PLACES}
              isInteger={false}
              minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
              maxValue={RATION_COST_MAX_VALUE}
              textAlign={LEFT_TEXT_ALIGNMENT}
              customInputContainerStyle={styles.customInputStyle}
              onChange={handleChange(MILK_SOLID_EVALUATION_FIELDS.RATION_COST)}
              reference={input => {
                rationCostPerAnimal = input;
              }}
              onSubmitEditing={() => {
                Keyboard.dismiss();
              }}
              blurOnSubmit={false}
              disabled={!isEditable}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.DONE}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  dismiss: true,
                });
              }}
              hasCommas={true}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default HerdInformation;
