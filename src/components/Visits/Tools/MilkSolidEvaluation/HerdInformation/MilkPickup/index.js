import React from 'react';
import { useSelector } from 'react-redux';
import { useFormikContext } from 'formik';

//components
import HorizontalSingleSelect from '../../../../../common/HorizontalSingleSelect';

//constant
import { ENUM_CONSTANTS } from '../../../../../../constants/AppConstants';
import { MILK_SOLID_EVALUATION_FIELDS } from '../../../../../../constants/FormConstants';

// helpers
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';

// localization
import i18n from '../../../../../../localization/i18n';

//styles
import styles from '../styles';

const MilkPickup = ({
  values,
  errors,
  touched,
  handleChange,
  isEditable = false,
}) => {
  const { handleBlur } = useFormikContext();

  const enumsState = useSelector(state => state.enums.enum);

  // backend enum extra value "not-set" is remove
  let data = !stringIsEmpty(enumsState)
    ? enumsState[ENUM_CONSTANTS.MILK_PICKUP]
    : [];

  return (
    <HorizontalSingleSelect
      label={i18n.t('milkPickUp')}
      required
      isEditable={!isEditable}
      options={data?.slice(1) || []}
      value={values[MILK_SOLID_EVALUATION_FIELDS.MILK_PICK_UP]}
      error={
        touched[MILK_SOLID_EVALUATION_FIELDS.MILK_PICK_UP] &&
        errors[MILK_SOLID_EVALUATION_FIELDS.MILK_PICK_UP]
      }
      onChange={item => {
        handleChange(MILK_SOLID_EVALUATION_FIELDS.MILK_PICK_UP, item);
      }}
      customInputContainerStyle={styles.customInputStyle}
      handleBlur={handleBlur(MILK_SOLID_EVALUATION_FIELDS.MILK_PICK_UP)}
    />
  );
};

export default MilkPickup;
