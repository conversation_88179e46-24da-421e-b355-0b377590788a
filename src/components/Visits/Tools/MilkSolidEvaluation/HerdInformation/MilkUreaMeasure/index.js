import React from 'react';
import { useSelector } from 'react-redux';

//components
import HorizontalSingleSelect from '../../../../../common/HorizontalSingleSelect';

//constant
import { ENUM_CONSTANTS } from '../../../../../../constants/AppConstants';
import { MILK_SOLID_EVALUATION_FIELDS } from '../../../../../../constants/FormConstants';

// helpers
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';

// localization
import i18n from '../../../../../../localization/i18n';

//styles
import styles from '../styles';

const MilkUreaMeasure = ({
  values,
  errors,
  touched,
  handleChange,
  isEditable = false,
}) => {
  const enumsState = useSelector(state => state.enums.enum);

  return (
    <HorizontalSingleSelect
      label={i18n.t('milkUreaMeasure')}
      required
      isEditable={!isEditable}
      options={
        !stringIsEmpty(enumsState)
          ? enumsState[ENUM_CONSTANTS.MILK_UREA_MEASURE]
          : []
      }
      value={values[MILK_SOLID_EVALUATION_FIELDS.MILK_UREA_MEASURE]}
      error={
        touched[MILK_SOLID_EVALUATION_FIELDS.MILK_UREA_MEASURE] &&
        errors[MILK_SOLID_EVALUATION_FIELDS.MILK_UREA_MEASURE]
      }
      customInputContainerStyle={styles.customInputStyle}
      onChange={e =>
        handleChange(MILK_SOLID_EVALUATION_FIELDS.MILK_UREA_MEASURE, e)
      }
    />
  );
};

export default MilkUreaMeasure;
