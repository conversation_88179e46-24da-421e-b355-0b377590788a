import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    marginHorizontal: 0,
    paddingBottom: normalize(15),
  },

  formHeading: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(24),
    color: colors.primaryMain,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    height: normalize(48),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.primaryMain,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  plusIcon: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(19),
    lineHeight: normalize(16),
    textAlign: 'center',
    color: colors.primaryMain,
    // textTransform: 'uppercase',
    letterSpacing: 1.25,
    paddingTop: normalize(1),
    marginRight: normalize(7),
  },
  createPenText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    // textTransform: 'uppercase',
  },
  parent: {
    flex: 1,
    backgroundColor: colors.white,
  },
  pickupList: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: normalize(20),
  },
  flexOne: {
    flex: 1,
    justifyContent:'center'
  },
  iconContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  textPick: {
    fontSize: normalize(14),
    color: colors.grey1,
    fontFamily: fonts.HelveticaNeueBold,
    fontWeight: 'bold',
  },
  disabledButton: {
    borderColor: colors.grey2,
  },
  disabledText: {
    color: colors.grey2,
  },
};
