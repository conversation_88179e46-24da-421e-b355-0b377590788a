import React from 'react';

//react native
import { View, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';

//forms
import { Formik } from 'formik';

// localization
import i18n from '../../../../../../localization/i18n';

//navigation
import { useNavigation } from '@react-navigation/native';

//components
import AddPickupForm from './Form';
import TopBar from '../../../../../../components/TopBar';
import FormButton from '../../../../../../components/common/FormButton';
import { showAlertMsg } from '../../../../../../components/common/Alerts';

//constants
import { BUTTON_TYPE } from '../../../../../../constants/FormConstants';

//styles
import styles from './styles';

//validations
import addPickupValidationSchema from '../../../../../../helpers/validation/addPickup';

//helpers
import { pickupInitialValues } from '../../../../../../helpers/milkSolidHelper';

const AddPickup = props => {
  const { screenTitle } = props.route.params || {};
  const {
    onSubmit,
    selectedPickup,
    milkSoldData,
    isUpdate,
    selectedPickupIndex,
    isEditable = false,
    weightUnit,
    unitOfMeasure,
  } = props.route.params;
  const navigation = useNavigation();
  const formInitialValue = pickupInitialValues(
    selectedPickup,
    milkSoldData,
    unitOfMeasure,
    // true,
  );

  const goBack = () => {
    navigation.goBack();
  };

  const onCrossClick = dirty => {
    if (dirty) {
      showAlertMsg('', i18n.t('dataLossMessage'), [
        {
          text: i18n.t('no'),
          onPress: () => {},
        },
        {
          text: i18n.t('yes'),
          onPress: goBack,
        },
      ]);
    } else {
      goBack();
    }
  };

  const onSave = values => {
    if (isUpdate) {
      onSubmit(values, isUpdate, selectedPickupIndex);
    } else {
      onSubmit(values);
    }
  };

  return (
    <View style={styles.flexOne}>
      <Formik
        initialValues={formInitialValue}
        enableReinitialize={true}
        validationSchema={addPickupValidationSchema}
        validateOnMount={true}
        onSubmit={onSave}>
        {({
          handleSubmit,
          handleChange,
          values,
          errors,
          touched,
          dirty,
          isValid,
          handleBlur,
          setFieldValue,
          setFieldTouched,
          setFieldError,
        }) => {
          return (
            <>
              <TopBar
                crossIcon
                crossClick={() => onCrossClick(dirty)}
                title={screenTitle}
                titleStyles={styles.titleText}
                customHeaderStyle={styles.customHeaderStyle}
              />
              <KeyboardAvoidingView
                style={styles.flex1}
                behavior="padding"
                keyboardVerticalOffset={
                  Platform.OS.toLowerCase() == 'ios' ? 10 : 40
                }>
                <View style={styles.flexOne}>
                  <ScrollView
                    keyboardDismissMode="on-drag"
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled">
                    <View style={styles.addAnimalFormView}>
                      <View style={styles.flex1}>
                        <AddPickupForm
                          values={values}
                          errors={errors}
                          touched={touched}
                          handleChange={handleChange}
                          handleBlur={handleBlur}
                          setFieldValue={setFieldValue}
                          setFieldTouched={setFieldTouched}
                          setFieldError={setFieldError}
                          isEditable={isEditable}
                          milkSoldData={milkSoldData}
                          weightUnit={weightUnit}
                        />
                      </View>
                    </View>
                  </ScrollView>
                </View>
                <View style={styles.bottomButtomView}>
                  <FormButton
                    type={BUTTON_TYPE.PRIMARY}
                    label={i18n.t('save')}
                    onPress={handleSubmit}
                    disabled={!isValid || !isEditable}
                    customButtonStyle={styles.button}
                    customButtonTextStyle={styles.buttonText}
                  />
                </View>
              </KeyboardAvoidingView>
            </>
          );
        }}
      </Formik>
    </View>
  );
};

export default AddPickup;
