import React, { useRef, useState } from 'react';

import { Keyboard, View } from 'react-native';

// localization
import i18n from '../../../../../../../localization/i18n';

//constants
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
  PICKUP_FIELDS,
} from '../../../../../../../constants/FormConstants';

//components
import NumberFormInput from '../../../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../../../../Accounts/AddEdit/CustomInput';

//styles
import styles from '../styles';
import {
  LEFT_TEXT_ALIGNMENT,
  MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE,
  ANIMALS_IN_TANK_MAX_VALUE,
  MILK_SOLD_EVALUATION_PICKUP_DAYS_IN_TANK_MAX_VALUE,
  MILK_SOLD_EVALUATION_PICKUP_MILK_FAT_MAX_VALUE,
  MILK_SOLD_EVALUATION_PICKUP_MILK_PROTEIN_MAX_VALUE,
  MILK_SOLD_EVALUATION_PICKUP_MILK_SOLD_MAX_VALUE,
  MILK_SOLD_EVALUATION_PICKUP_NON_FAT_SOLID_MAX_VALUE,
  MILK_SOLD_EVALUATION_PICKUP_MUN_Milk_Urea_MAX_VALUE,
  MILK_SOLD_EVALUATION_TWO_DECIMAL_PLACES,
  MILK_SOLD_EVALUATION_ONE_DECIMAL_PLACES,
  MILK_SOLD_EVALUATION_PICKUP_MASTITIS_MAX_VALUE,
  NEXT_FIELD_TEXT,
  SOMATIC_CELL_COUNT_MAX_VALUE,
  BACTERIA_CELL_COUNT_MAX_VALUE,
  BACTERIA_CELL_COUNT_DECIMAL_PLACE,
} from '../../../../../../../constants/AppConstants';
import { renderMilkUreaLabel } from '../../../../../../../helpers/milkSolidHelper';

const AddPickupForm = ({
  values,
  errors,
  touched,
  handleChange,
  handleBlur,
  isEditable = false,
  milkSoldData,
  weightUnit,
}) => {
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  let milkSolid = useRef();
  let animalsInTank = useRef();
  let daysInTank = useRef();
  let milkFat = useRef();
  let milkProtein = useRef();
  let nonFatSolid = useRef();
  let MUN = useRef();
  let somaticCellCountWithUnit = useRef();
  let bacteriaCellCountWithUnit = useRef();
  let Mastitis = useRef();

  return (
    <View>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.formInputView}>
        <NumberFormInput
          label={`${i18n.t('milkSolid(kg)')} (${weightUnit})`}
          required
          placeholder={i18n.t('numberPlaceholder')}
          // value={getFormattedCommaNumber(values[PICKUP_FIELDS.MILK_SOLID])}
          value={values[PICKUP_FIELDS.MILK_SOLID]}
          error={
            touched[PICKUP_FIELDS.MILK_SOLID] &&
            errors[PICKUP_FIELDS.MILK_SOLID]
          }
          decimalPoints={MILK_SOLD_EVALUATION_TWO_DECIMAL_PLACES}
          isInteger={false}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={MILK_SOLD_EVALUATION_PICKUP_MILK_SOLD_MAX_VALUE}
          onChange={handleChange(PICKUP_FIELDS.MILK_SOLID)}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onBlur={handleBlur(PICKUP_FIELDS.MILK_SOLID)}
          reference={input => {
            milkSolid = input;
          }}
          onSubmitEditing={() => {
            animalsInTank?.focus();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: animalsInTank,
            });
          }}
          hasCommas={true}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('animalsInTank')}
          // unit={i18n.t('usd')}
          required
          placeholder={i18n.t('numberPlaceholder')}
          // value={getFormattedCommaNumber(values[PICKUP_FIELDS.ANIMALS_IN_TANK])}
          value={values[PICKUP_FIELDS.ANIMALS_IN_TANK]}
          error={
            touched[PICKUP_FIELDS.ANIMALS_IN_TANK] &&
            errors[PICKUP_FIELDS.ANIMALS_IN_TANK]
          }
          isInteger={true}
          keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={ANIMALS_IN_TANK_MAX_VALUE}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onChange={handleChange(PICKUP_FIELDS.ANIMALS_IN_TANK)}
          onBlur={handleBlur(PICKUP_FIELDS.ANIMALS_IN_TANK)}
          reference={input => {
            animalsInTank = input;
          }}
          onSubmitEditing={() => {
            daysInTank?.focus();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: daysInTank,
            });
          }}
          hasCommas={true}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('daysInTank')}
          // unit={i18n.t('usd')}
          required
          placeholder={i18n.t('numberPlaceholder')}
          value={values[PICKUP_FIELDS.DAYS_IN_TANK]}
          error={
            touched[PICKUP_FIELDS.DAYS_IN_TANK] &&
            errors[PICKUP_FIELDS.DAYS_IN_TANK]
          }
          isInteger={true}
          keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={MILK_SOLD_EVALUATION_PICKUP_DAYS_IN_TANK_MAX_VALUE}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onChange={handleChange(PICKUP_FIELDS.DAYS_IN_TANK)}
          onBlur={handleBlur(PICKUP_FIELDS.DAYS_IN_TANK)}
          reference={input => {
            daysInTank = input;
          }}
          onSubmitEditing={() => {
            milkFat?.focus();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: milkFat,
            });
          }}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('milkFat(%)')}
          // unit={i18n.t('usd')}

          placeholder={i18n.t('numberPlaceholder')}
          value={values[PICKUP_FIELDS.MILK_FAT]}
          error={
            touched[PICKUP_FIELDS.MILK_FAT] && errors[PICKUP_FIELDS.MILK_FAT]
          }
          decimalPoints={MILK_SOLD_EVALUATION_TWO_DECIMAL_PLACES}
          isInteger={false}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={MILK_SOLD_EVALUATION_PICKUP_MILK_FAT_MAX_VALUE}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onChange={handleChange(PICKUP_FIELDS.MILK_FAT)}
          onBlur={handleBlur(PICKUP_FIELDS.MILK_FAT)}
          reference={input => {
            milkFat = input;
          }}
          onSubmitEditing={() => {
            milkProtein?.focus();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: milkProtein,
            });
          }}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('milkProtein(%)')}
          // unit={i18n.t('usd')}

          placeholder={i18n.t('numberPlaceholder')}
          value={values[PICKUP_FIELDS.MILK_PROTEIN]}
          error={
            touched[PICKUP_FIELDS.MILK_PROTEIN] &&
            errors[PICKUP_FIELDS.MILK_PROTEIN]
          }
          decimalPoints={MILK_SOLD_EVALUATION_TWO_DECIMAL_PLACES}
          isInteger={false}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={MILK_SOLD_EVALUATION_PICKUP_MILK_PROTEIN_MAX_VALUE}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onChange={handleChange(PICKUP_FIELDS.MILK_PROTEIN)}
          onBlur={handleBlur(PICKUP_FIELDS.MILK_PROTEIN)}
          reference={input => {
            milkProtein = input;
          }}
          onSubmitEditing={() => {
            nonFatSolid?.focus();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: nonFatSolid,
            });
          }}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('nonFatSolid')}
          // unit={i18n.t('usd')}

          placeholder={i18n.t('numberPlaceholder')}
          value={values[PICKUP_FIELDS.NON_FAT_SOLID]}
          error={
            touched[PICKUP_FIELDS.NON_FAT_SOLID] &&
            errors[PICKUP_FIELDS.NON_FAT_SOLID]
          }
          decimalPoints={MILK_SOLD_EVALUATION_TWO_DECIMAL_PLACES}
          isInteger={false}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={MILK_SOLD_EVALUATION_PICKUP_NON_FAT_SOLID_MAX_VALUE}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onChange={handleChange(PICKUP_FIELDS.NON_FAT_SOLID)}
          onBlur={handleBlur(PICKUP_FIELDS.NON_FAT_SOLID)}
          reference={input => {
            nonFatSolid = input;
          }}
          onSubmitEditing={() => {
            MUN?.focus();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: MUN,
            });
          }}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={renderMilkUreaLabel(milkSoldData?.milkUreaMeasure)}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[PICKUP_FIELDS.MUN_Milk_Urea]}
          error={
            touched[PICKUP_FIELDS.MUN_Milk_Urea] &&
            errors[PICKUP_FIELDS.MUN_Milk_Urea]
          }
          decimalPoints={MILK_SOLD_EVALUATION_TWO_DECIMAL_PLACES}
          isInteger={false}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={MILK_SOLD_EVALUATION_PICKUP_MUN_Milk_Urea_MAX_VALUE}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onChange={handleChange(PICKUP_FIELDS.MUN_Milk_Urea)}
          onBlur={handleBlur(PICKUP_FIELDS.MUN_Milk_Urea)}
          reference={input => {
            MUN = input;
          }}
          onSubmitEditing={() => {
            somaticCellCountWithUnit?.focus();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: somaticCellCountWithUnit,
            });
          }}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('somaticCellCountWithUnit')}
          // unit={i18n.t('usd')}

          placeholder={i18n.t('numberPlaceholder')}
          value={values[PICKUP_FIELDS.SOMATIC_CELL_COUNT]}
          error={
            touched[PICKUP_FIELDS.SOMATIC_CELL_COUNT] &&
            errors[PICKUP_FIELDS.SOMATIC_CELL_COUNT]
          }
          isInteger={true}
          keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={SOMATIC_CELL_COUNT_MAX_VALUE}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onChange={handleChange(PICKUP_FIELDS.SOMATIC_CELL_COUNT)}
          onBlur={handleBlur(PICKUP_FIELDS.SOMATIC_CELL_COUNT)}
          reference={input => {
            somaticCellCountWithUnit = input;
          }}
          onSubmitEditing={() => {
            bacteriaCellCountWithUnit?.focus();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: bacteriaCellCountWithUnit,
            });
          }}
          hasCommas={true}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('bacteriaCellCountWithUnit')}
          // unit={i18n.t('usd')}

          placeholder={i18n.t('numberPlaceholder')}
          value={values[PICKUP_FIELDS.BACTERIA_CELL_COUNT]}
          error={
            touched[PICKUP_FIELDS.BACTERIA_CELL_COUNT] &&
            errors[PICKUP_FIELDS.BACTERIA_CELL_COUNT]
          }
          decimalPoints={BACTERIA_CELL_COUNT_DECIMAL_PLACE}
          isInteger={false}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={BACTERIA_CELL_COUNT_MAX_VALUE}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onChange={handleChange(PICKUP_FIELDS.BACTERIA_CELL_COUNT)}
          onBlur={handleBlur(PICKUP_FIELDS.BACTERIA_CELL_COUNT)}
          reference={input => {
            bacteriaCellCountWithUnit = input;
          }}
          onSubmitEditing={() => {
            Mastitis?.focus();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: Mastitis,
            });
          }}
          hasCommas={true}
        />
      </View>

      <View style={[styles.formInputView, styles.bottomPadding]}>
        <NumberFormInput
          label={i18n.t('Mastitis(#/Month)')}
          // unit={i18n.t('usd')}

          placeholder={i18n.t('numberPlaceholder')}
          // value={getFormattedCommaNumber(values[PICKUP_FIELDS.MASTITIS])}
          value={values[PICKUP_FIELDS.MASTITIS]}
          error={
            touched[PICKUP_FIELDS.MASTITIS] && errors[PICKUP_FIELDS.MASTITIS]
          }
          isInteger={true}
          keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
          minValue={MILK_SOLD_EVALUATION_INITIAL_MIN_VALUE}
          maxValue={MILK_SOLD_EVALUATION_PICKUP_MASTITIS_MAX_VALUE}
          textAlign={LEFT_TEXT_ALIGNMENT}
          customInputContainerStyle={styles.customInputStyle}
          onChange={handleChange(PICKUP_FIELDS.MASTITIS)}
          onBlur={handleBlur(PICKUP_FIELDS.MASTITIS)}
          reference={input => {
            Mastitis = input;
          }}
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
          blurOnSubmit={false}
          disabled={!isEditable}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              dismiss: true,
            });
          }}
          hasCommas={true}
        />
      </View>
    </View>
  );
};

export default AddPickupForm;
