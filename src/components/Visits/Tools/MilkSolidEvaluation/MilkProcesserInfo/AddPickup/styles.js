import colors from '../../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  flexOne: {
    flex: 1,
    backgroundColor: colors.white,
  },
  customHeaderStyle: {
    height: normalize(126),
  },
  titleText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(18),
    color: colors.grey1,
    marginLeft: normalize(-8),
    // textTransform: 'capitalize',
  },
  addAnimalFormView: {
    flex: 1,
    marginHorizontal: normalize(24),
  },
  flex1: {
    flex: 1,
  },
  bottomButtomView: {
    position: 'absolute',
    bottom: normalize(0),
    height: normalize(150),
    width: '100%',
    borderTopWidth: normalize(2),
    borderTopColor: colors.searchBoxBorder,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingHorizontal: normalize(24),
  },
  button: {
    borderWidth: 0,
  },
  buttonText: {
    letterSpacing: 1.25,
  },

  formInputView: {
    marginBottom: normalize(30),
  },
  customInputStyle: {
    width: '100%',
  },
  bottomPadding:{
     paddingBottom: normalize(120) 
  }
};
