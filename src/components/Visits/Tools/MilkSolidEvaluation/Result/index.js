// modules
import React, { useEffect } from 'react';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// components
import MilkSoldOutputSummary from './Summary';
import MilkSoldToolGraphContainer from './ToolGraphContainer';
import ToolBottomTabs from '../../common/ToolBottomTabs';

// constants
import { MILK_SOLD_RESULTS_TABS } from '../../../../../constants/toolsConstants/MilkSoldEvaluationConstants';

// actions
import { changeActiveMilkSoldResultTab } from '../../../../../store/actions/tools/milkSoldEvaluation';

const MilkSoldResult = ({ data, selectedVisits, recentVisit }) => {
  const dispatch = useDispatch();

  const activeResultsTab = useSelector(
    state => state.milkSoldEvaluation.activeResultTab,
  );

  useEffect(() => {
    return () =>
      dispatch(changeActiveMilkSoldResultTab(MILK_SOLD_RESULTS_TABS[0]));
  }, []);

  const _handleChangeResultsTab = tab => {
    dispatch(changeActiveMilkSoldResultTab(tab));
  };

  return (
    <View style={styles.container}>
      <View style={styles.container}>
        <MilkSoldOutputSummary activeResultsTab={activeResultsTab} />

        <MilkSoldToolGraphContainer
          activeResultsTab={activeResultsTab}
          selectedVisits={selectedVisits}
          recentVisit={recentVisit}
          data={data}
        />
      </View>

      <ToolBottomTabs
        tabs={MILK_SOLD_RESULTS_TABS}
        onTabChange={_handleChangeResultsTab}
        selectedTab={activeResultsTab}
      />
    </View>
  );
};
export default MilkSoldResult;
