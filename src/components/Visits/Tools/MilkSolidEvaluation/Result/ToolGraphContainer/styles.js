import DeviceInfo from 'react-native-device-info';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';

export default {
  dropdownContainer: {
    flex: 0.9,
  },
  dropdownTextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: normalize(1),
    maxWidth: '75%',
  },
  dropdownText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: 0.15,
    marginRight: normalize(8),
  },
  iconStyles: {
    marginTop: normalize(5),
    width: normalize(12),
    height: normalize(8),
  },
  labelsView: {
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: normalize(30),
    marginTop: normalize(10),
  },
  herdAvg: {
    width: normalize(14),
    height: normalize(14),
    borderRadius: normalize(7),
    backgroundColor: colors.secondary2,
  },
  herdGoal: {
    width: normalize(12),
    height: normalize(12),
    borderRadius: normalize(6),
    borderWidth: 1,
    backgroundColor: colors.graphHerdGoal,
    borderColor: colors.graphHerdGoal,
  },
  labelsTitle: {
    height: normalize(20),
    fontSize: normalize(10),
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    paddingHorizontal: normalize(10),
    maxWidth: '50%',
  },
  graphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth - 15,
    },
    android: {
      width: Platform.deviceWidth - (DeviceInfo.isTablet() ? 30 : 15),
    },
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.38
          : Platform.deviceHeight * 0.44,
    },
    android: {
      height: normalize(Platform.deviceHeight / 2) - 50,
    },
  }),
  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight + 0)
          : Platform.deviceHeight - 55,
    },
    android: {
      width: DeviceInfo.isTablet()
        ? normalize(Platform.deviceHeight * 0.75)
        : normalize(Platform.deviceHeight) - 55,
    },
  }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 70)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.5)
          : normalize(Platform.deviceWidth - 80),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 340 : 150),
      ),
    },
  }),
  domainPadding: {
    x: [0, 35],
  },
  customXAxisLabelStyles: {
    padding: normalize(37),
  },
  rightAxisTicks: {
    fill: colors.purple,
  },
};
