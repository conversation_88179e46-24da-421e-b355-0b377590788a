// modules
import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import MilkSoldGraph from '../Graph';
import ToolGraph from '../../../common/ToolGraph';
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';
import { showToast } from '../../../../../common/CustomToast';

// icons
import { CHEVRON_DOWN_BLUE_ICON } from '../../../../../../constants/AssetSVGConstants';

// constants
import {
  BOTTOM_SHEET_TYPE,
  TOAST_TYPE,
} from '../../../../../../constants/FormConstants';
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
  TOOL_RESULTS_TABS,
} from '../../../../../../constants/AppConstants';

import { MILK_SOLD_GRAPH_COMPARISON } from '../../../../../../constants/toolsConstants/MilkSoldEvaluationConstants';

// localization
import i18n from '../../../../../../localization/i18n';

// helpers
import {
  getDomain,
  getSetGraphValue,
  mapGraphDataForMilkSoldExport,
} from '../../../../../../helpers/milkSolidHelper';
import { getWeightUnitByMeasure } from '../../../../../../helpers/appSettingsHelper';

// services
import { isOnline } from '../../../../../../services/netInfoService';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../../../../../store/actions/tool';

// styles
import styles from './styles';

const MilkSoldToolGraphContainer = ({
  selectedVisits,
  recentVisit,
  data,
  activeResultsTab,
}) => {
  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit?.visit);

  const unitOfMeasure = visitState?.unitOfMeasure;
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const [layout, setLayout] = useState(null);
  const [lineGraphData, setLineGraphData] = useState([]);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);
  const [showPenBottomSheet, setShowPenBottomSheet] = useState(false);
  const [selectedOption, setSelectedOption] = useState(
    MILK_SOLD_GRAPH_COMPARISON(data?.milkUreaMeasure, weightUnit)[0],
  );

  useEffect(() => {
    setGraphValue(selectedOption);
  }, [selectedVisits, recentVisit]);

  const setGraphValue = param => {
    let temp = recentVisit.slice(1);
    let obj = {
      data: data,
      outputs: data?.outputs,
      date: visitState?.visitDate,
      id: visitState?.id,
    };
    temp.unshift(obj);
    temp = temp.reverse();
    let value = getSetGraphValue(temp, param);
    setLineGraphData(value);
  };

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const downloadMilkSoldData = async (
    graphData,
    type,
    data,
    graphType,
    label1,
    label2,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForMilkSoldExport(
        visitState,
        graphData,
        data,
        graphType,
        label1,
        label2,
      );

      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.MILK_SOLD_EVALUATION_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType: EXPORT_REPORT_TYPES.MILK_SOLD_EVALUATION_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareMilkSolidData = async (
    graphData,
    type,
    data,
    graphType,
    exportMethod,
    label1,
    label2,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForMilkSoldExport(
        visitState,
        graphData,
        data,
        graphType,
        label1,
        label2,
      );

      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.MILK_SOLD_EVALUATION_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.MILK_SOLD_EVALUATION_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const openPenBottomSheet = () => {
    setShowPenBottomSheet(true);
  };

  const onOptionChange = item => {
    setSelectedOption(item);
    setGraphValue(item);
    closePenBottomSheet();
  };

  const closePenBottomSheet = () => {
    setShowPenBottomSheet(false);
  };

  // hide component when summary screen is showing
  if (activeResultsTab?.key !== TOOL_RESULTS_TABS.GRAPH) return null;

  return (
    <View onLayout={({ nativeEvent }) => setLayout(nativeEvent.layout)}>
      <ToolGraph
        showExpandIcon
        handleExpandIconPress={onExpandIconPress}
        showDownloadIcon={!landscapeModalVisible}
        showShareIcon={!landscapeModalVisible}
        landscapeModalVisible={landscapeModalVisible}
        onDownloadPress={option =>
          downloadMilkSoldData(
            lineGraphData,
            option,
            data,
            selectedOption.graphType,
            selectedOption?.label1,
            selectedOption?.label2,
          )
        }
        onSharePress={(option, exportMethod) =>
          onShareMilkSolidData(
            lineGraphData,
            option,
            data,
            selectedOption.graphType,
            exportMethod,
            selectedOption?.label1,
            selectedOption?.label2,
          )
        }
        customGraphTitleComponent={
          <>
            <View style={styles.dropdownContainer}>
              {!landscapeModalVisible && (
                <TouchableOpacity
                  style={styles.dropdownTextContainer}
                  onPress={openPenBottomSheet}>
                  <Text style={styles.dropdownText} numberOfLines={1}>
                    {selectedOption?.name || ''}
                  </Text>
                  <CHEVRON_DOWN_BLUE_ICON {...styles.iconStyles} />
                </TouchableOpacity>
              )}
            </View>
          </>
        }
        graphComponent={
          <>
            <SafeAreaView>
              <MilkSoldGraph
                labels={lineGraphData?.map(a => a.date)}
                data1={lineGraphData?.map(x => x.prop1)}
                data2={lineGraphData?.map(x => x.prop2)}
                width={
                  landscapeModalVisible
                    ? styles.graphWidthLandscape.width
                    : styles.graphWidth.width
                }
                height={
                  landscapeModalVisible
                    ? styles.graphHeightLandscape.height
                    : styles.graphHeight.height
                }
                xAxisDomainPadding={
                  !landscapeModalVisible ? styles.domainPadding : null
                }
                landscapeModalVisible={landscapeModalVisible}
                showRightAxis={true}
                leftYAxisLabel={selectedOption?.label1}
                rightYAxisLabel={selectedOption?.label2}
                leftAxisDomain={getDomain(lineGraphData?.map(x => x.prop1))}
                rightAxisDomain={getDomain(lineGraphData?.map(x => x.prop2))}
                customXAxisLabelStyles={styles.customXAxisLabelStyles}
                customRightYAxisTickStyle={styles.rightAxisTicks}
                graphType={selectedOption.graphType}
              />
            </SafeAreaView>
            {layout && (
              <View style={styles.labelsView}>
                <View style={styles.herdAvg}></View>
                <Text style={styles.labelsTitle}>{selectedOption?.label1}</Text>
                <View style={styles.herdGoal}></View>
                <Text style={styles.labelsTitle}>{selectedOption?.label2}</Text>
              </View>
            )}
          </>
        }
      />

      {showPenBottomSheet && (
        <CustomBottomSheet
          type={BOTTOM_SHEET_TYPE.SIMPLE}
          selectLabel={i18n.t('result')}
          searchPlaceHolder={i18n.t('searchResult')}
          data={MILK_SOLD_GRAPH_COMPARISON(data?.milkUreaMeasure, weightUnit)}
          onChange={onOptionChange}
          onClose={closePenBottomSheet}
        />
      )}
    </View>
  );
};

export default MilkSoldToolGraphContainer;
