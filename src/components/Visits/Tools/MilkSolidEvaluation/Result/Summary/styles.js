import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  formInputView: {
    backgroundColor: colors.white,
    flex: 1,
  },
  listItemView: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: normalize(5),
    marginHorizontal: normalize(20),
    backgroundColor: colors.grey10,
    borderRadius: normalize(4),
    paddingVertical: normalize(10),
  },
  labelView: {
    flex: 1,
    marginHorizontal: normalize(15),
  },
  labelText: {
    fontSize: normalize(13),
    fontWeight: '500',
    lineHeight: normalize(14),
    color: colors.grey9,
    fontFamily: fonts.HelveticaNeueRegular,
  },
  valueView: {
    flex: 0.35,
    justifyContent: 'center',
    marginRight: normalize(15),
  },
  ValueText: {
    fontSize: normalize(13),
    fontWeight: '500',
    lineHeight: normalize(14),
    color: colors.grey9,
    fontFamily: fonts.HelveticaNeueRegular,
    textAlign: 'center',
  },
};
