// modules
import React from 'react';
import { View, Text, FlatList } from 'react-native';
import { useFormikContext } from 'formik';
import { useSelector } from 'react-redux';

// helpers
import {
  getOutputData,
  onUpdateMilkSoldData,
} from '../../../../../../helpers/milkSolidHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';
import { getWeightUnitByMeasure } from '../../../../../../helpers/appSettingsHelper';

// styles
import styles from './styles';

// constants
import { TOOL_RESULTS_TABS } from '../../../../../../constants/AppConstants';

const MilkSoldOutputSummary = ({ activeResultsTab }) => {
  const { values } = useFormikContext();

  const visitState = useSelector(state => state.visit?.visit);
  const siteData = useSelector(state => state.tool?.siteData);

  const unitOfMeasure = visitState?.unitOfMeasure;
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const publishedVisit = visitState?.visitStatus;

  const milkSoldData = onUpdateMilkSoldData(
    values,
    visitState,
    siteData,
    weightUnit,
    unitOfMeasure,
  );

  const outputData = getOutputData(
    milkSoldData,
    siteData,
    weightUnit,
    unitOfMeasure,
    publishedVisit,
  );

  const renderItem = ({ item, index }) => {
    return (
      <View style={styles.listItemView} key={index}>
        <View style={styles.labelView}>
          <Text style={styles.labelText}>{item?.label}</Text>
        </View>
        <View style={styles.valueView}>
          <Text style={styles.ValueText}>
            {convertInputNumbersToRegionalBasis(item?.value, 2)}
          </Text>
        </View>
      </View>
    );
  };

  if (activeResultsTab?.key !== TOOL_RESULTS_TABS.SUMMARY) return;

  return (
    <View style={styles.formInputView}>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={outputData}
        renderItem={renderItem}
      />
    </View>
  );
};

export default MilkSoldOutputSummary;
