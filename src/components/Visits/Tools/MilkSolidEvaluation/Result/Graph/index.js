// modules
import React from 'react';
import {
  Victory<PERSON>ine,
  Victory<PERSON>hart,
  VictoryScatter,
  VictoryAxis,
  VictoryLabel,
} from 'victory-native';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

// helpers
import { joinDataForBCSHerdAnalysisGraph } from '../../../../../../helpers/toolHelper';

// styles
import styles from './styles';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';
import { getMinMaxByGraphType } from '../../../../../../helpers/milkSolidHelper';
import { normalize } from '../../../../../../constants/theme/variables/customFont';

const MilkSoldGraph = ({
  labels,
  data1,
  data2,
  width,
  height,
  showRightAxis,
  xAxisLabel,
  leftYAxisLabel,
  rightYAxisLabel,
  leftAxisDomain,
  rightAxisDomain,
  customLeftYAxisLabelStyles,
  customRightYAxisLabelStyles,
  customLeftYAxisTickStyle,
  customRightYAxisTickStyle,
  landscapeModalVisible,
  graphType,
}) => {
  const authState = useSelector(state => state?.authentication?.user);

  let { leftMinMax, rightMinMax } = getMinMaxByGraphType(
    graphType,
    authState?.countryId,
  );

  // const maxima = [
  //   leftAxisDomain[1] - leftAxisDomain[0],
  //   rightAxisDomain[1] - rightAxisDomain[0],
  // ];
  // render left y-axis labels from data(y-axis dependant)

  let greatestData1Value = Math.max(...data1);
  let greatestData2Value = Math.max(...data2);

  let maxVal = Math.ceil(
    greatestData1Value > greatestData2Value
      ? greatestData1Value
      : greatestData2Value,
  );
  maxVal = (Math.trunc(maxVal / 50) + 1) * 50;

  const renderLeftVerticalAxis = graphData => {
    if (graphData?.length > 0) {
      return graphData?.map((item, ind) => (
        <VictoryAxis
          key={ind}
          dependentAxis
          label={leftYAxisLabel}
          style={{
            axis: styles.axisStyles,
            axisLabel: {
              ...styles.axisLabelStyles,
              ...customLeftYAxisLabelStyles,
            },
            tickLabels: {
              ...styles.verticalLabels,
              ...customLeftYAxisTickStyle,
            },
          }}
          // tickFormat={t => `${t.toFixed(1)}`}
          tickFormat={t => `${convertInputNumbersToRegionalBasis(t, 1, true)}`}
          crossAxis={false}
          domain={[leftMinMax.min, leftMinMax.max]}
        />
      ));
    }
  };

  // render right y-axis labels from data(y-axis dependant)
  const renderRightVerticalAxis = graphData => {
    if (graphData?.length > 0) {
      return graphData?.map((item, ind) => (
        <VictoryAxis
          key={'right_' + ind}
          dependentAxis
          label={rightYAxisLabel}
          orientation="right"
          style={{
            axis: styles.axisStyles,
            axisLabel: {
              ...styles.axisLabelStyles,
              ...customRightYAxisLabelStyles,
            },
            tickLabels: {
              ...styles.verticalLabels,
              ...customRightYAxisTickStyle,
            },
          }}
          crossAxis={false}
          domain={[rightMinMax.min, rightMinMax.max]}
          tickFormat={t => `${convertInputNumbersToRegionalBasis(t, 1, true)}`}
        />
      ));
    }
  };

  // render x-axis not dependant
  const renderHorizontalAxis = graphData => {
    if (graphData?.length > 0) {
      return (
        <VictoryAxis
          label={xAxisLabel}
          orientation="bottom"
          offsetY={50}
          style={{
            axis: styles.axisStyles,
            tickLabels: {
              ...styles.horizontalLabels,
            },
          }}
          domainPadding={{ x: [15, 15] }}
        />
      );
    }
  };

  const renderMilkGraph = data => {
    if (data.length > 0 && labels.length > 0) {
      let combinedData = joinDataForBCSHerdAnalysisGraph(labels, data);

      return (
        <VictoryLine
          data={combinedData}
          interpolation="monotoneX"
          style={{ data: styles.avgMilkLine }}
        />
      );
    }
  };

  const renderMilkScatterGraph = data => {
    if (data.length > 0 && labels.length > 0) {
      let combinedData = joinDataForBCSHerdAnalysisGraph(labels, data);
      return (
        <VictoryScatter
          data={combinedData}
          size={6}
          style={{
            data: styles.avgMilkScatter,
          }}
          labelComponent={
            <VictoryLabel dy={-20} dx={0} style={{ ...styles.dotNumber1 }} />
          }
          labels={({ datum }) =>
            convertInputNumbersToRegionalBasis(datum.y, 2, true)
          }
        />
      );
    }
  };
  const renderAvgBCSGraph = data => {
    if (data.length > 0 && labels.length > 0) {
      const combinedData = joinDataForBCSHerdAnalysisGraph(labels, data);

      return (
        <VictoryLine
          data={combinedData}
          interpolation="monotoneX"
          style={{ data: styles.avgBCSLine }}
        />
      );
    }
  };

  const renderAvgBCSScatterGraph = data => {
    if (data.length > 0 && labels.length > 0) {
      const combinedData = joinDataForBCSHerdAnalysisGraph(labels, data);

      return (
        <VictoryScatter
          data={combinedData}
          size={6}
          style={{
            data: styles.avgBCSScatter,
          }}
          labelComponent={
            <VictoryLabel dy={-20} dx={0} style={{ ...styles.dotNumber }} />
          }
          labels={({ datum }) =>
            convertInputNumbersToRegionalBasis(datum.y, 2, true)
          }
        />
      );
    }
  };

  const renderCurrentVisit = data => {
    if (data.length > 0 && labels.length > 0) {
      const combinedData = joinDataForBCSHerdAnalysisGraph(labels, data);
      return (
        <VictoryAxis
          dependentAxis
          axisValue={combinedData[data?.length - 1].x}
          tickFormat={t => ''}
          style={{
            axis: styles.axisStylesCurrentVisit,
          }}
        />
      );
    }
  };

  return (
    <View
      style={{
        height: landscapeModalVisible ? normalize(320) : normalize(350),
        width: '100%',
      }}>
      <View style={styles.container}>
        <VictoryChart
          height={normalize(350)}
          width={landscapeModalVisible ? width - 100 : width}>
          {renderHorizontalAxis(labels)}
          {renderLeftVerticalAxis(data1)}
          {renderAvgBCSGraph(data1)}
          {renderAvgBCSScatterGraph(data1)}
        </VictoryChart>
      </View>
      <View style={styles.container}>
        <VictoryChart
          height={normalize(350)}
          width={landscapeModalVisible ? width - 100 : width}>
          {showRightAxis && renderRightVerticalAxis(data2)}
          {renderMilkGraph(data2)}
          {renderMilkScatterGraph(data2)}
          {renderCurrentVisit(data2)}
        </VictoryChart>
      </View>
    </View>
  );
};

export default MilkSoldGraph;
