import DeviceInfo from 'react-native-device-info';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  verticalLabels: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(10),
    lineHeight: normalize(12),
    letterSpacing: normalize(0.15),
    fill: colors.alphabetIndex,
  },
  horizontalLabels: {
    fontFamily: fonts.HelveticaNeueRegular,
    letterSpacing: normalize(0.15),
    fill: colors.alphabetIndex,
    fontSize: normalize(11),
    lineHeight: normalize(13),
  },
  axisStyles: {
    stroke: colors.grey14,
    strokeWidth: 1,
    strokeDasharray: '2, 4',
  },
  axisStylesCurrentVisit: {
    stroke: colors.grey1,
    strokeWidth: 1,
    strokeDasharray: '1, 5',
  },
  axisLabelStyles: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(11),
    // lineHeight: normalize(50),
    fill: colors.alphabetIndex,
    padding: normalize(DeviceInfo.isTablet() ? 28 : 39),
  },
  minGoalsLine: {
    stroke: colors.minBCSGoalColor,
    strokeWidth: normalize(1),
    strokeDasharray: '4, 4',
  },
  maxGoalsLine: {
    stroke: colors.error4,
    strokeWidth: normalize(1),
    strokeDasharray: '4, 4',
  },
  avgBCSLine: {
    stroke: colors.secondary2,
    strokeWidth: normalize(2),
  },
  avgBCSScatter: {
    fill: colors.secondary2,
  },
  avgMilkLine: {
    stroke: colors.purple,
    strokeWidth: normalize(2),
  },
  avgMilkScatter: {
    fill: colors.purple,
  },
  domainPadding: {
    x: [0, 35],
  },
  dotNumber: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(10),
    lineHeight: normalize(12),
    fill: colors.secondary2,
    letterSpacing: normalize(0.5),
    color: colors.secondary2,
  },
  dotNumber1: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(10),
    lineHeight: normalize(12),
    fill: colors.purple,
    letterSpacing: normalize(0.5),
    color: colors.purple,
  },
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  graphPadding: {
    top: 20,
    left: 50,
    right: 50,
    bottom: 25,
  },
  graphPaddingLandscape: {
    top: 0,
    left: 60,
    right: 40,
    bottom: 30,
  },
  lineGraphPadding: {
    top: 20,
    left: 57,
    right: 57,
    bottom: 25,
  },
  horizontalLabels: {
    fontFamily: fonts.HelveticaNeueRegular,
    letterSpacing: normalize(0.15),
    fill: colors.alphabetIndex,
    fontSize: normalize(11),
    lineHeight: normalize(13),
    bottom: 20,
  },
  transparentValue: {
    fill: colors.transparent,
  },
  areaDataStyles: {
    fill: colors.topColor,
  },
  barTopLabel: {
    fontWeight: '700',
    fontFamily: fonts.HelveticaNeueRegular,
    letterSpacing: normalize(0.5),
    fill: colors.alphabetIndex,
    fontSize: normalize(10),
    lineHeight: normalize(12),
  },
  lineStyles: {
    stroke: colors.legendCircleColor,
    strokeWidth: normalize(2),
  },
};
