import DeviceInfo from 'react-native-device-info';
import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';
export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
    // paddingLeft: normalize(10),
  },
  dropdownContainer: {
    flex: 0.9,
  },
  dropdownTextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    // paddingBottom: normalize(10),
    paddingHorizontal: normalize(1),
    maxWidth: '75%',
  },
  dropdownText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: 0.15,
    marginRight: normalize(8),
  },
  marginTop: {
    marginTop: normalize(5),
  },
  infoColumn: {
    flexDirection: 'column',
  },
  labelValue: {
    color: colors.alphabetIndex,
    fontSize: normalize(12),
    paddingBottom: normalize(5),
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: normalize(6),
  },
  statsTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  statsValue: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  leftMargin: {
    marginLeft: normalize(20),
  },
  penAnalysisTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(20),
    color: colors.alphabetIndex,
    letterSpacing: normalize(0.15),
  },
  penAnalysisValue: {
    fontFamily: fonts.HelveticaNeueBold,
  },
  extraGraphWidth: normalize(20),
  reduceGraphHeight: normalize(90),
  dataLineColor: colors.secondary2,
  labelsView: {
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: normalize(30),
    marginTop: normalize(10),
  },
  herdAvg: {
    width: normalize(14),
    height: normalize(14),
    borderRadius: normalize(7),
    backgroundColor: colors.secondary2,
  },
  herdGoal: {
    width: normalize(12),
    height: normalize(12),
    borderRadius: normalize(6),
    // borderStyle: 'dashed',
    borderWidth: 1,
    backgroundColor: colors.graphHerdGoal,
    borderColor: colors.graphHerdGoal,
  },
  labelsTitle: {
    fontSize: normalize(10),
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    paddingHorizontal: normalize(10),
    maxWidth: '50%',
  },
  yAxisLabel: {
    fill: colors.alphabetIndex,
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(11),
  },

  graphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth - 15,
    },
    android: {
      width: Platform.deviceWidth - (DeviceInfo.isTablet() ? 30 : 15),
    },
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.38
          : Platform.deviceHeight * 0.44,
    },
    android: {
      height: normalize(Platform.deviceHeight / 2) - 50,
    },
  }),

  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight + 0)
          : Platform.deviceHeight - 55,
    },
    android: {
      width: DeviceInfo.isTablet()
        ? normalize(Platform.deviceHeight * 0.75)
        : normalize(Platform.deviceHeight) - 55,
    },
  }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 70)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.5)
          : normalize(Platform.deviceWidth - 80),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 340 : 150),
      ),
    },
  }),
  offsetX: RNPlatform.select({
    ios: Platform.deviceWidth - 20,
    android: Platform.deviceWidth - 40,
  }),
  offsetXLandscape: RNPlatform.select({
    ios:
      Platform.deviceHeight < 700
        ? Platform.deviceHeight - 33
        : Platform.deviceHeight - 80,
    android: normalize(Platform.deviceHeight - 75),
  }),
  domainPadding: {
    x: [0, 35],
  },

  customXAxisLabelStyles: {
    padding: normalize(37),
  },
  rightAxisTicks: {
    fill: colors.purple,
  },
};
