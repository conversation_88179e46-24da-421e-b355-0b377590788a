// modules
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import ToolAlert from '../../common/ToolAlert';

// styling constants
import customColor from '../../../../../constants/theme/variables/customColor';

// actions
import { hideMilkSolidToolToastRequest } from '../../../../../store/actions/userPreferences';

// constants
import { TOOL_TYPES } from '../../../../../constants/AppConstants';

const MilkSoldEvaluationToolAlert = ({ isEditable = false }) => {
  const dispatch = useDispatch();

  const defaultUserPreference = useSelector(
    state => state.userPreferences.userPreferences?.defaultValues,
  );

  const onCloseToast = () => {
    dispatch(hideMilkSolidToolToastRequest());
  };

  const milkSolidToast =
    defaultUserPreference?.[TOOL_TYPES.MILK_SOLD_EVALUATION] || false;

  if (!isEditable || !milkSolidToast) {
    return false;
  }

  return (
    <View style={styles.alert}>
      <ToolAlert onCloseToast={onCloseToast} />
    </View>
  );
};

const styles = StyleSheet.create({
  alert: {
    backgroundColor: customColor.white,
  },
});

export default MilkSoldEvaluationToolAlert;
