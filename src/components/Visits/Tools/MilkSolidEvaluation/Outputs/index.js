import React from 'react';

//react-native
import { View } from 'react-native';

//components
import FormButton from '../../../../../components/common/FormButton';

//constant
import { BUTTON_TYPE } from '../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../localization/i18n';

//styles
import styles from './styles';

// @TODO to be remove along with output screen
const Outputs = ({ handleSubmit }) => {
  return (
    <View style={styles.parent}>
      <View style={styles.output}>
        <FormButton
          type={BUTTON_TYPE.PRIMARY}
          label={i18n.t('outputs')}
          onPress={handleSubmit}
          customButtonStyle={styles.button}
        />
      </View>
    </View>
  );
};

export default Outputs;
