import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  formInputView: {
    backgroundColor: colors.white,
    flex: 1,
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(18),
    color: colors.grey1,
    marginLeft: normalize(-8),
    // textTransform: 'capitalize',
  },
  customHeaderStyle: {
    height: normalize(120),
    borderBottomWidth: 1,
    borderBottomColor: colors.grey8,
  },

  listItemView: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: normalize(15),
    paddingHorizontal: normalize(20),
  },
  labelView: {
    flex: 3,
    alignItem: 'flex-start',
  },
  labelText: {
    fontSize: normalize(14),
    color: colors.grey1,
    fontFamily: fonts.HelveticaNeueRegular,
    textAlign: 'left',
  },
  ValueText: {
    fontSize: normalize(14),
    color: colors.grey1,
    fontFamily: fonts.HelveticaNeueMedium,
    textAlign: 'right',
  },
  valueView: {
    flex: 1,
    alignItem: 'flex-end',
  },
};
