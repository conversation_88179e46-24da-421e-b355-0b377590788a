import React from 'react';

import { FlatList, View, Text } from 'react-native';

// localization
import i18n from '../../../../../../localization/i18n';

//styles
import styles from './styles';
import TopBar from '../../../../../TopBar';
//helper
import { getOutputData } from '../../../../../../helpers/milkSolidHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';
const MilkSoldOutput = props => {
  const {
    milkSoldData,
    navigation,
    siteData,
    publishedVisit,
    weightUnit,
    unitOfMeasure,
  } = props.route.params;
  const outputData = getOutputData(
    milkSoldData,
    siteData,
    weightUnit,
    unitOfMeasure,
    publishedVisit,
  );
  const renderItem = ({ item, index }) => {
    return (
      <View style={styles.listItemView} key={index}>
        <View style={styles.labelView}>
          <Text style={styles.labelText}>{item?.label}</Text>
        </View>
        <View style={styles.valueView}>
          <Text style={styles.ValueText}>{convertInputNumbersToRegionalBasis(item?.value, 2)}</Text>
        </View>
      </View>
    );
  };
  return (
    <View style={styles.formInputView}>
      <TopBar
        backButton
        backButtonClick={() => navigation.goBack()}
        title={i18n.t('outputs')}
        titleStyles={styles.titleText}
        customHeaderStyle={styles.customHeaderStyle}
      />
      <View style={styles.formInputView}>
        <FlatList
          showsVerticalScrollIndicator={false}
          data={outputData}
          renderItem={renderItem}
        />
      </View>
    </View>
  );
};

export default MilkSoldOutput;
