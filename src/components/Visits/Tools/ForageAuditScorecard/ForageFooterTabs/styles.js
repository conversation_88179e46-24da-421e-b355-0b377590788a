import { Platform } from 'react-native';
import colors from '../../../../../constants/theme/variables/customColor';
import { normalize } from '../../../../../constants/theme/variables/customFont';

export default {
  bottomButtonView: {
    borderTopWidth: normalize(2),
    borderTopColor: colors.searchBoxBorder,
    backgroundColor: colors.white,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.05,
    shadowRadius: 20.0,
    elevation: 24,
    justifyContent: 'center',
    paddingBottom: Platform.OS === 'android' && normalize(15),
  },
  androidBottomSpacing: {
    paddingBottom: normalize(15),
  },
  bottomView: {
    position: 'absolute',
    width: '100%',
    backgroundColor: colors.white,
  },
  nestedButtonContainer: {
    marginTop: normalize(10),
    paddingHorizontal: normalize(24),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  arrowIcon: {
    flex: 1,
    height: normalize(52),
    justifyContent: 'center',
  },
  leftIconStyles: {
    stroke: colors.primaryMain,
    strokeWidth: normalize(2),
    width: normalize(20),
    height: normalize(20),
  },
  button: {
    flex: 1,
    borderWidth: 0,
    alignSelf: 'flex-end',
    width: normalize(120),
  },
};
