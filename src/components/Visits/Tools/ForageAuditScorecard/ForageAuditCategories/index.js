// modules
import React from 'react';
import { FlatList } from 'react-native';

// styles
import styles from './styles';

// constants
import { FORAGE_AUDIT_SCORECARD } from '../../../../../constants/FormConstants';

// components
import ForageAuditCategoryItem from '../ForageAuditCategoryItem';

const ForageAuditCategories = ({ forageAuditScorecardState, setIsDirty }) => {
  if (
    forageAuditScorecardState &&
    forageAuditScorecardState?.[FORAGE_AUDIT_SCORECARD.SECTIONS] &&
    forageAuditScorecardState?.[FORAGE_AUDIT_SCORECARD.SECTIONS]?.length > 0
  ) {
    // render individual accordion for categories
    const renderItem = (item, index) => (
      <ForageAuditCategoryItem
        item={item}
        index={index}
        setIsDirty={setIsDirty}
      />
    );

    return (
      <FlatList
        keyExtractor={item => item?.index?.toString()}
        showsVerticalScrollIndicator={false}
        data={
          forageAuditScorecardState?.[FORAGE_AUDIT_SCORECARD.SECTIONS] || []
        }
        contentContainerStyle={styles.containerStyle}
        style={styles.listStyles}
        renderItem={({ item, index }) => renderItem(item, index)}
      />
    );
  }

  return <></>;
};

export default ForageAuditCategories;
