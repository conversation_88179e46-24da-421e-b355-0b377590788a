import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  tabsContainer: {
    marginHorizontal: normalize(20),
    marginTop: normalize(12),
    height: normalize(40),
    flexDirection: 'row',
    borderWidth: 0.5,
    borderColor: colors.grey8,
    borderRadius: normalize(4),
    backgroundColor: colors.white,
    shadowColor: colors.grey8,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.5,
    shadowRadius: 2.22,
    elevation: 3,
  },
  selectedTab: {
    backgroundColor: colors.primaryMain,
    borderRadius: normalize(4),
  },
  tab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabText: {
    fontSize: normalize(14),
    fontWeight: '400',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.black,
    lineHeight: normalize(24),
  },
  selectedTabText: {
    color: colors.white,
    fontWeight: '500',
  },
};
