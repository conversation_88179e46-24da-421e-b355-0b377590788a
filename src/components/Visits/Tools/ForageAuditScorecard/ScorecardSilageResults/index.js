// modules
import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

// constants
import {
  FORAGE_AUDIT_RESULTS,
  FORAGE_AUDIT_RESULTS_TABS,
} from '../../../../../constants/toolsConstants/ForageAuditConstants';
import { FORAGE_AUDIT_SCORECARD } from '../../../../../constants/FormConstants';

// components
import ForageAuditSectionScore from '../ForageAuditSectionScore';
import ForageAuditSectionResponses from '../ForageAuditSectionResponses';
import { getScorecardResults } from '../../../../../helpers/forageAuditScorecard';

const ScorecardSilageResults = ({ sectionData }) => {
  const selectedSilageState = useSelector(
    state => state.forageAuditScorecard?.selectedSilageState,
  );

  const [selectedTab, setSelectedTab] = useState(FORAGE_AUDIT_RESULTS_TABS[0]);

  const getProgressValue = () => {
    const progressValue = getScorecardResults(selectedSilageState);

    if (progressValue) {
      return progressValue;
    } else {
      return 0;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabsContainer}>
        {FORAGE_AUDIT_RESULTS_TABS?.map(tab => {
          return (
            <TouchableOpacity
              key={tab.key}
              onPress={() => setSelectedTab(tab)}
              style={[
                styles.tab,
                selectedTab.key === tab.key ? styles.selectedTab : null,
              ]}>
              <Text
                style={[
                  styles.tabText,
                  selectedTab.key === tab.key ? styles.selectedTabText : null,
                ]}>
                {tab?.value}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {selectedTab.key === FORAGE_AUDIT_RESULTS.FORAGE_AUDIT_SCORE ? (
        <ForageAuditSectionScore
          value={getProgressValue()}
          sectionData={sectionData}
          selectedSilageState={selectedSilageState}
        />
      ) : (
        <ForageAuditSectionResponses
          selectedSilageState={selectedSilageState}
        />
      )}
    </View>
  );
};

export default ScorecardSilageResults;
