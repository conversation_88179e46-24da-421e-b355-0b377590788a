import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';
import { Platform } from 'react-native';

export default {
  container: {
    height: normalize(70),
    marginHorizontal: normalize(20),
    borderWidth: 1,
    borderColor: colors.grey4,
    marginVertical: normalize(16),
    borderRadius: normalize(8),
    alignItems: 'center',
    paddingHorizontal: normalize(12),
    flexDirection: 'row',
  },
  sectionName: {
    fontSize: normalize(14),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    lineHeight: normalize(14),
  },
  detailsContainer: {
    marginLeft: normalize(16),
  },
  silageName: {
    marginTop: normalize(4),
    fontSize: normalize(12),
    fontWeight: '400',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    lineHeight: normalize(14),
    letterSpacing: 0.2,
  },
  percentageText: {
    fontSize: normalize(14),
    fontWeight: '700',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    position: 'absolute',
    marginLeft: Platform.select({
      ios: normalize(20),
      android: normalize(15),
    }),
  },
  svgContainer: {
    width: normalize(52),
    height: normalize(52),
  },
  progressBarColors: [
    colors.progressRed,
    colors.leadingTitleColor,
    colors.progressGreen,
  ],
  backgroundCircle: {
    cx: '50',
    cy: '50',
    r: '45',
    stroke: colors.analysisCardColor,
    strokeWidth: '2',
    fill: colors.transparent,
  },
  animatedCircleProps: {
    cx: '50',
    cy: '50',
    r: '45',
    strokeWidth: '10',
    stroke: colors.transparent,
    fill: colors.transparent,
    // stroke="rgb(246, 79, 89)"
    // fill="rgba(255,255,255,0.2)"
  },
};
