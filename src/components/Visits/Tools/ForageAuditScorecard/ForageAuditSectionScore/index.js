// modules
import React, { useEffect } from 'react';
import { View, Text, TextInput } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import Animated, {
  createAnimatedPropAdapter,
  interpolateColor,
  processColor,
  useAnimatedProps,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

// styles
import styles from './styles';

// constants
import { FORAGE_AUDIT_SCORECARD } from '../../../../../constants/FormConstants';
import {
  CIRCLE_ANIMATION_DURATION,
  CIRCLE_CIRCUMFERENCE,
  CIRCLE_COLOR_INTERPOLATION_RANGE,
  STROKE,
  TEXT_TRANSITION_DURATION,
} from '../../../../../constants/toolsConstants/ForageAuditConstants';
import { getKeyFromLocaleFile } from '../../../../../helpers/forageAuditScorecard';
import i18n from '../../../../../localization/i18n';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);
const AnimatedText = Animated.createAnimatedComponent(TextInput);

const ForageAuditSectionScore = ({
  value,
  sectionData,
  selectedSilageState,
}) => {
  const progress = useSharedValue(0);
  const strokeOffset = useSharedValue(0);

  const sectionDataKey = getKeyFromLocaleFile(
    sectionData?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME],
  );
  const selectedSilageStateKey = getKeyFromLocaleFile(
    selectedSilageState?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME],
  );

  const adapter = createAnimatedPropAdapter(
    props => {
      if ([STROKE, 'value'].includes(Object.keys(props))) {
        props.stroke = {
          type: 0,
          payload: processColor(props?.[STROKE]),
        };
        props.text = props.value;
        delete props.value;
      }
    },
    [STROKE, 'text'],
  );

  const percentage = useDerivedValue(() => {
    const number = (strokeOffset.value / 100) * CIRCLE_CIRCUMFERENCE;
    return withTiming(number, { duration: CIRCLE_ANIMATION_DURATION });
  });

  const animatedText = useDerivedValue(() => {
    return withTiming(progress.value, { duration: CIRCLE_ANIMATION_DURATION });
  });

  // const strokeColor = useDerivedValue(() => {
  //   return interpolateColor(
  //     animatedText.value,
  //     CIRCLE_COLOR_INTERPOLATION_RANGE,
  //     styles.progressBarColors,
  //   );
  // });

  const animatedCircleProps = useAnimatedProps(
    () => {
      return {
        strokeDasharray: [
          percentage.value,
          CIRCLE_CIRCUMFERENCE - percentage.value,
        ],
        // stroke: strokeColor.value,
      };
    },
    [],
    adapter,
  );

  const animatedTextProps = useAnimatedProps(
    () => {
      return {
        text: `${Math.round(animatedText.value)}%`,
      };
    },
    [],
    adapter,
  );

  const checkAnimatedValue = value => {
    // reanimated reserved word for running function in native UI thread
    'worklet';
    if (value == 100) {
      return 0;
    }
    return value < 10 ? 10 : 5;
  };

  const rTextStyle = useAnimatedStyle(() => {
    return {
      // color: interpolateColor(
      //   animatedText.value,
      //   CIRCLE_COLOR_INTERPOLATION_RANGE,
      //   styles.progressBarColors,
      // ),
      transform: [
        {
          translateX: withTiming(checkAnimatedValue(animatedText.value), {
            duration: TEXT_TRANSITION_DURATION,
          }),
        },
      ],
    };
  });

  const getStrokeColor = value => {
    return value > CIRCLE_COLOR_INTERPOLATION_RANGE[1]
      ? styles.progressBarColors[2]
      : value >= CIRCLE_COLOR_INTERPOLATION_RANGE[0]
      ? styles.progressBarColors[1]
      : styles.progressBarColors[0];
  };

  useEffect(() => {
    strokeOffset.value = value == 0 ? 0.1 : value;
    progress.value = value == 0 ? 0.1 : value;
  }, []);

  return (
    <View style={styles.container}>
      <AnimatedText
        style={[
          styles.percentageText,
          rTextStyle,
          { color: getStrokeColor(value) },
        ]}
        animatedProps={animatedTextProps}
      />
      <Svg {...styles.svgContainer} viewBox="0 0 100 100">
        <Circle
          {...styles.backgroundCircle}
          strokeDasharray={CIRCLE_CIRCUMFERENCE}
        />
        <AnimatedCircle
          {...styles.animatedCircleProps}
          stroke={getStrokeColor(value)}
          animatedProps={animatedCircleProps}
          strokeDasharray={CIRCLE_CIRCUMFERENCE}
        />
      </Svg>
      <View style={styles.detailsContainer}>
        <Text style={styles.sectionName}>
          {/* {sectionData?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME] || ''} */}
          {sectionDataKey
            ? i18n.t(sectionDataKey)
            : sectionData?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME] || ''}
        </Text>
        {selectedSilageState?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME] && (
          <Text style={styles.silageName}>
            {selectedSilageStateKey
              ? i18n.t(selectedSilageStateKey)
              : selectedSilageState?.[
                  FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME
                ] || ''}
          </Text>
        )}
      </View>
    </View>
  );
};

export default ForageAuditSectionScore;
