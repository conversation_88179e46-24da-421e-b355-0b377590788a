// modules
import React from 'react';
import { View, Text, FlatList } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import QuestionListItem from '../QuestionListItem';

// styles
import styles from './styles';

// constants
import { FORAGE_AUDIT_SCORECARD } from '../../../../../constants/FormConstants';

// helpers
import {
  getKeyFromLocaleFile,
  updateForageAuditData,
  updateSilageState,
} from '../../../../../helpers/forageAuditScorecard';

// actions
import {
  updateForageAuditScorecardData,
  updateSilageStateQuestions,
} from '../../../../../store/actions/tools/forageAuditScorecard';

// localization
import i18n from '../../../../../localization/i18n';

const QuestionsList = ({ sectionData, isEditable = false, setIsDirty }) => {
  const dispatch = useDispatch();

  const selectedSilageState = useSelector(
    state => state.forageAuditScorecard?.selectedSilageState,
  );
  const forageAuditScorecardState = useSelector(
    state => state.forageAuditScorecard?.forageAuditScorecardState,
  );

  const updateSelectedQuestion = question => {
    const updateSilage = updateSilageState(selectedSilageState, question);
    if (updateSilage) {
      setIsDirty && setIsDirty(true);
      dispatch(updateSilageStateQuestions(updateSilage));
    }

    // TODO update reducer state of forage audit main state
    const updatedForageAudit = updateForageAuditData(
      forageAuditScorecardState,
      updateSilage,
    );

    // TODO switch to mutate reducer state from action
    // dispatch(updateForageAuditScorecardData(updatedForageAudit));
  };

  // render individual accordion for categories
  const renderItem = (item, index) => (
    <QuestionListItem
      question={item}
      index={index}
      handleUpdateSelectedAnswer={updateSelectedQuestion}
      totalQuestion={
        selectedSilageState?.[FORAGE_AUDIT_SCORECARD.QUESTIONS]?.length || 0
      }
      isEditable={isEditable}
    />
  );

  const silageHeaderKey = getKeyFromLocaleFile(
    selectedSilageState?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME],
  );

  let sectionName = sectionData?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME] || '';
  if (sectionData?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME]) {
    sectionName = i18n.t(
      `${getKeyFromLocaleFile(
        sectionData?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME],
      )}`,
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.sectionName}>{sectionName || ''}</Text>

        {selectedSilageState?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME] && (
          <Text style={styles.silageName}>
            {` / `}
            {i18n.t(silageHeaderKey)}
          </Text>
        )}
      </View>

      {selectedSilageState &&
        selectedSilageState?.[FORAGE_AUDIT_SCORECARD.QUESTIONS] &&
        selectedSilageState?.[FORAGE_AUDIT_SCORECARD.QUESTIONS]?.length > 0 && (
          <FlatList
            keyExtractor={item => item?.index?.toString()}
            data={selectedSilageState?.[FORAGE_AUDIT_SCORECARD.QUESTIONS] || []}
            style={styles.listStyles}
            renderItem={({ item, index }) => renderItem(item, index)}
          />
        )}
    </View>
  );
};

export default QuestionsList;
