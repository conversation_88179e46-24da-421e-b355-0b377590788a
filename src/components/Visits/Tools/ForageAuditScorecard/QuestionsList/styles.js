import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    flexDirection: 'row',
    borderWidth: normalize(0.5),
    borderColor: colors.grey8,
    paddingHorizontal: normalize(20),
    alignItems: 'center',
  },
  sectionName: {
    paddingVertical: normalize(12),
    fontSize: normalize(13),
    fontWeight: '400',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.black,
    opacity: 0.45,
    lineHeight: normalize(18),
  },
  silageName: {
    fontSize: normalize(13),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    lineHeight: normalize(18),
  },
  listStyles: {
    paddingHorizontal: normalize(20),
  },
};
