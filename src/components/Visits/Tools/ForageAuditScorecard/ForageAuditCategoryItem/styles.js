import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  itemContainer: {
    borderWidth: 1,
    marginTop: normalize(12),
    paddingHorizontal: normalize(16),
    // paddingVertical: normalize(12),
    borderRadius: normalize(4),
    borderColor: colors.grey4,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: normalize(12),
  },
  accordionHeading: {
    flex: 1,
    fontSize: normalize(14),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    lineHeight: normalize(14),
  },
  accordionHeadingActive: {
    flex: 1,
    fontSize: normalize(14),
    fontWeight: '400',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    lineHeight: normalize(14),
  },
  completedStatus: {
    paddingHorizontal: normalize(4),
    paddingVertical: normalize(1),
    fontSize: normalize(11),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.completeStatus,
    lineHeight: normalize(14),
    backgroundColor: colors.completeStatusBackground,
    marginRight: normalize(12),
  },
  separator: {
    height: normalize(0),
    borderWidth: 0.5,
    borderColor: colors.grey8,
    backgroundColor: colors.white,
  },
  silagesContainer: {
    marginTop: normalize(10),
  },
  silageItem: {
    flexDirection: 'row',
    height: normalize(24),
    alignItems: 'center',
    marginBottom: normalize(12),
  },
  silageName: {
    flex: 1,
    fontSize: normalize(14),
    fontWeight: '400',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    lineHeight: normalize(24),
  },
  questionsAnswered: {
    fontSize: normalize(12),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.alphabetIndex,
    lineHeight: normalize(14),
    paddingHorizontal: normalize(4),
    paddingVertical: normalize(1),
    backgroundColor: colors.grey7,
  },
  categoryAnswers: {
    marginRight: normalize(20),
  },
  singleCategoryCompleted: {
    marginRight: normalize(20),
  },
  sectionCompleted: {
    marginRight: normalize(0),
  },
};
