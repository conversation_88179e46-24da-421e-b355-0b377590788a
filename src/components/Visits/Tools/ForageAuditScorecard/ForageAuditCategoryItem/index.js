// modules
import React, { useMemo, useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// styles
import styles from './styles';

// constants
import { FORAGE_AUDIT_SCORECARD } from '../../../../../constants/FormConstants';
import { FORWARD_FILL_ICON } from '../../../../../constants/AssetSVGConstants';
import ROUTE_CONSTANTS from '../../../../../constants/RouteConstants';

// helpers
import {
  isTotalSectionAnswered,
  getScorecardQuestionsDetails,
  getKeyFromLocaleFile,
} from '../../../../../helpers/forageAuditScorecard';

// localization
import i18n from '../../../../../localization/i18n';

// actions
import { setSelectedSilageData } from '../../../../../store/actions/tools/forageAuditScorecard';

const ForageAuditCategoryItem = ({ item, index, setIsDirty }) => {
  const { navigate } = useNavigation();
  const dispatch = useDispatch();
  const rotation = useSharedValue(0);

  const [activeIndex, setActiveIndex] = useState(null);

  // const isSectionCompleted = useMemo(
  //   () => isTotalSectionAnswered(item),
  //   [item],
  // );

  const isSectionCompleted = isTotalSectionAnswered(item);

  const handleAccordionPress = () => {
    if (
      item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES] &&
      item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES]?.length > 1
    ) {
      setActiveIndex(activeIndex === index ? null : index);
      rotation.value = withSpring(activeIndex === index ? 0 : 90);
    } else if (
      item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES] &&
      item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES]?.length > 0
    ) {
      const selectedSilage =
        item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES][0];
      handleScorecardSilagePress(selectedSilage);
    }
  };

  const handleScorecardSilagePress = selectedSilage => {
    if (selectedSilage) {
      dispatch(setSelectedSilageData(selectedSilage));
      const sectionData = {
        sectionIndex: item?.index,
        sectionName: item?.sectionName,
      };
      navigate(ROUTE_CONSTANTS.FORAGE_AUDIT_QUESTIONS, {
        sectionData,
        setIsDirty,
      });
    }
  };

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotation.value}deg` }],
    };
  });

  const renderScorecardSilages = () => (
    <View>
      <View style={styles.separator} />
      <View style={styles.silagesContainer}>
        {item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES]?.map(
          (silage, silageIndex) => {
            const totalQuestionsAnswered =
              getScorecardQuestionsDetails(silage) || 0;
            const key = getKeyFromLocaleFile(
              silage?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME],
            );

            return (
              <TouchableOpacity
                onPress={() => handleScorecardSilagePress(silage)}
                style={styles.silageItem}
                key={silageIndex}>
                <Text style={styles.silageName}>
                  {/* {silage?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME] || ''} */}
                  {key
                    ? i18n.t(`${key}`)
                    : silage?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME] || ''}
                </Text>

                {totalQuestionsAnswered === 0 ? (
                  <></>
                ) : totalQuestionsAnswered ===
                  silage?.[FORAGE_AUDIT_SCORECARD.QUESTIONS]?.length ? (
                  <Text
                    style={[styles.completedStatus, styles.sectionCompleted]}>
                    {i18n.t('completed')}
                  </Text>
                ) : (
                  <Text style={styles.questionsAnswered}>
                    {totalQuestionsAnswered}/
                    {silage?.[FORAGE_AUDIT_SCORECARD.QUESTIONS]?.length || 0}
                  </Text>
                )}
              </TouchableOpacity>
            );
          },
        )}
      </View>
    </View>
  );

  const isExpandable =
    item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES] &&
    item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES]?.length > 1
      ? true
      : false;

  const towerSilosORForageQuality =
    item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES]?.length === 1
      ? item?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES]?.[0]
      : null;
  const totalQuestionsAnswered =
    getScorecardQuestionsDetails(towerSilosORForageQuality) || 0;

  return (
    <TouchableOpacity
      style={styles.itemContainer}
      key={index}
      activeOpacity={1}
      onPress={handleAccordionPress}>
      <View style={styles.headerContainer}>
        <Text
          style={
            activeIndex !== index
              ? styles.accordionHeadingActive
              : styles.accordionHeading
          }>
          {/* {item?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME] || ''} */}
          {getKeyFromLocaleFile(item?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME])
            ? i18n.t(
                `${getKeyFromLocaleFile(
                  item?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME],
                )}`,
              )
            : item?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME] || ''}
        </Text>

        {isSectionCompleted && (
          <Text
            style={[
              styles.completedStatus,
              towerSilosORForageQuality ? styles.singleCategoryCompleted : null,
            ]}>
            {i18n.t('completed')}
          </Text>
        )}

        {!isSectionCompleted &&
          towerSilosORForageQuality &&
          totalQuestionsAnswered !== 0 && (
            <Text style={[styles.questionsAnswered, styles.categoryAnswers]}>
              {totalQuestionsAnswered}/
              {towerSilosORForageQuality?.[FORAGE_AUDIT_SCORECARD.QUESTIONS]
                ?.length || 0}
            </Text>
          )}

        {isExpandable && (
          <Animated.View style={[animatedStyles]}>
            <FORWARD_FILL_ICON />
          </Animated.View>
        )}
      </View>

      {activeIndex === index && isExpandable && renderScorecardSilages()}
    </TouchableOpacity>
  );
};

export default ForageAuditCategoryItem;
