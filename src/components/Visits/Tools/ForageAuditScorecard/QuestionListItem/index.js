// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

// styles
import styles from './styles';

// constants
import { FORAGE_AUDIT_SCORECARD } from '../../../../../constants/FormConstants';
import {
  CHECKED_FILL_ICON,
  EMPTY_CIRCLE_ICON,
} from '../../../../../constants/AssetSVGConstants';

// localization
import i18n from '../../../../../localization/i18n';

// helpers
import { getKeyFromLocaleFile, updateQuestionWithAnswer } from '../../../../../helpers/forageAuditScorecard';

const QuestionListItem = ({
  question,
  index,
  totalQuestion,
  handleUpdateSelectedAnswer,
  isEditable = false,
}) => {
  const handleAnswerSelect = selectedAnswer => {
    if (
      question?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER] &&
      question?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER]?.[
        FORAGE_AUDIT_SCORECARD.INDEX
      ] === selectedAnswer?.[FORAGE_AUDIT_SCORECARD.INDEX]
    ) {
      return;
    }

    const updatedQuestion = updateQuestionWithAnswer(question, selectedAnswer);
    if (updatedQuestion) {
      handleUpdateSelectedAnswer(updatedQuestion);
    }
  };

  const questionNumber = `${
    question?.[FORAGE_AUDIT_SCORECARD?.INDEX] + 1
  }/${totalQuestion}`;

  const questionKey = getKeyFromLocaleFile(
    question?.[FORAGE_AUDIT_SCORECARD.QUESTION_TEXT],
  );

  return (
    <View style={styles.container} key={index}>
      <Text style={styles.questionNumber}>
        {i18n.t('question')} {questionNumber || 0}
      </Text>

      <Text style={styles.questionText}>
        {/* {question?.[FORAGE_AUDIT_SCORECARD.QUESTION_TEXT]} */}
        {questionKey
          ? i18n.t(questionKey)
          : question?.[FORAGE_AUDIT_SCORECARD.QUESTION_TEXT]}
      </Text>

      {question?.[FORAGE_AUDIT_SCORECARD.AVAILABLE_ANSWERS] &&
        question?.[FORAGE_AUDIT_SCORECARD.AVAILABLE_ANSWERS]?.length > 0 &&
        question?.[FORAGE_AUDIT_SCORECARD.AVAILABLE_ANSWERS]?.map(answer => {
          const isAnswered =
            question?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER] &&
            question?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER]?.[
              FORAGE_AUDIT_SCORECARD.INDEX
            ] === answer?.[FORAGE_AUDIT_SCORECARD.INDEX]
              ? true
              : false;

          const answerKey = getKeyFromLocaleFile(
            answer?.[FORAGE_AUDIT_SCORECARD.ANSWER_TEXT],
          );

          return (
            <TouchableOpacity
              onPress={() => handleAnswerSelect(answer)}
              style={styles.optionContainer}
              activeOpacity={0.7}
              disabled={!isEditable}
              key={`${index}_${answer?.[FORAGE_AUDIT_SCORECARD.INDEX]}`}>
              {isAnswered ? (
                <CHECKED_FILL_ICON {...styles.emptyIcon} />
              ) : (
                <EMPTY_CIRCLE_ICON {...styles.emptyIcon} />
              )}

              <Text style={styles.answerText}>
                {/* {answer?.[FORAGE_AUDIT_SCORECARD.ANSWER_TEXT]} */}
                {answerKey
                  ? i18n.t(answerKey)
                  : answer?.[FORAGE_AUDIT_SCORECARD.ANSWER_TEXT]}
              </Text>
            </TouchableOpacity>
          );
        })}
    </View>
  );
};

export default QuestionListItem;
