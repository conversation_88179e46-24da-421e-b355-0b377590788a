import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default {
  container: {
    marginTop: normalize(12),
  },
  questionNumber: {
    fontSize: normalize(11),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.primaryMain,
    lineHeight: normalize(16),
    letterSpacing: 0.2,
  },
  questionText: {
    fontSize: normalize(14),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    marginTop: normalize(4),
    marginBottom: normalize(8),
  },
  optionContainer: {
    borderWidth: 1,
    borderRadius: normalize(4),
    borderColor: colors.grey4,
    height: normalize(40),
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(20),
    marginTop: normalize(8),
  },
  emptyIcon: {
    width: normalize(16),
    height: normalize(16)
  },
  answerText: {
    marginLeft: normalize(16),
    fontSize: normalize(14),
    fontWeight: '400',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.alphabetIndex,
    lineHeight: normalize(14),
    letterSpacing: 0.2,
  },

};
