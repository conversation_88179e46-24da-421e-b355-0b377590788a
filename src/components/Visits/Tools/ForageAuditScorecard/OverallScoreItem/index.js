// modules
import React from 'react';
import { View, Text } from 'react-native';

// constants
import { FORAGE_AUDIT_SCORECARD } from '../../../../../constants/FormConstants';

// components
import CircularProgress from '../CircularProgress';

// styles
import styles from './styles';
import { getKeyFromLocaleFile } from '../../../../../helpers/forageAuditScorecard';
import i18n from '../../../../../localization/i18n';

const OverallScoreItem = ({ section, index }) => {
  const sectionName = section?.[FORAGE_AUDIT_SCORECARD.SECTION_NAME] || '';
  const sectionNameKey = getKeyFromLocaleFile(sectionName);

  return (
    <View key={index}>
      {/* <Text style={styles.sectionName}>{sectionName}</Text> */}
      <Text style={styles.sectionName}>
        {sectionNameKey ? i18n.t(sectionNameKey) : sectionName}
      </Text>

      {section?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES] &&
        section?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES]?.length > 0 &&
        section?.[FORAGE_AUDIT_SCORECARD.SCORECARD_SILAGES]?.map(item => {
          let individualScorePercent = 0;

          if (
            item?.[FORAGE_AUDIT_SCORECARD.QUESTIONS] &&
            item?.[FORAGE_AUDIT_SCORECARD.QUESTIONS]?.length > 0
          ) {
            let totalSelectedAnswersScore = 0;
            let totalQuestionScore = 0;

            item?.[FORAGE_AUDIT_SCORECARD.QUESTIONS]?.map(question => {
              if (
                question?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER] &&
                question?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER] != null &&
                question?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER] != undefined
              ) {
                totalSelectedAnswersScore =
                  totalSelectedAnswersScore +
                    question?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER]?.[
                      FORAGE_AUDIT_SCORECARD.POINT_VALUE
                    ] || 0;
              }

              const optimalAnswer =
                question?.[FORAGE_AUDIT_SCORECARD.AVAILABLE_ANSWERS] &&
                question?.[FORAGE_AUDIT_SCORECARD.AVAILABLE_ANSWERS]?.length >
                  0 &&
                question?.[FORAGE_AUDIT_SCORECARD.AVAILABLE_ANSWERS]?.reduce(
                  (prev, current) => {
                    return prev?.[FORAGE_AUDIT_SCORECARD.POINT_VALUE] >
                      current?.[FORAGE_AUDIT_SCORECARD.POINT_VALUE]
                      ? prev
                      : current;
                  },
                );

              totalQuestionScore =
                totalQuestionScore +
                  optimalAnswer?.[FORAGE_AUDIT_SCORECARD.POINT_VALUE] || 0;
            });

            individualScorePercent = Math.round(
              (totalSelectedAnswersScore / totalQuestionScore) * 100,
            );
          }

          return (
            <CircularProgress
              identifier={`${
                item?.[FORAGE_AUDIT_SCORECARD.SECTION_SILAGE_TYPE]
              }_${item?.[FORAGE_AUDIT_SCORECARD.SECTION_INDEX]}_${
                item?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME]
              }`}
              value={individualScorePercent}
              heading={
                (item?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME]
                  ? item?.[FORAGE_AUDIT_SCORECARD.SILAGE_TYPE_NAME]
                  : sectionName) || ''
              }
            />
          );
        })}
    </View>
  );
};

export default OverallScoreItem;
