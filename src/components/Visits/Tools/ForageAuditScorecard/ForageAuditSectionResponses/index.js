// modules
import React from 'react';
import { View, Text, FlatList } from 'react-native';

// styles
import styles from './styles';

// constants
import { FORAGE_AUDIT_SCORECARD } from '../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../localization/i18n';
import { getKeyFromLocaleFile } from '../../../../../helpers/forageAuditScorecard';

const ForageAuditSectionResponses = ({ selectedSilageState }) => {
  const renderItem = (item, index) => {
    const selectedAnswer =
      (item?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER] &&
        item?.[FORAGE_AUDIT_SCORECARD.SELECTED_ANSWER]?.[
          FORAGE_AUDIT_SCORECARD.ANSWER_TEXT
        ]) ||
      '';

    const optimalAnswer =
      item?.[FORAGE_AUDIT_SCORECARD.AVAILABLE_ANSWERS] &&
      item?.[FORAGE_AUDIT_SCORECARD.AVAILABLE_ANSWERS]?.length > 0 &&
      item?.[FORAGE_AUDIT_SCORECARD.AVAILABLE_ANSWERS]?.reduce(
        (prev, current) => {
          return prev.pointValue > current.pointValue ? prev : current;
        },
      );

    const questionTextKey = getKeyFromLocaleFile(item?.[FORAGE_AUDIT_SCORECARD.QUESTION_TEXT]);
    const selectedAnswerKey = getKeyFromLocaleFile(selectedAnswer);
    const optimalAnswerKey = getKeyFromLocaleFile(optimalAnswer?.[FORAGE_AUDIT_SCORECARD.ANSWER_TEXT]);

    return (
      <View style={styles.itemContainer}>
        <Text style={styles.questionText}>
          {questionTextKey ? i18n.t(questionTextKey) : item?.[FORAGE_AUDIT_SCORECARD.QUESTION_TEXT] || ''}
        </Text>
        <View style={styles.answersContainer}>
          <Text style={styles.selectedAnswerText}>{selectedAnswerKey ? i18n.t(selectedAnswerKey) : selectedAnswer || ''}</Text>
          <Text style={[styles.selectedAnswerText, styles.optimalAnswerText]}>
            {(optimalAnswer &&
              (optimalAnswerKey ? i18n.t(optimalAnswerKey) : optimalAnswer?.[FORAGE_AUDIT_SCORECARD.ANSWER_TEXT]) || '')}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.answerText}>{i18n.t('answers')}</Text>
        <Text style={[styles.answerText, styles.optimalText]}>
          {i18n.t('optimal')}
        </Text>
      </View>

      {selectedSilageState &&
        selectedSilageState?.[FORAGE_AUDIT_SCORECARD.QUESTIONS] &&
        selectedSilageState?.[FORAGE_AUDIT_SCORECARD.QUESTIONS]?.length > 0 && (
          <FlatList
            keyExtractor={item => item?.index?.toString()}
            data={selectedSilageState?.[FORAGE_AUDIT_SCORECARD.QUESTIONS] || []}
            style={styles.listStyles}
            renderItem={({ item, index }) => renderItem(item, index)}
            ItemSeparatorComponent={() => <LineSeparator />}
          />
        )}
    </View>
  );
};

export const LineSeparator = () => {
  return <View style={styles.separator} />;
};

export default ForageAuditSectionResponses;
