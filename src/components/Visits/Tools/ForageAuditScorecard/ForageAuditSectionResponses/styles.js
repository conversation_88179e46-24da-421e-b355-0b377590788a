import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    paddingHorizontal: normalize(20),
    marginTop: normalize(16),
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: normalize(10),
    borderBottomWidth: 1,
    borderColor: colors.grey8,
  },
  answerText: {
    fontSize: normalize(13),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.popoverTextColor,
    lineHeight: normalize(20),
  },
  optimalText: {
    color: colors.primaryMain,
  },
  listStyles: {
    paddingHorizontal: normalize(20),
  },
  itemContainer: {
    marginVertical: normalize(16),
  },
  questionText: {
    fontSize: normalize(12),
    fontWeight: '400',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.questionColor,
    opacity: 0.64,
    lineHeight: normalize(18),
  },
  answersContainer: {
    flexDirection: 'row',
    marginTop: normalize(4),
    justifyContent: 'space-between',
  },
  selectedAnswerText: {
    flex: 1,
    fontSize: normalize(13),
    fontWeight: '400',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.popoverTextColor,
    lineHeight: normalize(20),
  },
  optimalAnswerText: {
    color: colors.primaryMain,
    alignSelf: 'flex-end',
    textAlign: 'right'
  },
  separator: {
    height: 1,
    backgroundColor: colors.separatorColor,
  },
};
