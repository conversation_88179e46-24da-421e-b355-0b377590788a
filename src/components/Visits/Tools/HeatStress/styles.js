import fonts, {
  normalize,
} from '../../../../constants/theme/variables/customFont';
import colors from '../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    marginHorizontal: 0,
    marginBottom: normalize(16),
  },

  formHeading: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(24),
    color: colors.primaryMain,
  },
  requiredLabelRow: {
    flexDirection: 'row',
    marginTop: normalize(4),
  },
  starIcon: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(14),
    letterSpacing: 0.2,
    color: colors.stericColor,
  },
  requiredLabel: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontStyle: 'italic',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginLeft: normalize(6),
    // marginBottom: normalize(30),
  },
  formInputView: {
    marginBottom: normalize(16),
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: colors.grey1,
    // textTransform: 'capitalize',
    marginBottom: normalize(10),
  },

  parent: {
    backgroundColor: colors.white,
    paddingBottom: normalize(80),
    borderColor: colors.grey8,
    borderTopWidth: 0.7,
  },
  customInputStyle: {
    width: '100%',
  },

  addHerdInformationForm: {
    flex: 1,
    marginHorizontal: normalize(24),
    marginTop: normalize(16),
  },
  flex1: {
    flex: 1,
  },
  customInputContainerStyle: {
    width: '100%',
  },
};
