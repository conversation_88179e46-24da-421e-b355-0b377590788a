import { Dimensions } from 'react-native';
import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
    // padding: normalize(20),
  },
  subContainer: { paddingHorizontal: normalize(20), paddingVertical: 16 },
  stressThresholdContainer: {
    // width: normalize(335),
    // height: normalize(90),
    // marginHorizontal: normalize(20),
    borderRadius: normalize(8),
    alignItems: 'flex-start',
    justifyContent: 'center',
    padding: normalize(16),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
  },
  flexRow: { flexDirection: 'row' },
  paddingRight: { paddingRight: normalize(16) },
  stressThresholdMargin: { marginBottom: normalize(8) },
  tempsContainer: { flexDirection: 'row', justifyContent: 'flex-start' },
  tempHumidIndexText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  tempText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(18),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginTop: normalize(4),
  },
  tempMargin: { marginRight: normalize(16) },
  stressThresholdBoxText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginTop: normalize(4),
  },
  dataBox: {
    // backgroundColor: 'red',
    flex: 1,
    marginTop: normalize(8),
    // width: SCREEN_WIDTH - normalize(12) - normalize(SCREEN_WIDTH / 2),
    borderRadius: normalize(8),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
    padding: normalize(16),
    // marginRight: normalize(8),
    // height: normalize(85),
    minHeight: normalize(85),
  },
  marginRight: { marginRight: normalize(4) },
  marginLeft: { marginLeft: normalize(4) },
  dataBoxTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  dataBoxContent: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginTop: normalize(4),
    // marginBottom: normalize(30),
  },
  riyalLineHeight: {
    lineHeight: normalize(26),
  },
  dataBoxContentBigFloat: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginTop: normalize(4),
    // marginBottom: normalize(30),
  },
  legendSectionHeading: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    marginTop: normalize(16),
    marginBottom: normalize(12),
  },
  legendTitleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  legendBody: { marginTop: normalize(4) },
  marginBottom: { marginBottom: normalize(8) },
  heatStressGraph: { alignItems: 'center', marginTop: normalize(16) },
  flexOne: { flex: 1 },
};
