// modules
import React from 'react';
import { <PERSON>, Text, ScrollView, FlatList } from 'react-native';
import { Svg } from 'react-native-svg';

// components
import ToolGraph from '../../common/ToolGraph';

// localization
import i18n, { getLanguage } from '../../../../../localization/i18n';

// styles
import styles from './styles';

//  helpers
import {
  getColorCodedSvg,
  getColorCodingImperial,
  getColorCodingMetric,
  getLegendDescriptionForCard,
  getLegendInformation,
  returnDashIfNull,
} from '../../../../../helpers/heatStressHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';

import {
  HEAT_STRESS_GRAPH,
  HEAT_STRESS_GRAPH_CHINESE,
  HEAT_STRESS_GRAPH_FRENCH,
  HEAT_STRESS_GRAPH_FRENCH_CA,
  HEAT_STRESS_GRAPH_ITALIAN,
  HEAT_STRESS_GRAPH_KOREAN,
  HEAT_STRESS_GRAPH_POLISH,
  HEAT_STRESS_GRAPH_PORTUGUESE,
  HEAT_STRESS_GRAPH_RUSSIAN,
} from '../../../../../constants/AssetSVGConstants';
import { HEAT_STRESS_FIELD_CONSTANTS } from '../../../../../constants/toolsConstants/heatStressConstants';
import {
  CURRENCY,
  UNIT_OF_MEASURE,
} from '../../../../../constants/AppConstants';
import { getHeatStressGraph } from '../../../../../helpers/visitReport/heatStress';

const HeatStressResult = props => {
  const {
    result = [],
    onDownloadPress,
    onSharePress,
    selectedCurrency,
    unitOfMeasure,
    isFullIntakeAdjustment,
  } = props;

  const getTextStyle = value => {
    const styleArray = [];
    if (value?.length <= 14) {
      styleArray.push(styles.dataBoxContent);
    } else {
      styleArray.push(styles.dataBoxContentBigFloat);
    }
    if (selectedCurrency === CURRENCY.SAR) {
      styleArray.push(styles.riyalLineHeight);
    }
    return styleArray;
  };

  const renderHeatStressValues = (
    value,
    unit = '',
    unitAsPrefix = false,
    showCustomValue = null,
    decimalCount = 1,
  ) => {
    if (value) {
      // const formattedValue =
      //   getCommaSeparatedValues(
      //     showCustomValue ? showCustomValue : value,
      //     decimalCount,
      //   ) || value;

      const formattedValue =
        convertInputNumbersToRegionalBasis(
          showCustomValue ? showCustomValue : value,
          decimalCount,
          true,
        ) || value;

      if (unitAsPrefix && selectedCurrency !== CURRENCY.SAR) {
        return (
          <View style={styles.flexRow}>
            <Text style={getTextStyle(value)}>{unit}</Text>
            <Text style={[getTextStyle(formattedValue), styles.container]}>
              {showCustomValue ? showCustomValue : formattedValue}
            </Text>
          </View>
        );
      } else {
        return (
          <View style={styles.flexRow}>
            <Text style={[getTextStyle(formattedValue)]}>
              {showCustomValue ? showCustomValue : formattedValue}
            </Text>
            <Text style={getTextStyle(formattedValue)}>{unit}</Text>
          </View>
        );
      }
    } else {
      return <Text style={styles.dataBoxContent}>-</Text>;
    }
  };

  const stressThresholdHeader = () => {
    const THIMetric = result?.filter?.(
      item => item.name === HEAT_STRESS_FIELD_CONSTANTS.THI_CELSIUS,
    )[0];
    const THIMImperial = result?.filter?.(
      item => item.name === HEAT_STRESS_FIELD_CONSTANTS.THI_FARENHEIT,
    )[0];
    const THIMETRICSVG = getColorCodedSvg(THIMetric?.value);
    const THIMetricColor = getColorCodingMetric(THIMetric?.value);
    const THIImperialColor = getColorCodingImperial(THIMImperial?.value);
    const cardDescription = getLegendDescriptionForCard(THIMetric?.value);
    return (
      <View style={styles.stressThresholdContainer}>
        <View style={styles.flexRow}>
          <View style={styles.paddingRight}>
            <THIMETRICSVG />
          </View>
          <View>
            <Text style={styles.tempHumidIndexText}>
              {i18n.t('temperatureHumidityIndex')}
            </Text>
            <View style={styles.tempsContainer}>
              <Text
                style={[
                  styles.tempText,
                  styles.tempMargin,
                  ,
                  { color: THIImperialColor },
                ]}>
                {convertInputNumbersToRegionalBasis(
                  returnDashIfNull(
                    convertInputNumbersToRegionalBasis(THIMImperial?.value, 1),
                    THIMImperial?.unit,
                  ),
                )}
              </Text>
              <Text style={[styles.tempText, { color: THIMetricColor }]}>
                {returnDashIfNull(
                  convertInputNumbersToRegionalBasis(THIMetric?.value, 1),
                  THIMetric?.unit,
                )}
              </Text>
            </View>
            <Text
              style={[
                styles.stressThresholdBoxText,
                { color: THIImperialColor },
              ]}>
              {/* {i18n.t('stressThreshold')} */}
              {cardDescription}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const stressThresholdFooter = (SVG, title, text, index) => (
    <View
      style={[styles.stressThresholdContainer, styles.marginBottom]}
      key={index + title}>
      <View style={styles.flexRow}>
        <View style={styles.paddingRight}>
          <SVG />
        </View>
        <View style={styles.flexOne}>
          <Text style={styles.legendTitleText}>{title}</Text>
          <Text style={[styles.tempHumidIndexText, styles.legendBody]}>
            {text}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <ToolGraph
        showDownloadIcon
        showShareIcon
        // landscapeModalVisible={landscapeModalVisible}
        onSharePress={(option, exportMethod) =>
          onSharePress(option, exportMethod)
        }
        onDownloadPress={option => onDownloadPress(option)}
        customGraphTitleComponent={
          <Text style={[styles.stressThresholdBoxText]}>
            {i18n.t('stressThreshold')}
          </Text>
        }
      />

      <View style={styles.subContainer}>
        <FlatList
          data={result}
          numColumns={2}
          keyExtractor={item => '0xa' + item.name}
          ListHeaderComponent={stressThresholdHeader}
          renderItem={item => {
            let decimalCount = 1;
            let showCustomValue = null;
            if (isFullIntakeAdjustment) {
              switch (item?.item?.name) {
                case HEAT_STRESS_FIELD_CONSTANTS.INTAKE_ADJUSTMENT:
                  showCustomValue = 100;
                  break;

                case HEAT_STRESS_FIELD_CONSTANTS.DMI_PERCENT:
                case HEAT_STRESS_FIELD_CONSTANTS.ENERGY_EQUIVALENT_MILK_LOSS_WEIGHT:
                  showCustomValue = '0.0';
                  break;

                // case HEAT_STRESS_FIELD_CONSTANTS.LOSS_OF_ENERGY_CONSUMED:
                case HEAT_STRESS_FIELD_CONSTANTS.REDUCTION_IN_DMI_WEIGHT:
                  // case HEAT_STRESS_FIELD_CONSTANTS.MILK_LOSS_VALUE_DAY:
                  // case HEAT_STRESS_FIELD_CONSTANTS.MILK_LOSS_VALUE_MONTH:
                  showCustomValue = '0.0';
                  break;

                case HEAT_STRESS_FIELD_CONSTANTS.MILK_LOSS_VALUE_DAY:
                case HEAT_STRESS_FIELD_CONSTANTS.MILK_LOSS_VALUE_MONTH:
                case HEAT_STRESS_FIELD_CONSTANTS.LOSS_OF_ENERGY_CONSUMED:
                  if (parseFloat(result[item?.index].value) < 0) {
                    showCustomValue = '0.0';
                  }
                  break;
              }
            }

            if (
              HEAT_STRESS_FIELD_CONSTANTS.LOSS_OF_ENERGY_CONSUMED ===
              item?.item?.name
            ) {
              decimalCount = 2;
            }

            return item?.item?.name ===
              HEAT_STRESS_FIELD_CONSTANTS.THI_CELSIUS ||
              item?.item?.name === HEAT_STRESS_FIELD_CONSTANTS.THI_FARENHEIT ? (
              <></>
            ) : (
              <View
                style={[
                  styles.dataBox,
                  item?.index % 2 === 0
                    ? styles.marginRight
                    : styles.marginLeft,
                ]}>
                <Text style={styles.dataBoxTitle}>
                  {i18n.t(`${result[item?.index].displayName}`)}
                </Text>
                {result[item?.index].name ===
                  HEAT_STRESS_FIELD_CONSTANTS.MILK_LOSS_VALUE_DAY ||
                result[item.index].name ===
                  HEAT_STRESS_FIELD_CONSTANTS.MILK_LOSS_VALUE_MONTH
                  ? renderHeatStressValues(
                      result[item?.index].value,
                      `${result[item?.index].unit} `,
                      true,
                      showCustomValue,
                    )
                  : renderHeatStressValues(
                      result[item?.index].value,
                      ` ${result[item?.index].unit}`,
                      false,
                      showCustomValue,
                      decimalCount,
                    )}
              </View>
            );
          }}
        />

        <View style={styles.heatStressGraph}>
          <Svg width={336} height={336}>
            {getHeatStressGraph()}
          </Svg>
        </View>

        <Text style={styles.legendSectionHeading}>{i18n.t('legend')}</Text>

        {getLegendInformation().map((item, index) => {
          return stressThresholdFooter(item.svg, item.title, item.body, index);
        })}
      </View>
    </ScrollView>
  );
};
export default HeatStressResult;
