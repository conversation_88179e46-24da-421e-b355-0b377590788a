import React, { useRef, useState } from 'react';

//react-native
import { View, Text, Keyboard } from 'react-native';

//components
import NumberFormInput from '../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../Accounts/AddEdit/CustomInput';

//constant
import {
  DONE,
  KG_REGEX,
  LEFT_TEXT_ALIGNMENT,
  UNIT_OF_MEASURE,
} from '../../../../constants/AppConstants';
import {
  CONTENT_TYPE,
  HEAT_STRESS_FIELDS,
} from '../../../../constants/FormConstants';
import { HEAT_STRESS_INPUTS } from '../../../../constants/toolsConstants/heatStressConstants';

// localization
import i18n from '../../../../localization/i18n';

//styles
import styles from './styles';

//helper
import {
  getFormattedCommaNumber,
  getKeyboardType,
} from '../../../../helpers/alphaNumericHelper';
import {
  getHeatStressFieldLabel,
  getSectionLabel,
} from '../../../../helpers/heatStressHelper';
import { useSelector } from 'react-redux';
import { getWeightUnitByMeasure } from '../../../../helpers/appSettingsHelper';

const HerdInformation = ({
  values,
  handleChange,
  isEditable = false,
  onBlurInput,
  siteData,
  selectedCurrency,
}) => {
  //redux state
  const visitState = useSelector(state => state.visit?.visit);
  const unitOfMeasure = visitState?.unitOfMeasure || {};

  //local state
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  //data parsing
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const handleSubmitEditing = currentFieldIndex => {
    if (HEAT_STRESS_INPUTS[currentFieldIndex + 1]?.inputRef) {
      HEAT_STRESS_INPUTS[currentFieldIndex + 1]?.inputRef?.current?.focus();
    } else {
      Keyboard.dismiss();
    }
  };

  const sectionLabel = (label, showRequiredSymbol = false) => {
    return (
      <View style={styles.container}>
        <Text style={styles.formHeading}>{label}</Text>
        {showRequiredSymbol && (
          <View style={styles.requiredLabelRow}>
            <Text style={styles.starIcon}>{i18n.t('starSign')}</Text>
            <Text style={styles.requiredLabel}>
              {i18n.t('requiredFieldMsg')}
            </Text>
          </View>
        )}
      </View>
    );
  };

  const getTemperatureField = () => {
    if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
      return i18n.t('temperature(F)');
    }
    return i18n.t('temperature(C)');
  };

  const getFieldLabel = (label, key) => {
    const el = getHeatStressFieldLabel(
      unitOfMeasure,
      key,
      label,
      selectedCurrency,
      weightUnit,
    );
    return el;
  };

  return (
    <View style={styles.parent}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.addHerdInformationForm}>
        <View style={styles.flex1}>
          {HEAT_STRESS_INPUTS.map((field, index) => {
            return (
              <View key={field.key}>
                {field.key === HEAT_STRESS_FIELDS.LACTATING_ANIMALS ||
                field.key === HEAT_STRESS_FIELDS.TEMPERATURE ||
                field.key === HEAT_STRESS_FIELDS.HOURS_OF_SUN ? (
                  sectionLabel(
                    getSectionLabel(field.key),
                    field.key === HEAT_STRESS_FIELDS.LACTATING_ANIMALS
                      ? true
                      : false,
                  )
                ) : (
                  <></>
                )}
                <View style={styles.formInputView}>
                  <NumberFormInput
                    label={
                      field?.key === HEAT_STRESS_FIELDS.TEMPERATURE
                        ? getTemperatureField()
                        : getFieldLabel(field?.label, field.key)
                    }
                    required={field?.required}
                    // newRequiredDesign
                    placeholder={field?.placeholder || 0}
                    keyboardType={getKeyboardType(field?.decimalPoints)}
                    returnKeyType={
                      index === HEAT_STRESS_INPUTS?.length - 1
                        ? DONE
                        : field?.returnKeyType
                    }
                    minValue={field?.minValue}
                    maxValue={field?.maxValue}
                    reference={field?.inputRef}
                    decimalPoints={field?.decimalPoints}
                    hasCommas={field?.hasCommas}
                    onChange={handleChange(field?.key || 0)}
                    customInputContainerStyle={styles.customInputContainerStyle}
                    textAlign={LEFT_TEXT_ALIGNMENT}
                    // value={getFormattedCommaNumber(values[field.key])}
                    value={values[field.key]}
                    onSubmitEditing={() => handleSubmitEditing(index)}
                    blurOnSubmit={false}
                    disabled={!isEditable}
                    isInteger={field?.isInteger}
                    forceOnBlur={
                      values[field?.key] === '' || values[field?.key] === null
                    }
                    onBlur={() =>
                      onBlurInput(
                        field?.key,
                        values[field?.key],
                        siteData[field?.key],
                      )
                    }
                    inputAccessoryViewID="customInputAccessoryView"
                    onFocus={() => {
                      setType(CONTENT_TYPE.NUMBER);
                      if (index === HEAT_STRESS_INPUTS?.length - 1) {
                        setAction({
                          dismiss: true,
                        });
                        return;
                      }
                      setAction({
                        currentRef:
                          HEAT_STRESS_INPUTS[index + 1]?.inputRef?.current,
                      });
                    }}
                  />
                </View>
              </View>
            );
          })}
        </View>
      </View>
    </View>
  );
};

export default HerdInformation;
