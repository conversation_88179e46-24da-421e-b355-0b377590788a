// modules
import { StyleSheet, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import ToolAlert from '../../../common/ToolAlert';

// actions
import { hideLocomotionToolToastRequest } from '../../../../../../store/actions/userPreferences';

// constants
import { TOOL_TYPES } from '../../../../../../constants/AppConstants';

// colors
import customColor from '../../../../../../constants/theme/variables/customColor';

const LocomotionPenToolToast = ({ isEditable }) => {
  const dispatch = useDispatch();

  const userPreferencesState = useSelector(state => state.userPreferences);

  const onCloseToast = () => {
    dispatch(hideLocomotionToolToastRequest());
  };

  const getToolToast = () => {
    const {
      userPreferences: { defaultValues },
    } = userPreferencesState || {};
    const { [TOOL_TYPES.LOCOMOTION_SCORE]: locomotionPenToast } =
      defaultValues || false;
    return isEditable ? locomotionPenToast : false;
  };

  return (
    <>
      {!!getToolToast() && (
        <View style={styles.alert}>
          <ToolAlert onCloseToast={onCloseToast} />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  alert: {
    backgroundColor: customColor.white,
  },
});

export default LocomotionPenToolToast;
