// modules
import { useRef, useState } from 'react';
import { View, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// helpers
import {
  pensInPercent,
  animalInPerPen,
} from '../../../../../../helpers/locomotionHelper';
import { penExistsInPublishedVisit } from '../../../../../../helpers/toolHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

// components
import CircularCounter from '../../../../../common/CircularCounter';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import { CONTENT_TYPE } from '../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../constants/AppConstants';

// styles
import styles from './styles';

// actions
import { updateSelectedPenDataRequest } from '../../../../../../store/actions/tools/locomotionScore';

const PenCategoriesList = ({ totalAnimalsRef, setIsDirty }) => {
  //refs
  const inputReferences = useRef([]);

  const dispatch = useDispatch();

  const isEditable = useSelector(state => state.visit.visit?.isEditable);
  const selectedPen = useSelector(state => state.locomotionScore.selectedPen);
  const locomotionState = useSelector(
    state => state.locomotionScore.locomotionToolData,
  );

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  // Animal observed click decrement btn when pen%  animals in per pen and animal milk loss calculated at run time
  const onDecrementClick = index => {
    setIsDirty(true);
    let categoryListState = [...selectedPen?.categories];
    categoryListState[index].animalsObserved -= 1;
    dispatch(updateSelectedPenDataRequest(categoryListState));
  };

  // Animal observed click increment btn when pen%  animals in per pen and animal milk loss calculated at run time
  const onIncrementClick = index => {
    setIsDirty(true);
    let categoryListState = [...selectedPen?.categories];
    categoryListState[index].animalsObserved += 1;
    dispatch(updateSelectedPenDataRequest(categoryListState));
  };

  const onAnimalCountChange = (index, value) => {
    setIsDirty(true);
    let categoryListState = [...selectedPen?.categories];
    categoryListState[index].animalsObserved = parseInt(value);
    dispatch(updateSelectedPenDataRequest(categoryListState));
  };

  const renderAnimalInPen = (index, categories, animalsInPen) => {
    let getAnimalsInPen = animalInPerPen(index, categories, animalsInPen);
    if (getAnimalsInPen > 0) {
      getAnimalsInPen = Number(getAnimalsInPen).toFixed(2);
      return getAnimalsInPen;
    }
    return '-';
  };

  if (
    !selectedPen ||
    !selectedPen?.categories ||
    selectedPen?.categories?.length <= 0
  ) {
    return null;
  }

  return (
    <>
      <CustomInputAccessoryView doneAction={action} type={type} />

      {selectedPen &&
        selectedPen?.categories?.map((item, index) => {
          return (
            <View key={index}>
              <View style={styles.listView}>
                <Text style={styles.catName}>
                  {i18n.t('cat')} {item.category.toFixed(1)}
                </Text>
              </View>
              <View style={styles.listChildView}>
                <View style={styles.animalsInPen}>
                  <View style={styles.listHeaderView}>
                    <Text style={styles.listHeaderText}>
                      {i18n.t('animalInPen')}
                    </Text>
                  </View>
                  <View style={styles.listValue}>
                    <Text style={styles.listViewText}>
                      {convertInputNumbersToRegionalBasis(
                        renderAnimalInPen(
                          index,
                          selectedPen?.categories,
                          selectedPen?.totalAnimalsInPen,
                        ),
                        2,
                        true,
                      )}
                    </Text>
                  </View>
                </View>
                <View style={styles.animalsInPen}>
                  <View style={styles.listHeaderView}>
                    <Text style={styles.listHeaderText}>
                      {i18n.t('pen')} {i18n.t('%')}
                    </Text>
                  </View>
                  <View style={styles.listValue}>
                    <Text style={styles.listViewText}>
                      {convertInputNumbersToRegionalBasis(
                        pensInPercent(index, selectedPen?.categories),
                      )}
                    </Text>
                  </View>
                </View>
                <View style={styles.animalObservedView}>
                  <View style={styles.listHeaderView}>
                    <Text style={styles.listHeaderText}>
                      {i18n.t('animalObserved')}
                    </Text>
                  </View>
                  <View>
                    <CircularCounter
                      disabled={!isEditable}
                      count={
                        penExistsInPublishedVisit(
                          isEditable,
                          locomotionState,
                          selectedPen,
                          (forLocomotion = true),
                        )
                          ? selectedPen?.categories[index]?.animalsObserved || 0
                          : '-'
                      }
                      onDecrementClick={() => onDecrementClick(index)}
                      onIncrementClick={() => onIncrementClick(index)}
                      onChangeText={count => onAnimalCountChange(index, count)}
                      blurOnSubmit={false}
                      showInput={true}
                      reference={input => {
                        inputReferences.current[index] = input;
                      }}
                      onSubmitEditing={() => {
                        if (index < inputReferences.current.length - 1) {
                          inputReferences?.current[index + 1]?.focus();
                        } else {
                          totalAnimalsRef?.current?.focus?.();
                        }
                      }}
                      inputAccessoryViewID="customInputAccessoryView"
                      returnKeyType={NEXT_FIELD_TEXT.NEXT}
                      onFocus={() => {
                        setType(CONTENT_TYPE.NUMBER);
                        setAction({
                          currentRef:
                            index < inputReferences.current.length - 1
                              ? inputReferences?.current[index + 1]
                              : totalAnimalsRef.current,
                        });
                      }}
                    />
                  </View>
                </View>
              </View>
            </View>
          );
        })}
    </>
  );
};

export default PenCategoriesList;
