import colors from '../../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import DeviceInfo from 'react-native-device-info';

export default {
  listView: {
    width: '100%',
    paddingVertical: normalize(15),
    backgroundColor: colors.grey7,
    borderTopColor: colors.lightWhite,
    borderTopWidth: normalize(1),
    borderBottomColor: colors.lightWhite,
    borderBottomWidth: normalize(1),
    alignItems: 'center',
  },
  catName: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  listChildView: {
    flexDirection: 'row',
    padding: normalize(10),
    paddingVertical: normalize(20),
    backgroundColor: colors.white,
  },
  animalsInPen: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    padding: normalize(5),
  },
  listHeaderText: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  listValue: {
    flexDirection: 'row',
    height: normalize(80),
    justifyCenter: 'center',
    alignItems: 'center',
  },
  listViewText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  animalObservedView: {
    flex: DeviceInfo.isTablet() ? 1 : 2,
    padding: normalize(5),
    alignItems: 'center',
  },
  listHeaderView: {
    height: normalize(35),
  },
  input: {
    width: '100%',
    height: normalize(50),
    color: colors.black,
    textAlign: 'center',
  },
};
