import DeviceInfo from 'react-native-device-info';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';
export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingLeft: normalize(10),
  },
  infoColumn: {
    flexDirection: 'column',
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: normalize(6),
  },
  statsTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  statsValue: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  leftMargin: {
    marginLeft: normalize(20),
  },
  penAnalysisTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(20),
    color: colors.alphabetIndex,
    letterSpacing: normalize(0.15),
  },
  penAnalysisValue: {
    fontFamily: fonts.HelveticaNeueBold,
  },
  dataLineColor: colors.secondary2,

  yAxisLabel: {
    fill: colors.alphabetIndex,
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(11),
  },
  extraGraphWidth: normalize(20),
  reduceGraphHeight: normalize(90),

  graphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth + 30,
    },
    android: {
      width: Platform.deviceWidth + 20,
    },
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.45
          : Platform.deviceHeight * 0.48,
    },
    android: {
      height: normalize(Platform.deviceHeight / 2),
    },
  }),

  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight + 0)
          : Platform.deviceHeight - 30,
    },
    android: {
      width: normalize(
        DeviceInfo.isTablet()
          ? Platform.deviceHeight * 0.75
          : normalize(Platform.deviceHeight) - 10,
      ),
    },
  }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 50)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.5)
          : normalize(Platform.deviceWidth - 100),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 350 : 100),
      ),
    },
  }),
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: normalize(10),
    marginHorizontal: normalize(18),
  },
};
