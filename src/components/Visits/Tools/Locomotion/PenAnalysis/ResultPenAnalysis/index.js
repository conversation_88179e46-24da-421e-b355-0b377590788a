// modules
import { useState, useEffect } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import ToolGraph from '../../../common/ToolGraph';
import CustomLineGraph from '../../../../../common/LineGraph';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import {
  DATE_FORMATS,
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
  VISIT_TABLE_FIELDS,
} from '../../../../../../constants/AppConstants';

// helpers
import { sortRecentVisitsForGraph } from '../../../../../../helpers/toolHelper';
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';
import { getFormattedDate } from '../../../../../../helpers/dateHelper';
import {
  getAllLocomotionScoreData,
  getLocomotionAvg,
  getLocomotionStdDeviation,
  getTotalCowsCountLocomotion,
  mapGraphDataForPenAnalysisExport,
  setAnimalAnalysisGraphData,
} from '../../../../../../helpers/locomotionHelper';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
  getRecentVisitsForToolRequest,
} from '../../../../../../store/actions/tool';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';
import { isOnline } from '../../../../../../services/netInfoService';
import { TOAST_TYPE } from '../../../../../../constants/FormConstants';
import { showToast } from '../../../../../common/CustomToast';

const PenAnalysisResults = ({ penData, selectedVisits }) => {
  const dispatch = useDispatch();

  const recentVisitsLoading = useSelector(
    state => state.tool.recentVisitsLoading,
  );
  const visit = useSelector(state => state.visit.visit);
  const recentVisitsState = useSelector(state => state.tool.recentVisits);
  const selectedPen = useSelector(state => state.locomotionScore.selectedPen);

  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);
  const [layout, setLayout] = useState(null);
  const [recentVisits, setRecentVisits] = useState([]);
  const [penGraphData, setPenGraphData] = useState([]);

  useEffect(() => {
    const siteId = !stringIsEmpty(visit.siteId)
      ? visit.siteId
      : visit.localSiteId;
    const accountId = !stringIsEmpty(visit.customerId)
      ? visit.customerId
      : visit.localCustomerId;

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visit.id,
        tool: VISIT_TABLE_FIELDS.LOCOMOTION_SCORE,
      }),
    );
  }, []);

  useEffect(() => {
    if (!recentVisitsLoading) {
      const data = recentVisitsState.map(visitObj => {
        const allData = visitObj?.locomotionScore
          ? getAllLocomotionScoreData(JSON.parse(visitObj?.locomotionScore))
          : [];
        if (allData?.length > 0 && selectedPen) {
          let filteredPenArray = [];
          if (!stringIsEmpty(selectedPen?.sv_id)) {
            filteredPenArray = allData.filter(
              penObj => penObj.penId === selectedPen?.sv_id,
            );
          } else {
            filteredPenArray = allData.filter(
              penObj => penObj?.penId === selectedPen?.id,
            );
          }
          if (filteredPenArray && filteredPenArray?.length > 0) {
            const currentPenObject = filteredPenArray[0];
            return {
              ...currentPenObject,
              visitId: visitObj.id,
              date: visitObj.visitDate,
            };
          }
          return { visitId: visitObj.id, date: visitObj.visitDate };
        }
        return { visitId: visitObj.id, date: visitObj.visitDate };
      });
      setRecentVisits(data);
    }
  }, [recentVisitsLoading]);

  useEffect(() => {
    let selectedRecentVisits = recentVisits.slice(1);
    selectedRecentVisits = selectedRecentVisits.filter(visit =>
      selectedVisits?.includes(visit.visitId),
    );
    selectedRecentVisits.push(penData);
    selectedRecentVisits = sortRecentVisitsForGraph(selectedRecentVisits);

    const graphData = {
      labels: [
        ...selectedRecentVisits.map(visit =>
          getFormattedDate(visit.date, DATE_FORMATS.MM_dd),
        ),
      ],
      datasets: [
        {
          data: [...selectedRecentVisits.map(visit => getLocomotionAvg(visit))],
          color: () => styles.dataLineColor,
        },
      ],
    };
    setPenGraphData(setAnimalAnalysisGraphData(selectedRecentVisits));
  }, [penData, recentVisits, selectedVisits]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const downloadPenAnalysisData = async (graphData, type, penData) => {
    if (await isOnline()) {
      const model = mapGraphDataForPenAnalysisExport(
        visit,
        graphData,
        selectedPen.penName,
        penData,
      );
      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onSharePenAnalysisData = async (
    graphData,
    type,
    penData,
    exportMethod,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForPenAnalysisExport(
        visit,
        graphData,
        selectedPen.penName,
        penData,
      );

      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_PEN_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const customGraphTitleComponent = (
    <View style={styles.infoColumn}>
      <View style={styles.statsRow}>
        <Text style={styles.statsTitle}>
          {`${i18n.t('avg')}: `}
          <Text style={styles.statsValue}>
            {convertInputNumbersToRegionalBasis(
              getLocomotionAvg(penData).toFixed(2),
              2,
              true,
            )}
          </Text>
        </Text>
        <Text style={[styles.statsTitle, styles.leftMargin]}>
          {`${i18n.t('std')}: `}
          <Text style={styles.statsValue}>
            {convertInputNumbersToRegionalBasis(
              getLocomotionStdDeviation(penData).toFixed(2),
              2,
              true,
            )}
          </Text>
        </Text>
      </View>
    </View>
  );

  const graphComponent = (
    <View>
      <View style={styles.textContainer}>
        <Text style={styles.penAnalysisTitle}>
          {i18n.t('pen') || ''}:{' '}
          <Text style={styles.penAnalysisValue}>
            {selectedPen?.penName || ''}
          </Text>
        </Text>
        <Text style={styles.penAnalysisTitle}>
          {i18n.t('animalsObserved') || ''}:{' '}
          <Text style={styles.penAnalysisValue}>
            {getTotalCowsCountLocomotion(penData)}
          </Text>
        </Text>
      </View>
      <CustomLineGraph
        verticalAxisDomain
        showVerticalYAxis={true}
        verticalAxisLabel={`${i18n.t('category')}`}
        showVictory={true}
        animate={false}
        data={penGraphData}
        width={
          landscapeModalVisible
            ? styles.graphWidthLandscape.width
            : styles.graphWidth.width
        }
        height={
          landscapeModalVisible
            ? styles.graphHeightLandscape.height
            : styles.graphHeight.height
        }
        showLabelsValue={true}
      />
    </View>
  );
  return (
    <ScrollView
      style={styles.container}
      onLayout={({ nativeEvent }) => setLayout(nativeEvent.layout)}>
      <ToolGraph
        showExpandIcon
        handleExpandIconPress={onExpandIconPress}
        showDownloadIcon={!landscapeModalVisible}
        onDownloadPress={option =>
          downloadPenAnalysisData(penGraphData, option, penData)
        }
        onSharePress={(option, exportMethod) =>
          onSharePenAnalysisData(penGraphData, option, penData, exportMethod)
        }
        showShareIcon={!landscapeModalVisible}
        landscapeModalVisible={landscapeModalVisible}
        customGraphTitleComponent={customGraphTitleComponent}
        graphComponent={layout && graphComponent}
      />
    </ScrollView>
  );
};

export default PenAnalysisResults;
