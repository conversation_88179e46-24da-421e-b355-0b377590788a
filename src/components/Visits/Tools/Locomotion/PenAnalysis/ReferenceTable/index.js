// modules
import { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';

// components
import AccordionActiveIcon from '../../../../../common/AccordionActiveIcon';
import AccordionInActiveIcon from '../../../../../common/AccordionInActiveIcon';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import { LOCOMOTION_CATEGORY_LIST } from '../../../../../../constants/AppConstants';

// helpers
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const CategoriesReferenceTable = () => {
  const selectedPen = useSelector(state => state.locomotionScore.selectedPen);

  const [accordion, setAccordion] = useState(false);

  return (
    <View style={styles.accordionView}>
      <TouchableOpacity
        onPress={() => setAccordion(!accordion)}
        style={[
          styles.accordionButton,
          !accordion ? styles.accordionClose : styles.accordionOpen,
        ]}>
        {!accordion ? <AccordionInActiveIcon /> : <AccordionActiveIcon />}

        <Text style={styles.accordionText}>{i18n.t('referenceTable')}</Text>
      </TouchableOpacity>

      <View
        style={{
          display: accordion ? 'flex' : 'none',
        }}>
        <View style={styles.tableContainer}>
          <View style={styles.tableHeaderViewContainer}>
            <View style={styles.tableLeftView}>
              <Text style={styles.tableHeaderText}>{i18n.t('cat')}</Text>
            </View>
            <View style={styles.tableRightView}>
              <Text style={styles.tableHeaderRightText}>
                {i18n.t('%')} {i18n.t('lossCow')}
              </Text>
            </View>
          </View>
          {selectedPen &&
            Object.keys(selectedPen)?.length > 0 &&
            selectedPen?.categories?.map((item, index) => (
              <View key={index} style={styles.tableHeaderViewChildContainer}>
                <View style={styles.tableLeftView}>
                  <Text style={styles.tableLeftRowText}>
                    {item.category.toFixed(1)}
                  </Text>
                </View>
                <View style={styles.tableRightView}>
                  <Text style={styles.tableRightRowText}>
                    {!stringIsEmpty(LOCOMOTION_CATEGORY_LIST[index].lossCow)
                      ? `${convertInputNumbersToRegionalBasis(
                          LOCOMOTION_CATEGORY_LIST[index].lossCow,
                        )} ${i18n.t('%')}`
                      : '-'}
                  </Text>
                </View>
              </View>
            ))}
        </View>
      </View>
    </View>
  );
};

export default CategoriesReferenceTable;
