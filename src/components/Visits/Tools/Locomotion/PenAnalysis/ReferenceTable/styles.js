import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  accordionView: {
    borderTopLeftRadius: normalize(8),
    borderTopRightRadius: normalize(8),
    borderRadius: normalize(8),
    borderColor: colors.accordionBorder,
    borderWidth: normalize(1),
    backgroundColor: colors.white,
    marginHorizontal: normalize(15),
    marginBottom: normalize(20),
  },
  accordionButton: {
    height: normalize(60),
    borderRadius: normalize(8),
    backgroundColor: colors.grey5,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: normalize(20),
  },
  accordionOpen: {
    borderRadius: normalize(0),
    borderTopLeftRadius: normalize(8),
    borderTopRightRadius: normalize(8),
  },
  accordionClose: {
    borderRadius: normalize(8),
  },
  accordionText: {
    paddingHorizontal: normalize(10),
    fontSize: normalize(14),
    fontFamily: customFont.HelveticaNeueMedium,
    color: colors.grey1,
  },
  tableContainer: {
    flex: 1,
    paddingHorizontal: normalize(20),
  },
  tableHeaderViewContainer: {
    flex: 1,
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingHorizontal: normalize(10),
    paddingVertical: normalize(15),
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightWhite,
  },
  tableHeaderViewChildContainer: {
    flex: 1,
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingHorizontal: normalize(10),
    paddingVertical: normalize(10),
    alignItems: 'center',
  },
  tableHeaderText: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueBold,
    width: '100%',
  },
  tableHeaderRightText: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueBold,
    width: '100%',
    textAlign: 'center',
  },
  tableLeftRowText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueRegular,
    width: '100%',
  },
  tableRightRowText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueRegular,
    width: '100%',
    alignItems: 'center',
    textAlign: 'center',
  },
  tableRightView: {
    flex: 1,
    marginRight: normalize(0),
  },
  tableLeftView: {
    flex: 1,
    marginRight: normalize(20),
    marginLeft: normalize(20),
  },
};
