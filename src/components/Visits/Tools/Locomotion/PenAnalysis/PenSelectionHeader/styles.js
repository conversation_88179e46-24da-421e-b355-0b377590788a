import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../constants/theme/variables/customColor';
import DeviceInfo from 'react-native-device-info';

export default {
  dropdownIcon: {
    width: normalize(12),
    height: normalize(8),
  },
  penNameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(20),
  },
  totalCowsCountView: {
    justifyContent: 'center',
  },
  totalCowsCountText: {
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.alphabetIndex,
    lineHeight: normalize(21),
    letterSpacing: 0.25,
  },
  firstDropdown: {
    width: DeviceInfo.isTablet() ? '50%' : '70%',
    borderWidth: normalize(0),
    alignSelf: 'flex-start',
  },
  customValue: {
    fontSize: normalize(14),
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.primaryMain,
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    paddingLeft: normalize(0),
    paddingRight: normalize(0),
  },
  dropdownIcon: {
    width: normalize(20),
  },
  iconStrokeColor: customColor.primaryMain,
  customFieldLabel: {
    marginBottom: normalize(0),
    height: normalize(0),
  },
};
