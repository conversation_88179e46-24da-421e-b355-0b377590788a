// modules
import { useEffect, useRef } from 'react';
import { View, Platform, ScrollView, KeyboardAvoidingView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

//lodash
import { debounce } from 'lodash';

// Reusable Components
import FromPenSetup from './FromPenSetup';
import PenCategoriesList from './PenCategories';
import PenAnalysisGraph from './ResultPenAnalysis';
import PenSelectionAndCount from './PenSelectionHeader';
import CategoriesReferenceTable from './ReferenceTable';
import LocomotionPenToolToast from './LocomotionPenToolToast';

// styles
import styles from './styles';

// helpers
import {
  saveSelectedPenInReducer,
  pickPenInReducerFromPensList,
} from '../../../../../helpers/visitHelper';
import {
  shouldEnableResultsButton,
  replaceSelectedPenInLocomotionData,
  createLocomotionPenPayload,
} from '../../../../../helpers/locomotionHelper';
import { dateHelper } from '../../../../../helpers/dateHelper';

// actions
import { resetUpdatePenRequest } from '../../../../../store/actions/pen';
import { setSelectedPenRequest } from '../../../../../store/actions/tool';
import {
  changeLocomotionPen,
  setSelectedLocomotionPenRequest,
  saveLocomotionPenAnalysisRequest,
  initializeLocomotionSelectedPenRequest,
} from '../../../../../store/actions/tools/locomotionScore';

// constants
import { TOOL_ANALYSIS_TYPES } from '../../../../../constants/AppConstants';

const PenAnalysis = props => {
  const {
    isDirty,
    setIsDirty,
    currentStep,
    totalToolSteps,
    selectedVisits,
    setEnableResults,
    healthCurrentActivePen,
  } = props;

  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit.visit);
  const pensList = useSelector(state => state.tool.pensList);
  const penUpdateSuccess = useSelector(state => state.pen.updateSuccess);
  const selectedPen = useSelector(state => state.locomotionScore.selectedPen);
  const locomotionData = useSelector(
    state => state.locomotionScore.locomotionToolData,
  );
  const { isEditable = false, unitOfMeasure, id, visitDate } = visitState;

  //refs
  const totalAnimalsRef = useRef();

  useEffect(() => {
    if (pensList?.length > 0) {
      if (healthCurrentActivePen) {
        const pen = pickPenInReducerFromPensList(
          pensList,
          healthCurrentActivePen,
          null,
          null,
          true,
        );

        console.log('if- useeffect', pen);
        dispatch(setSelectedLocomotionPenRequest(pen));
      }
    }
    dispatch(initializeLocomotionSelectedPenRequest());
  }, [pensList]);

  //saves selected pen in reducer on pen change
  useEffect(() => {
    dispatch(setSelectedPenRequest(selectedPen));
  }, [selectedPen?.id, selectedPen?.sv_id, selectedPen?.penId]);

  useEffect(() => {
    debounce_fun(selectedPen);
  }, [selectedPen]);

  // disables results button if pen's values sum to 0
  const debounce_fun = debounce(penAnalysisArray => {
    setEnableResults(
      shouldEnableResultsButton(
        TOOL_ANALYSIS_TYPES.PEN_ANALYSIS,
        penAnalysisArray,
      ),
    );
  }, 1000);

  useEffect(() => {
    if (penUpdateSuccess) {
      dispatch(resetUpdatePenRequest());
    }
  }, [penUpdateSuccess]);

  const saveLocomotionPenAnalysis = () => {
    if (isDirty && isEditable) {
      const penPayload = createLocomotionPenPayload(selectedPen, unitOfMeasure);

      const onSaveLocomotionData = replaceSelectedPenInLocomotionData(
        penPayload,
        locomotionData,
      );

      dispatch(
        saveLocomotionPenAnalysisRequest({
          locomotionScoreData: onSaveLocomotionData,
          localVisitId: id,
          updated_at: dateHelper.getUnixTimestamp(new Date()),
          mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
        }),
      );
    }
    setIsDirty(false);
  };

  const _handleChangePen = item => {
    saveLocomotionPenAnalysis();
    saveSelectedPenInReducer(dispatch, item);
    dispatch(changeLocomotionPen(item));
  };

  return (
    <>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        contentContainerStyle={{ flexGrow: 1 }}
        behavior={Platform.OS === 'ios' ? 'position' : 'padding'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : -120}>
        {currentStep === totalToolSteps ? (
          <PenAnalysisGraph
            selectedVisits={selectedVisits}
            penData={{ ...selectedPen, visitId: id, date: visitDate }}
          />
        ) : (
          <View style={styles.flexOne}>
            <ScrollView
              keyboardDismissMode="on-drag"
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}>
              <View style={styles.container}>
                <PenSelectionAndCount onChangePen={_handleChangePen} />

                <PenCategoriesList
                  setIsDirty={setIsDirty}
                  totalAnimalsRef={totalAnimalsRef}
                />

                <FromPenSetup
                  setIsDirty={setIsDirty}
                  totalAnimalsRef={totalAnimalsRef}
                />

                <CategoriesReferenceTable />
              </View>
            </ScrollView>
          </View>
        )}
      </KeyboardAvoidingView>

      <LocomotionPenToolToast isEditable={isEditable} />
    </>
  );
};
export default PenAnalysis;
