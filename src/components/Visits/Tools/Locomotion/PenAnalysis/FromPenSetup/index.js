// modules
import { useRef, useState } from 'react';
import { View, Text, Platform, Keyboard } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import NumberFormInput from '../../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// helper
import {
  onUpdatePen,
  onValueChange,
  calculateMilkLoss,
} from '../../../../../../helpers/locomotionHelper';
import {
  convertWeightToMetric,
  getWeightUnitByMeasure,
  convertWeightToImperial,
} from '../../../../../../helpers/appSettingsHelper';
import {
  removeStringCommas,
  convertStringToNumber,
  stringIsEmpty,
} from '../../../../../../helpers/alphaNumericHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

// constants
import {
  NEXT_FIELD_TEXT,
  UNIT_OF_MEASURE,
} from '../../../../../../constants/AppConstants';
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
} from '../../../../../../constants/FormConstants';

// actions
import { updatePenRequest } from '../../../../../../store/actions/pen';
import { updateSelectedPenFormData } from '../../../../../../store/actions/tools/locomotionScore';

// styles
import styles from './styles';

const FromPenSetup = ({ totalAnimalsRef, setIsDirty }) => {
  const dimRef = useRef();
  const milkProductionRef = useRef();

  const dispatch = useDispatch();

  const pensList = useSelector(state => state.tool.pensList);
  const isEditable = useSelector(state => state.visit.visit?.isEditable);
  const selectedPen = useSelector(state => state.locomotionScore.selectedPen);
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const onBlurInput = (key, value) => {
    const filteredPen = pensList.filter(
      pen => pen.sv_id === selectedPen.penId || pen.id === selectedPen.penId,
    );
    if (filteredPen && filteredPen.length > 0) {
      const initialPenObj = filteredPen[0];
      if (initialPenObj[key] == value) {
        return;
      }
      onPenSetupUpdated(initialPenObj);
    }
  };

  const onPenSetupUpdated = pen => {
    const onUpdatedPenObj = onUpdatePen(
      selectedPen?.totalAnimalsInPen,
      selectedPen?.daysInMilk,
      unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
        ? convertWeightToMetric(
            convertStringToNumber(selectedPen?.milkProductionInKg),
          )
        : convertStringToNumber(selectedPen?.milkProductionInKg),
      pen,
    );
    dispatch(updatePenRequest(onUpdatedPenObj));
  };

  // user manually change animal in per pen form pen setup calculate run time pens% and animal in per pen
  const onChangeAnimalInPerPen = value => {
    value = removeStringCommas(value);
    if (value?.length > 1 && value.indexOf('0') === 0) {
      value = value.replace('0', '');
    }
    if (onValueChange(value, 0, 9999, true, 0)) {
      setIsDirty(true);

      let stateCategoryList = selectedPen?.categories;
      stateCategoryList.map(
        a => (a.animalsInPen = ((Number(a.pens) * value) / 100).toFixed(3)),
      );

      const payload = {
        ...selectedPen,
        totalAnimalsInPen: value,
      };
      dispatch(updateSelectedPenFormData(payload));
    }
  };

  const onChangeDaysInMilk = e => {
    if (onValueChange(e, -100, 999, true, 0, true)) {
      const payload = {
        ...selectedPen,
        daysInMilk: e,
      };
      dispatch(updateSelectedPenFormData(payload));
      setIsDirty(true);
    }
  };

  const onChangeMilkProduction = e => {
    if (!stringIsEmpty(e) && onValueChange(e, 0, 999, false, 1)) {
      const payload = {
        ...selectedPen,
        milkProductionInKg: e,
      };
      dispatch(updateSelectedPenFormData(payload));
      setIsDirty(true);
    }
  };

  return (
    <View style={styles.penSetupContainer}>
      <CustomInputAccessoryView doneAction={action} type={type} />

      <View style={styles.penSetupView}>
        <Text style={styles.penSetupText}>{i18n.t('fromPenSetup')}</Text>
      </View>

      <View style={styles.penSetupChildContainer}>
        <View style={styles.penViewChildContainer}>
          <View style={styles.justifyCenter}>
            <Text style={[styles.penChildText, styles.penChildTextWidth]}>
              {i18n.t('totalAnimalsInPen')}
            </Text>
          </View>
          <NumberFormInput
            disabled={!isEditable}
            placeholder="0"
            onBlur={() =>
              onBlurInput('animals', selectedPen?.totalAnimalsInPen)
            }
            blurOnSubmit={false}
            keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
            value={selectedPen?.totalAnimalsInPen}
            hasCommas={true}
            isInteger={true}
            onChange={onChangeAnimalInPerPen}
            style={styles.input}
            reference={input => (totalAnimalsRef.current = input)}
            inputAccessoryViewID="customInputAccessoryView"
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({
                currentRef: dimRef.current,
              });
            }}
            onSubmitEditing={() => dimRef.current?.focus()}
          />
        </View>

        <View style={styles.penViewChildContainer}>
          <View style={styles.justifyCenter}>
            <Text style={styles.penChildText}>{i18n.t('DIM')}</Text>
          </View>
          <NumberFormInput
            disabled={!isEditable}
            keyboardType={
              Platform.OS === 'ios'
                ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
                : KEYBOARD_TYPE.NUMBER_PAD
            }
            onBlur={() => onBlurInput('daysInMilk', selectedPen?.daysInMilk)}
            placeholder="0"
            blurOnSubmit={false}
            onChange={onChangeDaysInMilk}
            minValue={-100}
            maxValue={999}
            isNegative={true}
            isInteger={true}
            value={selectedPen?.daysInMilk}
            style={styles.input}
            reference={input => (dimRef.current = input)}
            onSubmitEditing={() => milkProductionRef.current?.focus()}
            inputAccessoryViewID="customInputAccessoryView"
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({
                currentRef: milkProductionRef.current,
              });
            }}
          />
        </View>

        <View style={styles.penViewChildContainer}>
          <View style={styles.justifyCenter}>
            <Text style={styles.penChildText}>
              {`${i18n.t('milkProduction')} (${weightUnit})`}
            </Text>
          </View>
          <NumberFormInput
            keyboardType={KEYBOARD_TYPE.DECIMAL}
            disabled={!isEditable}
            placeholder={i18n.t('singleDecimalNumberPlaceholder')}
            onBlur={() =>
              onBlurInput(
                'milk',
                unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
                  ? convertWeightToImperial(selectedPen?.milkProductionInKg, 1)
                  : selectedPen?.milkProductionInKg,
              )
            }
            blurOnSubmit={false}
            minValue={0}
            maxValue={999}
            onChange={onChangeMilkProduction}
            value={selectedPen?.milkProductionInKg}
            decimalPoints={1}
            style={styles.input}
            reference={input => (milkProductionRef.current = input)}
            onSubmitEditing={() => Keyboard?.dismiss()}
            inputAccessoryViewID="customInputAccessoryView"
            returnKeyType={NEXT_FIELD_TEXT.DONE}
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({
                dismiss: true,
              });
            }}
          />
        </View>
        <View style={styles.penViewChildContainer}>
          <View style={styles.milkLossViewHeader}>
            <Text style={styles.penChildText}>{i18n.t('milkLoss')} </Text>
            <Text style={styles.penChildText}>({weightUnit})</Text>
          </View>
          <View style={styles.milkLossView}>
            <Text style={styles.milkLossText}>
              {convertInputNumbersToRegionalBasis(
                calculateMilkLoss(
                  selectedPen?.categories,
                  selectedPen?.milkProductionInKg,
                ),
              )}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default FromPenSetup;
