import colors from '../../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import commonColor from '../../../../../../constants/theme/variables/commonColor';

const width = commonColor.deviceWidth;

export default {
  penSetupContainer: {
    borderTopWidth: normalize(1),
    borderTopColor: colors.lightWhite,
    flex: 1,
    paddingHorizontal: normalize(10),
    paddingVertical: normalize(20),
  },
  penSetupView: {
    flex: 1,
    paddingHorizontal: normalize(15),
  },
  penSetupText: {
    color: colors.primaryMain,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  penViewChildContainer: {
    flex: 1,
    paddingVertical: normalize(5),
    marginTop: normalize(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: normalize(10),
    marginRight: normalize(5),
  },
  milkLossViewHeader: {
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
  penChildText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  input: {
    width: '100%',
    height: normalize(50),
    color: colors.black,
    textAlign: 'center',
  },
  penSetupChildContainer: {},
  milkLossView: {
    width: normalize(98),
    height: normalize(48),
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexDirection: 'row',
    paddingRight: normalize(2),
    justifyCenter: 'center',
  },
  milkLossText: {
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
    color: colors.grey1,
    width: '100%',
    textAlign: 'center',
  },
  kgsUnit: {
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueRegular,
    color: colors.grey1,
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  penChildTextWidth: {
    width: width - normalize(150),
  },
};
