import colors from '../../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  penSetupContainer: {
    borderTopWidth: normalize(1),
    borderTopColor: colors.lightWhite,
    flex: 1,
    paddingHorizontal: normalize(5),
    paddingVertical: normalize(20),
  },
  penSetupView: {
    flex: 1,
    paddingHorizontal: normalize(15),
  },
  penSetupText: {
    color: colors.primaryMain,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  penViewChildContainer: {
    flex: 1,
    paddingVertical: normalize(5),
    marginTop: normalize(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: normalize(10),
  },
  milkLossViewHeader: {
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
  penChildText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  input: {
    width: '100%',
    height: normalize(50),
    color: colors.black,
    textAlign: 'center',
  },
  milkLossView: {
    width: normalize(98),
    height: normalize(48),
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexDirection: 'row',
    paddingRight: normalize(5),
  },
  milkLossText: {
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
    color: colors.grey1,
  },
  penSetupChildContainer: {},
  justifyCenter: { justifyContent: 'center' },
};
