import DeviceInfo from 'react-native-device-info';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';
export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingLeft: normalize(10),
  },
  infoColumn: {
    flexDirection: 'column',
  },
  labelValue: {
    color: colors.alphabetIndex,
    fontSize: normalize(12),
    paddingBottom: normalize(5),
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: normalize(6),
  },
  statsTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  statsValue: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  leftMargin: {
    marginLeft: normalize(20),
  },
  penAnalysisTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(20),
    color: colors.alphabetIndex,
    letterSpacing: normalize(0.15),
  },
  penAnalysisValue: {
    fontFamily: fonts.HelveticaNeueBold,
  },
  extraGraphWidth: normalize(20),
  reduceGraphHeight: normalize(90),
  dataLineColor: colors.secondary2,
  labelsView: {
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: normalize(30),
    marginTop: normalize(10),
  },
  herdAvg: {
    width: normalize(14),
    height: normalize(14),
    borderRadius: normalize(7),
    backgroundColor: colors.secondary2,
  },
  herdGoal: {
    width: normalize(14),
    height: normalize(14),
    borderRadius: normalize(7),
    backgroundColor: colors.purple,
  },
  labelsTitle: {
    fontSize: normalize(10),
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    paddingHorizontal: normalize(10),
  },
  yAxisLabel: {
    color: colors.alphabetIndex,
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(11),
    textAlign: 'center',
  },

  graphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth + 10,
    },
    android: {
      width: Platform.deviceWidth + 20,
    },
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.38
          : Platform.deviceHeight * 0.44,
    },
    android: {
      height: normalize(Platform.deviceHeight / 2) - 50,
    },
  }),

  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight + 0)
          : Platform.deviceHeight - 20,
    },
    android: {
      width: normalize(
        DeviceInfo.isTablet()
          ? Platform.deviceHeight * 0.75
          : normalize(Platform.deviceHeight) - 25,
      ),
    },
  }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 70)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.45)
          : normalize(Platform.deviceWidth - 140),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 350 : 100),
      ),
    },
  }),
  offsetX: RNPlatform.select({
    ios: Platform.deviceWidth - 20,
    android: Platform.deviceWidth - 40,
  }),
  offsetXLandscape: RNPlatform.select({
    ios:
      Platform.deviceHeight < 700
        ? Platform.deviceHeight - 33
        : Platform.deviceHeight - 80,
    android: normalize(Platform.deviceHeight - 75),
  }),
  domainPadding: {
    x: [0, 35],
  },
  scrollView: {
    height: 80,
    paddingBottom: 8,
    backgroundColor: colors.white,
  },
};
