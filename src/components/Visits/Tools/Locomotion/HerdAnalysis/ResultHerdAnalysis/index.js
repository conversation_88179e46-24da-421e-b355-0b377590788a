// modules
import { useState, useEffect } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { VictoryLabel } from 'victory-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import ToolGraph from '../../../common/ToolGraph';
import CustomLineGraph from '../../../../../common/LineGraph';
import { showToast } from '../../../../../common/CustomToast';

// localization
import i18n from '../../../../../../localization/i18n';

//  helpers
import {
  setHerdGraphData,
  getCalculateHerdAvgLocomotionScore,
  getLocomotionHerdStdDeviation,
  mapGraphDataForHerdAnalysisExport,
} from '../../../../../../helpers/locomotionHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

// styles
import styles from './styles';

// services
import { isOnline } from '../../../../../../services/netInfoService';

// constants
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
} from '../../../../../../constants/AppConstants';
import { TOAST_TYPE } from '../../../../../../constants/FormConstants';

// actions
import {
  emailToolImageRequest,
  emailToolExcelRequest,
  downloadToolExcelRequest,
  downloadToolImageRequest,
} from '../../../../../../store/actions/tool';

const ResultHerdAnalysis = ({ selectedVisits }) => {
  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit.visit);
  const locomotionHerdData = useSelector(
    state => state.locomotionScore.herdData,
  );

  const [layout, setLayout] = useState(null);
  const [lineGraphData, setLineGraphData] = useState([]);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  useEffect(() => {
    let graphData = setHerdGraphData(locomotionHerdData);
    setLineGraphData(graphData);
  }, [selectedVisits]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const downloadHerdAnalysisData = async (graphData, type, herdData) => {
    if (await isOnline()) {
      const model = mapGraphDataForHerdAnalysisExport(
        visitState,
        graphData,
        herdData,
      );
      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareHerdAnalysisData = async (
    graphData,
    type,
    herdData,
    exportMethod,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForHerdAnalysisExport(
        visitState,
        graphData,
        herdData,
      );
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const graphComponent = (
    <ScrollView>
      <CustomLineGraph
        showYAxis={
          <VictoryLabel
            x={layout?.width / 2}
            y={
              landscapeModalVisible
                ? styles.graphHeightLandscape.height - 20
                : styles.graphHeight.height - 20
            }
            textAnchor="middle"
            style={styles.yAxisLabel}
          />
        }
        showVerticalYAxis={true}
        verticalAxisLabel={`${i18n.t('percent')} ${i18n.t('%')}`}
        showVictory={true}
        animate={false}
        data={lineGraphData}
        width={
          landscapeModalVisible
            ? styles.graphWidthLandscape.width
            : styles.graphWidth.width
        }
        height={
          landscapeModalVisible
            ? styles.graphHeightLandscape.height
            : styles.graphHeight.height
        }
        showLabelsValue={true}
        showGradient={false}
      />
      <Text style={styles.yAxisLabel}>{i18n.t('locomotionScore')}</Text>
    </ScrollView>
  );

  return (
    <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
      <View
        onLayout={({ nativeEvent }) => setLayout(nativeEvent.layout)}
        style={styles.container}>
        <ToolGraph
          showExpandIcon
          handleExpandIconPress={onExpandIconPress}
          showDownloadIcon={!landscapeModalVisible}
          showShareIcon={!landscapeModalVisible}
          landscapeModalVisible={landscapeModalVisible}
          onDownloadPress={option =>
            downloadHerdAnalysisData(lineGraphData, option, locomotionHerdData)
          }
          onSharePress={(option, exportMethod) => {
            onShareHerdAnalysisData(
              lineGraphData,
              option,
              locomotionHerdData,
              exportMethod,
            );
          }}
          customGraphTitleComponent={
            <View style={styles.infoColumn}>
              <View>
                <Text style={styles.labelValue}>
                  {' '}
                  {`${i18n.t('locomotionScore')}`}
                </Text>
              </View>
              <View style={styles.statsRow}>
                <Text style={styles.statsTitle}>
                  {`${i18n.t('avg')}: `}
                  <Text style={styles.statsValue}>
                    {convertInputNumbersToRegionalBasis(
                      getCalculateHerdAvgLocomotionScore(locomotionHerdData),
                      2,
                      true,
                    )}
                  </Text>
                </Text>
                <Text style={[styles.statsTitle, styles.leftMargin]}>
                  {`${i18n.t('std')}: `}
                  <Text style={styles.statsValue}>
                    {convertInputNumbersToRegionalBasis(
                      getLocomotionHerdStdDeviation(locomotionHerdData).toFixed(
                        2,
                      ),
                      2,
                      true,
                    )}
                  </Text>
                </Text>
              </View>
            </View>
          }
          graphComponent={layout && graphComponent}
        />
        {layout && (
          <View style={styles.labelsView}>
            <View style={styles.herdAvg}></View>
            <Text style={styles.labelsTitle}>{i18n.t('herdAverage')}</Text>
            <View style={styles.herdGoal}></View>
            <Text style={styles.labelsTitle}>{i18n.t('goal')}</Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

export default ResultHerdAnalysis;
