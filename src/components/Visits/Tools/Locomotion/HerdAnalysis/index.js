// modules
import { useEffect, useRef } from 'react';
import {
  View,
  Text,
  Platform,
  ScrollView,
  KeyboardAvoidingView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { debounce } from 'lodash';

// localization
import i18n from '../../../../../localization/i18n';

//components
import HerdSiteForm from './HerdSiteForm/index.js';
import ResultHerdAnalysis from './ResultHerdAnalysis';
import HerdAnalysisCategories from './HerdCategories/index.js';
import LocomotionHerdToolToast from './LocomotionHerdToolToast/index.js';

// styles
import styles from './styles';

// helpers
import { shouldEnableResultsButton } from '../../../../../helpers/locomotionHelper';

//actions
import { initializeLocomotionHerdAnalysisRequest } from '../../../../../store/actions/tools/locomotionScore.js';

// constants
import { TOOL_ANALYSIS_TYPES } from '../../../../../constants/AppConstants';

const HerdAnalysis = props => {
  const {
    currentStep,
    totalToolSteps,
    selectedVisits,
    setEnableResults,
    herdDataForHerdSum,
  } = props;

  const totalAnimalsRef = useRef();

  const dispatch = useDispatch();

  //redux states
  const herdData = useSelector(state => state.locomotionScore.herdData);
  const isEditable = useSelector(state => state.visit.visit?.isEditable);
  const loadingHerdData = useSelector(
    state => state.locomotionScore.loadingHerdData,
  );

  useEffect(() => {
    dispatch(initializeLocomotionHerdAnalysisRequest());
  }, []);

  useEffect(() => {
    herdDataForHerdSum.current = locomotionHerdData;
    debounce_fun(locomotionHerdData);
  }, [herdData]);

  //disables results button if herd's values sum to 0
  const debounce_fun = debounce(penAnalysisArray => {
    setEnableResults(
      shouldEnableResultsButton(
        TOOL_ANALYSIS_TYPES.HERD_ANALYSIS,
        penAnalysisArray,
      ),
    );
  }, 1000);

  return (
    <>
      {currentStep === totalToolSteps ? (
        <ResultHerdAnalysis selectedVisits={selectedVisits} />
      ) : (
        <View style={styles.flexOne}>
          <KeyboardAvoidingView
            style={styles.keyboardContainer}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={
              Platform.OS === 'ios'
                ? styles.keyboardVerticalOffsetIOS
                : styles.keyboardVerticalOffsetAndroid
            }>
            <ScrollView showsVerticalScrollIndicator={false}>
              <View style={styles.container}>
                <Text style={styles.headingText}>
                  {i18n.t('locomotionScoreAnalysis')}
                </Text>

                <HerdAnalysisCategories
                  herdData={herdData}
                  isEditable={isEditable}
                  loadingHerdData={loadingHerdData}
                  totalAnimalsRef={totalAnimalsRef}
                />

                <HerdSiteForm
                  herdData={herdData}
                  isEditable={isEditable}
                  totalAnimalsRef={totalAnimalsRef}
                  loadingHerdData={loadingHerdData}
                />
              </View>
            </ScrollView>
          </KeyboardAvoidingView>

          <LocomotionHerdToolToast isEditable={isEditable} />
        </View>
      )}
    </>
  );
};

export default HerdAnalysis;
