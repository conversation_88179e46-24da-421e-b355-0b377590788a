// modules
import { StyleSheet, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import ToolAlert from '../../../common/ToolAlert';

// actions
import { hideLocomotionToolToastRequest } from '../../../../../../store/actions/userPreferences';

// colors
import customColor from '../../../../../../constants/theme/variables/customColor';

const LocomotionHerdToolToast = ({ isEditable }) => {
  const dispatch = useDispatch();

  const userPreferencesState = useSelector(state => state.userPreferences);

  const onCloseToast = () => {
    dispatch(hideLocomotionToolToastRequest(true));
  };

  const getToolToast = () => {
    const {
      userPreferences: { defaultValues },
    } = userPreferencesState || {};
    const { ['LocomotionHerdAnalysis']: locomotionHerdToast } =
      defaultValues || false;
    return isEditable ? locomotionHerdToast : false;
  };

  return (
    <>
      {!!getToolToast() && (
        <View style={styles.alert}>
          <ToolAlert onCloseToast={onCloseToast} />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  alert: {
    backgroundColor: customColor.white,
  },
});

export default LocomotionHerdToolToast;
