import colors from '../../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  listView: {
    width: '100%',
    paddingVertical: normalize(15),
    backgroundColor: colors.grey7,
    borderTopColor: colors.lightWhite,
    borderTopWidth: normalize(1),
    borderBottomColor: colors.lightWhite,
    borderBottomWidth: normalize(1),
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  catName: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  animalObserved: {
    color: colors.alphabetIndex,
    fontSize: normalize(12),
    paddingLeft: normalize(5),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  listChildView: {
    flexDirection: 'row',
    padding: normalize(10),
    paddingVertical: normalize(20),
    backgroundColor: colors.white,
  },
  animalsInPen: {
    flex: 1,
    alignItems: 'center',
    padding: normalize(5),
  },
  listHeaderText: {
    color: colors.grey1,
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  listViewText: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueRegular,
  },
  listValue: {
    height: normalize(48),
    alignItems: 'center',
    flexDirection: 'row',
  },
  animalObservedView: {
    flex: 1,
    padding: normalize(5),
    alignItems: 'center',
  },
  listHeaderView: {
    height: normalize(35),
  },
  inputView: {
    width: normalize(98),
    height: normalize(48),
    justifyContent: 'center',
    bottom: normalize(10),
  },
  input: {
    width: '100%',
    height: normalize(50),
    color: colors.black,
    textAlign: 'center',
  },
};
