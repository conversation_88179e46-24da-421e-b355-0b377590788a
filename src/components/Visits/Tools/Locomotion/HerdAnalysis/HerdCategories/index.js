// modules
import { useRef, useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useDispatch } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// helpers
import {
  onValueChange,
  getCalculateHerdAverage,
  getCalculateTotalAnimals,
  getCategoryWiseAnimalObserved,
} from '../../../../../../helpers/locomotionHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

// components
import NumberFormInput from '../../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
} from '../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../constants/AppConstants';

// styles
import styles from './styles';

// actions
import { updateLocomotionHerdAnalysisData } from '../../../../../../store/actions/tools/locomotionScore';

const HerdAnalysisCategories = ({
  totalAnimalsRef,
  isEditable,
  herdData,
  loadingHerdData,
}) => {
  const inputRef = useRef([]);

  const dispatch = useDispatch();

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const onChangeGoal = (value, index) => {
    let data = { ...herdData };
    if (onValueChange(value)) {
      data.categories[index].herdGoal = value;
      dispatch(updateLocomotionHerdAnalysisData(data));
    }
  };

  const renderHerdAvg = item => {
    let herdAvg = getCalculateHerdAverage(item, herdData);
    if (herdAvg <= 0) {
      return '-';
    } else {
      return herdAvg;
    }
  };

  const renderTotalAnimals = item => {
    let calculateTotalAnimals = getCalculateTotalAnimals(
      item,
      herdData,
      herdData?.totalAnimalsInHerd,
    );
    if (calculateTotalAnimals <= 0) {
      return '-';
    } else {
      return calculateTotalAnimals;
    }
  };

  if (loadingHerdData) {
    return <ActivityIndicator size={'small'} />;
  }

  if (!herdData || !herdData?.categories || herdData?.categories?.length <= 0) {
    return null;
  }

  return (
    <>
      <CustomInputAccessoryView doneAction={action} type={type} />

      {herdData?.categories?.map((item, index) => {
        return (
          <View key={index}>
            <View style={styles.listView}>
              <Text style={styles.catName}>
                {`${i18n.t('cat')} ${item.category}`}
              </Text>
              <Text style={styles.animalObserved}>
                {`(${i18n.t('animalObserved')}: ${getCategoryWiseAnimalObserved(
                  item,
                  herdData,
                )})`}
              </Text>
            </View>
            <View style={styles.listChildView}>
              <View style={styles.animalObservedView}>
                <View style={styles.listHeaderView}>
                  <Text style={styles.listHeaderText}>
                    {i18n.t('herdGoal')} ({i18n.t('%')})
                  </Text>
                </View>

                <View style={styles.inputView}>
                  <NumberFormInput
                    disabled={!isEditable}
                    keyboardType={KEYBOARD_TYPE.DECIMAL}
                    onChange={text => onChangeGoal(text, index)}
                    decimalPoints={2}
                    value={String(item?.herdGoal)}
                    style={styles.input}
                    reference={e => {
                      inputRef.current[index] = e;
                    }}
                    minValue={0}
                    maxValue={100}
                    blurOnSubmit={false}
                    onSubmitEditing={() => {
                      if (index < inputRef.current.length - 1) {
                        inputRef?.current[index + 1]?.focus();
                      } else {
                        totalAnimalsRef?.current?.focus?.();
                      }
                    }}
                    inputAccessoryViewID="customInputAccessoryView"
                    returnKeyType={NEXT_FIELD_TEXT.NEXT}
                    onFocus={() => {
                      setType(CONTENT_TYPE.NUMBER);
                      setAction({
                        currentRef:
                          index < inputRef.current.length - 1
                            ? inputRef?.current[index + 1]
                            : totalAnimalsRef.current,
                      });
                    }}
                  />
                </View>
              </View>
              <View style={styles.animalsInPen}>
                <View style={styles.listHeaderView}>
                  <Text style={styles.listHeaderText}>
                    {i18n.t('herdAverage')} ({i18n.t('%')})
                  </Text>
                </View>
                <View style={styles.listValue}>
                  <Text style={styles.listViewText}>
                    {convertInputNumbersToRegionalBasis(renderHerdAvg(item), 2)}
                  </Text>
                </View>
              </View>
              <View style={styles.animalsInPen}>
                <View style={styles.listHeaderView}>
                  <Text style={styles.listHeaderText}>
                    {i18n.t('totalAnimals')}
                  </Text>
                </View>
                <View style={styles.listValue}>
                  <Text style={styles.listViewText}>
                    {convertInputNumbersToRegionalBasis(
                      renderTotalAnimals(item),
                      0,
                      true,
                    )}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        );
      })}
    </>
  );
};

export default HerdAnalysisCategories;
