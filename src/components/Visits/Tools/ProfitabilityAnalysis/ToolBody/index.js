// modules
import React from 'react';
import { useSelector } from 'react-redux';

// components
import ProfitabilityAnalysisResultsBody from '../ToolResultsBody';
import ProfitabilityAnalysisFormBody from '../ToolFormBody';

// constants
import { PROFITABILITY_TOOL_BODY } from '../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';

const ProfitabilityAnalysisToolBody = ({ formRef, scrollViewRef }) => {
  const activeToolBody = useSelector(
    state => state.profitabilityAnalysis.activeToolBody,
  );

  switch (activeToolBody) {
    case PROFITABILITY_TOOL_BODY.FORM:
      return (
        <ProfitabilityAnalysisFormBody
          formRef={formRef}
          scrollViewRef={scrollViewRef}
        />
      );

    case PROFITABILITY_TOOL_BODY.RESULTS:
      return <ProfitabilityAnalysisResultsBody />;

    default:
      return <></>;
  }
};

export default ProfitabilityAnalysisToolBody;
