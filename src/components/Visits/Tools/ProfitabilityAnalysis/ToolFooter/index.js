// modules
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// components
import StepsFooter from '../../common/ToolStepsFooter';

// constants
import { HEAT_STRESS_STEPS } from '../../../../../constants/AppConstants';
import { PROFITABILITY_TOOL_BODY } from '../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';

// actions
import {
  changeProfitabilityFormBody,
  saveOrUpdateProfitabilityDataInDBRequest,
} from '../../../../../store/actions/tools/profitabilityAnalysis';

const ProfitabilityAnalysisFooter = ({ formRef, scrollViewRef }) => {
  const dispatch = useDispatch();
  const { goBack } = useNavigation();

  const isFormValid = useSelector(
    state => state.profitabilityAnalysis?.isFormValid,
  );
  const activeToolBody = useSelector(
    state => state.profitabilityAnalysis.activeToolBody,
  );
  const currentActiveTool = useSelector(state => state.tool.currentActiveTool);

  // save form data in visit table and move to results screen
  const _handleResultsButtonPress = () => {
    if (activeToolBody === PROFITABILITY_TOOL_BODY.FORM) {
      // action for moving to results screen
      dispatch(changeProfitabilityFormBody(PROFITABILITY_TOOL_BODY.RESULTS));

      // action to save tool data in database
      dispatch(
        saveOrUpdateProfitabilityDataInDBRequest(formRef?.current?.values),
      );
    } else {
      goBack();
    }

    scrollViewRef?.current?.scrollTo({ y: 0, animated: true });
  };

  // function to return to form screen from results
  const _handleReturnFormScreen = () =>
    dispatch(changeProfitabilityFormBody(PROFITABILITY_TOOL_BODY.FORM));

  // it works same as above but it's used in bottom sheet to navigate
  const _handleChangeStepFromBottomSheet = ({ step }) => {
    dispatch(
      changeProfitabilityFormBody(
        step === 1
          ? PROFITABILITY_TOOL_BODY.FORM
          : PROFITABILITY_TOOL_BODY.RESULTS,
      ),
    );
  };

  const currentStep = activeToolBody === PROFITABILITY_TOOL_BODY.FORM ? 1 : 2;

  return (
    <StepsFooter
      herdAnalysis={true}
      isValid={isFormValid}
      currentStep={currentStep}
      totalSteps={2}
      currentActiveTool={currentActiveTool}
      onLeftArrowClick={_handleReturnFormScreen}
      onResultPress={_handleResultsButtonPress}
      bottomSheetSteps={HEAT_STRESS_STEPS}
      onDropdownStepSelect={_handleChangeStepFromBottomSheet}
      disableBottomSheet={
        activeToolBody === PROFITABILITY_TOOL_BODY.FORM ? true : false
      }
    />
  );
};

export default ProfitabilityAnalysisFooter;
