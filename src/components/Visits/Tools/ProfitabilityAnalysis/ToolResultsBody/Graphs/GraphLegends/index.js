// modules
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

// styling constants
import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../../constants/theme/variables/customColor';

// constants
import { PROFITABILITY_GRAPH_LEGENDS } from '../../../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';

// localization
import i18n from '../../../../../../../localization/i18n';

const ProfitabilityGraphLegend = ({ selectedGraph, currencySymbol }) => {
  const legends = PROFITABILITY_GRAPH_LEGENDS.find(
    item => item.key === selectedGraph?.key,
  );

  // temporary checking for last graph and showing the currency
  const extraSecondLabel =
    legends.key === 'RPCPDxTDC'
      ? `(${currencySymbol}/${i18n.t('cow')}/${i18n.t('day')})`
      : '';

  const extraFirstLabe =
    legends.key === 'MPxFCPLM' ? ` (${currencySymbol})` : '';

  return (
    <View style={styles.container}>
      <View style={styles.legendItem}>
        <View style={styles.legendCircle} />
        <Text style={styles.legendText}>
          {legends?.firstLegend + extraFirstLabe}
        </Text>
      </View>

      <View style={styles.legendItem}>
        <View style={[styles.legendCircle, styles.emptyCircle]} />
        <Text style={styles.legendText}>
          {legends?.secondLegend + extraSecondLabel}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: '90%',
    marginHorizontal: normalize(20),
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: normalize(20),
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendCircle: {
    width: normalize(10),
    height: normalize(10),
    borderRadius: normalize(50),
    backgroundColor: customColor.topColor,
    marginRight: normalize(12),
  },
  emptyCircle: {
    backgroundColor: customColor.transparent,
    borderWidth: 1,
    borderColor: customColor.legendCircleColor,
  },
  legendText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(15),
    color: customColor.grey1,
    letterSpacing: 0.15,
  },
});

export default ProfitabilityGraphLegend;
