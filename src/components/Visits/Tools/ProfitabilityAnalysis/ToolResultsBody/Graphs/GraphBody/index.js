// modules
import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

// components
import ProfitabilityGraphLegend from '../GraphLegends';
import BarAndLineGraph from './profitabilityGraphs';

// helpers
import { getSelectedGraphData } from '../../../../../../../helpers/profitabilityAnalysis';
import { getCurrencyForTools } from '../../../../../../../helpers/appSettingsHelper';

const GraphBody = ({ selectedGraph, isLandscape, selectedRecentVisits }) => {
  const profitabilityToolState = useSelector(
    state => state.profitabilityAnalysis?.profitabilityToolState,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);

  const selectedCurrency = selectedRecentVisits[0]?.selectedCurrency || null;

  // get currency form selected currency
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  const { lineGraphData, graphConfig, barGraphData } = getSelectedGraphData(
    selectedGraph,
    selectedRecentVisits,
    profitabilityToolState,
    currencySymbol,
  );

  return (
    <ScrollView>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.container}
        contentContainerStyle={styles.containerStyle}>
        <BarAndLineGraph
          isLandscape={isLandscape}
          lineGraphData={lineGraphData}
          graphConfig={graphConfig}
          barGraphData={barGraphData}
        />
      </ScrollView>
      <ProfitabilityGraphLegend
        selectedGraph={selectedGraph}
        currencySymbol={currencySymbol}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  containerStyle: {
    flexGrow: 1,
  },
});

export default GraphBody;
