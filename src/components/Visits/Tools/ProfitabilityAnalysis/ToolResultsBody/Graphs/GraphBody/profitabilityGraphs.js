// modules
import React from 'react';
import { View, StyleSheet, useWindowDimensions } from 'react-native';
import {
  Bar,
  VictoryAxis,
  VictoryBar,
  VictoryChart,
  VictoryLabel,
  VictoryLine,
  VictoryScatter,
} from 'victory-native';

// styling constants
import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../../constants/theme/variables/customColor';

// helpers
import { mapProfitabilityGraphDataToVictoryNative } from '../../../../../../../helpers/profitabilityAnalysis';
import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';

const BarAndLineGraph = ({
  isLandscape,
  graphConfig,
  lineGraphData,
  barGraphData,
}) => {
  const { width } = useWindowDimensions();

  const data = mapProfitabilityGraphDataToVictoryNative(
    barGraphData,
    lineGraphData,
  );

  // adding temporary condition because of single visit to show pointer label
  if (data?.length === 1) {
    data.push({
      date: data[0]?.date,
      leftAxisValue: null,
      rightAxisValue: null,
    });
  }

  return (
    <View
      style={{
        height: isLandscape ? normalize(320) : normalize(350),
        width: '100%',
      }}>
      {/* First chart for Bars (Production data) */}
      <View style={styles.container}>
        <VictoryChart
          domainPadding={10}
          height={normalize(320)}
          width={isLandscape ? width - 100 : width}
          padding={
            isLandscape ? styles.graphPaddingLandscape : styles.graphPadding
          }>
          {/* X-axis */}
          <VictoryAxis
            tickValues={data.map(item => item.date)}
            tickFormat={data.map(item => item.date)}
            domainPadding={{ x: [10, 10] }}
            style={{
              axis: styles.axisStyles,
              tickLabels: {
                ...styles.horizontalLabels,
              },
            }}
          />

          {/* Y-axis for Production */}
          <VictoryAxis
            dependentAxis
            tickValues={graphConfig?.leftAxisTicks}
            label={graphConfig?.leftAxisLabel}
            axisLabelComponent={<VictoryLabel dy={-8} />}
            style={{
              ticks: { stroke: 'transparent' },
              axis: styles.axisStyles,
              tickLabels: {
                ...styles.horizontalLabels,
              },
              axisLabel: styles.axisLabelStyles,
            }}
          />

          {/* Bar chart */}
          <VictoryBar
            data={data}
            x="date"
            barWidth={20}
            y={datum => datum?.leftAxisValue}
            style={{
              data: {
                ...styles.areaDataStyles,
              },
            }}
            domainPadding={{ x: [5, 5] }}
            dataComponent={<Bar />}
            labelComponent={<VictoryLabel style={styles.barTopLabel} dy={-5} />}
            labels={({ datum }) =>
              !!datum?.leftAxisValue > 0
                ? convertInputNumbersToRegionalBasis(
                    parseFloat(datum?.leftAxisValue)?.toFixed(2),
                    2,
                  )
                : ''
            }
          />
        </VictoryChart>
      </View>

      {/* Second chart for Line (Ratio data) */}
      <View style={styles.container}>
        <VictoryChart
          domainPadding={isLandscape ? 25 : 10}
          height={normalize(320)}
          width={isLandscape ? width - 100 : width}
          padding={
            isLandscape ? styles.graphPaddingLandscape : styles.lineGraphPadding
          }>
          {/* Y-axis for Ratio */}
          <VictoryAxis
            offsetX={isLandscape ? null : 50}
            dependentAxis
            tickValues={graphConfig?.rightAxisTicks}
            label={graphConfig?.rightAxisLabel}
            axisLabelComponent={<VictoryLabel dy={5} />}
            style={{
              ticks: { stroke: 'transparent' },
              axis: styles.axisStyles,
              tickLabels: {
                ...styles.horizontalLabels,
              },
              axisLabel: styles.axisLabelStyles,
            }}
            orientation="right"
          />

          {/* Line chart */}
          <VictoryLine
            data={data}
            x="date"
            y={datum => datum?.rightAxisValue || null}
            style={{
              data: { ...styles.lineStyles },
              labels: {
                ...styles.dotNumber,
              },
            }}
            labels={({ datum }) =>
              !!datum?.rightAxisValue > 0
                ? convertInputNumbersToRegionalBasis(
                    parseFloat(datum?.rightAxisValue)?.toFixed(2),
                    2,
                  )
                : ''
            }
            labelComponent={
              <VictoryLabel
                dy={20}
                dx={({ index }) => {
                  if (lineGraphData?.length === 1) return 0; // shifting label component to center if only single visit
                  if (index == 0) return 10; // Shift first label right
                  if (index == data.length - 1) return -10; // Shift last label left
                  return 0; // No shift for middle labels
                }}
              />
            }
          />

          {/* showing pointer for line graph */}
          <VictoryScatter
            x="date"
            y={datum => datum?.rightAxisValue || null}
            data={data}
            size={4}
            style={{
              data: {
                fill: customColor.legendCircleColor,
              },
            }}
          />
        </VictoryChart>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  graphPadding: {
    top: 20,
    left: 50,
    right: 50,
    bottom: 25,
  },
  graphPaddingLandscape: {
    top: 0,
    left: 60,
    right: 40,
    bottom: 30,
  },
  lineGraphPadding: {
    top: 20,
    left: 57,
    right: 57,
    bottom: 25,
  },
  horizontalLabels: {
    fontFamily: customFont.HelveticaNeueRegular,
    letterSpacing: normalize(0.15),
    fill: customColor.alphabetIndex,
    fontSize: normalize(11),
    lineHeight: normalize(13),
    bottom: 20,
  },
  transparentValue: {
    fill: customColor.transparent,
  },
  axisStyles: {
    stroke: customColor.grey1,
    strokeWidth: 1,
    strokeDasharray: '1, 5',
  },
  areaDataStyles: {
    fill: customColor.topColor,
  },
  barTopLabel: {
    fontWeight: '700',
    fontFamily: customFont.HelveticaNeueRegular,
    letterSpacing: normalize(0.5),
    fill: customColor.alphabetIndex,
    fontSize: normalize(10),
    lineHeight: normalize(12),
  },
  axisLabelStyles: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(11),
    lineHeight: normalize(13),
    fill: customColor.alphabetIndex,
    // padding: DeviceInfo.isTablet() ? normalize(15) : normalize(30),
  },
  lineStyles: {
    stroke: customColor.legendCircleColor,
    strokeWidth: normalize(2),
  },
  dotNumber: {
    fontWeight: '700',
    fontFamily: customFont.HelveticaNeueBold,
    fontSize: normalize(10),
    lineHeight: normalize(12),
    fill: customColor.legendCircleColor,
    letterSpacing: normalize(0.5),
    color: customColor.legendCircleColor,
  },
});
export default BarAndLineGraph;
