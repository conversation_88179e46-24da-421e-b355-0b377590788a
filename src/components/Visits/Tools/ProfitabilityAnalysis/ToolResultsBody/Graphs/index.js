// modules
import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import GraphBody from './GraphBody';
import ToolGraph from '../../../common/ToolGraph';
import GraphHeaderSelection from './GraphHeaderSelection';

// styling constants
import { normalize } from '../../../../../../constants/theme/variables/customFont';

// constants
import { PROFITABILITY_GRAPH_TYPES } from '../../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';

// actions
import {
  downloadProfitabilityGraphsRequest,
  shareProfitabilityGraphsRequest,
} from '../../../../../../store/actions/tools/profitabilityAnalysis';

// helpers
import { getCurrencyForTools } from '../../../../../../helpers/appSettingsHelper';

const ProfitabilityGraphs = ({ selectedRecentVisits }) => {
  const dispatch = useDispatch();

  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const [isLandscape, setIsLandscape] = useState(false);
  const [selectedGraph, setSelectedGraph] = useState(
    PROFITABILITY_GRAPH_TYPES[0],
  );

  const _handlePressExpand = () => {
    setIsLandscape(!isLandscape);
  };

  // get current active currency symbol to show in fields label
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  const _handlePressShare = async (option, exportMethod) => {
    const payload = {
      exportType: option,
      exportMethod,
      selectedGraph,
      selectedRecentVisits,
      currencySymbol,
    };

    dispatch(shareProfitabilityGraphsRequest(payload));
  };

  const _handlePressDownload = option => {
    const payload = {
      exportType: option,
      selectedGraph,
      selectedRecentVisits,
      currencySymbol,
    };

    dispatch(downloadProfitabilityGraphsRequest(payload));
  };

  const _handleSelectGraph = graphType => {
    setSelectedGraph(graphType);
  };

  return (
    <View style={styles.container}>
      <ToolGraph
        showExpandIcon
        showDownloadIcon={!isLandscape}
        showShareIcon={!isLandscape}
        landscapeModalVisible={isLandscape}
        handleExpandIconPress={_handlePressExpand}
        onDownloadPress={_handlePressDownload}
        onSharePress={_handlePressShare}
        customContainerStyles={
          isLandscape
            ? styles.headerContainerStylesLandscape
            : styles.headerContainerStyles
        }
        customGraphTitleComponent={
          <GraphHeaderSelection
            isLandscape={isLandscape}
            selectedGraph={selectedGraph}
            _handleSelectGraph={_handleSelectGraph}
          />
        }
        graphComponent={
          <GraphBody
            selectedGraph={selectedGraph}
            isLandscape={isLandscape}
            selectedRecentVisits={selectedRecentVisits}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginVertical: normalize(10),
  },
  headerContainerStyles: {
    alignItems: 'center',
    marginTop: normalize(0),
  },
  headerContainerStylesLandscape: {
    alignItems: 'center',
    marginTop: normalize(10),
  },
});

export default ProfitabilityGraphs;
