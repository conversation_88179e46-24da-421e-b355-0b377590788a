// modules
import React from 'react';
import { StyleSheet, View } from 'react-native';

// components
import CustomBottomSheet from '../../../../../../common/CustomBottomSheet';

// constants
import { BOTTOM_SHEET_TYPE } from '../../../../../../../constants/FormConstants';
import { PROFITABILITY_GRAPH_TYPES } from '../../../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';

// localization
import i18n from '../../../../../../../localization/i18n';

// styling constants
import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../../constants/theme/variables/customColor';

const GraphHeaderSelection = ({
  selectedGraph,
  _handleSelectGraph,
  isLandscape,
}) => {
  if (isLandscape) return <View />;

  return (
    <CustomBottomSheet
      value={selectedGraph?.key || ''}
      type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
      selectLabel={i18n.t('selectOne')}
      data={PROFITABILITY_GRAPH_TYPES || []}
      searchPlaceHolder={i18n.t('search')}
      onChange={_handleSelectGraph}
      customLabelStyle={styles.customLabelStyle}
      customInputStyle={styles.customInputStyles}
      customValueStyle={styles.customValueStyles}
      customIconStyles={styles.dropdownIcon}
      iconStroke={styles.iconStrokeColor}
      selectFieldProps={{
        numberOfLines: 1,
      }}
    />
  );
};

const styles = StyleSheet.create({
  customLabelStyle: {
    display: 'none',
    marginTop: 0,
    paddingTop: 0,
  },
  customInputStyles: {
    height: normalize(40),
    width: '110%',
    borderWidth: 0,
  },
  customValueStyles: {
    fontSize: normalize(14),
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.primaryMain,
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    paddingLeft: normalize(0),
    paddingRight: normalize(0),
  },
  dropdownIcon: {
    width: normalize(20),
  },
  iconStrokeColor: customColor.primaryMain,
});

export default GraphHeaderSelection;
