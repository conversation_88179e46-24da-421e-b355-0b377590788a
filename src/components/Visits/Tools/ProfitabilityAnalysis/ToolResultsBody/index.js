// modules
import React, { useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import reusableStyles from '../ToolFormBody/styles';

// components
import ToolBottomTabs from '../../common/ToolBottomTabs';
import ProfitabilityGraph from './Graphs';
import ProfitabilitySummary from './Summary';

// constants
import {
  TOOL_RESULTS_TABS,
  VISIT_TABLE_FIELDS,
} from '../../../../../constants/AppConstants';
import { PROFITABILITY_RESULTS_TABS } from '../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';

// actions
import { changeProfitabilityResultsTab } from '../../../../../store/actions/tools/profitabilityAnalysis';
import { getRecentVisitsForToolRequest } from '../../../../../store/actions/tool';

// helpers
import { sortRecentVisitsForGraph } from '../../../../../helpers/toolHelper';
import { getSiteAndAccountIdFromVisit } from '../../../../../helpers/visitHelper';

const ProfitabilityAnalysisResultsBody = () => {
  const dispatch = useDispatch();

  const activeResultsTab = useSelector(
    state => state.profitabilityAnalysis.activeResultsTab,
  );
  const visitState = useSelector(state => state.visit?.visit);

  useEffect(() => {
    const { siteId = '', accountId = '' } =
      getSiteAndAccountIdFromVisit(visitState);

    dispatch(
      getRecentVisitsForToolRequest({
        siteId: siteId,
        accountId: accountId,
        localVisitId: visitState?.id,
        tool: VISIT_TABLE_FIELDS.PROFITABILITY_ANALYSIS,
      }),
    );
  }, []);

  const _handleChangeResultsTab = tab => {
    dispatch(changeProfitabilityResultsTab(tab));
  };

  return (
    <View style={reusableStyles.formContainer}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        keyboardDismissMode="on-drag"
        keyboardShouldPersistTaps="handled">
        <ProfitabilityActiveResultBody activeResultsTab={activeResultsTab} />
      </ScrollView>

      <ToolBottomTabs
        tabs={PROFITABILITY_RESULTS_TABS}
        onTabChange={_handleChangeResultsTab}
        selectedTab={activeResultsTab}
      />
    </View>
  );
};

const ProfitabilityActiveResultBody = ({ activeResultsTab }) => {
  const recentVisits = useSelector(state => state.tool.recentVisits);
  const comparingVisits = useSelector(
    state => state.profitabilityAnalysis.comparingVisits,
  );

  // comparing data
  let selectedRecentVisits = recentVisits?.filter(visit =>
    comparingVisits?.includes(visit?.visitId || visit?.id),
  );
  selectedRecentVisits = sortRecentVisitsForGraph(selectedRecentVisits);

  switch (activeResultsTab?.key) {
    case TOOL_RESULTS_TABS.SUMMARY:
      return (
        <ProfitabilitySummary selectedRecentVisits={selectedRecentVisits} />
      );

    case TOOL_RESULTS_TABS.GRAPH:
      return <ProfitabilityGraph selectedRecentVisits={selectedRecentVisits} />;

    default:
      return null;
  }
};

export default ProfitabilityAnalysisResultsBody;
