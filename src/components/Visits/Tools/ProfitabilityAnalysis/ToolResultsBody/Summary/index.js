// modules
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

// components
import RecentVisitsSummaryList from './VisitsSummaryList';
import ProfitabilityOutput from './ProfitabilityOutput';

// helpers
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';
import { getSummaryHeaderAndRowData } from '../../../../../../helpers/profitabilityAnalysis';

const ProfitabilitySummary = ({ selectedRecentVisits }) => {
  const profitabilityToolState = useSelector(
    state => state.profitabilityAnalysis?.profitabilityToolState,
  );
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const waterQualityEnums = useSelector(
    state => state.enums.enum?.waterQuality,
  );

  // get current active currency symbol to show in fields label
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  // get unit of measure to show as fields label
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const {
    tableHeaders,
    animalInformation,
    milkingInformation,
    feedingInformation,
    othersInformation,
  } = useMemo(
    () =>
      getSummaryHeaderAndRowData(
        profitabilityToolState,
        selectedRecentVisits,
        unitOfMeasure,
        currencySymbol,
        waterQualityEnums,
      ),
    [selectedRecentVisits],
  );

  if (!profitabilityToolState) return null;

  return (
    <>
      <ProfitabilityOutput
        profitabilityToolState={profitabilityToolState}
        currencySymbol={currencySymbol}
        weightUnit={weightUnit}
      />

      <RecentVisitsSummaryList
        tableHeaders={tableHeaders}
        animalInformation={animalInformation}
        milkingInformation={milkingInformation}
        feedingInformation={feedingInformation}
        othersInformation={othersInformation}
      />
    </>
  );
};

export default ProfitabilitySummary;
