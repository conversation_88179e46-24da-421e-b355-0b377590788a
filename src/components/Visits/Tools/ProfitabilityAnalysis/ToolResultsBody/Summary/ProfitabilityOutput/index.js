// modules
import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';

// localization
import i18n from '../../../../../../../localization/i18n';

// constants
import { PROFITABILITY_ANALYSIS_FIELDS } from '../../../../../../../constants/FormConstants';

// styling constants
import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../../constants/theme/variables/customColor';

// components
import Accordion from '../../../../../../common/Accordion';

// helpers
import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';

const ProfitabilityOutput = ({
  profitabilityToolState,
  currencySymbol,
  weightUnit,
}) => {
  const [showOutputs, setShowOutputs] = useState(true);

  const _handlePressAccordion = () => {
    setShowOutputs(!showOutputs);
  };

  const milkLitersPerKgConcentrate = isFinite(
    profitabilityToolState[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION] /
      profitabilityToolState[
        PROFITABILITY_ANALYSIS_FIELDS.CONCENTRATE_TOTAL_CONSUMED
      ],
  )
    ? profitabilityToolState[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION] /
      profitabilityToolState[
        PROFITABILITY_ANALYSIS_FIELDS.CONCENTRATE_TOTAL_CONSUMED
      ]
    : '';

  return (
    <Accordion
      title={i18n.t('outputs')}
      isActive={showOutputs}
      handleAccordionPress={_handlePressAccordion}>
      <OutputItem
        label={i18n.t('animalInHerd')}
        value={
          convertInputNumbersToRegionalBasis(
            profitabilityToolState[
              PROFITABILITY_ANALYSIS_FIELDS.ANIMALS_IN_HERD
            ],
            null,
            true,
          ) || '-'
        }
      />

      <OutputItem
        label={`${i18n.t('currentMilkPrice')} (${currencySymbol})`}
        value={
          convertInputNumbersToRegionalBasis(
            profitabilityToolState[PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE],
            3,
            true,
          ) || '-'
        }
      />

      <OutputItem
        label={`${i18n.t('totalProductionHerd')} (${weightUnit}/${i18n.t(
          'day',
        )})`}
        value={
          convertInputNumbersToRegionalBasis(
            profitabilityToolState[
              PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION_HERD
            ],
            2,
            true,
          ) || '-'
        }
      />

      <OutputItem
        label={`${i18n.t('totalDietCost')} (${currencySymbol}/${i18n.t(
          'cow',
        )}/${i18n.t('day')})`}
        value={
          convertInputNumbersToRegionalBasis(
            profitabilityToolState[
              PROFITABILITY_ANALYSIS_FIELDS.TOTAL_DIET_COST
            ],
            2,
            true,
          ) || '-'
        }
      />

      <OutputItem
        label={`${i18n.t('totalProduction')} (${weightUnit}/${i18n.t(
          'cow',
        )}/${i18n.t('day')})`}
        value={
          convertInputNumbersToRegionalBasis(
            profitabilityToolState[
              PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION
            ],
            2,
            true,
          ) || '-'
        }
      />

      <OutputItem
        label={i18n.t('milkLitersPerKgConcentrate')}
        value={
          milkLitersPerKgConcentrate
            ? convertInputNumbersToRegionalBasis(
                milkLitersPerKgConcentrate,
                2,
                true,
              )
            : '-'
        }
      />

      <OutputItem
        label={i18n.t('revenuePerCowPerDay')}
        value={convertInputNumbersToRegionalBasis(
          profitabilityToolState[
            PROFITABILITY_ANALYSIS_FIELDS.REVENUE_PER_COW_PER_DAY
          ].toFixed(2),
          2,
          true,
        )}
      />

      <OutputItem
        label={i18n.t('feedConcentrate')}
        value={
          convertInputNumbersToRegionalBasis(
            profitabilityToolState[
              PROFITABILITY_ANALYSIS_FIELDS.CONCENTRATE_TOTAL_CONSUMED
            ],
            2,
            true,
          ) || '-'
        }
      />

      <OutputItem
        label={i18n.t('IOFC')}
        value={
          convertInputNumbersToRegionalBasis(
            profitabilityToolState[
              PROFITABILITY_ANALYSIS_FIELDS.REVENUE_PER_COW_PER_DAY
            ] -
              profitabilityToolState[
                PROFITABILITY_ANALYSIS_FIELDS.TOTAL_DIET_COST
              ],
            2,
            true,
          ) || '-'
        }
      />

      <OutputItem
        label={`${i18n.t('totalDietCost')}/ ${i18n.t('revenue')} (%)`}
        value={
          convertInputNumbersToRegionalBasis(
            (profitabilityToolState[
              PROFITABILITY_ANALYSIS_FIELDS.TOTAL_DIET_COST
            ] /
              profitabilityToolState[
                PROFITABILITY_ANALYSIS_FIELDS.REVENUE_PER_COW_PER_DAY
              ]) *
              100,
            2,
            true,
          ) || '-'
        }
      />
    </Accordion>
  );
};

const OutputItem = ({ label, value }) => {
  return (
    <View style={styles.itemContainer}>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.value}>{value}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    marginVertical: normalize(10),
    marginHorizontal: normalize(1),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    fontWeight: '400',
    fontSize: normalize(14),
    fontFamily: customFont.HelveticaNeueRegular,
    lineHeight: normalize(22),
    letterSpacing: normalize(0.2),
    color: customColor.grey1,
  },
  value: {
    fontWeight: '500',
    fontSize: normalize(14),
    fontFamily: customFont.HelveticaNeueRegular,
    lineHeight: normalize(18),
    letterSpacing: normalize(0.2),
    color: customColor.grey1,
  },
});

export default ProfitabilityOutput;
