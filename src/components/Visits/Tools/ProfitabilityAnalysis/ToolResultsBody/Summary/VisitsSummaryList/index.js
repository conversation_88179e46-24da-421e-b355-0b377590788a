// modules
import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import Animated, { FadeInUp, SlideInLeft } from 'react-native-reanimated';

// localization
import i18n from '../../../../../../../localization/i18n';

// components
import ColumnItem from './ColumnItem';

// styling constants
import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../../constants/theme/variables/customColor';

const RecentVisitsSummaryList = ({
  tableHeaders,
  animalInformation,
  milkingInformation,
  feedingInformation,
  othersInformation,
}) => {
  // table header of visit dates
  const renderTableHeaders = () => {
    if (tableHeaders?.length > 0) {
      return tableHeaders?.map((item, index) => (
        <Animated.View
          entering={FadeInUp.delay(200).duration(300)}
          key={`${item}_${index}`}
          style={[styles.headerBox, index == 0 && styles.initialHeaderBox]}>
          <Text style={styles.headerTitle}>{item || ''}</Text>
        </Animated.View>
      ));
    }

    return <></>;
  };

  // table data for animal information rows and columns
  const renderAnimalInformation = () => {
    if (animalInformation) {
      const animalValues = Object.values(animalInformation);
      const animalKeys = Object.keys(animalInformation);

      return animalValues?.map((animal, index) => (
        <View key={animalKeys[index]} style={styles.flexRow}>
          {animal.map((value, itemIndex) => (
            <ColumnItem
              key={`${value}_${itemIndex}`}
              index={`${value}_${itemIndex}`}
              itemIndex={itemIndex}
              value={value}
            />
          ))}
        </View>
      ));
    }

    return <></>;
  };

  // table data for animal information rows and columns
  const renderMilkingInformation = () => {
    if (milkingInformation) {
      const milkingValues = Object.values(milkingInformation);
      const milkingKeys = Object.keys(milkingInformation);

      return milkingValues?.map((milk, index) => (
        <View key={milkingKeys[index]} style={styles.flexRow}>
          {milk.map((value, itemIndex) => (
            <ColumnItem
              key={`${value}_${itemIndex}`}
              index={index}
              itemIndex={itemIndex}
              value={value}
            />
          ))}
        </View>
      ));
    }

    return <></>;
  };

  // table data for feeding information rows and columns
  const renderFeedingInformation = () => {
    if (feedingInformation) {
      const feedingValues = Object.values(feedingInformation);
      const feedingKeys = Object.keys(feedingInformation);

      return feedingValues?.map((feed, index) => (
        <View key={feedingKeys[index]} style={styles.flexRow}>
          {feed.map((value, itemIndex) => (
            <ColumnItem
              key={`${value}_${itemIndex}`}
              index={index}
              itemIndex={itemIndex}
              value={value}
            />
          ))}
        </View>
      ));
    }

    return <></>;
  };

  // table data for other information rows and columns
  const renderOtherInformation = () => {
    if (othersInformation) {
      const otherValues = Object.values(othersInformation);
      const otherKeys = Object.keys(othersInformation);

      return otherValues?.map((other, index) => (
        <View key={otherKeys[index]} style={styles.flexRow}>
          {other.map((value, itemIndex) => (
            <ColumnItem
              key={`${value}_${itemIndex}`}
              index={index}
              itemIndex={itemIndex}
              value={value}
            />
          ))}
        </View>
      ));
    }

    return <></>;
  };
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.container}
      contentContainerStyle={styles.flexGrow}>
      <View>
        <Animated.View
          style={styles.headerContainer}
          entering={SlideInLeft.duration(300)}>
          {renderTableHeaders()}
        </Animated.View>

        <View style={styles.tableBody}>
          <View style={styles.tableRowLabelContainer}>
            {renderAnimalInformation()}
          </View>

          <Text style={styles.separatorText}>{i18n.t('milkInformation')}</Text>

          <View style={styles.tableRowLabelContainer}>
            {renderMilkingInformation()}
          </View>

          <Text style={styles.separatorText}>
            {i18n.t('feedingInformation')}
          </Text>

          <View style={styles.tableRowLabelContainer}>
            {renderFeedingInformation()}
          </View>

          <Text style={styles.separatorText}>
            {i18n.t('comfortAndWellBeing')}
          </Text>

          <View style={styles.tableRowLabelContainer}>
            {renderOtherInformation()}
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flexGrow: {
    flexGrow: 1,
  },
  flexRow: {
    flexDirection: 'row',
  },
  headerContainer: {
    height: normalize(44),
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: normalize(20),
    marginHorizontal: normalize(16),
    borderRadius: normalize(4),
    backgroundColor: customColor.primaryMain,
  },
  headerBox: {
    width: normalize(88),
  },
  initialHeaderBox: {
    width: normalize(143),
  },
  headerTitle: {
    fontFamily: customFont.HelveticaNeueMedium,
    fontSize: normalize(13),
    lineHeight: normalize(14),
    color: customColor.white,
    textAlign: 'center',
  },
  tableBody: {
    paddingHorizontal: normalize(16),
  },
  tableRowLabelContainer: {
    // flexDirection: 'row',
  },
  separatorText: {
    marginTop: normalize(5),
    fontWeight: '500',
    fontFamily: customFont.HelveticaNeueMedium,
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: normalize(0.2),
    color: customColor.primaryMain,
  },
});

export default RecentVisitsSummaryList;
