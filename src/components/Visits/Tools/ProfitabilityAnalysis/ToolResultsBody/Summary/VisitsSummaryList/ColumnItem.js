// modules
import React from 'react';
import { StyleSheet } from 'react-native';
import Animated, { FadeInUp, SlideInLeft } from 'react-native-reanimated';

// helpers
import { getCommaSeparatedValues } from '../../../../../../../helpers/genericHelper';

// constants
import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../../constants/theme/variables/customColor';

const ColumnItem = ({ itemIndex, index, value }) => {
  let decimalsAllowed = 0;
  //for en
  if (value && value?.toString()?.split('.')?.length > 0) {
    decimalsAllowed = value?.toString()?.split('.')?.[1]?.length || 0;
  }

  const formattedNumber =
    parseFloat(value) !== NaN
      ? getCommaSeparatedValues(value, decimalsAllowed)
      : value;

  return (
    <Animated.View
      key={`${itemIndex}_${index}_${formattedNumber}`}
      style={[
        styles.rowDataBox,
        itemIndex === 0 && styles.rowLabelBox,
        itemIndex === 1 && styles.currentRowBox,
      ]}
      entering={SlideInLeft.delay(50 * itemIndex)}>
      <Animated.Text
        style={[styles.rowDataText, itemIndex === 0 && styles.rowLabelText]}
        entering={FadeInUp.delay(100 * itemIndex)}>
        {formattedNumber}
      </Animated.Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  rowLabelBox: {
    width: normalize(143),
    backgroundColor: customColor.primaryMain,
    marginTop: normalize(8),
    height: normalize(48),
    justifyContent: 'center',
    paddingHorizontal: normalize(8),
    borderTopLeftRadius: normalize(4),
    borderBottomLeftRadius: normalize(4),
  },
  rowLabelText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(18),
    letterSpacing: normalize(0.2),
    color: customColor.white,
    textAlign: 'left',
  },
  rowDataBox: {
    width: normalize(88),
    height: normalize(48),
    backgroundColor: customColor.grey10,
    marginTop: normalize(8),
    justifyContent: 'center',
  },
  currentRowBox: {
    backgroundColor: customColor.currentBoxColor,
  },
  rowDataText: {
    fontFamily: customFont.HelveticaNeueMedium,
    fontSize: normalize(13),
    lineHeight: normalize(14),
    letterSpacing: normalize(0.2),
    color: customColor.grey9,
    textAlign: 'center',
  },
});

export default ColumnItem;
