// modules
import React, { useEffect } from 'react';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import ToolsInProgress from '../../common/ToolsInProgress';

// constants
import { PROFITABILITY_TOOL_BODY } from '../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';

// actions
import { setProfitabilityComparingVisits } from '../../../../../store/actions/tools/profitabilityAnalysis';

const ProfitabilityAnalysisToolHeader = () => {
  const dispatch = useDispatch();

  const recentVisits = useSelector(state => state.tool?.recentVisits);
  const visitState = useSelector(state => state.visit?.visit);
  const activeToolBody = useSelector(
    state => state.profitabilityAnalysis.activeToolBody,
  );
  const comparingVisits = useSelector(
    state => state.profitabilityAnalysis.comparingVisits,
  );

  useEffect(() => {
    // get all id's of recent visit so that results screen showing comparison of all visits
    const payload = recentVisits?.map(visit => visit?.id) || [];
    dispatch(setProfitabilityComparingVisits(payload));
  }, [recentVisits]);

  const _handleSelectComparingVisits = visits =>
    dispatch(setProfitabilityComparingVisits(visits));

  return (
    <>
      {activeToolBody === PROFITABILITY_TOOL_BODY.FORM && (
        <View style={{ height: 1 }} />
      )}
      <ToolsInProgress
        initialStep={1}
        currentStep={2}
        header={activeToolBody === PROFITABILITY_TOOL_BODY.FORM ? true : false}
        hideInProgressToolsCount={true}
        showCompareGraph={true}
        currentVisit={visitState?.id}
        isEditable={visitState?.isEditable}
        compareModalData={recentVisits || []}
        selectedVisits={comparingVisits}
        onConfirmCompareModal={_handleSelectComparingVisits}
      />
    </>
  );
};

export default ProfitabilityAnalysisToolHeader;
