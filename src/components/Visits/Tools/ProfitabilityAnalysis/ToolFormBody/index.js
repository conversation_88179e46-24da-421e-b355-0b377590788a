// modules
import React, { useEffect, useState } from 'react';
import { View, KeyboardAvoidingView, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Formik } from 'formik';

// components
import ToggleFormValidation from '../ToolFormBody/ToggleFormValidation';
import AnimalInputsForm from '../ToolFormBody/AnimalInputsForm';
import MilkInformationForm from '../ToolFormBody/MilkInformationForm';
import UpdateSiteButton from '../ToolFormBody/UpdateSiteButton';
import FeedingInformationForm from '../ToolFormBody/FeedingInformationForm';

// helpers
import { initialProfitabilityFormData } from '../../../../../helpers/profitabilityAnalysis';
import ProfitabilityValidationSchema from '../../../../../helpers/validation/profitabilityAnalysis';

// actions
import { saveOrUpdateProfitabilityDataInDBRequest } from '../../../../../store/actions/tools/profitabilityAnalysis';

// styles
import reusableStyles from './styles';

const ProfitabilityAnalysisFormBody = ({ formRef, scrollViewRef }) => {
  const dispatch = useDispatch();

  const siteData = useSelector(state => state.tool?.siteData);
  const heatStressData = useSelector(state => state.visit.visit?.heatStress);
  const weightUnit = useSelector(state => state.visit.visit?.unitOfMeasure);
  const profitabilityAnalysis = useSelector(
    state => state.visit.visit?.profitabilityAnalysis,
  );
  const isEditable =
    useSelector(state => state.visit.visit?.isEditable) || false;

  const [formValues, setFormValues] = useState(initialProfitabilityFormData);

  useEffect(() => {
    if (siteData) {
      const initialFormValues = initialProfitabilityFormData(
        siteData,
        profitabilityAnalysis,
        heatStressData,
        weightUnit,
      );

      setFormValues(initialFormValues);
    }
  }, [siteData]);

  const _handleSubmitForm = values => {
    // action to save tool data in database
    dispatch(saveOrUpdateProfitabilityDataInDBRequest(values));
  };

  return (
    <View style={reusableStyles.formContainer}>
      <Formik
        innerRef={formRef}
        initialValues={formValues}
        enableReinitialize={true}
        validateOnMount={true}
        validateOnChange={true}
        validationSchema={ProfitabilityValidationSchema}
        onSubmit={_handleSubmitForm}>
        {({ values, handleChange, setFieldValue, setValues, errors }) => {
          return (
            <KeyboardAvoidingView
              style={reusableStyles.formContainer}
              behavior={reusableStyles.paddingBehavior}
              keyboardVerticalOffset={reusableStyles.keyboardVerticalOffset}>
              <ScrollView
                ref={scrollViewRef}
                showsVerticalScrollIndicator={false}
                keyboardDismissMode="on-drag"
                keyboardShouldPersistTaps="handled">
                <ToggleFormValidation />

                <AnimalInputsForm
                  values={values}
                  errors={errors}
                  handleChange={handleChange}
                  setFieldValue={setFieldValue}
                  setValues={setValues}
                  isEditable={isEditable}
                  scrollTo={scrollViewRef.current?.scrollTo}
                />

                <MilkInformationForm
                  values={values}
                  errors={errors}
                  handleChange={handleChange}
                  setFieldValue={setFieldValue}
                  setValues={setValues}
                  isEditable={isEditable}
                  scrollTo={scrollViewRef.current?.scrollTo}
                />

                <UpdateSiteButton isEditable={isEditable} />

                <FeedingInformationForm
                  values={values}
                  errors={errors}
                  handleChange={handleChange}
                  setFieldValue={setFieldValue}
                  setValues={setValues}
                  isEditable={isEditable}
                  scrollTo={scrollViewRef.current?.scrollTo}
                />
              </ScrollView>
            </KeyboardAvoidingView>
          );
        }}
      </Formik>
    </View>
  );
};

export default ProfitabilityAnalysisFormBody;
