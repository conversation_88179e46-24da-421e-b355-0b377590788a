// modules
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Animated from 'react-native-reanimated';

// components
import Accordion from '../../../../../common/Accordion';
import CommercialConcentrateAndMineralBaseMixFields from './CommercialConcentrateAndMineralBaseMix';
import QuickSwitchToggles from './QuickSwitchToggles';
import ConcentrateAndQualityFields from './ConcentrateAndQualityFields';
import TemperatureAndCostFields from './TemperatureAndCostFields';

// constants
import { PROFITABILITY_FORM_TYPES } from '../../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';

// localization
import i18n from '../../../../../../localization/i18n';

// actions
import { changeProfitabilityFormAccordion } from '../../../../../../store/actions/tools/profitabilityAnalysis';

const FeedingInformationForm = props => {
  const dispatch = useDispatch();

  const activeInputAccordion = useSelector(
    state => state.profitabilityAnalysis.activeInputAccordion,
  );

  const _handlePressFeedingInputAccordion = () => {
    dispatch(
      changeProfitabilityFormAccordion(
        PROFITABILITY_FORM_TYPES.FEEDING_INFORMATION,
      ),
    );

    props?.scrollTo({ y: 150, animated: true });
  };

  const showForm =
    PROFITABILITY_FORM_TYPES.FEEDING_INFORMATION === activeInputAccordion
      ? true
      : false;

  return (
    <Accordion
      title={i18n.t('feedingInformation')}
      isActive={showForm}
      handleAccordionPress={_handlePressFeedingInputAccordion}>
      {showForm && (
        <Animated.View>
          <CommercialConcentrateAndMineralBaseMixFields {...props} />

          <QuickSwitchToggles {...props} />

          <ConcentrateAndQualityFields {...props} />

          <TemperatureAndCostFields {...props} />
        </Animated.View>
      )}
    </Accordion>
  );
};

export default FeedingInformationForm;
