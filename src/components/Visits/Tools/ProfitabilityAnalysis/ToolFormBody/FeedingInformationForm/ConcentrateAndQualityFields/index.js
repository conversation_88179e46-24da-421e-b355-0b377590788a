// modules
import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

// components
import NumberFormInput from '../../../../../../common/NumberFormInput';
import CustomBottomSheet from '../../../../../../common/CustomBottomSheet';

// localization
import i18n from '../../../../../../../localization/i18n';

// constants
import {
  BOTTOM_SHEET_TYPE,
  KEYBOARD_TYPE,
  PROFITABILITY_ANALYSIS_FIELDS,
} from '../../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../../constants/AppConstants';

// styles
import reusableStyles from '../../../ToolFormBody/styles';

const ConcentrateAndQualityFields = ({
  values,
  handleChange,
  setFieldValue,
  isEditable,
}) => {
  const profitabilityQualityAnalysisEnums = useSelector(
    state => state.enums.enum?.profitabilityQualityAnalysis,
  );
  const waterQuality = useSelector(state => state.enums.enum?.waterQuality);
  const productionSystem = useSelector(
    state => state.enums.enum?.productionSystem,
  );

  const showBeddingQuality = [
    productionSystem[0]?.key || null,
    productionSystem[1]?.key || null,
  ].includes(values?.[PROFITABILITY_ANALYSIS_FIELDS.PRODUCTION_SYSTEM]);

  return (
    <>
      <View style={reusableStyles.spacer} />

      <NumberFormInput
        hasCommas
        maxLength={9}
        decimalPoints={3}
        maxValue={999999}
        disabled={!isEditable}
        textAlign={'left'}
        label={i18n.t('concentrateTotalConsumed')}
        placeholder={i18n.t('twoNumberPlaceholder')}
        keyboardType={KEYBOARD_TYPE.DECIMAL}
        returnKeyType={NEXT_FIELD_TEXT.DONE}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.CONCENTRATE_TOTAL_CONSUMED]}
        onChange={handleChange(
          PROFITABILITY_ANALYSIS_FIELDS.CONCENTRATE_TOTAL_CONSUMED,
        )}
        customInputContainerStyle={reusableStyles.customInputContainerStyle}
        customContainerStyle={reusableStyles.customContainerStyle}
        customInputStyle={reusableStyles.customInputStyle}
      />

      <View style={reusableStyles.spacer20} />

      <CustomBottomSheet
        infoText={' '}
        disabled={!isEditable}
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        label={i18n.t('silage')}
        placeholder={i18n.t('selectOne')}
        selectLabel={i18n.t('selectOne')}
        data={profitabilityQualityAnalysisEnums || []}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.SILAGE]}
        onChange={silage =>
          setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.SILAGE, silage?.key)
        }
      />

      <View style={reusableStyles.spacer20} />

      <CustomBottomSheet
        infoText={' '}
        disabled={!isEditable}
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        label={i18n.t('haylage')}
        placeholder={i18n.t('selectOne')}
        selectLabel={i18n.t('selectOne')}
        data={profitabilityQualityAnalysisEnums || []}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.HAYLAGE]}
        onChange={haylage =>
          setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.HAYLAGE, haylage?.key)
        }
      />

      <View style={reusableStyles.spacer20} />

      <CustomBottomSheet
        infoText={' '}
        disabled={!isEditable}
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        label={i18n.t('hay')}
        placeholder={i18n.t('selectOne')}
        selectLabel={i18n.t('selectOne')}
        data={profitabilityQualityAnalysisEnums || []}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.HAY]}
        onChange={hay =>
          setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.HAY, hay?.key)
        }
      />

      <View style={reusableStyles.spacer20} />

      <CustomBottomSheet
        infoText={' '}
        disabled={!isEditable}
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        label={i18n.t('Pasture')}
        placeholder={i18n.t('selectOne')}
        selectLabel={i18n.t('selectOne')}
        data={profitabilityQualityAnalysisEnums || []}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.PASTURE]}
        onChange={pasture =>
          setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.PASTURE, pasture?.key)
        }
      />

      <View style={reusableStyles.spacer20} />

      <CustomBottomSheet
        infoText={' '}
        disabled={!isEditable}
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        label={i18n.t('waterQuality')}
        placeholder={i18n.t('selectOne')}
        selectLabel={i18n.t('selectOne')}
        data={waterQuality || []}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.WATER_QUALITY]}
        onChange={quality =>
          setFieldValue(
            PROFITABILITY_ANALYSIS_FIELDS.WATER_QUALITY,
            quality?.key,
          )
        }
      />

      {showBeddingQuality && (
        <>
          <View style={reusableStyles.spacer20} />

          <CustomBottomSheet
            infoText={' '}
            disabled={!isEditable}
            type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
            label={i18n.t('beddingQuality')}
            placeholder={i18n.t('selectOne')}
            selectLabel={i18n.t('selectOne')}
            data={waterQuality || []}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.BEDDING_QUALITY]}
            onChange={quality =>
              setFieldValue(
                PROFITABILITY_ANALYSIS_FIELDS.BEDDING_QUALITY,
                quality?.key,
              )
            }
          />
        </>
      )}
    </>
  );
};

export default ConcentrateAndQualityFields;
