// modules
import React from 'react';

// components
import SwitchButton from '../../../../../../common/SwitchButton';

// localization
import i18n from '../../../../../../../localization/i18n';

// styles
import reusableStyles from '../../../ToolFormBody/styles';

// constants
import { PROFITABILITY_ANALYSIS_FIELDS } from '../../../../../../../constants/FormConstants';

const QuickSwitchToggles = ({ values, setFieldValue, isEditable }) => (
  <>
    <SwitchButton
      label={i18n.t('nutritek')}
      defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.NUTRITEK]}
      onChangeSelection={selectionState =>
        setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.NUTRITEK, selectionState)
      }
      containerStyles={reusableStyles.switchButtonContainer}
      disabled={!isEditable}
    />

    <SwitchButton
      label={i18n.t('xpcUltra')}
      defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.XPC_ULTRA]}
      onChangeSelection={selectionState =>
        setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.XPC_ULTRA, selectionState)
      }
      containerStyles={reusableStyles.switchButtonContainer}
      disabled={!isEditable}
    />

    <SwitchButton
      label={i18n.t('actiforBoost')}
      defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.ACTIFOR_BOOST]}
      onChangeSelection={selectionState =>
        setFieldValue(
          PROFITABILITY_ANALYSIS_FIELDS.ACTIFOR_BOOST,
          selectionState,
        )
      }
      containerStyles={reusableStyles.switchButtonContainer}
      disabled={!isEditable}
    />

    <SwitchButton
      label={i18n.t('buffer')}
      defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.BUFFER]}
      onChangeSelection={selectionState =>
        setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.BUFFER, selectionState)
      }
      containerStyles={reusableStyles.switchButtonContainer}
      disabled={!isEditable}
    />

    <SwitchButton
      label={i18n.t('nutrigorduraLac')}
      defaultSwitchState={
        values[PROFITABILITY_ANALYSIS_FIELDS.NUTRIGORDURA_LAC]
      }
      onChangeSelection={selectionState =>
        setFieldValue(
          PROFITABILITY_ANALYSIS_FIELDS.NUTRIGORDURA_LAC,
          selectionState,
        )
      }
      containerStyles={reusableStyles.switchButtonContainer}
      disabled={!isEditable}
    />

    <SwitchButton
      label={i18n.t('ice')}
      defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.ICE]}
      onChangeSelection={selectionState =>
        setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.ICE, selectionState)
      }
      containerStyles={reusableStyles.switchButtonContainer}
      disabled={!isEditable}
    />

    <SwitchButton
      label={i18n.t('energyIce')}
      defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.ENERGY_ICE]}
      onChangeSelection={selectionState =>
        setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.ENERGY_ICE, selectionState)
      }
      containerStyles={reusableStyles.switchButtonContainer}
      disabled={!isEditable}
    />

    <SwitchButton
      label={i18n.t('monensin')}
      defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.MONENSIN]}
      onChangeSelection={selectionState =>
        setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.MONENSIN, selectionState)
      }
      containerStyles={reusableStyles.switchButtonContainer}
      disabled={!isEditable}
    />

    <SwitchButton
      label={i18n.t('soyPassBR')}
      defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.SOY_PASS_BR]}
      onChangeSelection={selectionState =>
        setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.SOY_PASS_BR, selectionState)
      }
      containerStyles={reusableStyles.switchButtonContainer}
      disabled={!isEditable}
    />
  </>
);

export default QuickSwitchToggles;
