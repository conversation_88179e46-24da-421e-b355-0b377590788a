// modules
import React, { useRef } from 'react';
import { Keyboard, View } from 'react-native';
import { useSelector } from 'react-redux';

// components
import SwitchButton from '../../../../../../common/SwitchButton';
import NumberFormInput from '../../../../../../common/NumberFormInput';

// styles
import reusableStyles from '../../../ToolFormBody/styles';

// localization
import i18n from '../../../../../../../localization/i18n';

// constants
import {
  KEYBOARD_TYPE,
  PROFITABILITY_ANALYSIS_FIELDS,
} from '../../../../../../../constants/FormConstants';
import {
  NEXT_FIELD_TEXT,
  UNIT_OF_MEASURE,
} from '../../../../../../../constants/AppConstants';

// helpers
import { getCurrencyForTools } from '../../../../../../../helpers/appSettingsHelper';

const TemperatureAndCostFields = ({
  values,
  errors,
  handleChange,
  setFieldValue,
  isEditable,
}) => {
  const inputRefs = useRef([]);

  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );
  const productionSystem = useSelector(
    state => state.enums.enum?.productionSystem,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const weightUnit = useSelector(state => state.visit.visit?.unitOfMeasure);

  // get current active currency symbol to show in fields label
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  // explicitly checking for first 2 enums of production system, that should always be in following order
  // 1. free stall
  // 2. Compost Barn
  const showCowLayingDown = [
    productionSystem[0]?.key || null,
    productionSystem[1]?.key || null,
  ].includes(values?.[PROFITABILITY_ANALYSIS_FIELDS.PRODUCTION_SYSTEM]);

  return (
    <>
      <SwitchButton
        label={i18n.t('ventilation')}
        defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.VENTILATION]}
        onChangeSelection={selection =>
          setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.VENTILATION, selection)
        }
        containerStyles={reusableStyles.switchButtonContainer}
        disabled={!isEditable}
      />

      <SwitchButton
        label={i18n.t('sprinkler')}
        defaultSwitchState={values[PROFITABILITY_ANALYSIS_FIELDS.SPRINKLER]}
        onChangeSelection={selection =>
          setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.SPRINKLER, selection)
        }
        containerStyles={reusableStyles.switchButtonContainer}
        disabled={!isEditable}
      />

      <View style={reusableStyles.spacer} />

      <NumberFormInput
        hasCommas
        minValue={0}
        maxValue={weightUnit === UNIT_OF_MEASURE.IMPERIAL ? 212 : 100}
        decimalPoints={2}
        blurOnSubmit={false}
        disabled={!isEditable}
        textAlign={'left'}
        label={i18n.t(
          weightUnit === UNIT_OF_MEASURE.IMPERIAL
            ? 'temperature(F)'
            : 'temperature(C)',
        )}
        placeholder={i18n.t('twoNumberPlaceholder')}
        keyboardType={KEYBOARD_TYPE.DECIMAL}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.TEMPERATURE_IN_C]}
        onChange={handleChange(PROFITABILITY_ANALYSIS_FIELDS.TEMPERATURE_IN_C)}
        onSubmitEditing={() => inputRefs.current[0].focus()}
        customInputContainerStyle={reusableStyles.customInputContainerStyle}
        customContainerStyle={reusableStyles.customContainerStyle}
        customInputStyle={reusableStyles.customInputStyle}
      />

      <View style={reusableStyles.spacer20} />

      <NumberFormInput
        required
        hasCommas
        minValue={0}
        maxValue={100}
        decimalPoints={2}
        textAlign={'left'}
        blurOnSubmit={false}
        disabled={!isEditable}
        label={i18n.t('airRU')}
        placeholder={i18n.t('twoNumberPlaceholder')}
        keyboardType={KEYBOARD_TYPE.DECIMAL}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.AIR_RU_PERCENTAGE]}
        error={errors[PROFITABILITY_ANALYSIS_FIELDS.AIR_RU_PERCENTAGE]}
        onChange={handleChange(PROFITABILITY_ANALYSIS_FIELDS.AIR_RU_PERCENTAGE)}
        reference={reference => (inputRefs.current[0] = reference)}
        onSubmitEditing={() => inputRefs.current[1].focus()}
        customInputContainerStyle={reusableStyles.customInputContainerStyle}
        customContainerStyle={reusableStyles.customContainerStyle}
        customInputStyle={reusableStyles.customInputStyle}
      />

      <View style={reusableStyles.spacer20} />

      <NumberFormInput
        hasCommas
        minValue={0}
        maxValue={100}
        decimalPoints={2}
        textAlign={'left'}
        blurOnSubmit={false}
        disabled={!isEditable}
        label={i18n.t('THI')}
        placeholder={i18n.t('twoNumberPlaceholder')}
        keyboardType={KEYBOARD_TYPE.DECIMAL}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.THI]}
        onChange={handleChange(PROFITABILITY_ANALYSIS_FIELDS.THI)}
        reference={reference => (inputRefs.current[1] = reference)}
        onSubmitEditing={() => inputRefs.current[2].focus()}
        customInputContainerStyle={reusableStyles.customInputContainerStyle}
        customContainerStyle={reusableStyles.customContainerStyle}
        customInputStyle={reusableStyles.customInputStyle}
      />

      <View style={reusableStyles.spacer20} />

      <NumberFormInput
        hasCommas
        minValue={0}
        maxValue={1000}
        decimalPoints={2}
        textAlign={'left'}
        blurOnSubmit={false}
        disabled={!isEditable}
        label={i18n.t('respiratoryMovement')}
        placeholder={i18n.t('twoNumberPlaceholder')}
        keyboardType={KEYBOARD_TYPE.DECIMAL}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.RESPIRATORY_MOVEMENT]}
        onChange={handleChange(
          PROFITABILITY_ANALYSIS_FIELDS.RESPIRATORY_MOVEMENT,
        )}
        reference={reference => (inputRefs.current[2] = reference)}
        onSubmitEditing={() =>
          inputRefs.current[showCowLayingDown ? 3 : 4].focus()
        }
        customInputContainerStyle={reusableStyles.customInputContainerStyle}
        customContainerStyle={reusableStyles.customContainerStyle}
        customInputStyle={reusableStyles.customInputStyle}
      />

      {showCowLayingDown && (
        <>
          <View style={reusableStyles.spacer20} />

          <NumberFormInput
            hasCommas
            minValue={0}
            maxValue={100}
            decimalPoints={2}
            textAlign={'left'}
            blurOnSubmit={false}
            disabled={!isEditable}
            label={i18n.t('cowLayingDown')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.DECIMAL}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={
              values[PROFITABILITY_ANALYSIS_FIELDS.COW_LAYING_DOWN_PERCENTAGE]
            }
            onChange={handleChange(
              PROFITABILITY_ANALYSIS_FIELDS.COW_LAYING_DOWN_PERCENTAGE,
            )}
            reference={reference => (inputRefs.current[3] = reference)}
            onSubmitEditing={() => inputRefs.current[4].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
            customInputStyle={reusableStyles.customInputStyle}
          />
        </>
      )}

      <View style={reusableStyles.spacer20} />

      <NumberFormInput
        hasCommas
        minValue={0}
        maxValue={1000}
        decimalPoints={2}
        textAlign={'left'}
        blurOnSubmit={false}
        disabled={!isEditable}
        label={`${i18n.t('totalDietCost')} (${currencySymbol}/${i18n.t(
          'cow',
        )}/${i18n.t('day')})`}
        placeholder={i18n.t('twoNumberPlaceholder')}
        keyboardType={KEYBOARD_TYPE.DECIMAL}
        returnKeyType={NEXT_FIELD_TEXT.DONE}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_DIET_COST]}
        onChange={handleChange(PROFITABILITY_ANALYSIS_FIELDS.TOTAL_DIET_COST)}
        reference={reference => (inputRefs.current[4] = reference)}
        onSubmitEditing={() => Keyboard.dismiss()}
        customInputContainerStyle={reusableStyles.customInputContainerStyle}
        customContainerStyle={reusableStyles.customContainerStyle}
        customInputStyle={reusableStyles.customInputStyle}
        customLabelStyle={reusableStyles.customLabelStyle}
      />

      <View style={reusableStyles.spacer20} />

      <NumberFormInput
        hasCommas
        disabled
        textAlign={'left'}
        blurOnSubmit={false}
        label={i18n.t('revenuePerCowPerDay')}
        placeholder={i18n.t('twoNumberPlaceholder')}
        keyboardType={KEYBOARD_TYPE.DECIMAL}
        returnKeyType={NEXT_FIELD_TEXT.DONE}
        value={values[PROFITABILITY_ANALYSIS_FIELDS.REVENUE_PER_COW_PER_DAY]}
        customInputContainerStyle={[
          reusableStyles.customInputContainerStyle,
          reusableStyles.disabledInputColor,
        ]}
        customContainerStyle={reusableStyles.customContainerStyle}
        customInputStyle={reusableStyles.customInputStyle}
        decimalPoints={2}
      />
    </>
  );
};

export default TemperatureAndCostFields;
