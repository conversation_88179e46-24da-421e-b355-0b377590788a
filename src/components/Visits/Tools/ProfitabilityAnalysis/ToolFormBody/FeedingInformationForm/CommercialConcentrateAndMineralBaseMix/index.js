// modules
import React from 'react';
import { useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../../localization/i18n';

// constants
import {
  KEYBOARD_TYPE,
  PROFITABILITY_ANALYSIS_FIELDS,
} from '../../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../../constants/AppConstants';

// styles
import reusableStyles from '../../../ToolFormBody/styles';

// components
import SwitchButton from '../../../../../../common/SwitchButton';
import NumberFormInput from '../../../../../../common/NumberFormInput';

// helpers
import { getWeightUnitByMeasure } from '../../../../../../../helpers/appSettingsHelper';

const CommercialConcentrateAndMineralBaseMixFields = ({
  values,
  setFieldValue,
  handleChange,
  isEditable,
}) => {
  const weightUnit = useSelector(state => state.visit.visit?.unitOfMeasure);

  const unitOfMeasure = getWeightUnitByMeasure(weightUnit);

  return (
    <>
      <SwitchButton
        label={i18n.t('commercialConcentrate')}
        defaultSwitchState={
          values[PROFITABILITY_ANALYSIS_FIELDS.COMMERCIAL_CONCENTRATE_TOGGLE]
        }
        onChangeSelection={selectionState =>
          setFieldValue(
            PROFITABILITY_ANALYSIS_FIELDS.COMMERCIAL_CONCENTRATE_TOGGLE,
            selectionState,
          )
        }
        containerStyles={reusableStyles.switchButtonContainer}
        disabled={!isEditable}
      />

      {values[PROFITABILITY_ANALYSIS_FIELDS.COMMERCIAL_CONCENTRATE_TOGGLE] && (
        <NumberFormInput
          hasCommas
          minValue={0}
          maxValue={100}
          decimalPoints={2}
          disabled={!isEditable}
          textAlign={'left'}
          placeholder={i18n.t('twoNumberPlaceholder')}
          keyboardType={KEYBOARD_TYPE.DECIMAL}
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          unit={`| ${unitOfMeasure}/${i18n.t('cow')}/${i18n.t('day')}`}
          value={values[PROFITABILITY_ANALYSIS_FIELDS.COMMERCIAL_CONCENTRATE]}
          onChange={handleChange(
            PROFITABILITY_ANALYSIS_FIELDS.COMMERCIAL_CONCENTRATE,
          )}
          customUnitStyle={reusableStyles.customUnitStyle}
          customInputContainerStyle={reusableStyles.customInputContainerStyle}
          customInputStyle={reusableStyles.customInputStyle}
        />
      )}

      <SwitchButton
        label={i18n.t('mineralBaseMix')}
        defaultSwitchState={
          values[PROFITABILITY_ANALYSIS_FIELDS.MINERAL_BASE_MIX]
        }
        onChangeSelection={selectionState =>
          setFieldValue(
            PROFITABILITY_ANALYSIS_FIELDS.MINERAL_BASE_MIX,
            selectionState,
          )
        }
        containerStyles={reusableStyles.switchButtonContainer}
        disabled={!isEditable}
      />

      {values[PROFITABILITY_ANALYSIS_FIELDS.MINERAL_BASE_MIX] && (
        <NumberFormInput
          hasCommas
          minValue={0}
          maxValue={1000}
          decimalPoints={2}
          disabled={!isEditable}
          textAlign={'left'}
          placeholder={i18n.t('twoNumberPlaceholder')}
          keyboardType={KEYBOARD_TYPE.DECIMAL}
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          value={values[PROFITABILITY_ANALYSIS_FIELDS.MINERAL_BASE_MIX_VALUE]}
          onChange={handleChange(
            PROFITABILITY_ANALYSIS_FIELDS.MINERAL_BASE_MIX_VALUE,
          )}
          unit={`| ${unitOfMeasure}/${i18n.t('cow')}/${i18n.t('day')}`}
          customUnitStyle={reusableStyles.customUnitStyle}
          customInputContainerStyle={reusableStyles.customInputContainerStyle}
          customInputStyle={reusableStyles.customInputStyle}
        />
      )}
    </>
  );
};

export default CommercialConcentrateAndMineralBaseMixFields;
