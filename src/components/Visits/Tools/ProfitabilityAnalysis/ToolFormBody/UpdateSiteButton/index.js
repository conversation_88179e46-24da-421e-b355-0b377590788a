// modules
import React from 'react';
import { useFormikContext } from 'formik';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import SecondaryButton from '../../../../../common/FormButton/SecondaryButton';

// localization
import i18n from '../../../../../../localization/i18n';

// styling constants
import { normalize } from '../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../constants/theme/variables/customColor';

// actions
import {
  saveOrUpdateProfitabilityDataInDBRequest,
  updateSiteSetupData,
} from '../../../../../../store/actions/tools/profitabilityAnalysis';

const UpdateSiteButton = ({ isEditable }) => {
  const dispatch = useDispatch();
  const { values } = useFormikContext();

  const enableSiteUpdate = useSelector(
    state => state.profitabilityAnalysis.enableSiteUpdate,
  );
  const loadingSiteUpdate = useSelector(
    state => state.profitabilityAnalysis.loadingSiteUpdate,
  );

  const _handlePressUpdateSiteSetup = () => {
    // action to start request for updating site setup data
    dispatch(updateSiteSetupData(values));

    // // action to save tool data in database
    dispatch(saveOrUpdateProfitabilityDataInDBRequest(values));
  };

  const buttonChildren = loadingSiteUpdate ? (
    <ActivityIndicator color={customColor.primaryMain} />
  ) : (
    i18n.t('updateSiteSetup')
  );

  return (
    <View style={styles.container}>
      <SecondaryButton
        disabled={!isEditable || !enableSiteUpdate}
        label={buttonChildren}
        onPress={_handlePressUpdateSiteSetup}
        customButtonStyle={styles.buttonStyles}
        customButtonTextStyle={styles.customButtonTextStyle}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: normalize(20),
    marginVertical: normalize(5),
  },
  buttonStyles: {},
  customButtonTextStyle: {},
});

export default UpdateSiteButton;
