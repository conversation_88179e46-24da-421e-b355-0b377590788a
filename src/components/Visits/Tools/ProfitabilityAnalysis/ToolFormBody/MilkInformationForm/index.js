// modules
import React, { useCallback, useRef } from 'react';
import { View, Platform, Keyboard } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Animated from 'react-native-reanimated';
import _ from 'lodash';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import Accordion from '../../../../../common/Accordion';
import NumberFormInput from '../../../../../common/NumberFormInput';
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';

// actions
import {
  changeProfitabilityFormAccordion,
  validateSiteSetupFieldsRequest,
} from '../../../../../../store/actions/tools/profitabilityAnalysis';

// constants
import {
  MILKING_NUMBERS,
  PROFITABILITY_FORM_TYPES,
} from '../../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';
import {
  BOTTOM_SHEET_TYPE,
  KEYBOARD_TYPE,
  PROFITABILITY_ANALYSIS_FIELDS,
} from '../../../../../../constants/FormConstants';
import { BACTERIA_CELL_COUNT_DECIMAL_PLACE, BACTERIA_CELL_COUNT_MAX_VALUE, NEXT_FIELD_TEXT } from '../../../../../../constants/AppConstants';

// styles
import reusableStyles from '../../ToolFormBody/styles';

// helpers
import { sanitizedFormateProfitabilityFields } from '../../../../../../helpers/profitabilityAnalysis';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';

const MilkInformationForm = ({
  values,
  errors,
  handleChange,
  setFieldValue,
  setValues,
  scrollTo,
  isEditable,
}) => {
  const inputRefs = useRef([]);
  const dispatch = useDispatch();

  const activeInputAccordion = useSelector(
    state => state.profitabilityAnalysis.activeInputAccordion,
  );
  const weightUnit = useSelector(state => state.visit.visit?.unitOfMeasure);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);

  // get current active currency symbol to show in fields label
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  // get unit of measure to show as fields label
  const unitOfMeasure = getWeightUnitByMeasure(weightUnit);

  const _handlePressAnimalInputAccordion = () => {
    dispatch(
      changeProfitabilityFormAccordion(
        PROFITABILITY_FORM_TYPES.MILK_INFORMATION,
      ),
    );

    scrollTo({ y: 50, animated: true });
  };

  // total production in herd input handler to update value
  const _handleChangeTotalProductionInHerd = productionValue => {
    setFieldValue(
      PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION_HERD,
      productionValue,
    );

    // updater function to calculate values that requires formulation
    updateFormulatedFields(
      PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION_HERD,
      productionValue,
      values,
    );
  };

  // milk fat percentage input handler to update value
  const _handleChangeTotalMilkFat = milkFat => {
    setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.MILK_FAT_PERCENTAGE, milkFat);

    // updater function to calculate values that requires formulation
    updateFormulatedFields(
      PROFITABILITY_ANALYSIS_FIELDS.MILK_FAT_PERCENTAGE,
      milkFat,
      values,
    );

    // debounce validator function for site setup update fields
    validateSiteDependantFields(
      PROFITABILITY_ANALYSIS_FIELDS.MILK_FAT_PERCENTAGE,
      milkFat,
    );
  };

  // day in milk DIM input handler to update value
  const _handleChangeDIM = dim => {
    setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.DIM, dim);

    // updater function to calculate values that requires formulation
    updateFormulatedFields(PROFITABILITY_ANALYSIS_FIELDS.DIM, dim, values);

    // debounce validator function for site setup update fields
    validateSiteDependantFields(PROFITABILITY_ANALYSIS_FIELDS.DIM, dim);
  };

  // Milk Price input handler to update value
  const _handleChangeMilkPrice = milkPrice => {
    setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE, milkPrice);

    // updater function to calculate values that requires formulation
    updateFormulatedFields(
      PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE,
      milkPrice,
      values,
    );

    // debounce validator function for site setup update fields
    validateSiteDependantFields(
      PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE,
      milkPrice,
    );
  };

  // Milk Protein field change handler and validator for site setup update button
  const _handleChangeMilkProtein = milkProtein => {
    setFieldValue(
      PROFITABILITY_ANALYSIS_FIELDS.MILK_PROTEIN_PERCENTAGE,
      milkProtein,
    );

    // debounce validator function for site setup update fields
    validateSiteDependantFields(
      PROFITABILITY_ANALYSIS_FIELDS.MILK_PROTEIN_PERCENTAGE,
      milkProtein,
    );
  };

  // Somantic cell count field change handler and validator for site setup update button
  const _handleChangeSomanticCellCount = cellCount => {
    setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.SOMANTIC_CELL_COUNT, cellCount);

    // debounce validator function for site setup update fields
    validateSiteDependantFields(
      PROFITABILITY_ANALYSIS_FIELDS.SOMANTIC_CELL_COUNT,
      cellCount,
    );
  };

  // Bacteria cell count field change handler and validator for site setup update button
  const _handleChangeBacteriaCellCount = cellCount => {
    setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.BACTERIA_CELL_COUNT, cellCount);

    // debounce validator function for site setup update fields
    validateSiteDependantFields(
      PROFITABILITY_ANALYSIS_FIELDS.BACTERIA_CELL_COUNT,
      cellCount,
    );
  };

  // debounce function to run after total production in herd field update to minimize performance effect of form
  const updateFormulatedFields = useCallback(
    _.debounce((fieldKey, fieldValue, formValues) => {
      const payload = {
        [PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_LACTATING_ANIMALS]:
          formValues[
            PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_LACTATING_ANIMALS
          ],
        [PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION_HERD]:
          formValues[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION_HERD],
        [PROFITABILITY_ANALYSIS_FIELDS.MILK_FAT_PERCENTAGE]:
          formValues[PROFITABILITY_ANALYSIS_FIELDS.MILK_FAT_PERCENTAGE],
        [PROFITABILITY_ANALYSIS_FIELDS.DIM]:
          formValues[PROFITABILITY_ANALYSIS_FIELDS.DIM],
        [PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE]:
          formValues[PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE],

        [fieldKey]: fieldValue,
      };

      const { totalProduction, productionIn150DIM, revenuePerCowPerDay } =
        sanitizedFormateProfitabilityFields(payload);

      setValues({
        ...formValues,
        ...payload,
        [PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION]: totalProduction,
        [PROFITABILITY_ANALYSIS_FIELDS.PRODUCTION_IN_150_DIM]:
          productionIn150DIM,
        [PROFITABILITY_ANALYSIS_FIELDS.REVENUE_PER_COW_PER_DAY]:
          revenuePerCowPerDay,
      });
    }, 700),
    [],
  );

  // debounce function to validate change in fields of site setup and enable or disable update site setup button
  const validateSiteDependantFields = useCallback(
    _.debounce((fieldKey, fieldValue) => {
      const payload = { fieldKey, fieldValue };

      dispatch(validateSiteSetupFieldsRequest(payload));
    }, 1500),
    [],
  );

  const showForm =
    PROFITABILITY_FORM_TYPES.MILK_INFORMATION === activeInputAccordion
      ? true
      : false;

  return (
    <Accordion
      title={i18n.t('milkInformation')}
      isActive={showForm}
      handleAccordionPress={_handlePressAnimalInputAccordion}>
      {showForm && (
        <Animated.View>
          <View style={reusableStyles.spacer} />

          <CustomBottomSheet
            required
            disabled={!isEditable}
            infoText={' '}
            label={i18n.t('milkingNumber')}
            placeholder={i18n.t('selectOne')}
            selectLabel={i18n.t('selectOne')}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.NUMBER_ON_MILKINGS]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.NUMBER_ON_MILKINGS]}
            data={MILKING_NUMBERS}
            type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
            onChange={item =>
              setFieldValue(
                PROFITABILITY_ANALYSIS_FIELDS.NUMBER_ON_MILKINGS,
                item?.key,
              )
            }
          />

          <NumberFormInput
            required
            hasCommas
            blurOnSubmit={false}
            disabled={!isEditable}
            maxValue={99999}
            decimalPoints={2}
            textAlign={'left'}
            label={`${i18n.t('totalProductionHerd')} (${unitOfMeasure}/${i18n.t(
              'day',
            )})`}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.DECIMAL}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION_HERD]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION_HERD]}
            onChange={_handleChangeTotalProductionInHerd}
            onSubmitEditing={() => inputRefs.current[1].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
            customLabelStyle={reusableStyles.customLabelStyle}
          />

          <NumberFormInput
            disabled
            hasCommas
            textAlign={'left'}
            decimalPoints={2}
            label={`${i18n.t('totalProduction')} (${unitOfMeasure}/${i18n.t(
              'cow',
            )}/${i18n.t('day')})`}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION]}
            customLabelStyle={reusableStyles.customLabelStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
            customInputContainerStyle={[
              reusableStyles.customInputContainerStyle,
              reusableStyles.disabledInputColor,
            ]}
          />

          <NumberFormInput
            required
            hasCommas
            blurOnSubmit={false}
            disabled={!isEditable}
            maxValue={1000}
            decimalPoints={3}
            textAlign={'left'}
            label={`${i18n.t(
              'currentMilkPrice',
            )} (${currencySymbol}/${unitOfMeasure})`}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.DECIMAL}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE]}
            onChange={_handleChangeMilkPrice}
            reference={reference => (inputRefs.current[1] = reference)}
            onSubmitEditing={() => inputRefs.current[2].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />

          <NumberFormInput
            required
            isNegative
            isInteger
            blurOnSubmit={false}
            disabled={!isEditable}
            minValue={-100}
            maxValue={999}
            textAlign={'left'}
            label={i18n.t('daysInMilk')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={
              Platform.OS === 'ios'
                ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
                : KEYBOARD_TYPE.NUMBER_PAD
            }
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.DIM]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.DIM]}
            onChange={_handleChangeDIM}
            reference={reference => (inputRefs.current[2] = reference)}
            onSubmitEditing={() => inputRefs.current[4].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />

          <NumberFormInput
            disabled
            isInteger={true}
            hasCommas={true}
            textAlign={'left'}
            decimalPoints={2}
            label={i18n.t('productionIn150Dim')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.PRODUCTION_IN_150_DIM]}
            customContainerStyle={reusableStyles.customContainerStyle}
            customInputContainerStyle={[
              reusableStyles.customInputContainerStyle,
              reusableStyles.disabledInputColor,
            ]}
          />

          <NumberFormInput
            required
            hasCommas
            blurOnSubmit={false}
            disabled={!isEditable}
            maxValue={100}
            decimalPoints={2}
            textAlign={'left'}
            label={i18n.t('milkFat(%)')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.DECIMAL}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.MILK_FAT_PERCENTAGE]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.MILK_FAT_PERCENTAGE]}
            onChange={_handleChangeTotalMilkFat}
            reference={reference => (inputRefs.current[4] = reference)}
            onSubmitEditing={() => inputRefs.current[5].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />

          <NumberFormInput
            required
            hasCommas
            blurOnSubmit={false}
            disabled={!isEditable}
            maxValue={100}
            decimalPoints={2}
            textAlign={'left'}
            label={i18n.t('milkProtein(%)')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.DECIMAL}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={
              values[PROFITABILITY_ANALYSIS_FIELDS.MILK_PROTEIN_PERCENTAGE]
            }
            error={
              errors[PROFITABILITY_ANALYSIS_FIELDS.MILK_PROTEIN_PERCENTAGE]
            }
            onChange={_handleChangeMilkProtein}
            reference={reference => (inputRefs.current[5] = reference)}
            onSubmitEditing={() => inputRefs.current[6].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />

          <NumberFormInput
            required
            isInteger
            hasCommas
            blurOnSubmit={false}
            disabled={!isEditable}
            minValue={0}
            maxValue={99999}
            textAlign={'left'}
            label={i18n.t('somaticCellCountWithUnit')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.SOMANTIC_CELL_COUNT]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.SOMANTIC_CELL_COUNT]}
            onChange={_handleChangeSomanticCellCount}
            reference={reference => (inputRefs.current[6] = reference)}
            onSubmitEditing={() => inputRefs.current[7].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />

          <NumberFormInput
            required
            hasCommas
            blurOnSubmit={false}
            disabled={!isEditable}
            maxValue={BACTERIA_CELL_COUNT_MAX_VALUE}
            decimalPoints={BACTERIA_CELL_COUNT_DECIMAL_PLACE}
            textAlign={'left'}
            label={i18n.t('bacteriaCellCountWithUnit')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.DECIMAL}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.BACTERIA_CELL_COUNT]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.BACTERIA_CELL_COUNT]}
            onChange={_handleChangeBacteriaCellCount}
            reference={reference => (inputRefs.current[7] = reference)}
            onSubmitEditing={() => inputRefs.current[8].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />

          <NumberFormInput
            required
            hasCommas
            blurOnSubmit={false}
            disabled={!isEditable}
            maxValue={999}
            decimalPoints={2}
            textAlign={'left'}
            label={i18n.t('MUN(mg/dL)')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.DECIMAL}
            returnKeyType={NEXT_FIELD_TEXT.DONE}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.MUN]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.MUN]}
            onChange={handleChange(PROFITABILITY_ANALYSIS_FIELDS.MUN)}
            reference={reference => (inputRefs.current[8] = reference)}
            onSubmitEditing={() => Keyboard.dismiss()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />
        </Animated.View>
      )}
    </Accordion>
  );
};

export default MilkInformationForm;
