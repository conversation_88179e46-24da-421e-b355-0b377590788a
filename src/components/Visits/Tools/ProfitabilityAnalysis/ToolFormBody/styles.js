import { Platform, StyleSheet } from 'react-native';
import { normalize } from '../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../constants/theme/variables/customColor';

const reusableStyles = StyleSheet.create({
  formContainer: {
    flex: 1,
    backgroundColor: customColor.white,
  },
  keyboardVerticalOffset: Platform.OS == 'ios' ? normalize(200) : normalize(40),
  paddingBehavior: Platform.OS == 'ios' ? 'padding' : 'height',
  customContainerStyle: {
    paddingTop: normalize(10),
  },
  customInputContainerStyle: {
    width: '100%',
  },
  spacer: {
    height: normalize(10),
  },
  spacer20: {
    height: normalize(20),
  },
  switchButtonContainer: {
    marginTop: normalize(20),
  },
  customUnitStyle: {
    position: 'absolute',
    right: normalize(10),
    bottom: normalize(11),
    lineHeight: normalize(22),
    textTransform: 'lowercase',
  },
  customInputStyle: {
    flex: 0,
    width: '75%',
  },
  disabledInputColor: {
    backgroundColor: customColor.grey4,
  },
  customLabelStyle: {
    // textTransform: 'lowercase',
  },
});

export default reusableStyles;
