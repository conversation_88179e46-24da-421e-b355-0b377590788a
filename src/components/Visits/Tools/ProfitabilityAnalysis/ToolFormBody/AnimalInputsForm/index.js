// modules
import React, { useCallback, useRef } from 'react';
import { Keyboard, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Animated from 'react-native-reanimated';
import _ from 'lodash';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import Accordion from '../../../../../common/Accordion';
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';
import NumberFormInput from '../../../../../common/NumberFormInput';

// constants
import { PROFITABILITY_FORM_TYPES } from '../../../../../../constants/toolsConstants/ProfitabilityAnalysisConstants';
import {
  BOTTOM_SHEET_TYPE,
  KEYBOARD_TYPE,
  PROFITABILITY_ANALYSIS_FIELDS,
} from '../../../../../../constants/FormConstants';
import {
  LACTATING_ANIMALS_MAX_VALUE,
  LACTATING_ANIMALS_MIN_VALUE,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';

// styles
import reusableStyles from '../../ToolFormBody/styles';

// actions
import {
  changeProfitabilityFormAccordion,
  validateSiteSetupFieldsRequest,
} from '../../../../../../store/actions/tools/profitabilityAnalysis';

// helpers
import { sanitizedFormateProfitabilityFields } from '../../../../../../helpers/profitabilityAnalysis';

const AnimalInputsForm = ({
  values,
  errors,
  handleChange,
  setFieldValue,
  setValues,
  scrollTo,
  isEditable,
}) => {
  const inputRefs = useRef([]);
  const dispatch = useDispatch();

  const breedEnums = useSelector(state => state.enums.enum?.breed);
  const productionSystemEnums = useSelector(
    state => state.enums.enum?.productionSystem,
  );
  const activeInputAccordion = useSelector(
    state => state.profitabilityAnalysis.activeInputAccordion,
  );

  // show or hide accordion listener
  const _handlePressAnimalInputAccordion = () => {
    dispatch(
      changeProfitabilityFormAccordion(PROFITABILITY_FORM_TYPES.ANIMAL_INPUTS),
    );

    scrollTo({ y: 0, animated: true });
  };

  // animals in herd input handler to update animals
  const _handleChangeAnimalInHerd = animals => {
    setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.ANIMALS_IN_HERD, animals);

    validateSiteDependantFields(animals);
  };

  // total number of lactating animals input handler to update and also runs function to calculate fields where current field input changes matter
  const _handleChangeTotalNumbersOfLactatingAnimals = lactatingAnimals => {
    setFieldValue(
      PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_LACTATING_ANIMALS,
      lactatingAnimals,
    );

    updateFormulateFields(lactatingAnimals, values);
  };

  // input change listener for production system
  const _handleChangeProductionSystem = system => {
    /**
     * @description
     * removing bedding quality value from form values inside formik
     * removing value only for the following 3 production system categories
     * 1. Semiconfinamento from production system
     * 2. Pasto from production system
     * 3. Noah from production system
     *
     * else show bedding quality filed if any other production system is selected
     *
     * @readonly @requires
     * following order must be followed for production system enums in order to compatible with bedding quality
     * 1. Freestall
     * 2. Compostbarn
     * 3. Semiconfinamento
     * 4. Pasto
     * 5. Noah
     */
    if (
      [
        productionSystemEnums[2]?.key,
        productionSystemEnums[3]?.key,
        productionSystemEnums[4]?.key,
      ].includes(system?.key)
    ) {
      setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.BEDDING_QUALITY, undefined);
    }

    setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.PRODUCTION_SYSTEM, system?.key);
  };

  // debounce function to run after animals in herd field update to minimize performance effect of form
  const updateFormulateFields = useCallback(
    _.debounce((lactatingAnimals, values) => {
      const { totalProduction, productionIn150DIM } =
        sanitizedFormateProfitabilityFields({
          [PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_LACTATING_ANIMALS]:
            lactatingAnimals,
          [PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION_HERD]:
            values[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION_HERD],
          [PROFITABILITY_ANALYSIS_FIELDS.MILK_FAT_PERCENTAGE]:
            values[PROFITABILITY_ANALYSIS_FIELDS.MILK_FAT_PERCENTAGE],
          [PROFITABILITY_ANALYSIS_FIELDS.DIM]:
            values[PROFITABILITY_ANALYSIS_FIELDS.DIM],
          [PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE]:
            values[PROFITABILITY_ANALYSIS_FIELDS.MILK_PRICE],
        });

      setValues({
        ...values,
        [PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_LACTATING_ANIMALS]:
          lactatingAnimals,
        [PROFITABILITY_ANALYSIS_FIELDS.TOTAL_PRODUCTION]: totalProduction,
        [PROFITABILITY_ANALYSIS_FIELDS.PRODUCTION_IN_150_DIM]:
          productionIn150DIM,
      });
    }, 700),
    [],
  );

  // debounce function to validate change in fields of site setup and enable or disable update site setup button
  const validateSiteDependantFields = useCallback(
    _.debounce(animalsInHerd => {
      const payload = {
        fieldKey: PROFITABILITY_ANALYSIS_FIELDS.ANIMALS_IN_HERD,
        fieldValue: animalsInHerd,
      };

      dispatch(validateSiteSetupFieldsRequest(payload));
    }, 1000),
    [],
  );

  /**
   * filtering out the noah option from the production system which are enums.
   * not removing noah from enums because it is using on some other places
   * @todo move noah string to constants to avoid any future issues.
   */
  const filterNoahFromProductionSystemEnums = productionSystemEnums?.filter(
    item => item.key !== 'Noah',
  );

  const showForm =
    PROFITABILITY_FORM_TYPES.ANIMAL_INPUTS === activeInputAccordion
      ? true
      : false;

  return (
    <Accordion
      title={i18n.t('animalInputs')}
      isActive={showForm}
      handleAccordionPress={_handlePressAnimalInputAccordion}>
      {showForm && (
        <Animated.View>
          <NumberFormInput
            required
            maxValue={99999}
            isInteger={true}
            hasCommas={true}
            blurOnSubmit={false}
            disabled={!isEditable}
            textAlign={'left'}
            label={i18n.t('animalInHerd')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.ANIMALS_IN_HERD]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.ANIMALS_IN_HERD]}
            onChange={_handleChangeAnimalInHerd}
            onSubmitEditing={() => inputRefs.current[0].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />

          <NumberFormInput
            required
            maxValue={99999}
            isInteger={true}
            hasCommas={true}
            blurOnSubmit={false}
            disabled={!isEditable}
            textAlign={'left'}
            label={i18n.t('totalNumberOfCows')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_COWS]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_COWS]}
            onChange={handleChange(
              PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_COWS,
            )}
            reference={reference => (inputRefs.current[0] = reference)}
            onSubmitEditing={() => inputRefs.current[1].focus()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />

          <NumberFormInput
            required
            minValue={LACTATING_ANIMALS_MIN_VALUE}
            maxValue={LACTATING_ANIMALS_MAX_VALUE}
            isInteger={true}
            hasCommas={true}
            blurOnSubmit={false}
            disabled={!isEditable}
            textAlign={'left'}
            label={i18n.t('totalLactatingAnimals')}
            placeholder={i18n.t('twoNumberPlaceholder')}
            keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
            returnKeyType={NEXT_FIELD_TEXT.DONE}
            value={
              values[
                PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_LACTATING_ANIMALS
              ]
            }
            error={
              errors[
                PROFITABILITY_ANALYSIS_FIELDS.TOTAL_NUMBER_OF_LACTATING_ANIMALS
              ]
            }
            onChange={_handleChangeTotalNumbersOfLactatingAnimals}
            reference={reference => (inputRefs.current[1] = reference)}
            onSubmitEditing={() => Keyboard.dismiss()}
            customInputContainerStyle={reusableStyles.customInputContainerStyle}
            customContainerStyle={reusableStyles.customContainerStyle}
          />

          <View style={reusableStyles.spacer} />

          <CustomBottomSheet
            required
            disabled={!isEditable}
            infoText={' '}
            label={i18n.t('breed')}
            placeholder={i18n.t('selectOne')}
            selectLabel={i18n.t('selectOne')}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.BREED]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.BREED]}
            data={breedEnums || []}
            type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
            onChange={item =>
              setFieldValue(PROFITABILITY_ANALYSIS_FIELDS.BREED, item?.key)
            }
          />

          <View style={reusableStyles.spacer} />

          <CustomBottomSheet
            required
            disabled={!isEditable}
            infoText={' '}
            label={i18n.t('productionSystem')}
            placeholder={i18n.t('selectOne')}
            selectLabel={i18n.t('selectOne')}
            value={values[PROFITABILITY_ANALYSIS_FIELDS.PRODUCTION_SYSTEM]}
            error={errors[PROFITABILITY_ANALYSIS_FIELDS.PRODUCTION_SYSTEM]}
            data={filterNoahFromProductionSystemEnums || []}
            type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
            onChange={_handleChangeProductionSystem}
          />
        </Animated.View>
      )}
    </Accordion>
  );
};

export default AnimalInputsForm;
