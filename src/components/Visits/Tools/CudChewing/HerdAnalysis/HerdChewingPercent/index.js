// modules
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, SafeAreaView } from 'react-native';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import CustomBarGraph from '../../../../../common/BarGraph';
import ToolGraph from '../../../common/ToolGraph';
// import CustomBarGraphVictory from '../../../../../common/BarGraphVictory';

// styles
import styles from './styles';

const HerdChewingPercent = ({ chewingBarData }) => {
  const [barData, setBarData] = useState(null);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  useEffect(() => {
    if (chewingBarData) {
      setBarData(chewingBarData);
    }
  }, [chewingBarData]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const graphTitleComponent = (
    <Text style={styles.title}>{`${i18n.t('chewing')} ${i18n.t('%')}`}</Text>
  );

  const graphComponent = (
    // <CustomBarGraphVictory />
    <ScrollView
      style={styles.scrollViewStyles}
      contentContainerStyle={styles.contentContainer}>
      <SafeAreaView>
        <CustomBarGraph
          data={barData}
          maxValue={100}
          minValue={0}
          width={
            landscapeModalVisible
              ? styles.graphWidthLandscape.width
              : styles.graphWidth.width
          }
          height={
            landscapeModalVisible
              ? styles.graphHeightLandscape.height
              : styles.graphHeight.height
          }
          xAxisLabelTextStyle={styles.axisTextLabel}
          hideRules={true}
          xAxisNumberOfLines={4}
        />
      </SafeAreaView>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <ToolGraph
        showExpandIcon
        handleExpandIconPress={onExpandIconPress}
        landscapeModalVisible={landscapeModalVisible}
        customGraphTitleComponent={graphTitleComponent}
        graphComponent={graphComponent}
      />
    </View>
  );
};

export default HerdChewingPercent;
