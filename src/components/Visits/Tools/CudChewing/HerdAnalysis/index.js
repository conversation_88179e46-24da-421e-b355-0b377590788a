// modules
import React, { useState, useEffect } from 'react';
import Animated, { SlideInRight, SlideOutRight } from 'react-native-reanimated';
import { useDispatch, useSelector } from 'react-redux';

// constants
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
} from '../../../../../constants/AppConstants';
import { TOAST_TYPE } from '../../../../../constants/FormConstants';
import {
  CUD_CHEW_HERD_ANALYSIS_TABS,
  CUD_CHEW_HERD_ANALYSIS_TYPES,
} from '../../../../../constants/toolsConstants/RumenHealthCudChewingConstants';

// styles
import styles from './styles';

// components
import ToolBottomTabs from '../../common/ToolBottomTabs';
import HerdScoreAnalysis from './HerdScoreAnalysis';
import HerdChewingPercent from './HerdChewingPercent';
import HerdChewsPerCud from './HerdChewsPerCud';
import HerdAnalysisResult from './HerdAnalysisResults';
import { showToast } from '../../../../common/CustomToast';

// helpers
import {
  getChewingPercentGraphData,
  getCudChewsAvgGraphData,
  mapGraphDataForHerdAnalysisExport,
  saveRumenHealthCudChewingHerdData,
  updatePensInDim,
} from '../../../../../helpers/rumenHealthHelper';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
  updatePenDimForTools,
} from '../../../../../store/actions/tool';
import {
  initializeCudChewingToolData,
  saveRumenHealthHerdAnalysisRequest,
} from '../../../../../store/actions/tools/cudChewing';

// localization
import i18n from '../../../../../localization/i18n';

//services
import { isOnline } from '../../../../../services/netInfoService';

const RHCudChewingHerdAnalysis = ({
  currentStep,
  totalSteps,
  penAnalysisData,
  penList,
  penFinalArray,
  enableResults,
  setEnableResults,
  scoreListForHerdSum,
}) => {
  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit?.visit);
  const { isEditable = false, id } = visitState;

  const [selectedTab, setSelectedTab] = useState(CUD_CHEW_HERD_ANALYSIS_TABS[0]);
  const [graphData, setGraphData] = useState(null);

  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    if (isEditable && !!penList?.length) {
      const data = saveRumenHealthCudChewingHerdData(penList, penAnalysisData);
      const payload = {
        visitId: visitState?.id || visitState?.sv_id,
        penAnalysisData: data,
      };
      dispatch(initializeCudChewingToolData(data));
      if (penFinalArray?.pens?.length > 0 && isDirty) {
        dispatch(saveRumenHealthHerdAnalysisRequest(payload));
      }
    }
  }, []);

  const onTabChange = tab => {
    switch (tab?.key) {
      case CUD_CHEW_HERD_ANALYSIS_TYPES.CHEWING:
        const barData = getChewingPercentGraphData({
          penList,
          penAnalysisData,
        });

        setGraphData(barData);

        break;

      case CUD_CHEW_HERD_ANALYSIS_TYPES.CHEWS_PER_CUD:
        const avgChews = getCudChewsAvgGraphData({
          penList,
          penAnalysisData,
        });

        setGraphData(avgChews);

        break;
    }

    setSelectedTab(tab);
  };

  const updateDimOfPen = pen => {
    const payload = {
      ...pen,
      penId: pen?.penId,
      daysInMilk: parseInt(pen?.daysInMilk),
      localSiteId: visitState?.localSiteId,
      siteId: visitState?.siteId,
    };

    dispatch(
      initializeCudChewingToolData(updatePensInDim(penAnalysisData, payload)),
    );

    dispatch(updatePenDimForTools(payload));
  };

  const downloadHerdAnalysisData = async (
    cudChewGraphData,
    chewPercentGraphData,
    type,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForHerdAnalysisExport(
        visitState,
        cudChewGraphData,
        chewPercentGraphData,
      );
      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.CUD_CHEWING_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType: EXPORT_REPORT_TYPES.CUD_CHEWING_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareHerdAnalysisData = async (
    cudChewGraphData,
    chewPercentGraphData,
    type,
    tab,
    exportMethod,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForHerdAnalysisExport(
        visitState,
        cudChewGraphData,
        chewPercentGraphData,
      );
      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.CUD_CHEWING_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.CUD_CHEWING_HERD_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const renderHerdAnalysisResults = () => (
    <HerdAnalysisResult
      penAnalysisData={penAnalysisData}
      penList={penList}
      onDownloadPress={downloadHerdAnalysisData}
      onShareHerdAnalysisData={onShareHerdAnalysisData}
      visitState={visitState}
    />
  );

  const renderToolTypesComponent = () => {
    switch (selectedTab?.key) {
      case CUD_CHEW_HERD_ANALYSIS_TYPES.SCORE_ANALYSIS:
        return (
          <HerdScoreAnalysis
            penList={penList}
            penAnalysisData={penAnalysisData}
            updateDimOfPen={updateDimOfPen}
            isEditable={isEditable}
            isDirty={isDirty}
            setIsDirty={setIsDirty}
            enableResults={enableResults}
            setEnableResults={setEnableResults}
            scoreListForHerdSum={scoreListForHerdSum}
          />
        );

      case CUD_CHEW_HERD_ANALYSIS_TYPES.CHEWING:
        return <HerdChewingPercent chewingBarData={graphData} />;

      case CUD_CHEW_HERD_ANALYSIS_TYPES.CHEWS_PER_CUD:
        return <HerdChewsPerCud chewsCountData={graphData} />;
    }
  };

  return (
    <Animated.View
      style={styles.container}
      entering={SlideInRight.duration(250)}
      exiting={SlideOutRight.duration(250)}>
      {/**
       * @todo TODO required better implementation for change in steps
       * @description condition to show pen analysis graph and other chewing related views
       */}

      {currentStep === totalSteps && renderHerdAnalysisResults()}

      {currentStep !== totalSteps && (
        <>
          <Animated.View style={styles.container}>
            {renderToolTypesComponent()}
          </Animated.View>

          <ToolBottomTabs
            tabs={CUD_CHEW_HERD_ANALYSIS_TABS}
            selectedTab={selectedTab}
            onTabChange={onTabChange}
          />
        </>
      )}
    </Animated.View>
  );
};

export default RHCudChewingHerdAnalysis;
