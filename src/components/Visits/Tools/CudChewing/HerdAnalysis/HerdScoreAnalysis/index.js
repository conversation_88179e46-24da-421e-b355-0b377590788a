// modules
import React from 'react';
import { KeyboardAvoidingView, Platform, Text, View } from 'react-native';

//redux
import { useDispatch, useSelector } from 'react-redux';

// components
import ScoreAnalysisForm from '../ScoreAnalysisForm';
import Tool<PERSON>lert from '../../../common/ToolAlert';
import GraphHeaderButton from '../../../common/GraphHeaderButtons';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import { TOOL_TYPES } from '../../../../../../constants/AppConstants';

//actions
import { hideCudChewingToastRequest } from '../../../../../../store/actions/userPreferences';

const HerdScoreAnalysis = ({
  penList,
  penAnalysisData,
  updateDimOfPen,
  isEditable = false,
  isDirty,
  setIsDirty,
  enableResults,
  setEnableResults,
  scoreListForHerdSum,
}) => {
  //dispatch
  const dispatch = useDispatch();

  const userPreferences = useSelector(state => state.userPreferences);
  const rumenHealthVisitState = useSelector(
    state => state.visit?.visit?.rumenHealth,
  );

  // header row component
  const headerRow = (
    <View style={styles.headerRow}>
      <Text style={styles.rowTitle}>{`${i18n.t('%')} ${i18n.t(
        'chewing',
      )}`}</Text>
      <Text style={[styles.rowTitle, styles.dim]}>
        {i18n.t('avgChewsPerCud')}
      </Text>
      <Text style={[styles.rowTitle, styles.dim]}>{i18n.t('DIM')}</Text>
    </View>
  );

  const graphTitleComponent = (
    <Text style={styles.title}>{i18n.t('cudChewingScoreAnalysis')}</Text>
  );

  const getToolToast = () => {
    const {
      userPreferences: { defaultValues },
    } = userPreferences || {};
    const { [TOOL_TYPES.RUMEN_HEALTH]: cudChewingToast } =
      defaultValues || false;
    return isEditable ? cudChewingToast : false;
  };

  const onCloseToast = () => {
    dispatch(hideCudChewingToastRequest());
  };

  return (
    <>
      <KeyboardAvoidingView
        style={styles.container}
        behavior="padding"
        enabled
        keyboardVerticalOffset={
          Platform.OS === 'ios'
            ? styles.keyboardVerticalOffsetIOS
            : styles.keyboardVerticalOffsetAndroid
        }>
        <GraphHeaderButton customGraphTitleComponent={graphTitleComponent} />

        {headerRow && headerRow}

        <ScoreAnalysisForm
          penList={penList}
          isEditable={isEditable}
          penAnalysisData={penAnalysisData}
          updateDimOfPen={updateDimOfPen}
          rumenHealthVisitState={rumenHealthVisitState}
          isDirty={isDirty}
          setIsDirty={setIsDirty}
          enableResults={enableResults}
          setEnableResults={setEnableResults}
          scoreListForHerdSum={scoreListForHerdSum}
        />
      </KeyboardAvoidingView>
      {!!getToolToast() && (
        <View style={styles.alert}>
          <ToolAlert onCloseToast={onCloseToast} />
        </View>
      )}
    </>
  );
};

export default HerdScoreAnalysis;
