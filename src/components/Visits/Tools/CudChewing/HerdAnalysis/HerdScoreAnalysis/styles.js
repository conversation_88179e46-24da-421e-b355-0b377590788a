import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';
import DeviceInfo from 'react-native-device-info';

export default {
  container: {
    flex: 1,
  },
  headerRow: {
    paddingHorizontal: normalize(20),
    backgroundColor: colors.grey7,
    borderWidth: 1,
    borderColor: colors.grey8,
    flexDirection: 'row',
    height: normalize(40),
    alignItems: 'center',
  },
  rowTitle: {
    flex: 1,
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(12),
    lineHeight: normalize(16),
    color: colors.grey1,
  },
  dim: {
    textAlign: 'center',
  },
  title: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(16),
    lineHeight: normalize(24),
    letterSpacing: 0.25,
    color: colors.grey1,
    marginBottom: normalize(10),
    marginTop: normalize(-6),
  },
  keyboardVerticalOffsetIOS: DeviceInfo.isTablet()
    ? normalize('225')
    : normalize('250'),
  keyboardVerticalOffsetAndroid: normalize('120'),
  alert: {
    backgroundColor: colors.white,
  },
};
