import DeviceInfo from 'react-native-device-info';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollViewStyles: {
    flex: 1,
    marginTop: normalize(15),
  },
  lineGraphScrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingRight: normalize(20)
  },
  axisTextLabel: {
    top: DeviceInfo.isTablet() ? normalize(25) : normalize(15),
    width: normalize(65),
    borderWidth: 1,
    borderColor: colors.transparent,
    fontSize: DeviceInfo.isTablet() ? normalize(12) : normalize(15),
  },
  lineGraphLabels: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(18),
    color: colors.alphabetIndex,
    letterSpacing: 0.2,
    textAnchor: 'start',
  },
  barLabelStyle: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(10),
    color: colors.alphabetIndex,
    letterSpacing: 0.5,
    width: normalize(45),
    alignSelf: 'center',
  },
  barLabelContainerStyle: {
    backgroundColor: colors.white,
    width: normalize(50),
    height: normalize(22),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4.0,
    elevation: 8,
    borderRadius: 4,
    position: 'absolute',
    left: normalize(1),
    bottom: normalize(5),
  },
  customChartConfig: {
    fillShadowGradientFrom: colors.graphHeaderBulletPrimary,
    fillShadowGradientFromOpacity: 0.1,
    fillShadowGradientTo: colors.graphHeaderBulletPrimary,
    fillShadowGradientToOpacity: 0,
    useShadowColorFromDataset: true,
  },
  customPoints: {
    color: colors.graphHeaderBulletPrimary,
  },

  extraHeight: RNPlatform.select({
    ios: normalize(45),
    android: normalize(45),
  }),

  graphWidth: RNPlatform.select({
    ios: {
      width:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 30)
          : normalize(Platform.deviceWidth - 40),
    },
    default: {
      width: normalize(Platform.deviceWidth - 40),
    },
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight * 0.26)
          : Platform.deviceHeight * 0.33,
    },
    android: {
      height: Platform.deviceHeight * 0.33,
    },
  }),

  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight)
          : normalize(Platform.deviceHeight - 100),
    },
    android: {
      width: normalize(Platform.deviceHeight - 100),
    },
  }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? Platform.deviceWidth * 0.6
          : Platform.deviceWidth * 0.6,
    },
    android: {
      height: Platform.deviceWidth * 0.55,
    },
  }),

  // styles for no of chews graph
  chewGraphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth * 2.05, // 60,
    },
    android: {
      width: Platform.deviceWidth * 2.2,
    },
    // ios: {
    //   width: Platform.deviceWidth + 60,
    //   // width:
    //   //   Platform.deviceWidth < 400
    //   //     ? Platform.deviceWidth + 50
    //   //     : Platform.deviceWidth,
    // },
    // default: {
    //   width: normalize(Platform.deviceWidth),
    // },
  }),
  chewsGraphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.44
          : Platform.deviceHeight * 0.5,
    },
    android: {
      height: Platform.deviceHeight * 0.49,
    },
    // ios: {
    //   height:
    //     Platform.deviceHeight < 700
    //       ? Platform.deviceHeight * 0.4
    //       : Platform.deviceHeight * 0.55,
    // },
    // android: {
    //   height: normalize(Platform.deviceHeight * 0.5),
    // },
  }),

  chewsGraphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight + 70)
          : Platform.deviceHeight + 40,
    },
    android: {
      width: normalize(Platform.deviceHeight),
    },
    // ios: {
    //   width:
    //     Platform.deviceHeight < 700
    //       ? normalize(Platform.deviceHeight + 70)
    //       : Platform.deviceHeight + 40,
    //   // width:
    //   //   Platform.deviceHeight < 700
    //   //     ? Platform.deviceHeight + 100
    //   //     : Platform.deviceHeight,
    // },
    // android: {
    //   width: normalize(Platform.deviceHeight + 5),
    // },
  }),
  chewsGraphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 40)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.45)
          : normalize(Platform.deviceWidth - 80),
    },
    android: {
      height: Platform.deviceWidth - 110,
    },
    // ios: {
    //   height:
    //     Platform.deviceWidth < 400
    //       ? normalize(Platform.deviceWidth / 1.1)
    //       : normalize(Platform.deviceWidth / 1.45),
    // },
    // android: {
    //   height: normalize(Platform.deviceWidth / 1.3),
    // },
  }),
};
