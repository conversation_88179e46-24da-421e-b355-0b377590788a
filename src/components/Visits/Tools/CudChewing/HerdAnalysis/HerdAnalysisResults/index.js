// modules
import React, { useState, useEffect } from 'react';
import { View, ScrollView, SafeAreaView } from 'react-native';

// constants
import {
  CUD_CHEWING_HERD_ANALYSIS_RESULTS_TABS,
  CUD_CHEWING_TYPES,
} from '../../../../../../constants/toolsConstants/RumenHealthCudChewingConstants';

// components
import CustomLineGraph from '../../../../../common/LineGraph';
import HerdAnalysisTabs from '../../../common/ToolBottomTabs';
import CustomBarGraph from '../../../../../common/BarGraph';
import ResultsGraphHeader from '../HerdResultsGraphHeader';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// helpers
import {
  herdAnalysisChewsAvg,
  herdAnalysisCudChewingGraph,
} from '../../../../../../helpers/rumenHealthHelper';
import { inProgressToolCount } from '../../../../../../helpers/visitHelper';

const HerdAnalysisResult = ({
  penAnalysisData,
  penList,
  onDownloadPress,
  visitState,
  onShareHerdAnalysisData,
}) => {
  const [selectedTab, setSelectedTab] = useState(
    CUD_CHEWING_HERD_ANALYSIS_RESULTS_TABS[0],
  );
  const [lineGraphData, setLineGraphData] = useState(null);
  const [barGraphData, setBarGraphData] = useState(null);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  const visitCount = inProgressToolCount(visitState);

  useEffect(() => {
    const barData = herdAnalysisCudChewingGraph(penList, penAnalysisData);
    const lineData = herdAnalysisChewsAvg(penList, penAnalysisData);
    setBarGraphData(barData);
    setLineGraphData(lineData);
  }, [selectedTab]);

  const onTabChange = tab => {
    setSelectedTab(tab);
  };

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const cudChewingGraphComponent = (
    <ScrollView
      style={styles.scrollViewStyles}
      contentContainerStyle={styles.contentContainer}>
      <SafeAreaView>
        <CustomBarGraph
          data={barGraphData}
          yAxisSuffix={i18n.t('%')}
          // barWidth={36}
          hideRules={true}
          xAxisType={'dashed'}
          customBarLabelStyle={styles.barLabelStyle}
          barLabelSuffix={i18n.t('%')}
          barLabelContainerStyle={styles.barLabelContainerStyle}
          xAxisLabelTextStyle={styles.axisTextLabel}
          xAxisNumberOfLines={4}
          maxValue={100}
          minValue={0}
          width={
            landscapeModalVisible
              ? styles.graphWidthLandscape.width
              : styles.graphWidth.width
          }
          height={
            landscapeModalVisible
              ? styles.graphHeightLandscape.height
              : visitCount && visitCount > 1
              ? styles.graphHeight.height
              : styles.graphHeight.height + styles.extraHeight
          }
        />
      </SafeAreaView>
    </ScrollView>
  );

  const cudChewsGraphComponent = (
    <ScrollView
      style={styles.lineGraphScrollView}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}>
      <SafeAreaView>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <CustomLineGraph
            data={lineGraphData}
            showVictory={true}
            showVerticalYAxis
            verticalAxisDomain
            showLabelsValue
            interpolation={'monotoneX'}
            horizontalLabelStyles={styles.lineGraphLabels}
            width={
              landscapeModalVisible
                ? styles.chewsGraphWidthLandscape.width
                : styles.chewGraphWidth.width
            }
            height={
              landscapeModalVisible
                ? styles.chewsGraphHeightLandscape.height
                : styles.chewsGraphHeight.height
            }
          />
        </ScrollView>
      </SafeAreaView>
    </ScrollView>
  );

  return (
    <>
      <View style={styles.container}>
        <ResultsGraphHeader
          selectedTab={selectedTab}
          cudChewingTypes={CUD_CHEWING_TYPES}
          handleExpandIconPress={onExpandIconPress}
          landscapeModalVisible={landscapeModalVisible}
          onDownloadPress={option =>
            onDownloadPress(barGraphData, lineGraphData, option, selectedTab)
          }
          onSharePress={(option, exportMethod) =>
            onShareHerdAnalysisData(
              barGraphData,
              lineGraphData,
              option,
              selectedTab,
              exportMethod,
            )
          }
          graphComponent={
            selectedTab?.key === CUD_CHEWING_TYPES.CUD_CHEWING ? (
              cudChewingGraphComponent
            ) : selectedTab?.key === CUD_CHEWING_TYPES.NUMBER_OF_CHEWS ? (
              cudChewsGraphComponent
            ) : (
              <></>
            )
          }
        />
      </View>

      <HerdAnalysisTabs
        tabs={CUD_CHEWING_HERD_ANALYSIS_RESULTS_TABS}
        selectedTab={selectedTab}
        onTabChange={onTabChange}
      />
    </>
  );
};

export default HerdAnalysisResult;
