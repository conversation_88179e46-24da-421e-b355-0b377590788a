// modules
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, SafeAreaView } from 'react-native';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import CustomBarGraph from '../../../../../common/BarGraph';
import ToolGraph from '../../../common/ToolGraph';

// styles
import styles from './styles';

const HerdChewsPerCud = ({ chewsCountData }) => {
  const [barData, setBarData] = useState(null);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  useEffect(() => {
    if (chewsCountData) {
      setBarData(chewsCountData);
    }
  }, [chewsCountData]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const graphTitleComponent = (
    <Text style={styles.title}>{i18n.t('noOfChewsPerCud')}</Text>
  );

  const graphComponent = (
    <ScrollView
      style={styles.scrollViewStyles}
      contentContainerStyle={styles.contentContainer}>
      <SafeAreaView>
        <CustomBarGraph
          data={barData}
          width={
            landscapeModalVisible
              ? styles.graphWidthLandscape.width
              : styles.graphWidth.width
          }
          height={
            landscapeModalVisible
              ? styles.graphHeightLandscape.height
              : styles.graphHeight.height
          }
          hideRules={true}
          xAxisLabelTextStyle={styles.axisTextLabel}
          xAxisNumberOfLines={4}
        />
      </SafeAreaView>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <ToolGraph
        showExpandIcon
        handleExpandIconPress={onExpandIconPress}
        landscapeModalVisible={landscapeModalVisible}
        customGraphTitleComponent={graphTitleComponent}
        graphComponent={graphComponent}
      />
    </View>
  );
};

export default HerdChewsPerCud;
