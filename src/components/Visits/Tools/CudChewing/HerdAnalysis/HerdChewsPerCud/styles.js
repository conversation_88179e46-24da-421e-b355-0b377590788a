import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';
import Platform from '../../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';

export default {
  container: {
    flex: 1,
  },
  scrollViewStyles: {
    flex: 1,
    marginTop: normalize(10),
  },
  contentContainer: {
    flexGrow: 1,
  },
  axisTextLabel: {
    top: normalize(15),
  },
  title: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(16),
    lineHeight: normalize(24),
    letterSpacing: 0.25,
    color: colors.grey1,
  },
  graphWidth: RNPlatform.select({
    ios: {
      width:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 30)
          : normalize(Platform.deviceWidth - 40),
    },
    default: {
      width: normalize(Platform.deviceWidth - 40),
    },
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight * 0.29)
          : Platform.deviceHeight * 0.35,
    },
    android: {
      height: Platform.deviceHeight * 0.34,
    },
  }),

  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight - 50)
          : normalize(Platform.deviceHeight - 160),
    },
    android: {
      width: normalize(Platform.deviceHeight - 100),
    },
  }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? Platform.deviceWidth * 0.65
          : Platform.deviceWidth * 0.65,
    },
    android: {
      height: Platform.deviceWidth * 0.6,
    },
  }),
};
