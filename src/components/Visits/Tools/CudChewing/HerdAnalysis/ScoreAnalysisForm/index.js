// modules
import React, { useState, useEffect, useRef } from 'react';
import { FlatList, Platform } from 'react-native';

// helpers
import {
  getScoreAnalysisState,
  shouldEnableResultsButton,
  updateDimAndReplacePen,
} from '../../../../../../helpers/rumenHealthHelper';

// components
import ScoreFormItem, { LineSeparator } from '../ScoreAnalysisFormItem';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';
import EmptyListComponent from '../../../../../common/EmptyListComponent';

// constants
import { CONTENT_TYPE } from '../../../../../../constants/FormConstants';
import {
  PLATFORM_DEVICE_TYPE,
  TOOL_ANALYSIS_TYPES,
} from '../../../../../../constants/AppConstants';

// localization
import i18n from '../../../../../../localization/i18n';

//lodash
import _ from 'lodash';

/**
 *
 * @param {Array} penList (from reducer)
 * @param {Object} penAnalysisData (from reducer)
 * @param {Function} updateDimOfPen to update dim value to pen setup
 * @param {Boolean} isEditable to check if UI is editable
 *
 * @returns component of pen list with inputs for DIM
 */

let previousInputState = null;

const ScoreAnalysisForm = ({
  penList,
  penAnalysisData,
  updateDimOfPen,
  isEditable = false,
  rumenHealthVisitState,
  isDirty,
  setIsDirty,
  enableResults,
  setEnableResults,
  scoreListForHerdSum,
}) => {
  //ref
  const inputRef = useRef([]);

  const [scoreList, setScoreList] = useState(null);
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  useEffect(() => {
    setPenListData();
  }, [penList, penAnalysisData]); //CDEA-2679: dependency added to re-invoke helper function on pen change

  useEffect(() => {
    scoreListForHerdSum.current = scoreList;
    //disables results button if no pens data exist
    if (!enableResults) {
      setEnableResults(
        shouldEnableResultsButton(TOOL_ANALYSIS_TYPES.HERD_ANALYSIS, scoreList),
      );
    }
  }, [scoreList]);

  const setPenListData = () => {
    if (penAnalysisData && rumenHealthVisitState) {
      // const data = getScoreAnalysisState(penAnalysisData, penList, isEditable);
      const data = getScoreAnalysisState(
        typeof rumenHealthVisitState === 'string'
          ? JSON.parse(rumenHealthVisitState)
          : rumenHealthVisitState,
        penList,
        isEditable,
      );
      setScoreList(data);
    }
  };

  // change pen dim in local state
  const onChangePen = (dim, pen) => {
    setIsDirty(true);
    const updatedDimPenArray = updateDimAndReplacePen(scoreList, pen, dim);
    if (updatedDimPenArray) {
      setScoreList([...updatedDimPenArray]);
    }
  };

  // set previous state on focus
  const onFocus = (pen, index) => {
    previousInputState = {
      penId: pen?.penId,
      daysInMilk:
        pen?.daysInMilk == 0 || pen?.daysInMilk
          ? Number(pen?.daysInMilk)
          : null,
    };
    setType(CONTENT_TYPE.NUMBER);
    setAction({
      currentRef: inputRef?.current[index + 1],
      dismiss: index < inputRef?.current?.length - 1 ? false : true,
    });
  };

  // set dim data and open alert
  const onBlurInput = item => {
    if (
      item?.penId === previousInputState?.penId &&
      item?.daysInMilk !== previousInputState?.daysInMilk
    ) {
      updateDimOfPen(item);
    }
  };

  const renderScoreListItem = ({ item, index }) => {
    return (
      <ScoreFormItem
        index={index}
        isEditable={isEditable}
        item={item}
        onChangePen={onChangePen}
        onFocus={onFocus}
        onBlurInput={onBlurInput}
        inputRef={inputRef}
      />
    );
  };

  const emptyComponent = () => {
    if (!isEditable) {
      return (
        <EmptyListComponent
          title={i18n.t('noResultShow')}
          description={i18n.t('noPensToShow')}
        />
      );
    }
    return (
      <EmptyListComponent
        title={i18n.t('noPensAdded')}
        description={i18n.t('addPensForHerdData')}
      />
    );
  };

  return (
    <>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <FlatList
        data={scoreList || []}
        extraData={scoreList}
        renderItem={renderScoreListItem}
        ListEmptyComponent={emptyComponent}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item, index) => 'pens_' + index}
        ItemSeparatorComponent={() => <LineSeparator />}
        keyboardDismissMode="on-drag"
        keyboardShouldPersistTaps="never"
      />
    </>
  );
};

export default ScoreAnalysisForm;
