// modules
import React from 'react';
import { View, Text } from 'react-native';

// localizations
import i18n from '../../../../../../localization/i18n';

// components
import ToolGraph from '../../../common/ToolGraph';

// styles
import styles from './styles';

const ResultsGraphHeader = ({
  selectedTab,
  cudChewingTypes,
  graphComponent,
  onDownloadPress,
  handleExpandIconPress,
  landscapeModalVisible,
  onSharePress,
}) => {
  const renderGraphTitle = (
    <View>
      <View style={styles.row}>
        <View style={styles.bullet} />

        <Text style={[styles.title, styles.herdAnalysisText]}>
          {selectedTab?.key === cudChewingTypes.CUD_CHEWING
            ? i18n.t('cudChewingPercent')
            : i18n.t('chewsPerRegurgitation')}
        </Text>
      </View>

      <View style={styles.separator} />

      <View style={styles.row}>
        <View
          style={[
            styles.bullet,
            selectedTab?.key === cudChewingTypes.NUMBER_OF_CHEWS
              ? styles.goalsChewsBullet
              : styles.goalsChewingBullet,
          ]}
        />

        <Text style={[styles.title, styles.herdAnalysisText]}>
          {selectedTab?.key === cudChewingTypes.CUD_CHEWING
            ? i18n.t('goalCudChewingPercent')
            : i18n.t('goalChews')}
        </Text>
      </View>
    </View>
  );

  return (
    <ToolGraph
      showDownloadIcon={!landscapeModalVisible}
      showShareIcon={!landscapeModalVisible}
      showExpandIcon
      onDownloadPress={onDownloadPress}
      onSharePress={onSharePress}
      customGraphTitleComponent={renderGraphTitle}
      graphComponent={graphComponent}
      handleExpandIconPress={handleExpandIconPress}
      landscapeModalVisible={landscapeModalVisible}
    />
  );
};

export default ResultsGraphHeader;
