import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  title: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(16),
    lineHeight: normalize(24),
    letterSpacing: 0.25,
    color: colors.grey1,
  },
  herdAnalysisText: {
    fontSize: normalize(10),
    lineHeight: normalize(12),
    fontFamily: fonts.HelveticaNeueMedium,
    letterSpacing: 0.15,
  },
  bullet: {
    marginRight: normalize(5),
    borderRadius: normalize(5),
    width: normalize(10),
    height: normalize(10),
    backgroundColor: colors.graphHeaderBulletPrimary,
  },
  goalsChewsBullet: {
    backgroundColor: colors.graphHeaderBullet3,
  },
  goalsChewingBullet: {
    backgroundColor: colors.graphHeaderBulletSecondary,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  separator: {
    height: normalize(10),
  },
};
