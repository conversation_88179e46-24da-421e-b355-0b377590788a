// modules
import React from 'react';
import { View, Text, Keyboard, Platform } from 'react-native';

// constants
import {
  INPUT_TYPE,
  KEYBOARD_TYPE,
} from '../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../constants/AppConstants';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import NumberFormInput from '../../../../../common/NumberFormInput';

// styles
import styles from './styles';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const ScoreFormItem = ({
  item,
  onChangePen,
  onBlurInput,
  onFocus,
  isEditable = false,
  index,
  inputRef,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.flex}>
        <Text style={styles.itemLabel} numberOfLines={1}>
          {i18n.t('pen')}:{' '}
          <Text style={styles.itemValue}>{item?.penName || '-'}</Text>
        </Text>

        <Text style={styles.percentValues}>
          {item?.percentage
            ? `${convertInputNumbersToRegionalBasis(
                item?.percentage?.toFixed(2),
                2,
              )} ${i18n.t('%')}`
            : '-'}
        </Text>
      </View>

      <View style={styles.chewPerCud}>
        <Text style={styles.percentValues}>
          {item?.avgChews
            ? convertInputNumbersToRegionalBasis(item?.avgChews?.toFixed(2), 2)
            : '-'}
        </Text>
      </View>

      <View style={styles.inputView}>
        <NumberFormInput
          type={INPUT_TYPE.TEXT}
          placeholder={'-'}
          keyboardType={
            Platform.OS === 'ios'
              ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
              : KEYBOARD_TYPE.NUMBER_PAD
          }
          placeholderColor={styles.placeHolderColor}
          value={
            item?.daysInMilk == 0 || item?.daysInMilk
              ? item?.daysInMilk?.toString()
              : null
          }
          onChange={text => onChangePen(text, item)}
          onBlur={() => onBlurInput(item)}
          forceOnBlur
          reference={e => {
            inputRef.current[index] = e;
          }}
          onSubmitEditing={() => {
            Keyboard.dismiss();
            inputRef?.current?.[index + 1]?.focus();
          }}
          customLabelStyle={styles.customLabelStyle}
          customInputContainer={styles.customInputContainer}
          inputTextAlign={'center'}
          disabled={!isEditable}
          // maxLength={5}
          minValue={-100}
          maxValue={999}
          isInteger={true}
          isNegative={true}
          decimalPoints={0}
          onFocus={() => onFocus(item, index)}
          blurOnSubmit
          customContainerStyle={styles.customContainerStyle}
          returnKeyType={
            index < inputRef.current.length - 1
              ? NEXT_FIELD_TEXT.NEXT
              : NEXT_FIELD_TEXT.DONE
          }
          inputAccessoryViewID="customInputAccessoryView"
        />
      </View>
    </View>
  );
};

export const LineSeparator = () => {
  return <View style={styles.separator} />;
};

export default ScoreFormItem;
