import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  flex: {
    flex: 1,
  },
  separator: {
    height: 1,
    backgroundColor: '#ededed',
  },
  container: {
    flexDirection: 'row',
    marginVertical: normalize(20),
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: normalize(24),
  },
  inputView: {
    flex: 1,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  customContainerStyle: {
    width: normalize(100),
  },
  customInputContainer: {
    paddingHorizontal: normalize(0),
  },
  customLabelStyle: {
    marginBottom: normalize(0),
    height: normalize(0),
  },
  itemLabel: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(20),
    letterSpacing: 0.15,
    color: colors.alphabetIndex,
  },
  itemValue: {
    fontFamily: fonts.HelveticaNeueBold,
  },
  percentValues: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(16),
    lineHeight: normalize(27),
    letterSpacing: 0.15,
    color: colors.grey1,
  },
  chewPerCud: {
    flex: 1,
    alignItems: 'center',
    alignSelf: 'center',
  },
  placeHolderColor: colors.black,
};
