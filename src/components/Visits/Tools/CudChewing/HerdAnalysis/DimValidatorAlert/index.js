// modules
import React from 'react';
import { View, Text } from 'react-native';

// constants
import { BUTTON_TYPE } from '../../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import BottomActionSheet from '../../../../../common/BottomActionSheet';
import FormButton from '../../../../../common/FormButton';

// styles
import styles from './styles';

const DimValidatorAlert = props => {
  const { isOpen, onClose, onClick, data } = props;

  return (
    <BottomActionSheet isOpen={isOpen} onClose={onClose}>
      <View style={styles.container}>
        <View style={styles.logoutIconContainer}>{data?.icon}</View>
        <Text style={[styles.titleText, styles.alert]}>{i18n.t('alert')}</Text>
        <Text style={styles.titleText}>{data?.description}</Text>
        <View style={styles.optionsContainer}>
          <FormButton
            type={BUTTON_TYPE.SECONDARY}
            label={i18n.t('no')}
            onPress={onClose}
            customButtonStyle={styles.buttonsStyle}
          />
          <FormButton
            type={BUTTON_TYPE.PRIMARY}
            label={i18n.t('yes')}
            onPress={onClick}
            customButtonStyle={[
              styles.buttonsStyle,
              data?.error ? styles.errorButtonsStyle : {},
            ]}
          />
        </View>
      </View>
    </BottomActionSheet>
  );
};

export default DimValidatorAlert;
