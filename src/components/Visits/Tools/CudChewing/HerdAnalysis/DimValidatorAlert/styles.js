import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flexDirection: 'column',
    marginVertical: normalize(20),
  },
  logoutIconContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    marginVertical: normalize(16),
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(24),
    color: colors.grey1,
    alignSelf: 'center',
    marginHorizontal: normalize(35),
    textAlign: 'center',
    marginBottom: normalize(20),
  },
  alert: {
    fontSize: normalize(18),
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: normalize(29),
    marginHorizontal: normalize(20),
  },
  buttonsStyle: {
    width: normalize(158),
    height: normalize(52),
  },
  errorButtonsStyle: {
    backgroundColor: colors.error4,
  },
};
