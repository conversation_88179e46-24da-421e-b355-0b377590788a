// modules
import React from 'react';

// constants
import { TOOL_ANALYSIS_TYPES } from '../../../../../constants/AppConstants';

// components
import ToolAnalysisTypes from '../../common/ToolAnalysisTypes';
import RHCudChewingPenAnalysis from '../PenAnalysis';
import RHCudChewingHerdAnalysis from '../HerdAnalysis';

const ToolsAnalysisComponent = ({
  selectedCategoryTool,
  currentStep,
  totalSteps,
  pensList,
  penAnalysisData,
  selectedVisits,
  onNextStepClick,
  healthCurrentActivePen,
  isDirty,
  setIsDirty,
  penFinalArray,
  setPenFinalArray,
  enableResults,
  setEnableResults,
  currentPenAnalysis,
  setCurrentPenAnalysis,
  scoreListForHerdSum,
  chewingTypeForHerdSum,
}) => {
  switch (selectedCategoryTool?.toolType || '') {
    case TOOL_ANALYSIS_TYPES.PEN_ANALYSIS:
      return (
        <RHCudChewingPenAnalysis
          currentStep={currentStep}
          totalSteps={totalSteps}
          pensList={pensList || []}
          penAnalysisData={penAnalysisData}
          selectedVisits={selectedVisits}
          healthCurrentActivePen={healthCurrentActivePen}
          isDirty={isDirty}
          setIsDirty={setIsDirty}
          penFinalArray={penFinalArray}
          setPenFinalArray={setPenFinalArray}
          setEnableResults={setEnableResults}
          currentPenAnalysis={currentPenAnalysis}
          setCurrentPenAnalysis={setCurrentPenAnalysis}
          chewingTypeForHerdSum={chewingTypeForHerdSum}
        />
      );

    case TOOL_ANALYSIS_TYPES.HERD_ANALYSIS:
      return (
        <RHCudChewingHerdAnalysis
          currentStep={currentStep}
          totalSteps={totalSteps}
          penList={pensList || []}
          penAnalysisData={penAnalysisData}
          isDirty={isDirty}
          setIsDirty={setIsDirty}
          penFinalArray={penFinalArray}
          enableResults={enableResults}
          setEnableResults={setEnableResults}
          scoreListForHerdSum={scoreListForHerdSum}
        />
      );

    default:
      return <ToolAnalysisTypes onStepChange={onNextStepClick} />;
  }
};

export default ToolsAnalysisComponent;
