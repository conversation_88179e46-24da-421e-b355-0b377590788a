// modules
import React, { useEffect, useRef, useState } from 'react';
import {
  TouchableOpacity,
  Text,
  ScrollView,
  Platform,
  KeyboardAvoidingView,
  Keyboard,
  View,
} from 'react-native';
import { useDispatch } from 'react-redux';

// components
import CircularCounter from '../../../../../common/CircularCounter';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import {
  ADD_ICON_PRIMARY,
  ADD_ICON,
} from '../../../../../../constants/AssetSVGConstants';
import {
  CONTENT_TYPE,
  RUMEN_HEALTH_CUD_CHEWING_FIELDS,
} from '../../../../../../constants/FormConstants';
import {
  COUNTER,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';

// helpers
import {
  addNewCowHelper,
  getTotalCowsCountCowsChew,
  penExistsInPublishedVisit,
  replaceCudChewsCountObject,
  updatedChewsCount,
  updatedChewsCountInput,
} from '../../../../../../helpers/rumenHealthHelper';

// actions
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';
import {
  addCudChewsCow,
  updateChewsPens,
} from '../../../../../../store/actions/tools/cudChewing';

const CowChewsCounter = ({
  cudChewingPenData,
  penAnalysisData,
  isEditable = false,
  isDirty,
  setIsDirty,
  selectedPen,
}) => {
  const dispatch = useDispatch();

  const scrollRef = useRef();

  const inputReferences = useRef([]);

  const [type, setType] = useState(CONTENT_TYPE.NUMBER);
  const [action, setAction] = useState(() => {});
  const [totalCowsCount, setTotalCowsCount] = useState(0);

  useEffect(() => {
    setTotalCowsCount(getTotalCowsCountCowsChew(penAnalysisData, selectedPen));
  }, [selectedPen]);

  // increase chews count for cow
  const handleChewingIncrement = data => {
    setIsDirty(true);
    const updateCowChewsPen = updatedChewsCount(
      data?.cowNumber,
      cudChewingPenData,
      COUNTER.INCREMENT_ONE,
    );

    if (updateCowChewsPen) {
      dispatch(
        updateChewsPens(
          replaceCudChewsCountObject({
            penId: updateCowChewsPen?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.PEN_ID],
            updateCowChewsPen,
            pensList: penAnalysisData?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.PENS],
          }),
        ),
      );
    }
    setTotalCowsCount(totalCowsCount + 1);
  };

  // decrease chews count for cow
  const handleChewingDecrement = data => {
    setIsDirty(true);
    const updateCowChewsPen = updatedChewsCount(
      data?.cowNumber,
      cudChewingPenData,
      COUNTER.DECREMENT_ONE,
    );

    if (updateCowChewsPen) {
      dispatch(
        updateChewsPens(
          replaceCudChewsCountObject({
            penId: updateCowChewsPen?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.PEN_ID],
            updateCowChewsPen,
            pensList: penAnalysisData?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.PENS],
          }),
        ),
      );
    }
    setTotalCowsCount(totalCowsCount - 1);
  };

  const handleChewingCountInput = (data, count) => {
    setIsDirty(true);
    const { updateCowChewsPen, _previousCount } = updatedChewsCountInput(
      data?.cowNumber,
      cudChewingPenData,
      parseInt(count),
    );

    if (updateCowChewsPen) {
      dispatch(
        updateChewsPens(
          replaceCudChewsCountObject({
            penId: updateCowChewsPen?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.PEN_ID],
            updateCowChewsPen,
            pensList: penAnalysisData?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.PENS],
          }),
        ),
      );
    }
    let _totalCowsCount = totalCowsCount - _previousCount;
    _totalCowsCount += parseInt(count);
    setTotalCowsCount(_totalCowsCount);
  };

  // add new cow in selected pen
  const handleAddNewCow = () => {
    setIsDirty(true);
    const updatedPensArray = addNewCowHelper(
      penAnalysisData,
      cudChewingPenData?.penId,
    );

    if (updatedPensArray?.length > 0) {
      dispatch(addCudChewsCow(updatedPensArray));

      requestAnimationFrame(() =>
        scrollRef.current.scrollToEnd({ animated: true }),
      );
    }
  };

  const renderCowsCudChews = () => {
    const cowChewsCountArray =
      cudChewingPenData?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.CUD_CHEWS_COUNT];

    if (cowChewsCountArray?.length > 0) {
      return cowChewsCountArray?.map((item, index) => (
        <CircularCounter
          key={item?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.COW_NUMBER] || index}
          title={`${i18n.t('cow')} ${
            item?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.COW_NUMBER]
          }`}
          count={
            penExistsInPublishedVisit(isEditable, penAnalysisData, selectedPen)
              ? item?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.CHEWS_COUNT] || 0
              : '-'
          }
          disabled={!isEditable}
          onDecrementClick={() => handleChewingDecrement(item)}
          onIncrementClick={() => handleChewingIncrement(item)}
          onChangeText={count => handleChewingCountInput(item, count)}
          showInput={true}
          reference={input => {
            inputReferences.current[index] = input;
          }}
          onSubmitEditing={() => {
            if (index < inputReferences.current.length - 1) {
              inputReferences?.current[index + 1]?.focus();
            } else {
              Keyboard?.dismiss();
            }
          }}
          returnKeyType={
            index < inputReferences.current.length - 1
              ? NEXT_FIELD_TEXT.NEXT
              : NEXT_FIELD_TEXT.DONE
          }
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            if (cowChewsCountArray?.length - 1 === index) {
              setAction({ dismiss: true });
              return;
            }
            setAction({
              currentRef: inputReferences?.current[index + 1],
            });
          }}
        />
      ));
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.flexOne}
      keyboardVerticalOffset={
        Platform.OS === 'ios' && styles.keyboardVerticalOffset
      }>
      <View style={styles.penNameRow}>
        <Text style={styles.totalCowsCountText}>
          {`${i18n.t('animalsObserved')}: ${totalCowsCount}`}
        </Text>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.container}
        contentContainerStyle={styles.contentContainerStyle}
        ref={scrollRef}
        keyboardDismissMode="on-drag"
        keyboardShouldPersistTaps="handled">
        <CustomInputAccessoryView doneAction={action} type={type} />

        {/* @desc render cows list component */}
        {renderCowsCudChews()}

        <TouchableOpacity
          style={styles.addButton}
          onPress={handleAddNewCow}
          activeOpacity={0.7}
          disabled={!isEditable}>
          {isEditable ? (
            <ADD_ICON_PRIMARY {...styles.addIcon} />
          ) : (
            <ADD_ICON {...styles.addIcon} />
          )}
          <Text style={[styles.buttonText, !isEditable && styles.disabledText]}>
            {i18n.t('addNewCow')}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default CowChewsCounter;
