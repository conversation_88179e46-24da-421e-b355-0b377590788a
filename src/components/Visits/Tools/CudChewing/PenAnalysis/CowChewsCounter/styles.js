import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../../constants/theme/variables/platform';

export default {
  container: {
    // marginVertical: normalize(20),
    // marginBottom: normalize(0),
    paddingHorizontal: normalize(20),
  },
  contentContainerStyle: {
    flexGrow: 1,
  },
  addButton: {
    marginTop: normalize(20),
    marginBottom: normalize(20),
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: normalize(8),
    width: '45%',
    flexWrap: 'nowrap',
  },
  buttonText: {
    marginLeft: normalize(10),
    fontSize: normalize(14),
    fontFamily: fonts.HelveticaNeueBold,
    color: colors.primaryMain,
    // lineHeight: normalize(14),
    letterSpacing: 1.25,
    // textTransform: 'uppercase',
  },
  addIcon: {
    width: normalize(14),
    height: normalize(14),
    // color:
  },
  flexOne: {
    flex: 1,
  },
  penNameRow: {
    paddingHorizontal: normalize(20),
    // height: normalize(54),
    backgroundColor: colors.grey7,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.grey8,
    paddingVertical: normalize(8),
  },
  totalCowsCountView: { justifyContent: 'center', marginRight: 12 },
  totalCowsCountText: {
    fontSize: normalize(12),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.alphabetIndex,
    lineHeight: normalize(21),
    letterSpacing: 0.25,
  },
  keyboardVerticalOffset:
    Platform.deviceHeight < 700 ? normalize(260) : normalize(260),
  disabledText: { color: colors.grey2 },
};
