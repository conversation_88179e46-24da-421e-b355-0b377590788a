// modules
import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { VictoryAxis, VictoryLabel } from 'victory-native';

// components
import CustomLineGraph from '../../../../../common/LineGraph';
import ToolGraph from '../../../common/ToolGraph';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// helpers
import {
  getSiteAndAccountIdFromVisit,
  getTotalCowsCountCowsChew,
  getTotalCowsCountCudChew,
  penAnalysisChewsPerCudData,
  penAnalysisCudChewingData,
} from '../../../../../../helpers/rumenHealthHelper';
import { sortRecentVisitsForGraph } from '../../../../../../helpers/toolHelper';

// constants
import { VISIT_TABLE_FIELDS } from '../../../../../../constants/AppConstants';
import { CUD_CHEWING_TYPES } from '../../../../../../constants/toolsConstants/RumenHealthCudChewingConstants';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../../store/actions/tool';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const PenAnalysisResult = ({
  selectedPen,
  onDownloadPress,
  penAnalysisData,
  graphDataType,
  selectedVisits,
  onSharePenAnalysisData,
  chewingType,
}) => {
  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit.visit);
  const { recentVisits = [] } = useSelector(state => state.tool);

  const [graphData, setGraphData] = useState(null);
  const [stdDeviation, setSTDDeviation] = useState(null);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  // useEffect for dispatch recent visits actions
  useEffect(() => {
    const { siteId, accountId } = getSiteAndAccountIdFromVisit(visitState);

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visitState?.id || '',
        tool: VISIT_TABLE_FIELDS.RUMEN_HEALTH,
        chewingType,
      }),
    );
  }, []);

  useEffect(() => {
    let selectedRecentVisits = recentVisits?.filter(visit =>
      selectedVisits?.includes(visit?.visitId || visit?.id),
    );
    selectedRecentVisits = sortRecentVisitsForGraph(selectedRecentVisits);

    if (graphDataType === CUD_CHEWING_TYPES.CUD_CHEWING) {
      const { data } = penAnalysisCudChewingData({
        selectedRecentVisits,
        selectedPen,
        penAnalysisData,
      });

      setGraphData(data);
      setSTDDeviation(null);
    } else if (graphDataType === CUD_CHEWING_TYPES.NUMBER_OF_CHEWS) {
      const { data, deviation } = penAnalysisChewsPerCudData({
        selectedRecentVisits,
        selectedPen,
        penAnalysisData,
      });

      setGraphData(data);
      setSTDDeviation(deviation);
    }
  }, [recentVisits, selectedVisits]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const verticalAxisLabel =
    graphDataType === CUD_CHEWING_TYPES.CUD_CHEWING
      ? `${i18n.t('%')} ${i18n.t('chewing')}`
      : graphDataType === CUD_CHEWING_TYPES.NUMBER_OF_CHEWS
      ? i18n.t('noOfChews')
      : '';

  // graph title component
  const graphTitleComponent = (
    <View style={styles.penTitleContainer}>
      {stdDeviation && (
        <Text style={styles.stdTitle}>
          {i18n.t('std')}:{' '}
          <Text style={styles.stdValue}>
            {convertInputNumbersToRegionalBasis(
              Number(stdDeviation).toFixed(2),
              2,
              true,
            )}
          </Text>
        </Text>
      )}
    </View>
  );

  let isSignalDataPlot =
    graphData &&
    graphData[0]?.data?.length == 2 &&
    graphData[0]?.data[0].y == null;

  const graphComponent = (
    <ScrollView
      style={styles.scrollViewStyles}
      contentContainerStyle={styles.contentContainer}>
      <SafeAreaView>
        <View style={styles.textContainer}>
          <Text style={styles.penAnalysisTitle} numberOfLines={1}>
            {i18n.t('pen') || ''}:{' '}
            <Text style={styles.penAnalysisValue}>
              {selectedPen?.name || ''}
            </Text>
          </Text>
          <Text style={styles.penAnalysisTitle}>
            {i18n.t('animalsObserved') || ''}:{' '}
            <Text style={styles.penAnalysisValue}>
              {graphDataType === CUD_CHEWING_TYPES.CUD_CHEWING
                ? getTotalCowsCountCudChew(penAnalysisData, selectedPen)
                : getTotalCowsCountCowsChew(penAnalysisData, selectedPen)}
            </Text>
          </Text>
        </View>
        <CustomLineGraph
          data={graphData}
          showVictory={true}
          showVerticalYAxis
          showLabelsValue
          verticalAxisDomain
          interpolation={'monotoneX'}
          verticalAxisLabel={verticalAxisLabel}
          xAxisDomainPadding={
            isSignalDataPlot
              ? styles.domainPaddingSingle
              : !landscapeModalVisible
              ? styles.domainPadding
              : null
          }
          width={
            landscapeModalVisible
              ? styles.graphWidthLandscape.width
              : styles.graphWidth.width
          }
          height={
            landscapeModalVisible
              ? styles.graphHeightLandscape.height
              : styles.graphHeight.height
          }
          showXAxis={
            <VictoryAxis
              dependentAxis
              tickLabelComponent={
                <VictoryLabel style={styles.transparentValue} />
              }
              offsetX={
                landscapeModalVisible
                  ? isSignalDataPlot
                    ? styles.offsetXLandscapeSingle
                    : styles.offsetXLandscape
                  : isSignalDataPlot
                  ? styles.offsetXSingle
                  : styles.offsetX
              }
              domainPadding={
                isSignalDataPlot
                  ? styles.domainPaddingSingle
                  : !landscapeModalVisible
                  ? styles.domainPadding
                  : null
              }
              style={{
                axis: styles.axisStyles,
              }}
            />
          }
        />
      </SafeAreaView>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <ToolGraph
        showExpandIcon
        handleExpandIconPress={onExpandIconPress}
        showDownloadIcon={!landscapeModalVisible}
        onDownloadPress={option =>
          onDownloadPress(
            graphData,
            option,
            stdDeviation,
            graphDataType === CUD_CHEWING_TYPES.NUMBER_OF_CHEWS,
          )
        }
        onSharePress={(option, exportMethod) =>
          onSharePenAnalysisData(
            graphData,
            option,
            stdDeviation,
            graphDataType === CUD_CHEWING_TYPES.NUMBER_OF_CHEWS,
            exportMethod,
          )
        }
        showShareIcon={!landscapeModalVisible}
        landscapeModalVisible={landscapeModalVisible}
        customGraphTitleComponent={graphTitleComponent}
        graphComponent={graphComponent}
      />
    </View>
  );
};

export default PenAnalysisResult;
