import DeviceInfo from 'react-native-device-info';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollViewStyles: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
  penTitleContainer: {
    flex: 1,
    marginRight: normalize(40),
  },
  penAnalysisTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(20),
    color: colors.alphabetIndex,
    letterSpacing: normalize(0.15),
  },
  penAnalysisValue: {
    fontFamily: fonts.HelveticaNeueBold,
  },

  stdTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    color: colors.grey9,
    letterSpacing: normalize(0.15),
  },
  stdValue: {
    fontFamily: fonts.HelveticaNeueMedium,
  },

  domainPadding: {
    x: [10, 35],
  },
  domainPaddingSingle: {
    x: [0, Platform.deviceWidth / 2],
  },
  transparentValue: {
    fill: colors.transparent,
  },
  axisStyles: {
    stroke: colors.grey1,
    strokeWidth: 1,
    strokeDasharray: '1, 5',
  },

  offsetX: RNPlatform.select({
    ios: Platform.deviceWidth - 20,
    android: Platform.deviceWidth - 40,
  }),
  offsetXSingle: RNPlatform.select({
    ios: Platform.deviceWidth / 1.73,
    android: Platform.deviceWidth / 2,
  }),
  offsetXLandscape: RNPlatform.select({
    ios:
      Platform.deviceHeight < 700
        ? Platform.deviceHeight - 33
        : Platform.deviceHeight - 80,
    android: DeviceInfo.isTablet()
      ? Platform.deviceHeight - 75
      : normalize(Platform.deviceHeight - 75),
  }),
  offsetXLandscapeSingle: RNPlatform.select({
    ios: Platform.deviceHeight / 2,
    android: Platform.deviceHeight / 2,
  }),
  graphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth + 60,
    },
    android: {
      width: Platform.deviceWidth + 40,
    },
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.5
          : Platform.deviceHeight * 0.51,
    },
    android: {
      height: normalize(Platform.deviceHeight / 2),
    },
  }),

  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight + 20)
          : Platform.deviceHeight,
    },
    android: {
      width: normalize(
        DeviceInfo.isTablet()
          ? Platform.deviceHeight * 0.75
          : normalize(Platform.deviceHeight),
      ),
    },
  }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 40)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.5)
          : normalize(Platform.deviceWidth - 80),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 300 : 60),
      ),
    },
  }),
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: normalize(18),
    marginTop: normalize(10),
  },
};
