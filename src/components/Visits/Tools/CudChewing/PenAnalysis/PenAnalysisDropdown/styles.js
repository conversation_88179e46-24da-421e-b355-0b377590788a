import DeviceInfo from 'react-native-device-info';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  dropdownContainer: {
    paddingHorizontal: normalize(20),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  customValue: {
    fontSize: normalize(14),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.primaryMain,
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    paddingLeft: normalize(0),
    paddingRight: normalize(0),
  },
  secondDropdown: {
    width: DeviceInfo.isTablet() ? '65%' : '90%',
    alignSelf: 'flex-end',
    borderWidth: normalize(0),
    backgroundColor: colors.white,
  },
  firstDropdown: {
    width: DeviceInfo.isTablet() ? '50%' : '70%',
    borderWidth: normalize(0),
    alignSelf: 'flex-start',
  },
  dropdownIcon: {
    width: normalize(20),
  },
  customFieldLabel: {
    // textTransform: 'capitalize',
    marginBottom: normalize(0),
    height: normalize(0),
  },
  iconStrokeColor: colors.primaryMain,
};
