import React from 'react';
import { View } from 'react-native';

// constants
import { BOTTOM_SHEET_TYPE } from '../../../../../../constants/FormConstants';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';

const PenAnalysisDropDown = ({
  pensList,
  cudChewingList,
  selectedPen,
  chewingType,
  onChangeChewing,
  onChangePen,
}) => {
  //save data to database
  return (
    <View style={styles.dropdownContainer}>
      <CustomBottomSheet
        value={selectedPen?.id || selectedPen?.sv_id || ''}
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        selectLabel={i18n.t('selectPen')}
        infoText={i18n.t('selectOne')}
        data={pensList || []}
        onChange={onChangePen}
        customInputStyle={styles.firstDropdown}
        customValueStyle={styles.customValue}
        customIconStyles={styles.dropdownIcon}
        iconStroke={styles.iconStrokeColor}
        customLabelStyle={styles.customFieldLabel}
        // selectFieldProps={{
        //   numberOfLines: 1,
        // }}
      />
      <CustomBottomSheet
        value={chewingType?.id || ''}
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        selectLabel={i18n.t('selectOne')}
        infoText={i18n.t('selectOne')}
        data={cudChewingList || []}
        onChange={onChangeChewing}
        customInputStyle={styles.secondDropdown}
        customLabelStyle={styles.customFieldLabel}
        customValueStyle={styles.customValue}
        customIconStyles={styles.dropdownIcon}
        iconStroke={styles.iconStrokeColor}
        // selectFieldProps={{
        //   numberOfLines:  1,
        // }}
      />
    </View>
  );
};

export default PenAnalysisDropDown;
