// modules
import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// constants
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
  TOOL_ANALYSIS_TYPES,
} from '../../../../../constants/AppConstants';
import {
  RUMEN_HEALTH_CUD_CHEWING_FIELDS,
  TOAST_TYPE,
} from '../../../../../constants/FormConstants';
import {
  CUD_CHEWING_TYPES,
  PEN_ANALYSIS_CUD_CHEWING_DROPDOWN,
} from '../../../../../constants/toolsConstants/RumenHealthCudChewingConstants';

// styles
import styles from './styles';

// components
import PenAnalysisResult from './PenAnalysisResults';
import PenAnalysisToolComponent from './ToolComponent';
import InfoAlert from '../../../../common/InfoAlert';
import { showToast } from '../../../../common/CustomToast';

// helpers
import {
  addCudChewingPenHelper,
  isPenExist,
  mapGraphDataForPenAnalysisExport,
  shouldEnableResultsButton,
} from '../../../../../helpers/rumenHealthHelper';
import {
  pickPenInReducerFromPensList,
  saveSelectedPenInReducer,
} from '../../../../../helpers/visitHelper';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
  setSelectedPenRequest,
} from '../../../../../store/actions/tool';

// localization
import i18n from '../../../../../localization/i18n';

//services
import { isOnline } from '../../../../../services/netInfoService';

//lodash
import _ from 'lodash';
import {
  addCudChewingPen,
  pushUpdatedCudChewing,
} from '../../../../../store/actions/tools/cudChewing';

const RHCudChewingPenAnalysis = ({
  currentStep,
  totalSteps,
  pensList,
  penAnalysisData,
  selectedVisits,
  healthCurrentActivePen,
  isDirty,
  setIsDirty,
  penFinalArray,
  setPenFinalArray,
  setEnableResults,
  currentPenAnalysis,
  setCurrentPenAnalysis,
  chewingTypeForHerdSum,
}) => {
  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit?.visit);
  const { isEditable = false } = visitState;

  const [chewingType, setChewingType] = useState(
    PEN_ANALYSIS_CUD_CHEWING_DROPDOWN[0],
  );
  const [selectedPen, setSelectedPen] = useState(null);
  // const [currentPenAnalysis, setCurrentPenAnalysis] = useState(null);

  const currentPenFromPenAnalysisData = selectedPenItem => {
    if (penAnalysisData && penAnalysisData?.pens) {
      const penData = penAnalysisData?.pens?.find(item => {
        return (
          item?.penId === selectedPenItem?.id ||
          item?.penId === selectedPenItem?.localId ||
          item?.penId === selectedPenItem?.sv_id
        );
        // return item?.penId === (selectedPenItem?.sv_id || selectedPenItem?.id);
      });
      if (penData) {
        return penData;
      } else {
        //ADDED TO RETURN DEFAULT INITIALIZED PEN OBJECT IN CASE NO PEN FOUND - USEFUL FOR SAVING
        return addCudChewingPenHelper({ pen: selectedPenItem });
      }
    }
    return null;
  };

  // useful for disabling graph button for cudChewing / noOfChews screen independently
  useEffect(() => {
    chewingTypeForHerdSum.current = chewingType;
  }, [chewingType]);

  useEffect(() => {
    const currentPenAnalysis = currentPenFromPenAnalysisData(selectedPen);
    //if current pen is dirty then it must have been altered
    //therefore, replace in pensFinalArray if already exist or add it if not
    if (isDirty) {
      const tempPenArray = { ...penFinalArray };

      if (currentPenAnalysis) {
        if (tempPenArray?.pens) {
          const index = tempPenArray.pens.findIndex(
            penObj => penObj?.penId === currentPenAnalysis?.penId,
          );
          if (index !== -1) {
            tempPenArray.pens[index] = currentPenAnalysis;
          } else {
            tempPenArray.pens.push(currentPenAnalysis);
          }
        } else {
          tempPenArray.pens = [];
          tempPenArray.pens.push(currentPenAnalysis);
        }
      }
      setPenFinalArray(tempPenArray);
    }
  }, [penAnalysisData, selectedPen]);

  useEffect(() => {
    if (healthCurrentActivePen) {
      if (pensList?.length > 0) {
        pickPenInReducerFromPensList(
          pensList,
          healthCurrentActivePen,
          setSelectedPen,
        );
      }
    } else {
      if (pensList?.length > 0) {
        setSelectedPen(pensList[0] || null);
      }
    }
  }, [pensList]);

  //saves selected pen in reducer on pen change
  useEffect(() => {
    dispatch(setSelectedPenRequest(selectedPen));
  }, [selectedPen?.id, selectedPen?.localId, selectedPen?.sv_id]);
  // }, [selectedPen?.sv_id, selectedPen?.id]);

  useEffect(() => {
    if (
      penAnalysisData &&
      penAnalysisData?.pens &&
      penAnalysisData?.pens?.length > 0
    ) {
      const penData = penAnalysisData?.pens?.find(item => {
        return (
          item?.penId === selectedPen?.id ||
          item?.penId === selectedPen?.localId ||
          item?.penId === selectedPen?.sv_id
        );
        // return item?.penId === (selectedPen?.sv_id || selectedPen?.id);
      });
      if (penData) {
        penData && setCurrentPenAnalysis(penData);
      } else {
        //ADDED TO RETURN DEFAULT INITIALIZED PEN OBJECT IN CASE NO PEN FOUND - USEFUL FOR SAVING
        setCurrentPenAnalysis(addCudChewingPenHelper({ pen: selectedPen }));
      }
      // penData && setCurrentPenAnalysis(penData);
    }
  }, [selectedPen]);

  const onChangeChewing = item => {
    setChewingType(item);
    saveCudChewingData();
  };

  const onChangePen = pen => {
    if (!isPenExist({ pen, penAnalysisData }) && isEditable) {
      // function to add new pen in penAnalysisData if it is not already exist
      dispatch(addCudChewingPen(addCudChewingPenHelper({ pen })));
    }
    setChewingType(PEN_ANALYSIS_CUD_CHEWING_DROPDOWN[0]);
    saveSelectedPenInReducer(dispatch, pen);
    setSelectedPen(pen);
    saveCudChewingData();
    setIsDirty(false);
    setEnableResults(false);
  };

  useEffect(() => {
    debounce_fun(currentPenAnalysis);
  }, [penFinalArray, currentPenAnalysis, chewingType]);

  //disables results button if pen's values sum to 0
  const debounce_fun = _.debounce(penAnalysisArray => {
    setEnableResults(
      shouldEnableResultsButton(
        TOOL_ANALYSIS_TYPES.PEN_ANALYSIS,
        penAnalysisArray,
        chewingType,
      ),
    );
  }, 1000);

  // save cud chewing data in db
  const saveCudChewingData = () => {
    if (
      penAnalysisData &&
      isEditable &&
      (isDirty || penFinalArray?.pens?.length > 0)
    ) {
      const payload = {
        visitId: visitState?.id,
        penAnalysisData: penFinalArray,
      };
      dispatch(pushUpdatedCudChewing(payload));
    }
  };

  const downloadPenAnalysisData = async (
    graphData,
    type,
    standardDeviation,
    isNoOfChews,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForPenAnalysisExport(
        visitState,
        graphData,
        selectedPen?.id || selectedPen?.sv_id,
        selectedPen?.name || selectedPen?.value || '',
        standardDeviation,
        isNoOfChews,
      );
      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.CUD_CHEWING_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType: EXPORT_REPORT_TYPES.CUD_CHEWING_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onSharePenAnalysisData = async (
    graphData,
    type,
    standardDeviation,
    isNoOfChews,
    exportMethod,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForPenAnalysisExport(
        visitState,
        graphData,
        selectedPen?.id || null,
        selectedPen?.value || '',
        standardDeviation,
        isNoOfChews,
      );
      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.CUD_CHEWING_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.CUD_CHEWING_PEN_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const renderInfoAlert = () => {
    if (
      chewingType?.type === CUD_CHEWING_TYPES.NUMBER_OF_CHEWS ||
      currentPenAnalysis?.[
        RUMEN_HEALTH_CUD_CHEWING_FIELDS.CUD_CHEWING_COWS_COUNT
      ]?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.TOTAL_COUNT] >= 10
    ) {
      return;
    }
    return <InfoAlert message={i18n.t('leastObserveAnimals')} />;
  };

  switch (currentStep) {
    case totalSteps:
      return (
        <View style={styles.container}>
          <PenAnalysisResult
            graphDataType={chewingType?.type}
            selectedPen={selectedPen}
            selectedVisits={selectedVisits}
            penAnalysisData={penAnalysisData}
            onDownloadPress={downloadPenAnalysisData}
            onSharePenAnalysisData={onSharePenAnalysisData}
            chewingType={chewingType}
          />
        </View>
      );

    default:
      return (
        <View style={styles.container}>
          <PenAnalysisToolComponent
            pensList={pensList}
            cudChewingList={PEN_ANALYSIS_CUD_CHEWING_DROPDOWN}
            selectedPen={selectedPen}
            chewingType={chewingType}
            onChangeChewing={onChangeChewing}
            onChangePen={onChangePen}
            currentPenAnalysis={currentPenAnalysis}
            penAnalysisData={penAnalysisData}
            isEditable={isEditable}
            isDirty={isDirty}
            setIsDirty={setIsDirty}
          />
          {renderInfoAlert()}
        </View>
      );
  }
};

export default RHCudChewingPenAnalysis;
