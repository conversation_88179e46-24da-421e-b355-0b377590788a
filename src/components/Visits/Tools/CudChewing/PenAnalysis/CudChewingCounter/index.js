// modules
import React, { useEffect, useRef, useState } from 'react';
import {
  Platform,
  ScrollView,
  KeyboardAvoidingView,
  Keyboard,
  View,
  Text,
} from 'react-native';
import { useDispatch } from 'react-redux';

// components
import CircularCounter from '../../../../../common/CircularCounter';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import {
  CONTENT_TYPE,
  RUMEN_HEALTH_CUD_CHEWING_FIELDS,
} from '../../../../../../constants/FormConstants';
import {
  COUNTER,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';

// helpers
import {
  penExistsInPublishedVisit,
  replaceCudChewingObject,
  updatedChewingPensCountNo,
  updatedChewingPensCountYes,
  getTotalCowsCountCudChew,
} from '../../../../../../helpers/rumenHealthHelper';

// actions
import { updateChewingCount } from '../../../../../../store/actions/tools/cudChewing';

const CudChewingCounter = ({
  currentPenAnalysis,
  penAnalysisData,
  isEditable = false,
  isDirty,
  setIsDirty,
  selectedPen,
}) => {
  const dispatch = useDispatch();

  //refs
  let notChewingRef = useRef();

  // local state
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [totalCowsCount, setTotalCowsCount] = useState(0);
  const [action, setAction] = useState(() => {});

  //helpers
  const focusNotChewingRef = () => {
    notChewingRef?.focus?.();
  };

  // updated pens array replaced with updated cud chewing object
  const getUpdatedPensArray = ({ cudChewingCountObject }) => {
    const updatedPensArray = replaceCudChewingObject({
      penId: currentPenAnalysis?.penId || '',
      cudChewingCount: cudChewingCountObject,
      pensList: penAnalysisData?.pens || [],
    });

    return updatedPensArray;
  };

  useEffect(() => {
    setTotalCowsCount(getTotalCowsCountCudChew(penAnalysisData, selectedPen));
  }, [selectedPen]);

  const handleChewingInput = count => {
    setIsDirty(true);
    const previousYesCount =
      currentPenAnalysis?.[
        RUMEN_HEALTH_CUD_CHEWING_FIELDS.CUD_CHEWING_COWS_COUNT
      ]?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.COUNT_YES];

    // increase yes count in cud chewing
    const cudChewingCountObject = updatedChewingPensCountYes(
      currentPenAnalysis || null,
      parseInt(count) - previousYesCount,
    );

    if (cudChewingCountObject) {
      dispatch(
        updateChewingCount(getUpdatedPensArray({ cudChewingCountObject })),
      );
    }
    let _totalCowsCount = totalCowsCount - previousYesCount;
    _totalCowsCount += parseInt(count);
    setTotalCowsCount(_totalCowsCount);
  };

  const handleChewingIncrement = () => {
    setIsDirty(true);
    // increase yes count in cud chewing
    const cudChewingCountObject = updatedChewingPensCountYes(
      currentPenAnalysis || null,
      COUNTER.INCREMENT_ONE,
    );

    if (cudChewingCountObject) {
      dispatch(
        updateChewingCount(getUpdatedPensArray({ cudChewingCountObject })),
      );
    }
    setTotalCowsCount(totalCowsCount + 1);
  };

  const handleChewingDecrement = () => {
    setIsDirty(true);
    // decrease yes count in cud chewing
    const cudChewingCountObject = updatedChewingPensCountYes(
      currentPenAnalysis || null,
      COUNTER.DECREMENT_ONE,
    );

    if (cudChewingCountObject) {
      dispatch(
        updateChewingCount(getUpdatedPensArray({ cudChewingCountObject })),
      );
    }
    setTotalCowsCount(totalCowsCount - 1);
  };

  const handleNotChewingInput = count => {
    setIsDirty(true);
    const previousNotCount =
      currentPenAnalysis?.[
        RUMEN_HEALTH_CUD_CHEWING_FIELDS.CUD_CHEWING_COWS_COUNT
      ]?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.COUNT_NO];

    // increase No count in cud chewing
    const cudChewingCountObject = updatedChewingPensCountNo(
      currentPenAnalysis || null,
      parseInt(count) - previousNotCount,
    );

    if (cudChewingCountObject) {
      dispatch(
        updateChewingCount(getUpdatedPensArray({ cudChewingCountObject })),
      );
    }
    let _totalCowsCount = totalCowsCount - previousNotCount;
    _totalCowsCount += parseInt(count);
    setTotalCowsCount(_totalCowsCount);
  };

  const handleNotChewingIncrement = () => {
    setIsDirty(true);
    // increase No count in cud chewing
    const cudChewingCountObject = updatedChewingPensCountNo(
      currentPenAnalysis || null,
      COUNTER.INCREMENT_ONE,
    );

    if (cudChewingCountObject) {
      dispatch(
        updateChewingCount(getUpdatedPensArray({ cudChewingCountObject })),
      );
    }
    setTotalCowsCount(totalCowsCount + 1);
  };

  const handleNotChewingDecrement = () => {
    setIsDirty(true);
    // decrease No count in cud chewing
    const cudChewingCountObject = updatedChewingPensCountNo(
      currentPenAnalysis || null,
      COUNTER.DECREMENT_ONE,
    );

    if (cudChewingCountObject) {
      dispatch(
        updateChewingCount(getUpdatedPensArray({ cudChewingCountObject })),
      );
    }
    setTotalCowsCount(totalCowsCount - 1);
  };

  return (
    <KeyboardAvoidingView
      style={styles.flexOne}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={
        Platform.OS === 'ios' && styles.keyboardVerticalOffset
      }>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.penNameRow}>
        <Text style={styles.totalCowsCountText}>
          {`${i18n.t('animalsObserved')}: ${totalCowsCount}`}
        </Text>
      </View>

      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.flexGrow}
        keyboardDismissMode="on-drag"
        keyboardShouldPersistTaps="handled">
        <CircularCounter
          title={i18n.t('chewing')}
          count={
            penExistsInPublishedVisit(isEditable, penAnalysisData, selectedPen)
              ? currentPenAnalysis?.[
                  RUMEN_HEALTH_CUD_CHEWING_FIELDS.CUD_CHEWING_COWS_COUNT
                ]?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.COUNT_YES] || 0
              : '-'
          }
          disabled={!isEditable}
          onDecrementClick={handleChewingDecrement}
          onIncrementClick={handleChewingIncrement}
          onChangeText={handleChewingInput}
          showInput={true}
          onSubmitEditing={() => {
            focusNotChewingRef();
          }}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: notChewingRef,
            });
          }}
        />
        <CircularCounter
          title={i18n.t('notChewing')}
          count={
            penExistsInPublishedVisit(isEditable, penAnalysisData, selectedPen)
              ? currentPenAnalysis?.[
                  RUMEN_HEALTH_CUD_CHEWING_FIELDS.CUD_CHEWING_COWS_COUNT
                ]?.[RUMEN_HEALTH_CUD_CHEWING_FIELDS.COUNT_NO] || 0
              : '-'
          }
          disabled={!isEditable}
          onDecrementClick={handleNotChewingDecrement}
          onIncrementClick={handleNotChewingIncrement}
          onChangeText={handleNotChewingInput}
          showInput={true}
          reference={input => {
            notChewingRef = input;
          }}
          onSubmitEditing={() => {
            Keyboard?.dismiss();
          }}
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              dismiss: true,
            });
          }}
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default CudChewingCounter;
