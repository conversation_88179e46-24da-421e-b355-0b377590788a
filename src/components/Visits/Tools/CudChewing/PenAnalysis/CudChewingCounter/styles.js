import customColor from '../../../../../../constants/theme/variables/customColor';
import customFont, { normalize } from '../../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../../constants/theme/variables/platform';

export default {
  container: {
    paddingHorizontal: normalize(20),
  },
  flexOne: {
    flex: 1,
  },
  flexGrow: {
    flexGrow: 1,
  },
  penNameRow: {
    paddingHorizontal: normalize(20),
    // height: normalize(54),
    backgroundColor: customColor.grey7,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: customColor.grey8,
    paddingVertical: normalize(8),
  },
  totalCowsCountView: { justifyContent: 'center', marginRight: 12 },
  totalCowsCountText: {
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.alphabetIndex,
    lineHeight: normalize(21),
    letterSpacing: 0.25,
  },
  keyboardVerticalOffset:
    Platform.deviceHeight < 700 ? normalize(270) : normalize(225),
};
