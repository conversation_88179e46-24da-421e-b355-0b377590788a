// modules
import React from 'react';

// constants
import { CUD_CHEWING_TYPES } from '../../../../../../constants/toolsConstants/RumenHealthCudChewingConstants';

// components
import CowChewsCounter from '../CowChewsCounter';
import CudChewingCounter from '../CudChewingCounter';
import PenAnalysisDropDown from '../PenAnalysisDropdown';

const PenAnalysisToolComponent = ({
  pensList,
  cudChewingList,
  selectedPen,
  chewingType,
  onChangeChewing,
  onChangePen,
  currentPenAnalysis,
  penAnalysisData,
  isEditable = false,
  isDirty,
  setIsDirty,
}) => {
  const renderToolTypesComponent = () => {
    switch (chewingType?.type) {
      case CUD_CHEWING_TYPES.CUD_CHEWING:
        return (
          <CudChewingCounter
            currentPenAnalysis={currentPenAnalysis}
            penAnalysisData={penAnalysisData}
            isEditable={isEditable}
            isDirty={isDirty}
            setIsDirty={setIsDirty}
            selectedPen={selectedPen}
          />
        );

      case CUD_CHEWING_TYPES.NUMBER_OF_CHEWS:
        return (
          <CowChewsCounter
            cudChewingPenData={currentPenAnalysis}
            penAnalysisData={penAnalysisData}
            isEditable={isEditable}
            isDirty={isDirty}
            setIsDirty={setIsDirty}
            selectedPen={selectedPen}
          />
        );
    }
  };

  return (
    <>
      <PenAnalysisDropDown
        pensList={pensList}
        cudChewingList={cudChewingList}
        selectedPen={selectedPen}
        chewingType={chewingType}
        onChangeChewing={onChangeChewing}
        onChangePen={onChangePen}
      />

      {renderToolTypesComponent()}
    </>
  );
};

export default PenAnalysisToolComponent;
