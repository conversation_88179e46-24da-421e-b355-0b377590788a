import { StyleSheet } from 'react-native';
import customFont, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../constants/theme/variables/customColor';

const styles = StyleSheet.create({
  parent: {
    flex: 1,
    marginHorizontal: normalize(20),
  },
  container: {
    borderRadius: 4,
    borderWidth: normalize(1),
    borderColor: customColor.grey4,
    paddingHorizontal: normalize(15),
    minHeight: normalize(45),
    justifyContent: 'center',
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  expandableContainer: {
    borderWidth: 1,
    borderTopWidth: 0,
    borderRadius: 4,
    borderWidth: normalize(1),
    borderColor: customColor.grey4,
    paddingVertical: normalize(10),
    paddingHorizontal: normalize(15),
    marginHorizontal: normalize(20),
  },
  phaseText: {
    fontSize: normalize(14),
    fontWeight: '500',
    fontFamily: customFont.HelveticaNeueRegular,
    color: customColor.grey1,
    lineHeight: normalize(14),
    left: normalize(20),
  },
  pointer: {
    position: 'absolute',
    width: normalize(10),
    height: normalize(10),
    borderRadius: normalize(5),
  },
  progressBarColors: [
    customColor.progressRed,
    customColor.leadingTitleColor,
    customColor.progressGreen,
  ],
});

export default styles;
