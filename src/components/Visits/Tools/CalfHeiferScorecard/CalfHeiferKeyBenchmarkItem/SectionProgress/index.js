// modules
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

// components
import ScorecardProgress from '../../../common/ScorecardProgress';

// styling constants
import { normalize } from '../../../../../../constants/theme/variables/customFont';

// helpers
import { singleSectionSurveyScore } from '../../../../../../helpers/calfHeiferScorecardHelper';

// localization
import i18n from '../../../../../../localization/i18n';

const SectionDetails = ({ sections }) => {
  const calfHeiferState = useSelector(
    state => state.calfHeiferScorecard.calfHeiferScorecardState,
  );

  if (!sections?.length) return null;

  return sections?.map(section => {
    const sectionQuestions = calfHeiferState?.sections?.find(
      item => item.index === section?.sectionIndex,
    );

    const sectionValue = singleSectionSurveyScore(sectionQuestions?.questions);

    return (
      <View
        key={`${section?.sectionIndex}_${section?.sectionName}`}
        style={styles.container}>
        <ScorecardProgress
          scoreValue={sectionValue}
          scoreName={i18n.t(section?.sectionName)}
          customContainerStyles={styles.progressStyles}
        />
      </View>
    );
  });
};

const styles = StyleSheet.create({
  container: {
    marginVertical: normalize(3),
  },
  progressStyles: {
    marginHorizontal: 0,
    marginVertical: 0,
  },
});

export default SectionDetails;
