// modules
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

// helpers
import { supportedQuestionInKeyBenchmarks } from '../../../../../../helpers/calfHeiferScorecardHelper';

// localization
import i18n from '../../../../../../localization/i18n';

// styling constants
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../constants/theme/variables/customColor';

// constants
import { CALF_HEIFER_SCORECARD } from '../../../../../../constants/FormConstants';

const SectionOptimalAnswers = ({ supportedQuestionIndex }) => {
  const calfHeiferState = useSelector(
    state => state.calfHeiferScorecard.calfHeiferScorecardState,
  );

  const questions = supportedQuestionInKeyBenchmarks(
    calfHeiferState,
    supportedQuestionIndex,
  );

  return (
    <>
      <View style={styles.headerContainer}>
        <Text style={styles.answerText}>{i18n.t('answers')}</Text>
        <Text style={[styles.answerText, styles.optimalText]}>
          {i18n.t('optimal')}
        </Text>
      </View>

      {questions?.length > 0 &&
        questions?.map(item => {
          const selectedAnswer =
            (item?.[CALF_HEIFER_SCORECARD.SELECTED_ANSWER] &&
              item?.[CALF_HEIFER_SCORECARD.SELECTED_ANSWER]?.[
                CALF_HEIFER_SCORECARD.ANSWER_TEXT
              ]) ||
            '-';

          const optimalAnswer =
            item?.[CALF_HEIFER_SCORECARD.AVAILABLE_ANSWERS]?.length > 0 &&
            item?.[CALF_HEIFER_SCORECARD.AVAILABLE_ANSWERS]?.find(
              item => !item?.isItemOfConcern,
            );

          return (
            <View key={item?.questionText} style={styles.itemContainer}>
              <Text style={styles.questionText}>
                {i18n.t(item?.[CALF_HEIFER_SCORECARD.QUESTION_TEXT])}
              </Text>
              <View style={styles.answersContainer}>
                <Text style={styles.selectedAnswerText}>
                  {i18n.t(selectedAnswer)}
                </Text>
                <Text
                  style={[styles.selectedAnswerText, styles.optimalAnswerText]}>
                  {i18n.t(optimalAnswer?.[CALF_HEIFER_SCORECARD.ANSWER_TEXT])}
                </Text>
              </View>
            </View>
          );
        })}
    </>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    marginTop: normalize(6),
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: normalize(5),
    borderBottomWidth: 1,
    borderColor: customColor.grey8,
  },
  answerText: {
    fontSize: normalize(13),
    fontWeight: '500',
    fontFamily: customFont.HelveticaNeueRegular,
    color: customColor.popoverTextColor,
    lineHeight: normalize(20),
  },
  optimalText: {
    color: customColor.primaryMain,
  },
  itemContainer: {
    marginTop: normalize(10),
    marginBottom: normalize(5),
  },
  questionText: {
    fontSize: normalize(12),
    fontWeight: '400',
    fontFamily: customFont.HelveticaNeueRegular,
    color: customColor.questionColor,
    opacity: 0.64,
    lineHeight: normalize(18),
  },
  answersContainer: {
    flexDirection: 'row',
    marginTop: normalize(4),
    justifyContent: 'space-between',
  },
  selectedAnswerText: {
    flex: 1,
    fontSize: normalize(13),
    fontWeight: '400',
    fontFamily: customFont.HelveticaNeueRegular,
    color: customColor.popoverTextColor,
    lineHeight: normalize(20),
  },
  optimalAnswerText: {
    color: customColor.primaryMain,
    alignSelf: 'flex-end',
    textAlign: 'right',
  },
});

export default SectionOptimalAnswers;
