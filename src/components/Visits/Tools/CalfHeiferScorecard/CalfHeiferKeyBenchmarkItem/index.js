// modules
import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

// icons
import { FORWARD_FILL_ICON } from '../../../../../constants/AssetSVGConstants';

// components
import SectionDetails from './SectionProgress';
import SectionOptimalAnswers from './SectionOptimalAnswers';

// localization
import i18n from '../../../../../localization/i18n';

// helpers
import { singleSectionSurveyScore } from '../../../../../helpers/calfHeiferScorecardHelper';

// constants
import { CIRCLE_COLOR_INTERPOLATION_RANGE } from '../../../../../constants/toolsConstants/ForageAuditConstants';

const CalfHeiferKeyBenchmarkItem = ({
  index,
  sections = [],
  supportedQuestionIndex = [],
}) => {
  const phaseProgress = useSharedValue(0);

  const calfHeiferState = useSelector(
    state => state.calfHeiferScorecard.calfHeiferScorecardState,
  );

  const [expandSection, setExpandSection] = useState(true);

  const _handleShowSectionDetails = () => setExpandSection(!expandSection);

  useEffect(() => {
    let sectionScore = 0;

    sections?.map(section => {
      const sectionQuestions = calfHeiferState?.sections?.find(
        item => item.index === section?.sectionIndex,
      );

      sectionScore += singleSectionSurveyScore(sectionQuestions?.questions);
    });

    phaseProgress.value =
      sectionScore > 0 ? sectionScore / sections.length : sectionScore;
  }, []);

  const animatedText = useDerivedValue(() =>
    withTiming(phaseProgress.value, {
      duration: 1000,
    }),
  );

  const phaseColor = useAnimatedStyle(() => ({
    backgroundColor: interpolateColor(
      animatedText.value,
      CIRCLE_COLOR_INTERPOLATION_RANGE,
      styles.progressBarColors,
    ),
  }));

  const iconStyles = useAnimatedStyle(() => ({
    transform: [
      {
        rotate: expandSection ? withSpring('0deg') : withSpring('90deg'),
      },
    ],
  }));

  const phaseIndex =
    index <= 1 ? `${index + 1}${index === 0 ? '' : '-3'}` : index + 2;

  return (
    <>
      <TouchableOpacity
        style={styles.parent}
        onPress={_handleShowSectionDetails}>
        <View style={styles.container}>
          <View style={styles.rowContainer}>
            <Animated.View style={[styles.pointer, phaseColor]} />

            <Text style={styles.phaseText}>
              {i18n.t('phase')} {phaseIndex}
            </Text>

            <Animated.View style={iconStyles}>
              <FORWARD_FILL_ICON />
            </Animated.View>
          </View>
        </View>
      </TouchableOpacity>

      {expandSection && (
        <View style={styles.expandableContainer}>
          <SectionDetails sections={sections} phaseProgress={phaseProgress} />

          <SectionOptimalAnswers
            sections={sections}
            supportedQuestionIndex={supportedQuestionIndex}
          />
        </View>
      )}
    </>
  );
};

export default CalfHeiferKeyBenchmarkItem;
