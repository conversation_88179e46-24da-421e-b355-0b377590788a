// modules
import React from 'react';
import { FlatList, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

// components
import SurveyCategoryItem from './SurveyCategoryItem';

// styling constants
import { normalize } from '../../../../../constants/theme/variables/customFont';

const CalfHeiferSurveyCategories = () => {
  const calfHeiferState = useSelector(
    state => state.calfHeiferScorecard.calfHeiferScorecardState,
  );

  /**
   * @description
   * adding component rendering condition to stop extra rendering of flat list.
   * only happens because of tool reducer updates for the first time.
   * @returns null
   */
  if (!calfHeiferState) return null;

  return (
    <FlatList
      data={calfHeiferState?.sections || []}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.containerStyle}
      style={styles.listStyles}
      renderItem={({ item, index }) => (
        <SurveyCategoryItem
          {...item}
          scorecardSection={calfHeiferState?.sections[index]}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  listStyles: {
    marginTop: normalize(12),
  },
  containerStyle: {
    paddingBottom: normalize(30),
  },
});

export default CalfHeiferSurveyCategories;
