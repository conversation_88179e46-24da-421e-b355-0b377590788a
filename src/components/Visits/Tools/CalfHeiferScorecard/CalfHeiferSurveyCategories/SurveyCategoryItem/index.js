// modules
import React from 'react';
import { View, Text, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// localization
import i18n from '../../../../../../localization/i18n';

// constants
import RouteConstants from '../../../../../../constants/RouteConstants';
import { CALF_HEIFER_SCORECARD } from '../../../../../../constants/FormConstants';

// actions
import { setActiveQuestionSection } from '../../../../../../store/actions/tools/calfHeiferScorecard';

// styling constants
import customColor from '../../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

/**
 * @description
 * Survey section flat list component item.
 * usable in calf heifer scorecard tool.
 *
 * required following parameters in order to component work.
 *
 * @param {Number} index section index required as the key for component
 * @param {String} section name required to show individual section name.
 * @param {Object} scorecardSection single tool reducer section state which contains updated questions and answers,
 * must be provided for section questions and answers count.
 *
 * @returns {React.Component} flat list component for survey section
 */
const SurveyCategoryItem = ({
  index,
  sectionName,
  scorecardSection = null,
}) => {
  const dispatch = useDispatch();
  const { navigate } = useNavigation();

  /**
   * @description
   * section item press listener,
   * creating payload with section index @requires @prop {Number} index, and @requires @prop {String} sectionName.
   * both props necessary to show questions list according to there sections.
   */
  const _handlePressSection = () => {
    const payload = {
      index,
      sectionName,
    };

    dispatch(setActiveQuestionSection(payload));

    navigate(RouteConstants.CALF_HEIFER_QUESTIONS);
  };

  // getting total number of questions.
  const totalQuestionsCount =
    scorecardSection?.[CALF_HEIFER_SCORECARD.QUESTIONS]?.length || 0;

  /**
   * getting total count of questions which are answered.
   * simply filtering all question which containing the @property {Object} selectedAnswer has any key-value pairs.
   */
  const totalQuestionsAnsweredCount =
    scorecardSection?.[CALF_HEIFER_SCORECARD.QUESTIONS]?.filter(
      item => item?.[CALF_HEIFER_SCORECARD.SELECTED_ANSWER],
    )?.length || 0;

  return (
    <TouchableWithoutFeedback onPress={_handlePressSection} key={index}>
      <View style={styles.container}>
        <Text style={styles.sectionName}>
          {i18n.t(sectionName) || sectionName}
        </Text>

        {totalQuestionsAnsweredCount === totalQuestionsCount ? (
          <View style={styles.completedContainer}>
            <Text style={styles.completedStatus}>{i18n.t('completed')}</Text>
          </View>
        ) : (
          <View style={styles.bulletContainer}>
            <Text style={styles.countText}>
              {totalQuestionsAnsweredCount}/{totalQuestionsCount}
            </Text>
          </View>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    height: normalize(40),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: customColor.grey4,
    borderRadius: normalize(4),
    marginVertical: normalize(8),
    paddingHorizontal: normalize(10),
  },
  sectionName: {
    flex: 1,
    fontFamily: customFont.HelveticaNeueRegular,
    color: customColor.grey1,
    fontSize: normalize(14),
    fontWeight: '400',
    lineHeight: normalize(14),
    textAlignVertical: 'center',
    marginRight: normalize(12),
  },
  bulletContainer: {
    borderRadius: normalize(2),
    height: normalize(18),
    backgroundColor: customColor.grey7,
    paddingHorizontal: normalize(5),
    justifyContent: 'center',
  },
  countText: {
    fontFamily: customFont.HelveticaNeueRegular,
    color: customColor.alphabetIndex,
    fontSize: normalize(12),
    fontWeight: '500',
    lineHeight: normalize(14),
  },
  completedContainer: {
    height: normalize(18),
    justifyContent: 'center',
    backgroundColor: customColor.completeStatusBackground,
    paddingHorizontal: normalize(5),
    borderRadius: normalize(2),
  },
  completedStatus: {
    fontSize: normalize(11),
    fontWeight: '500',
    fontFamily: customFont.HelveticaNeueRegular,
    color: customColor.completeStatus,
    lineHeight: normalize(14),
    textAlignVertical: 'center',
  },
});

export default SurveyCategoryItem;
