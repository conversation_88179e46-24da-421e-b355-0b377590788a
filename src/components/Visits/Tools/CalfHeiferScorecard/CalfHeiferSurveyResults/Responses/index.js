// modules
import React from 'react';
import { View, Text, FlatList } from 'react-native';
import { useSelector } from 'react-redux';

// constants
import { SCORECARD_RESULTS_HEADER_TABS } from '../../../../../../constants/AppConstants';
import { CALF_HEIFER_SCORECARD } from '../../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

const CalfHeiferResponses = ({ selectedResultTab = '' }) => {
  const calfHeiferState = useSelector(
    state => state.calfHeiferScorecard.calfHeiferScorecardState,
  );
  const activeSection = useSelector(
    state => state.calfHeiferScorecard.activeSection,
  );

  const surveySection = calfHeiferState?.sections?.find(
    item => item.index === activeSection?.index,
  );

  const renderItem = (item, index) => {
    const selectedAnswer =
      (item?.[CALF_HEIFER_SCORECARD.SELECTED_ANSWER] &&
        item?.[CALF_HEIFER_SCORECARD.SELECTED_ANSWER]?.[
          CALF_HEIFER_SCORECARD.ANSWER_TEXT
        ]) ||
      '-';

    const optimalAnswer =
      item?.[CALF_HEIFER_SCORECARD.AVAILABLE_ANSWERS]?.length > 0 &&
      item?.[CALF_HEIFER_SCORECARD.AVAILABLE_ANSWERS]?.find(
        item => !item?.isItemOfConcern,
      );

    return (
      <View style={styles.itemContainer}>
        <Text style={styles.questionText}>
          {i18n.t(item?.[CALF_HEIFER_SCORECARD.QUESTION_TEXT])}
        </Text>
        <View style={styles.answersContainer}>
          <Text style={styles.selectedAnswerText}>
            {i18n.t(selectedAnswer)}
          </Text>
          <Text style={[styles.selectedAnswerText, styles.optimalAnswerText]}>
            {i18n.t(optimalAnswer?.[CALF_HEIFER_SCORECARD.ANSWER_TEXT])}
          </Text>
        </View>
      </View>
    );
  };

  if (selectedResultTab !== SCORECARD_RESULTS_HEADER_TABS[1].key) return null;

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.answerText}>{i18n.t('answers')}</Text>
        <Text style={[styles.answerText, styles.optimalText]}>
          {i18n.t('optimal')}
        </Text>
      </View>

      {surveySection?.questions?.length > 0 && (
        <FlatList
          keyExtractor={item => item?.index?.toString()}
          data={surveySection?.questions || []}
          style={styles.listStyles}
          renderItem={({ item, index }) => renderItem(item, index)}
          ItemSeparatorComponent={() => <LineSeparator />}
        />
      )}
    </View>
  );
};

export const LineSeparator = () => {
  return <View style={styles.separator} />;
};

export default CalfHeiferResponses;
