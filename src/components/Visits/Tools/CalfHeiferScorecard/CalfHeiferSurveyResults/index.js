// modules
import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';

// components
import CalfHeiferScores from './Scores';
import CalfHeiferResponses from './Responses';
import ScorecardResultsHeaderTabs from '../../common/ScorecardResultsHeaderTabs';

// styling constants
import customColor from '../../../../../constants/theme/variables/customColor';

// constants
import { SCORECARD_RESULTS_HEADER_TABS } from '../../../../../constants/AppConstants';
import { CALF_HEIFER_TABS } from '../../../../../constants/toolsConstants/CalfHeiferScorecardConstants';

const CalfHeiferSurveyResults = ({ selectedTab = null }) => {
  const [selectedResultTab, setSelectedResultTab] = useState(
    SCORECARD_RESULTS_HEADER_TABS[0],
  );

  const onChangeTab = tab => {
    setSelectedResultTab(tab);
  };

  const shouldHideComponent =
    selectedTab === CALF_HEIFER_TABS.SECOND ? 'flex' : 'none';

  return (
    <View style={[styles.container, { display: shouldHideComponent }]}>
      <ScorecardResultsHeaderTabs
        selectedTab={selectedResultTab}
        onChangeTab={onChangeTab}
      />

      <CalfHeiferScores selectedResultTab={selectedResultTab.key} />

      <CalfHeiferResponses selectedResultTab={selectedResultTab.key} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: customColor.white,
    borderTopWidth: 0.5,
    borderColor: customColor.grey8,
  },
});

export default CalfHeiferSurveyResults;
