// modules
import React from 'react';
import { useSelector } from 'react-redux';

// components
import ScorecardProgress from '../../../common/ScorecardProgress';

// helpers
import { singleSectionSurveyScore } from '../../../../../../helpers/calfHeiferScorecardHelper';

// constants
import { SCORECARD_RESULTS_HEADER_TABS } from '../../../../../../constants/AppConstants';

// localization
import i18n from '../../../../../../localization/i18n';

const CalfHeiferScores = ({ selectedResultTab = '' }) => {
  const calfHeiferState = useSelector(
    state => state.calfHeiferScorecard.calfHeiferScorecardState,
  );
  const activeSection = useSelector(
    state => state.calfHeiferScorecard.activeSection,
  );

  const surveySection = calfHeiferState?.sections?.find(
    item => item.index === activeSection?.index,
  );

  const scoreValue = singleSectionSurveyScore(surveySection?.questions);

  if (selectedResultTab !== SCORECARD_RESULTS_HEADER_TABS[0].key) return null;

  return (
    <ScorecardProgress
      scoreValue={scoreValue}
      scoreName={i18n.t(activeSection?.sectionName)}
    />
  );
};

export default CalfHeiferScores;
