// modules
import React from 'react';
import { View, Text, FlatList } from 'react-native';
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

// components
import QuestionListItem from './QuestionListItem';

// constants
import { CALF_HEIFER_TABS } from '../../../../../constants/toolsConstants/CalfHeiferScorecardConstants';

// localization
import i18n from '../../../../../localization/i18n';

const CalfHeiferQuestionsList = ({ selectedTab = null }) => {
  const calfHeiferState = useSelector(
    state => state.calfHeiferScorecard.calfHeiferScorecardState,
  );
  const activeSection = useSelector(
    state => state.calfHeiferScorecard.activeSection,
  );

  const sectionQuestions = calfHeiferState?.sections?.find(
    item => item.index === activeSection?.index,
  );

  const shouldHideComponent =
    selectedTab === CALF_HEIFER_TABS.FIRST ? 'flex' : 'none';

  return (
    <View
      style={[
        styles.container,
        {
          display: shouldHideComponent,
        },
      ]}>
      <View style={styles.headerContainer}>
        <Text style={styles.sectionName}>
          {i18n.t(activeSection?.sectionName) || ''}
        </Text>
      </View>

      <FlatList
        keyExtractor={item => item?.index?.toString()}
        data={sectionQuestions?.questions || []}
        style={styles.listStyles}
        contentContainerStyle={styles.contentContainerStyles}
        renderItem={({ item, index }) => (
          <QuestionListItem
            question={item}
            index={index}
            totalQuestion={sectionQuestions?.questions?.length}
          />
        )}
      />
    </View>
  );
};

export default CalfHeiferQuestionsList;
