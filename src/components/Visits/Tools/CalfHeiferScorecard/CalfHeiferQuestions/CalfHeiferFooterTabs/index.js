// modules
import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// constants
import { IPHONE_LIST } from '../../../../../../constants/AppConstants';
import { BUTTON_TYPE } from '../../../../../../constants/FormConstants';
import { CHEVRON_LEFT_ICON } from '../../../../../../constants/AssetSVGConstants';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import FormButton from '../../../../../common/FormButton';

const CalfHeiferFooterTabs = ({
  showBackButton,
  showResultsButton,
  onLeftArrowClick,
  onNextPress,
}) => {
  const insets = useSafeAreaInsets();
  // temp check for styling
  const modelName = DeviceInfo.getModel();
  const addBottomSpace = IPHONE_LIST.includes(modelName);

  return (
    <>
      <View
        style={[
          styles.bottomButtonView,
          addBottomSpace && styles.androidBottomSpacing,
        ]}>
        <View style={styles.nestedButtonContainer}>
          {showBackButton && (
            <TouchableOpacity
              onPress={onLeftArrowClick}
              style={styles.arrowIcon}>
              <CHEVRON_LEFT_ICON {...styles.leftIconStyles} />
            </TouchableOpacity>
          )}

          <View style={styles.arrowIcon}>
            {showResultsButton && (
              <FormButton
                type={BUTTON_TYPE.PRIMARY}
                label={i18n.t('results')}
                onPress={onNextPress}
                customButtonStyle={styles.button}
              />
            )}
          </View>
        </View>

        {!addBottomSpace && (
          <View
            style={[
              styles.bottomView,
              {
                height: insets.bottom,
                bottom: insets.bottom * -1,
              },
            ]}
          />
        )}
      </View>
    </>
  );
};

export default CalfHeiferFooterTabs;
