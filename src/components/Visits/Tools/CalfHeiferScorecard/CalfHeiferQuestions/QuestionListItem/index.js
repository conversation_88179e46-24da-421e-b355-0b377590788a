// modules
import React from 'react';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// constants
import { CALF_HEIFER_SCORECARD } from '../../../../../../constants/FormConstants';

// helpers
import { updateSelectedQuestionWithAnswerInScorecardTool } from '../../../../../../helpers/toolHelper';

// components
import ScorecardAnswers from '../../../common/ScorecardAnswer';
import ScorecardQuestion from '../../../common/ScorecardQuestion';

// actions
import { updateAnsweredQuestionRequest } from '../../../../../../store/actions/tools/calfHeiferScorecard';

const QuestionListItem = ({ index, question, totalQuestion = 0 }) => {
  const dispatch = useDispatch();

  const isEditable = useSelector(state => state.visit.visit?.isEditable);

  const _handleSelectAnswer = selectedAnswer => {
    if (
      question?.[CALF_HEIFER_SCORECARD.SELECTED_ANSWER] &&
      question?.[CALF_HEIFER_SCORECARD.SELECTED_ANSWER]?.[
        CALF_HEIFER_SCORECARD.INDEX
      ] === selectedAnswer?.[CALF_HEIFER_SCORECARD.INDEX]
    ) {
      return;
    }

    const updatedQuestion = updateSelectedQuestionWithAnswerInScorecardTool({
      selectedAnswerKey: CALF_HEIFER_SCORECARD.SELECTED_ANSWER,
      question,
      selectedAnswer,
    });

    if (updatedQuestion) {
      dispatch(updateAnsweredQuestionRequest(updatedQuestion));
    }
  };

  return (
    <View key={index}>
      <ScorecardQuestion
        formKeys={CALF_HEIFER_SCORECARD}
        question={question}
        totalQuestion={totalQuestion}
      />

      <ScorecardAnswers
        index={index}
        isEditable={isEditable}
        formKeys={CALF_HEIFER_SCORECARD}
        selectedAnswer={question?.[CALF_HEIFER_SCORECARD.SELECTED_ANSWER]}
        availableAnswers={question?.[CALF_HEIFER_SCORECARD.AVAILABLE_ANSWERS]}
        onAnswerPress={_handleSelectAnswer}
      />
    </View>
  );
};

export default QuestionListItem;
