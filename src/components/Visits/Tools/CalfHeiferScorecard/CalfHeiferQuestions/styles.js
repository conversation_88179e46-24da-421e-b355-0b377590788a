import colors from '../../../../../constants/theme/variables/customColor';
import { normalize } from '../../../../../constants/theme/variables/customFont';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    height: normalize(40),
    justifyContent: 'center',
    borderTopWidth: 0.5,
    borderColor: colors.grey8,
    paddingHorizontal: normalize(20),
  },
  sectionName: {
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    fontWeight: '500',
    color: colors.grey1,
  },
  listStyles: {
    paddingHorizontal: normalize(20),
  },
  contentContainerStyles: {
    gap: normalize(15),
  },
};
