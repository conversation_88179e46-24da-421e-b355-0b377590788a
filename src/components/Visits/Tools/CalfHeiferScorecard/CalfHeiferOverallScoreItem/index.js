// modules
import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';

// components
import ScorecardProgress from '../../common/ScorecardProgress';

// constants
import { CALF_HEIFER_SCORECARD } from '../../../../../constants/FormConstants';
import RouteConstants from '../../../../../constants/RouteConstants';

// helpers
import { singleSectionSurveyScore } from '../../../../../helpers/calfHeiferScorecardHelper';

// icons
import { FORWARD_FILL_ICON } from '../../../../../constants/AssetSVGConstants';

// styling constants
import { normalize } from '../../../../../constants/theme/variables/customFont';

// localization
import i18n from '../../../../../localization/i18n';

/**
 * Renders a single section's scorecard progress within the Calf Heifer Overall Scorecard.

 * @param {Object} props - Component props.
 * @param {Object} props.section - The section object containing questions and section name.
 * @param {number} props.index - The index of the section.
 * @returns {JSX.Element} - JSX element representing the scorecard progress.
 */
const CalfHeiferOverallScoreItem = ({ section = null, index = null }) => {
  const { push } = useNavigation();

  const _handlePressKeyBenchmarks = () =>
    push(RouteConstants.CALF_HEIFER_KEY_BENCHMARKS);

  /**
   * Calculates the score for a single section.
   *
   * @param {Array} questions - An array of questions for the section.
   * @returns {number} - The calculated score.
   */
  const scoreValue = singleSectionSurveyScore(
    section?.[CALF_HEIFER_SCORECARD.QUESTIONS],
  );

  return (
    <TouchableOpacity
      key={index}
      activeOpacity={0.5}
      disabled={section?.index !== 4}
      style={styles.container}
      onPress={_handlePressKeyBenchmarks}>
      <ScorecardProgress
        scoreValue={scoreValue}
        scoreName={i18n.t(section?.[CALF_HEIFER_SCORECARD.SECTION_NAME]) || ''}
        customContainerStyles={styles.customContainerStyles}
      />

      {section?.index === 4 && (
        <View style={styles.iconContainer}>
          <FORWARD_FILL_ICON />
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  customContainerStyles: {
    marginVertical: normalize(5),
    flex: 1,
  },
  iconContainer: {
    right: normalize(40),
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CalfHeiferOverallScoreItem;
