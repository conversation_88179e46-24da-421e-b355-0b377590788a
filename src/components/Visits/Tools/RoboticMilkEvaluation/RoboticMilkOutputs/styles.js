import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  container: {
    paddingHorizontal: normalize(16),
    paddingTop: normalize(16),
  },
  outputContainer: {
    flexDirection: 'row',
    backgroundColor: colors.primaryMain,
    borderRadius: normalize(4),
    height: normalize(40),
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(11),
  },
  outputText: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.15,
    color: colors.white,
  },
  icon: {
    height: normalize(14),
    width: normalize(14),
  },
  innerContainer: {
    paddingTop: normalize(16),
  },
  heading: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    paddingBottom: normalize(16),
  },
  row: {
    marginBottom: normalize(16),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  value: {
    fontFamily: fonts.HelveticaNeueMedium,
    lineHeight: normalize(18),
  },
};
