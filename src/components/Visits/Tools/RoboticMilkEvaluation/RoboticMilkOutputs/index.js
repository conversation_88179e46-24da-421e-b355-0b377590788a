// modules
import React from 'react';
import { useState, memo } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import { useDispatch, useSelector } from 'react-redux';
import {
  KG_REGEX,
  UNIT_OF_MEASURE,
} from '../../../../../constants/AppConstants';

// constants
import { PROSPECT_CHEVRON_DOWN_ICON } from '../../../../../constants/AssetSVGConstants';
import { ROBOTIC_MILK_EVALUATION } from '../../../../../constants/FormConstants';
import {
  AMS_UTILIZATION,
  COW_EFFICIENCY,
} from '../../../../../constants/toolsConstants/RoboticMilkConstants';

// helpers
import { getWeightUnitByMeasure } from '../../../../../helpers/appSettingsHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';

const RoboticMilkOutputs = ({ roboticMilkState }) => {
  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit?.visit);
  const unitOfMeasure = visitState?.unitOfMeasure || {};

  //data parsing
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const rotation = useSharedValue(0);

  const [expandOutput, setExpandOutput] = useState(true);

  const outputObject =
    roboticMilkState?.[
      ROBOTIC_MILK_EVALUATION.VISIT_ROBOTIC_MILK_EVALUATION_DATA
    ]?.[ROBOTIC_MILK_EVALUATION.OUTPUTS] || null;

  const handleExpandPress = () => {
    setExpandOutput(!expandOutput);
    rotation.value = withSpring(expandOutput ? 0 : 180);
  };

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotation.value}deg` }],
    };
  });

  const getPoundsLabel = label => {
    if (label === i18n.t('milkPerRobot')) {
      let value = label.replace(KG_REGEX, weightUnit);
      return value;
    } else return label;
  };

  const getPoundsValues = item => {
    let value = (outputObject?.[item]).toFixed(2);

    const formattedNumber =
      convertInputNumbersToRegionalBasis(value, 2, true) || value;

    return formattedNumber;
  };

  const getFormattedValue = value => {
    value = value.toFixed(2);

    const formattedNumber =
      convertInputNumbersToRegionalBasis(value, 2, true) || value;

    return formattedNumber;
  };

  const getAmsUtilizationResult = item => {
    return outputObject?.[item] && outputObject?.[item] > 0
      ? unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
        ? getPoundsValues(item)
        : getFormattedValue(outputObject?.[item])
      : 0.0 || '-';
  };

  const renderAmsUtilization = () => {
    const amsUtilizationKeys = Object.keys(outputObject)?.filter(item =>
      Object.keys(AMS_UTILIZATION)?.includes(item),
    );

    return amsUtilizationKeys?.map((item, index) => (
      <View style={styles.row} key={index}>
        <Text style={styles.label}>
          {unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
            ? getPoundsLabel(AMS_UTILIZATION?.[item] || '')
            : AMS_UTILIZATION?.[item] || ''}
        </Text>
        <Text style={[styles.label, styles.value]}>
          {getAmsUtilizationResult(item)}
        </Text>
      </View>
    ));
  };

  const getCowEfficiency = item => {
    return outputObject?.[item] && outputObject?.[item] > 0
      ? getFormattedValue(outputObject?.[item])
      : 0.0 || '-';
  };

  const renderCowEfficiency = () => {
    const cowEfficiencyKeys = Object.keys(outputObject)?.filter(item =>
      Object.keys(COW_EFFICIENCY)?.includes(item),
    );

    return cowEfficiencyKeys?.map((item, index) => (
      <View style={styles.row} key={index}>
        <Text style={styles.label}>{COW_EFFICIENCY?.[item] || ''}</Text>
        <Text style={[styles.label, styles.value]}>
          {getCowEfficiency(item)}
        </Text>
      </View>
    ));
  };

  const renderHeading = title => <Text style={styles.heading}>{title}</Text>;

  return (
    <View style={styles.container}>
      <TouchableOpacity
        activeOpacity={0.7}
        style={styles.outputContainer}
        onPress={handleExpandPress}>
        <Text style={styles.outputText}>{i18n.t('outputs')}</Text>

        <Animated.View style={[animatedStyles]}>
          <PROSPECT_CHEVRON_DOWN_ICON {...styles.icon} />
        </Animated.View>
      </TouchableOpacity>

      {expandOutput && (
        <Animated.View
          style={styles.innerContainer}
          // entering={FadeInUp}
          // exiting={FadeOutUp}
        >
          {renderHeading(i18n.t('amsUtilization'))}

          {renderAmsUtilization()}

          {renderHeading(i18n.t('cowEfficiency'))}

          {renderCowEfficiency()}
        </Animated.View>
      )}
    </View>
  );
};

export default memo(RoboticMilkOutputs);
