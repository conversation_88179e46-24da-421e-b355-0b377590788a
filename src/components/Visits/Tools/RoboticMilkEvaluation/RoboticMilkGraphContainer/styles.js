import colors from '../../../../../constants/theme/variables/customColor';
import { normalize } from '../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../constants/theme/variables/platform';
import { Platform as RNPlatform } from 'react-native';
import DeviceInfo from 'react-native-device-info';

export default {
  domainPadding: {
    x: [0, 5],
  },
  transparentValue: {
    fill: colors.transparent,
  },
  axisStyles: {
    stroke: colors.grey1,
    strokeWidth: 1,
    strokeDasharray: '1, 5',
  },

  graphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth,
    },
    android: {
      width: Platform.deviceWidth,
    },
  }),

  singleLineGraphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth + 40,
    },
    android: {
      width: Platform.deviceWidth + 40,
    },
  }),

  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.34
          : Platform.deviceHeight * 0.43,
    },
    android: {
      height: Platform.deviceHeight / 2.3,
    },
  }),

  noTabsHeight: RNPlatform.select({
    ios: {
      height: Platform.deviceHeight < 700 ? 35 : 30,
    },
    android: {
      height: normalize(40),
    },
  }),

  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight - 50
          : Platform.deviceHeight - 60,
    },
    android: {
      width: Platform.deviceHeight - 10,
    },
  }),

  graphHeightLandscape: RNPlatform.select({
    ios: {
      height: Platform.deviceWidth - normalize(60),
      // Platform.deviceWidth < 400
      //   ? Platform.deviceWidth - normalize(60)
      //   : Platform.deviceWidth - normalize(60),
    },
    android: {
      height: Platform.deviceWidth - (DeviceInfo.isTablet() ? 200 : 90),
    },
  }),
};
