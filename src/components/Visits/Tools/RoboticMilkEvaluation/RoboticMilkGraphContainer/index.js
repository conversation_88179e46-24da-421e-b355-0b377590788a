// modules
import React from 'react';
import { VictoryAxis, VictoryLabel } from 'victory-native';
import { format } from 'date-fns';

// constants
import { ROBOTIC_MILK_EVALUATION } from '../../../../../constants/FormConstants';
import { DATE_FORMATS } from '../../../../../constants/AppConstants';

// helpers
import {
  addSpace,
  getLeftAxisLabel,
  getRightAxisLabel,
} from '../../../../../helpers/roboticMilkEvaluationHelper';

// components
import RoboticMilkMultiAxisGraph from '../ResultsGraph';

// styles
import styles from './styles';

const RoboticMilkGraph = ({
  graphData,
  graphConfig,
  isLandscapeModalVisible,
  selectedResultsTab,
  selectedRecentVisits,
  roboticMilkState,
  unitOfMeasure,
  weightUnit,
}) => {
  const getAxisValue = () => {
    if (graphData?.length > 0) {
      const axisValue = `${format(
        selectedRecentVisits[selectedRecentVisits?.length - 1]?.visitDate,
        DATE_FORMATS.MM_dd,
      )}${addSpace(selectedRecentVisits?.length - 1)}`;

      return axisValue;
    }
  };

  const showXAxis = (
    <VictoryAxis
      dependentAxis
      tickLabelComponent={<VictoryLabel style={styles.transparentValue} />}
      axisValue={getAxisValue()}
      style={{
        axis: styles.axisStyles,
      }}
    />
  );

  return (
    <RoboticMilkMultiAxisGraph
      data={graphData}
      width={
        isLandscapeModalVisible
          ? styles.graphWidthLandscape.width
          : [
              ROBOTIC_MILK_EVALUATION.MILKING_FAILURES,
              ROBOTIC_MILK_EVALUATION.COWS_PER_ROBOT,
            ].includes(selectedResultsTab?.key)
          ? styles.singleLineGraphWidth.width
          : styles.graphWidth.width
      }
      height={
        isLandscapeModalVisible
          ? styles.graphHeightLandscape.height
          : ROBOTIC_MILK_EVALUATION.AVERAGE_CONCENTRATE +
              ROBOTIC_MILK_EVALUATION.CONCENTRATE_PER_100_KG_MILK ===
            selectedResultsTab?.key
          ? styles.noTabsHeight.height + styles.graphHeight.height
          : styles.graphHeight.height
      }
      leftYAxisLabel={
        getLeftAxisLabel(
          selectedResultsTab,
          roboticMilkState,
          unitOfMeasure,
          weightUnit,
        ) || ''
      }
      rightYAxisLabel={
        getRightAxisLabel(
          selectedResultsTab,
          roboticMilkState,
          unitOfMeasure,
          weightUnit,
        ) || ''
      }
      graphConfig={graphConfig}
      showXAxis={showXAxis}
      customLeftAxisTickFormat={true}
      customRightAxisTickFormat={true}
    />
  );
};

export default RoboticMilkGraph;
