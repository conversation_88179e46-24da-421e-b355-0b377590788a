// modules
import React from 'react';
import { useEffect } from 'react';
import Animated, {
  useAnimatedProps,
  withTiming,
  withDelay,
  useSharedValue,
} from 'react-native-reanimated';
import { Path } from 'react-native-svg';

// helpers
import { generateFieldSvgCircleData } from '../../../../../helpers/roboticMilkEvaluationHelper';

const AnimatedPath = Animated.createAnimatedComponent(Path);

const RoboticMilkAnalysisCirclePath = ({ analysisField }) => {
  const svgCirclePath = generateFieldSvgCircleData(analysisField);

  return svgCirclePath?.map((path, index) => (
    <TestAnimate path={path} key={index} />
  ));
};

export default RoboticMilkAnalysisCirclePath;

const TestAnimate = ({ path }) => {
  const progress = useSharedValue(0);

  useEffect(() => {
    progress.value = withDelay(250, withTiming(1, { duration: 500 }));
  }, []);

  const animatedProps = useAnimatedProps(() => {
    const d = path?.d;

    return {
      d,
    };
  });

  return (
    <AnimatedPath
      x={100}
      y={80}
      d={path?.d}
      fill={path?.fill}
      rotation={'218'}
      animatedProps={animatedProps}
    />
  );
};
