// modules
import React from 'react';
import { ScrollView, TouchableOpacity, View, Text } from 'react-native';

// styles
import styles from './styles';

const ResultsTrendTabs = ({ tabs, onTabChange, selectedResultsTab }) => {
  const onTabSelect = tab => {
    if (selectedResultsTab?.key === tab?.key) return;

    requestAnimationFrame(() => onTabChange(tab));
  };

  return (
    <View>
      <ScrollView
        horizontal={true}
        style={styles.scrollView}
        showsHorizontalScrollIndicator={false}>
        {tabs?.map(item => (
          <TouchableOpacity
            key={item?.key}
            style={styles.button}
            activeOpacity={1}
            onPress={() => onTabSelect(item)}>
            <Text
              style={[
                styles.tabText,
                selectedResultsTab?.key === item?.key && styles.selectedText,
              ]}>
              {item?.value}
            </Text>

            <View
              style={[
                selectedResultsTab?.key === item?.key && styles.selectedButton,
              ]}
            />
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

export default ResultsTrendTabs;
