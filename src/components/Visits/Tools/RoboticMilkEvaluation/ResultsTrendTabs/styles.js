import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default {
  scrollView: {
    marginVertical: normalize(5),
    borderBottomWidth: 1,
    borderColor: colors.accordionBorder,
  },
  button: {
    paddingHorizontal: normalize(20),
  },
  selectedButton: {
    borderBottomWidth: 3,
    borderColor: colors.primaryMain,
    marginTop: normalize(3),
  },
  tabText: {
    fontSize: normalize(14),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.alphabetIndex,
    lineHeight: normalize(16),
    letterSpacing: 0.15,
  },
  selectedText: {
    color: colors.primaryMain,
  },
};
