// modules
import React from 'react';
import { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Popover } from 'native-base';

// styles
import styles from './styles';

// constants
import { BLACK_INFO_ICON } from '../../../../../constants/AssetSVGConstants';
import {
  KG_REGEX,
  UNIT_OF_MEASURE,
} from '../../../../../constants/AppConstants';

const MoreInfoPopOver = ({
  infoText,
  robotType,
  unitOfMeasure,
  weightUnit,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <View style={styles.iconButton}>
      <Popover
        isOpen={isOpen}
        onClose={() => setIsOpen(!isOpen)}
        trigger={triggerProps => {
          return (
            <TouchableOpacity
              style={styles.iconButton}
              {...triggerProps}
              onPress={() => setIsOpen(!isOpen)}>
              <BLACK_INFO_ICON {...styles.iconStyle(isOpen)} />
            </TouchableOpacity>
          );
        }}>
        <Popover.Content
          style={styles.popoverStyle}
          overflow={'visible'}
          rounded={'md'}>
          <Popover.Arrow style={styles.arrowStyles} />
          <Popover.Body
            style={styles.popoverBodyStyle}
            rounded={'md'}
            shadow={'0'}>
            {infoText?.[robotType]?.map((value, index) => (
              <Text style={styles.text} key={index}>
                {unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
                  ? !!value?.length
                    ? value.replace(KG_REGEX, weightUnit)
                    : ''
                  : value}
              </Text>
            ))}
          </Popover.Body>
        </Popover.Content>
      </Popover>
    </View>
  );
};

export default MoreInfoPopOver;
