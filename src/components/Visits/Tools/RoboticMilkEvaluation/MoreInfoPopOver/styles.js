import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default {
  iconButton: {
    position: 'absolute',
    alignSelf: 'flex-end',
    top: normalize(3),
    zIndex: 10,
  },
  iconStyle: isOpen => ({
    fill: isOpen ? colors.primaryMain : colors.grey1,
  }),
  icon: {
    // width: normalize(16),
    // height: normalize(16),
    // stroke: colors.grey1,
  },
  popoverStyle: {
    // marginTop: 5,
    marginRight: normalize(10),
    borderWidth: 1,
    borderColor: colors.popoverBorderColor,
    backgroundColor: colors.white,
    shadowColor: colors.popoverShadowColor,
    shadowOffset: {
      width: -2,
      height: 15,
    },
    shadowOpacity: 0.35,
    shadowRadius: 20,
    elevation: 8,
  },
  popoverBodyStyle: {
    backgroundColor: colors.white,
    shadowColor: colors.popoverShadowColor,
    shadowOffset: {
      width: -2,
      height: 15,
    },
    shadowOpacity: 0.35,
    shadowRadius: 20,
    elevation: 8,
  },
  arrowStyles: {
    backgroundColor: colors.white,
    borderColor: colors.popoverBorderColor,
  },
  text: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(20),
    letterSpacing: 0.25,
    color: colors.popoverTextColor,
  },
};
