import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  cardHeader: {
    height: normalize(40),
    backgroundColor: colors.primaryMain,
    flexDirection: 'row',
    paddingLeft: normalize(16),
    paddingRight: normalize(10),
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerLabel: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(20),
    color: colors.white,
  },
  headerValue: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(16),
    lineHeight: normalize(20),
    color: colors.white,
  },
};
