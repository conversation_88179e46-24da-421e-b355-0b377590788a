// modules
import React, { useMemo } from 'react';
import { View, Text } from 'react-native';
import {
  KG_REGEX,
  UNIT_OF_MEASURE,
} from '../../../../../constants/AppConstants';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';
import { convertNumberToString } from '../../../../../helpers/alphaNumericHelper';

const RoboticMilkAnalysisHeaderContainer = ({
  unitOfMeasure,
  weightUnit,
  analysisField,
  roboticMilkEvaluationData,
}) => {
  let value = useMemo(
    () =>
      convertInputNumbersToRegionalBasis(
        roboticMilkEvaluationData?.outputs?.[analysisField?.key],
        1,
        true,
      ) ||
      roboticMilkEvaluationData?.[analysisField?.key] ||
      0,
    [roboticMilkEvaluationData],
  );

  const getLabelInPounds = label => {
    if (label?.length) {
      label = label?.replace(KG_REGEX, weightUnit);
      return label;
    }
  };

  return (
    <View style={styles.cardHeader}>
      <Text style={styles.headerLabel}>
        {unitOfMeasure == UNIT_OF_MEASURE.IMPERIAL
          ? getLabelInPounds(analysisField?.label)
          : analysisField?.label || '-'}
      </Text>

      <Text style={styles.headerValue}>{value}</Text>
    </View>
  );
};

export default RoboticMilkAnalysisHeaderContainer;
