// modules
import React from 'react';
import { View } from 'react-native';
import { useDispatch } from 'react-redux';

// components
import CustomBottomSheet from '../../../../common/CustomBottomSheet';

// constants
import {
  BOTTOM_SHEET_TYPE,
  ROBOTIC_MILK_EVALUATION,
} from '../../../../../constants/FormConstants';

// actions
import {
  updateRoboticMilkCowFlowDesign,
  updateRoboticMilkRobotType,
} from '../../../../../store/actions/tools/roboticMilk';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';

const FormDropDownContainer = ({
  robotTypeEnums,
  cowFlowDesignEnums,
  roboticMilkState,
  isEditable = false,
  setIsDirty,
}) => {
  const dispatch = useDispatch();

  const handleChangeRobotType = robotType => {
    setIsDirty && setIsDirty(true);
    dispatch(updateRoboticMilkRobotType(robotType?.key));
  };

  const handleChangeCowFlowDesign = cowFlowDesign => {
    setIsDirty && setIsDirty(true);
    dispatch(updateRoboticMilkCowFlowDesign(cowFlowDesign?.key));
  };

  return (
    <>
      <CustomBottomSheet
        value={
          roboticMilkState?.[
            ROBOTIC_MILK_EVALUATION.VISIT_ROBOTIC_MILK_EVALUATION_DATA
          ]?.[ROBOTIC_MILK_EVALUATION.ROBOT_TYPE] || ''
        }
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        selectLabel={i18n.t('robotType')}
        label={i18n.t('robotType')}
        infoText={i18n.t('selectOne')}
        disabled={!isEditable}
        data={robotTypeEnums || []}
        required
        placeholder={i18n.t('selectOne')}
        searchPlaceHolder={i18n.t('search')}
        newRequiredDesign
        onChange={handleChangeRobotType}
        customLabelStyles={styles.customLabelStyles}
        selectFieldProps={{
          numberOfLines: 1,
        }}
      />
      <View style={styles.spacer} />
      <CustomBottomSheet
        value={
          roboticMilkState?.[
            ROBOTIC_MILK_EVALUATION.VISIT_ROBOTIC_MILK_EVALUATION_DATA
          ]?.[ROBOTIC_MILK_EVALUATION.COW_FLOW_DESIGN] || ''
        }
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        selectLabel={i18n.t('cowFlowDesign')}
        label={i18n.t('cowFlowDesign')}
        infoText={i18n.t('selectOne')}
        disabled={!isEditable}
        data={cowFlowDesignEnums || []}
        required
        placeholder={i18n.t('selectOne')}
        searchPlaceHolder={i18n.t('search')}
        newRequiredDesign
        onChange={handleChangeCowFlowDesign}
        customLabelStyles={styles.customLabelStyles}
        selectFieldProps={{
          numberOfLines: 1,
        }}
      />
    </>
  );
};

export default FormDropDownContainer;
