// modules
import React, { useState, useEffect } from 'react';
import { ScrollView, SafeAreaView } from 'react-native';

// helpers
import { createRoboticMilkGraphData } from '../../../../../helpers/roboticMilkEvaluationHelper';

// components
import GraphsBottomLegends from '../GraphBottomLegends';
import ResultsTabsContainer from '../ResultsTabsContainer';
import RoboticMilkGraph from '../RoboticMilkGraphContainer';

// styles
import styles from './styles';

const ResultsGraphContainer = ({
  isLandscapeModalVisible,
  selectedRecentVisits,
  selectedTrendType,
  roboticMilkState,
  selectedResultsTab,
  onTabChange,
  unitOfMeasure,
  weightUnit,
}) => {
  const [graphData, setGraphData] = useState(null);
  const [graphConfig, setGraphConfig] = useState(null);

  useEffect(() => {
    const resultGraphData = createRoboticMilkGraphData(
      selectedRecentVisits,
      selectedResultsTab,
      roboticMilkState,
      unitOfMeasure,
      true,
    );

    if (resultGraphData) {
      setGraphData(resultGraphData?.graphData);
      setGraphConfig(resultGraphData?.graphConfig);
    }
  }, [selectedResultsTab, selectedRecentVisits]);

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={[
        styles.scrollViewStyles,
        isLandscapeModalVisible && styles.landscapeBottomSpacing,
      ]}
      contentContainerStyle={styles.contentContainer}>
      <SafeAreaView>
        {!isLandscapeModalVisible && (
          <ResultsTabsContainer
            selectedTrendType={selectedTrendType}
            onTabChange={onTabChange}
            selectedResultsTab={selectedResultsTab}
            roboticMilkState={roboticMilkState}
          />
        )}

        <RoboticMilkGraph
          isLandscapeModalVisible={isLandscapeModalVisible}
          graphData={graphData}
          graphConfig={graphConfig}
          unitOfMeasure={unitOfMeasure}
          weightUnit={weightUnit}
          selectedResultsTab={selectedResultsTab}
          selectedRecentVisits={selectedRecentVisits}
          roboticMilkState={roboticMilkState}
        />

        <GraphsBottomLegends
          selectedResultsTab={selectedResultsTab}
          graphConfig={graphConfig}
          unitOfMeasure={unitOfMeasure}
          weightUnit={weightUnit}
          roboticMilkState={roboticMilkState}
        />
      </SafeAreaView>
    </ScrollView>
  );
};

export default ResultsGraphContainer;
