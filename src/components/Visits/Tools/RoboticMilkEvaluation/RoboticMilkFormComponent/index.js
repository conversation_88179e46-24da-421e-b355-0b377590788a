// modules
import React from 'react';
import { KeyboardAvoidingView, ScrollView, Platform, View } from 'react-native';
import Animated, { SlideOutLeft } from 'react-native-reanimated';

// components
import FormInputHeader from '../../common/FormInputHeader';
import FormInputContainer from '../FormInputContainer';
import FormDropDownContainer from '../FormDropDownContainer';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';

const RoboticMilkFormComponent = ({
  robotTypeEnums,
  cowFlowDesignEnums,
  roboticMilkState,
  isEditable = false,
  toolAlert,
  setIsDirty,
}) => {
  return (
    <Animated.View
      style={styles.container}
      exiting={SlideOutLeft.duration(250)}>
      <FormInputHeader
        headerTitle={i18n.t('herdLevelInformation')}
        subTitle={i18n.t('indicateRequiredField')}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.flexOne}
        keyboardVerticalOffset={
          Platform.OS === 'ios' && styles.keyboardVerticalOffset
        }>
        <ScrollView
          style={styles.flexOne}
          contentContainerStyle={styles.contentContainerStyle}
          keyboardDismissMode="on-drag"
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <FormDropDownContainer
            roboticMilkState={roboticMilkState}
            robotTypeEnums={robotTypeEnums}
            cowFlowDesignEnums={cowFlowDesignEnums}
            isEditable={isEditable}
            setIsDirty={setIsDirty}
          />

          <FormInputContainer
            roboticMilkState={roboticMilkState}
            isEditable={isEditable}
            setIsDirty={setIsDirty}
          />
        </ScrollView>
      </KeyboardAvoidingView>
      {!!toolAlert && <View style={styles.alert}>{toolAlert}</View>}
    </Animated.View>
  );
};

export default RoboticMilkFormComponent;
