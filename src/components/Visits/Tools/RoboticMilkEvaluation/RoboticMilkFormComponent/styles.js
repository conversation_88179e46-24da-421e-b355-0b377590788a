import { normalize } from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';
import Platform from '../../../../../constants/theme/variables/platform';

export default {
  flexOne: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: normalize(20),
    backgroundColor: colors.white,
  },
  contentContainerStyle: {
    flexGrow: 1,
    paddingTop: normalize(15),
    paddingBottom: normalize(25),
  },
  keyboardVerticalOffset: normalize(Platform.deviceHeight < 700 ? 170 : 200),
  alert: {
    backgroundColor: colors.white
  }
};
