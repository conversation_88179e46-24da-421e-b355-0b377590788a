// modules
import React from 'react';
import { View, Text } from 'react-native';

// styles
import styles from './styles';

const RoboticMilkAnalysisFooterContainer = ({ analysisField }) => {
  return (
    <View style={styles.tableFooter}>
      <View style={styles.rangeContainer}>
        <View style={styles.redIndicator} />
        <Text style={styles.rangeText}>{analysisField?.redRange}</Text>
      </View>

      <View style={styles.rangeContainer}>
        <View style={styles.yellowIndicator} />
        <Text style={styles.rangeText}>{analysisField?.yellowRange}</Text>
      </View>

      <View style={styles.rangeContainer}>
        <View style={styles.greenIndicator} />
        <Text style={styles.rangeText}>{analysisField?.greenRange}</Text>
      </View>
    </View>
  );
};

export default RoboticMilkAnalysisFooterContainer;
