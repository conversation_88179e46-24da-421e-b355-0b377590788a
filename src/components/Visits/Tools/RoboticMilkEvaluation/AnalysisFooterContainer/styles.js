import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  tableFooter: {
    borderTopWidth: 1,
    borderColor: colors.analysisCardColor,
    marginHorizontal: normalize(12),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(10),
    marginBottom: normalize(16),
  },
  redIndicator: {
    width: normalize(8),
    height: normalize(8),
    backgroundColor: colors.redIndicatorColor,
  },
  yellowIndicator: {
    width: normalize(8),
    height: normalize(8),
    backgroundColor: colors.yellowIndicatorColor,
  },
  greenIndicator: {
    width: normalize(8),
    height: normalize(8),
    backgroundColor: colors.greenIndicatorColor,
  },
  rangeText: {
    marginLeft: normalize(8),
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(11),
    lineHeight: normalize(15),
    color: colors.rangeTextColor,
  },
};
