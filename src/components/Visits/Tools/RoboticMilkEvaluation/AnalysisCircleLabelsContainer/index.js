// modules
import React from 'react';
import { Text } from 'react-native-svg';

// helpers
import { getCircleLabels } from '../../../../../helpers/roboticMilkEvaluationHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';

const RoboticMilkAnalysisCircleLabels = ({ analysisField }) => {
  const circleLabels = getCircleLabels(analysisField);

  return circleLabels?.map((label, index) => (
    <Text key={index} {...label}>
      {convertInputNumbersToRegionalBasis(label?.value, 1)}
    </Text>
  ));
};

export default RoboticMilkAnalysisCircleLabels;
