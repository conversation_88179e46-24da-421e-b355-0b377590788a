// modules
import React from 'react';
import { View } from 'react-native';
import Svg from 'react-native-svg';
import Animated, { SlideInRight } from 'react-native-reanimated';
import { useSelector } from 'react-redux';

// constants
import { NEEDLE_SVG } from '../../../../../constants/AssetSVGConstants';

// styles
import styles from './styles';

// components
import RoboticMilkAnalysisFooterContainer from '../AnalysisFooterContainer';
import RoboticMilkAnalysisCirclePath from '../AnalysisCirclePathContainer';
import RoboticMilkAnalysisCircleLabels from '../AnalysisCircleLabelsContainer';
import RoboticMilkAnalysisHeaderContainer from '../AnalysisHeaderContainer';

// helpers
import {
  getNeedleAngle,
  getRoboticMilkAnalysis,
} from '../../../../../helpers/roboticMilkEvaluationHelper';
import { getWeightUnitByMeasure } from '../../../../../helpers/appSettingsHelper';

const RoboticMilkAnalysisComponent = ({ roboticMilkState }) => {
  const roboticMilkEvaluationData =
    roboticMilkState?.visitRoboticMilkEvaluationData;

  const visitState = useSelector(state => state.visit?.visit);
  const unitOfMeasure = visitState?.unitOfMeasure || {};

  //data parsing
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const analysisList = getRoboticMilkAnalysis(roboticMilkEvaluationData);

  return (
    <Animated.ScrollView style={styles.container} entering={SlideInRight}>
      {analysisList?.map((analysisField, index) => {
        let needleAngle = -140;

        needleAngle = getNeedleAngle(
          analysisField,
          roboticMilkEvaluationData,
          true,
        );
        return (
          <View style={styles.cardContainer} key={index}>
            <RoboticMilkAnalysisHeaderContainer
              unitOfMeasure={unitOfMeasure}
              weightUnit={weightUnit}
              analysisField={analysisField}
              roboticMilkEvaluationData={roboticMilkEvaluationData}
            />

            <View style={styles.cardBody}>
              <View style={styles.bodyContainer}>
                <Svg height="180" width="200" viewBox="0 0 200 140">
                  <RoboticMilkAnalysisCirclePath
                    analysisField={analysisField}
                  />

                  <RoboticMilkAnalysisCircleLabels
                    analysisField={analysisField}
                  />
                </Svg>
                <View style={styles.needleContainer}>
                  <NEEDLE_SVG
                    height={styles.circleHeight}
                    style={{
                      transform: [{ rotate: `${needleAngle}deg` }],
                    }}
                  />
                </View>
              </View>

              <RoboticMilkAnalysisFooterContainer
                analysisField={analysisField || null}
              />
            </View>
          </View>
        );
      })}
    </Animated.ScrollView>
  );
};

export default RoboticMilkAnalysisComponent;
