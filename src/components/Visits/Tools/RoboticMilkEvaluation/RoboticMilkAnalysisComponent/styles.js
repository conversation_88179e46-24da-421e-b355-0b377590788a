import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';
import { isTablet } from 'react-native-device-info';
import { Dimensions, Platform } from 'react-native';

const { width } = Dimensions.get('window');

export default {
  container: {
    flex: 1,
    paddingHorizontal: normalize(20),
    paddingBottom: normalize(20),
  },
  cardContainer: {
    marginTop: 16,
  },
  cardBody: {
    borderWidth: 1,
    borderColor: colors.analysisCardColor,
    borderBottomLeftRadius: normalize(8),
    borderBottomRightRadius: normalize(8),
  },
  bodyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  needleContainer: {
    position: 'absolute',
    top: Platform.select({
      ios: isTablet()
        ? width > 850
          ? normalize(20)
          : normalize(35)
        : normalize(53),
      android: isTablet() ? normalize(35) : normalize(53),
    }),
  },
  circleHeight: isTablet() ? normalize(80) : normalize(100),
};
