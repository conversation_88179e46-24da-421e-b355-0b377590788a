// modules
import React from 'react';
import { View } from 'react-native';

// localization
import i18n from '../../../../../localization/i18n';

// constants
import { BOTTOM_SHEET_TYPE } from '../../../../../constants/FormConstants';
import {
  GRAPH_TYPES_VALUES,
  ROBOTIC_MILK_TREND_TYPE_ENUM_KEYS,
} from '../../../../../constants/toolsConstants/RoboticMilkConstants';

// components
import CustomBottomSheet from '../../../../common/CustomBottomSheet';

// styles
import styles from './styles';

const ResultsHeaderContainer = ({
  isLandscapeModalVisible,
  roboticMilkTrendsListTypeEnums,
  selectedTrendType,
  onchangeDropDown,
}) => {
  if (isLandscapeModalVisible) {
    return <View />;
  }

  let filteredRoboticMilkTrendsListTypeEnums =
    roboticMilkTrendsListTypeEnums?.filter(item =>
      [
        ROBOTIC_MILK_TREND_TYPE_ENUM_KEYS.AMS_UTILIZATION,
        ROBOTIC_MILK_TREND_TYPE_ENUM_KEYS.COW_EFFICIENCY,
        ROBOTIC_MILK_TREND_TYPE_ENUM_KEYS.CONCENTRATE_DISTRIBUTION,
      ].includes(item?.key),
    );
  filteredRoboticMilkTrendsListTypeEnums =
    filteredRoboticMilkTrendsListTypeEnums?.map(item => ({
      ...item,
      value: GRAPH_TYPES_VALUES[item.key],
    }));

  return (
    <CustomBottomSheet
      value={selectedTrendType || ''}
      type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
      selectLabel={i18n.t('selectOne')}
      data={filteredRoboticMilkTrendsListTypeEnums || []}
      searchPlaceHolder={i18n.t('search')}
      onChange={onchangeDropDown}
      customLabelStyle={styles.customLabelStyle}
      customInputStyle={styles.customInputStyles}
      customValueStyle={styles.customValueStyles}
      customIconStyles={styles.dropdownIcon}
      iconStroke={styles.iconStrokeColor}
      selectFieldProps={{
        numberOfLines: 1,
      }}
    />
  );
};

export default ResultsHeaderContainer;
