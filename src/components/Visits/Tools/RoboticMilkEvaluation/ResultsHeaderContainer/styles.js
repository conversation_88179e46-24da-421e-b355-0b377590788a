import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  headerContainerStyles: {
    alignItems: 'center',
    marginTop: 0,
  },
  customLabelStyle: {
    display: 'none',
    marginTop: 0,
    paddingTop: 0,
  },
  customInputStyles: {
    height: normalize(40),
    width: '80%',
    borderWidth: 0,
  },
  customValueStyles: {
    fontSize: normalize(14),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.primaryMain,
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    paddingLeft: normalize(0),
    paddingRight: normalize(0),
  },
  dropdownIcon: {
    width: normalize(20),
  },
  iconStrokeColor: colors.primaryMain,
};
