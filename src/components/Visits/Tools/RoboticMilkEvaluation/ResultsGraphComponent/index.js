// modules
import React from 'react';
import { View } from 'react-native';

// components
import ResultsHeaderContainer from '../ResultsHeaderContainer';
import ResultsGraphContainer from '../ResultsGraphContainer';
import ToolGraph from '../../common/ToolGraph';

// styles
import styles from './styles';

const ResultsGraphComponent = ({
  isLandscapeModalVisible,
  onExpandIconPress,
  roboticMilkTrendsListTypeEnums,
  selectedTrendType,
  onchangeDropDown,
  onTabChange,
  selectedResultsTab,
  selectedRecentVisits,
  roboticMilkState,
  onDownloadPress,
  onShareRoboticMilkData,
  unitOfMeasure,
  weightUnit,
}) => {
  const graphTitleComponent = (
    <ResultsHeaderContainer
      isLandscapeModalVisible={isLandscapeModalVisible}
      roboticMilkTrendsListTypeEnums={roboticMilkTrendsListTypeEnums}
      selectedTrendType={selectedTrendType}
      onchangeDropDown={onchangeDropDown}
    />
  );

  const graphComponent = (
    <ResultsGraphContainer
      selectedResultsTab={selectedResultsTab}
      isLandscapeModalVisible={isLandscapeModalVisible}
      selectedRecentVisits={selectedRecentVisits}
      selectedTrendType={selectedTrendType}
      roboticMilkState={roboticMilkState}
      onTabChange={onTabChange}
      unitOfMeasure={unitOfMeasure}
      weightUnit={weightUnit}
    />
  );

  return (
    <View style={styles.container}>
      <ToolGraph
        showExpandIcon
        handleExpandIconPress={onExpandIconPress}
        showDownloadIcon={!isLandscapeModalVisible}
        onDownloadPress={option => onDownloadPress(option)}
        onShareIconPress={null}
        onSharePress={(option, exportMethod) =>
          onShareRoboticMilkData(option, exportMethod)
        }
        showShareIcon={!isLandscapeModalVisible}
        landscapeModalVisible={isLandscapeModalVisible}
        customContainerStyles={
          isLandscapeModalVisible
            ? styles.headerContainerStylesLandscape
            : styles.headerContainerStyles
        }
        customGraphTitleComponent={graphTitleComponent}
        graphComponent={graphComponent}
      />
    </View>
  );
};

export default ResultsGraphComponent;
