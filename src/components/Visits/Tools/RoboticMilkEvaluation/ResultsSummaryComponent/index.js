// modules
import React from 'react';
import { ScrollView } from 'react-native';

// components
import RoboticMilkOutputs from '../RoboticMilkOutputs';
import SummaryTableContainer from '../RoboticMilkSummaryTable';

// styles
import styles from './styles';

const ResultsSummaryComponent = ({
  roboticMilkState,
  selectedRecentVisits,
  cowFlowDesignEnums,
}) => {
  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainerStyle}>
      <RoboticMilkOutputs roboticMilkState={roboticMilkState} />

      <SummaryTableContainer
        roboticMilkState={roboticMilkState}
        selectedRecentVisits={selectedRecentVisits}
        cowFlowDesignEnums={cowFlowDesignEnums}
      />
    </ScrollView>
  );
};

export default ResultsSummaryComponent;
