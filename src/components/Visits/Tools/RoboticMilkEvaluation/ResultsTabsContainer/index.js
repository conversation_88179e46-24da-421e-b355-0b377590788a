// modules
import React from 'react';

// constants
import { ROBOTIC_MILK_TREND_TYPE_ENUM_KEYS } from '../../../../../constants/toolsConstants/RoboticMilkConstants';

// helpers
import { getRoboticMilkGraphTabsByTrend } from '../../../../../helpers/roboticMilkEvaluationHelper';

// components
import ResultsTrendTabs from '../ResultsTrendTabs';

const ResultsTabsContainer = ({
  selectedTrendType,
  onTabChange,
  selectedResultsTab,
  roboticMilkState,
}) => {
  if (
    selectedTrendType ===
    ROBOTIC_MILK_TREND_TYPE_ENUM_KEYS.CONCENTRATE_DISTRIBUTION
  ) {
    return <></>;
  }

  const tabs = getRoboticMilkGraphTabsByTrend(
    selectedTrendType,
    roboticMilkState,
  );

  return (
    <ResultsTrendTabs
      tabs={tabs}
      onTabChange={onTabChange}
      selectedResultsTab={selectedResultsTab}
    />
  );
};

export default ResultsTabsContainer;
