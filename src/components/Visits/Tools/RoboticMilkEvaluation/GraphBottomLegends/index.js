// modules
import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import {
  KG_REGEX,
  UNIT_OF_MEASURE,
} from '../../../../../constants/AppConstants';

// constants
import { ROBOTIC_MILK_EVALUATION } from '../../../../../constants/FormConstants';
import { ROBOTIC_MILK_TREND_TYPE_ENUM_KEYS } from '../../../../../constants/toolsConstants/RoboticMilkConstants';

// helpers
import {
  getLeftAxisLabel,
  getRightAxisLabel,
} from '../../../../../helpers/roboticMilkEvaluationHelper';

// styles
import styles from './styles';

const GraphsBottomLegends = ({
  selectedResultsTab,
  graphConfig,
  roboticMilkState,
  unitOfMeasure,
  weightUnit,
}) => {
  const getFirstLegend = () => {
    let legend = getLeftAxisLabel(selectedResultsTab, roboticMilkState) || '';
    if (unitOfMeasure == UNIT_OF_MEASURE.IMPERIAL) {
      legend = legend.replace(KG_REGEX, weightUnit);
    }
    return legend;
  };

  const getSecondLegend = () => {
    let legend = getRightAxisLabel(selectedResultsTab, roboticMilkState) || '';
    if (unitOfMeasure == UNIT_OF_MEASURE.IMPERIAL) {
      legend = legend.replace(KG_REGEX, weightUnit);
    }
    return legend;
  };

  const isConcentrateDistribution = () => {
    return (
      selectedResultsTab?.key ===
      ROBOTIC_MILK_EVALUATION.AVERAGE_CONCENTRATE +
        ROBOTIC_MILK_EVALUATION.CONCENTRATE_PER_100_KG_MILK
    );
  };

  return (
    <View>
      <ScrollView
        horizontal
        contentContainerStyle={[
          styles.container,
          !isConcentrateDistribution() && styles.justifyContent,
        ]}
        style={styles.width}
        showsHorizontalScrollIndicator={false}>
        <View
          style={
            isConcentrateDistribution() ? styles.flexColumn : styles.flexRow
          }>
          <View
            style={[
              styles.row,
              isConcentrateDistribution() && styles.marginBottom,
            ]}>
            <View
              style={[
                styles.indicator,
                {
                  backgroundColor:
                    graphConfig?.[0]?.stroke || styles.transparent,
                },
              ]}
            />
            <Text style={styles.legendStyles}>{getFirstLegend()}</Text>
          </View>
          {![
            ROBOTIC_MILK_EVALUATION.COWS_PER_ROBOT,
            ROBOTIC_MILK_EVALUATION.MILKING_FAILURES,
          ].includes(selectedResultsTab?.key) && (
            <View style={styles.row}>
              <View
                style={[
                  styles.indicator,
                  {
                    backgroundColor:
                      graphConfig?.[1]?.stroke || styles.transparent,
                  },
                ]}
              />
              <Text style={styles.legendStyles}>{getSecondLegend()}</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default GraphsBottomLegends;
