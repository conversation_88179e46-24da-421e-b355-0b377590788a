import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default {
  container: {
    flexGrow: 1,
    marginBottom: normalize(5),
  },
  justifyContent: {
    justifyContent: 'space-around',
  },
  width: {
    width: '100%',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(20),
  },
  indicator: {
    width: normalize(10),
    height: normalize(10),
    marginRight: normalize(10),
    borderRadius: 5,
  },
  legendStyles: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(12),
    lineHeight: normalize(15),
    color: colors.grey1,
  },
  transparent: colors.transparent,
  flexRow: {
    flexDirection: 'row',
  },
  flexColumn: {
    flexDirection: 'column',
  },
  marginBottom: {
    marginBottom: normalize(16),
  },
};
