// modules
import React, { useState } from 'react';
import { Keyboard } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Animated, { FadeInDown } from 'react-native-reanimated';

// constants
import { ROBOTIC_MILK_FIELDS } from '../../../../../constants/toolsConstants/RoboticMilkConstants';
import {
  CONTENT_TYPE,
  ROBOTIC_MILK_EVALUATION,
} from '../../../../../constants/FormConstants';

// components
import NumberFormInput from '../../../../common/NumberFormInput';
import MoreInfoPopOver from '../MoreInfoPopOver';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';

// actions
import { updateRoboticMilkFormField } from '../../../../../store/actions/tools/roboticMilk';

// helpers
import {
  getKeyboardType,
  stringIsEmpty,
} from '../../../../../helpers/alphaNumericHelper';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';
import { getWeightUnitByMeasure } from '../../../../../helpers/appSettingsHelper';
import {
  KG_REGEX,
  UNIT_OF_MEASURE,
} from '../../../../../constants/AppConstants';

const FormInputContainer = ({
  roboticMilkState,
  isEditable = false,
  setIsDirty,
}) => {
  if (stringIsEmpty(roboticMilkState?.visitRoboticMilkEvaluationData)) {
    return;
  }

  //api calling
  const dispatch = useDispatch();

  //local state
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  //redux state
  const visitState = useSelector(state => state.visit);
  const unitOfMeasure = visitState?.visit?.unitOfMeasure || {};

  //data parsing
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  //handlers
  const handleChangeInput = (value, field) => {
    setIsDirty && setIsDirty(true);
    const payload = {
      fieldKey: field?.key,
      value,
      // value: field?.decimalPoints ? value : parseInt(value),
    };
    dispatch(updateRoboticMilkFormField(payload));
  };

  const handleSubmitEditing = currentFieldIndex => {
    if (ROBOTIC_MILK_FIELDS[currentFieldIndex + 1]?.inputRef) {
      ROBOTIC_MILK_FIELDS[currentFieldIndex + 1]?.inputRef?.current?.focus();
    } else {
      Keyboard.dismiss();
    }
  };

  const getMoreInfoMessage = info => {
    if (!!info?.length) {
      return info?.replace(KG_REGEX, weightUnit);
    }
    return null;
  };

  return (
    <>
      <CustomInputAccessoryView doneAction={action} type={type} />
      {ROBOTIC_MILK_FIELDS?.map((field, index) => {
        const visitEvaluationData =
          roboticMilkState?.visitRoboticMilkEvaluationData;

        let fieldLabel = null;

        if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
          let label = field?.key;

          if (field.customLabel) {
            label = i18n.t(field?.key + visitEvaluationData?.robotType);
            label = !!label?.length ? label.replace(KG_REGEX, weightUnit) : '';
          } else {
            label = field.label;
            label = !!label?.length ? label.replace(KG_REGEX, weightUnit) : '';
          }

          fieldLabel = label;
        } else {
          fieldLabel = field?.customLabel
            ? i18n.t(field?.key + visitEvaluationData?.robotType)
            : field?.label;
        }

        return (
          <Animated.View
            key={field.key}
            style={styles.container}
            // entering={FadeInDown.delay(index * 120).duration(700)}
          >
            {field?.moreInfo &&
              field?.moreInfo?.[visitEvaluationData?.robotType] && (
                <MoreInfoPopOver
                  weightUnit={weightUnit}
                  infoText={field?.moreInfo}
                  unitOfMeasure={unitOfMeasure}
                  robotType={visitEvaluationData?.robotType}
                />
              )}

            <NumberFormInput
              label={fieldLabel || ''}
              required={field?.required}
              newRequiredDesign
              placeholder={field.placeholder || 0}
              keyboardType={getKeyboardType(field?.decimalPoints)}
              returnKeyType={field?.returnKeyType}
              maxLength={field?.maxLimit}
              minValue={field?.minValue}
              maxValue={
                unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL &&
                field?.key === ROBOTIC_MILK_EVALUATION.AVERAGE_MILK_YIELD &&
                field?.key === ROBOTIC_MILK_EVALUATION.MILKING_SPEED
                  ? field?.lbsMaxValue
                  : field?.maxValue
              }
              reference={field?.inputRef}
              decimalPoints={field?.decimalPoints}
              hasCommas={field?.hasCommas}
              onChange={text => handleChangeInput(text, field)}
              customInputContainerStyle={styles.customInputContainerStyle}
              textAlign={'left'}
              value={
                field?.key === ROBOTIC_MILK_EVALUATION.LACTATING_COWS ||
                field?.key === ROBOTIC_MILK_EVALUATION.TOTAL_MILKING_FAILURES
                  ? visitEvaluationData?.[field?.key]
                  : (visitEvaluationData?.[field?.key] || '').toString()
              }
              onSubmitEditing={() => handleSubmitEditing(index)}
              blurOnSubmit={false}
              disabled={!isEditable}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: ROBOTIC_MILK_FIELDS[index + 1]?.inputRef?.current,
                  dismiss: ROBOTIC_MILK_FIELDS[index + 1]?.inputRef
                    ? false
                    : true,
                });
              }}
            />
          </Animated.View>
        );
      })}
    </>
  );
};

export default FormInputContainer;
