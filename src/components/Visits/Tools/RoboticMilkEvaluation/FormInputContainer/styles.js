import colors from '../../../../../constants/theme/variables/customColor';
import { normalize } from '../../../../../constants/theme/variables/customFont';

export default {
  container: {
    marginTop: normalize(15),
  },
  customInputContainerStyle: {
    width: '100%',
  },
  iconButton: {
    position: 'absolute',
    alignSelf: 'flex-end',
    top: normalize(3),
    zIndex: 10,
  },
  icon: {
    width: normalize(16),
    height: normalize(16),
    stroke: colors.grey1,
  },
};
