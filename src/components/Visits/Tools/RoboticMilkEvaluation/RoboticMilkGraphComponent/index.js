// modules
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';

// constants
import {
  AMS_UTILIZATION_GRAPH_TABS,
  ROBOTIC_MILK_TREND_TYPE_ENUM_KEYS,
} from '../../../../../constants/toolsConstants/RoboticMilkConstants';
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
} from '../../../../../constants/AppConstants';
import { TOAST_TYPE } from '../../../../../constants/FormConstants';

// services
import { isOnline } from '../../../../../services/netInfoService';

// component
import ResultsGraphComponent from '../ResultsGraphComponent';
import { showToast } from '../../../../common/CustomToast';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../../../../store/actions/tool';

// localization
import i18n from '../../../../../localization/i18n';

// helpers
import { roboticMilkGraphDataExportModeller } from '../../../../../helpers/roboticMilkEvaluationHelper';

const RoboticMilkGraphComponent = ({
  roboticMilkState,
  selectedRecentVisits,
  roboticMilkTrendsListTypeEnums,
  unitOfMeasure,
  weightUnit,
}) => {
  const dispatch = useDispatch();

  const [isLandscapeModalVisible, setIsLandscapeModalVisible] = useState(false);
  const [selectedTrendType, setSelectedTrendType] = useState(
    ROBOTIC_MILK_TREND_TYPE_ENUM_KEYS?.AMS_UTILIZATION,
  );
  const [selectedResultsTab, setSelectedResultsTab] = useState(
    AMS_UTILIZATION_GRAPH_TABS[0],
  );

  const onExpandIconPress = () => {
    setIsLandscapeModalVisible(!isLandscapeModalVisible);
  };

  const onChangeTrendType = trend => {
    setSelectedTrendType(trend?.key);

    const tab = AMS_UTILIZATION_GRAPH_TABS?.find(
      item => item.includesIn === trend?.key,
    );

    setSelectedResultsTab(tab);
  };

  const onTabChange = tab => {
    setSelectedResultsTab(tab);
  };

  const downloadRoboticMilkData = async type => {
    if (await isOnline()) {
      const model = roboticMilkGraphDataExportModeller(
        selectedTrendType,
        selectedResultsTab,
        selectedRecentVisits,
        roboticMilkState,
        unitOfMeasure,
        weightUnit,
      );

      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.ROBOTIC_MILK_EVALUATION_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType: EXPORT_REPORT_TYPES.ROBOTIC_MILK_EVALUATION_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareRoboticMilkData = async (type, exportMethod) => {
    if (await isOnline()) {
      const model = roboticMilkGraphDataExportModeller(
        selectedTrendType,
        selectedResultsTab,
        selectedRecentVisits,
        roboticMilkState,
        unitOfMeasure,
        weightUnit,
      );

      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.ROBOTIC_MILK_EVALUATION_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.ROBOTIC_MILK_EVALUATION_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  return (
    <ResultsGraphComponent
      isLandscapeModalVisible={isLandscapeModalVisible}
      onExpandIconPress={onExpandIconPress}
      roboticMilkTrendsListTypeEnums={roboticMilkTrendsListTypeEnums}
      selectedTrendType={selectedTrendType}
      onchangeDropDown={onChangeTrendType}
      selectedResultsTab={selectedResultsTab}
      onTabChange={onTabChange}
      unitOfMeasure={unitOfMeasure}
      weightUnit={weightUnit}
      selectedRecentVisits={selectedRecentVisits}
      roboticMilkState={roboticMilkState}
      onDownloadPress={downloadRoboticMilkData}
      onShareRoboticMilkData={onShareRoboticMilkData}
    />
  );
};

export default RoboticMilkGraphComponent;
