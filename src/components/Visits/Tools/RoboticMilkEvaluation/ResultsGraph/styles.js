import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default {
  axisStyles: {
    stroke: colors.grey14,
    strokeWidth: 1,
    strokeDasharray: '5, 5',
  },
  horizontalLabels: {
    fontFamily: fonts.HelveticaNeueRegular,
    letterSpacing: normalize(0.15),
    fill: colors.alphabetIndex,
    fontSize: normalize(11),
    lineHeight: normalize(13),
    // left: normalize(0),
  },
  axisLabelStyles: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(11),
    fill: colors.alphabetIndex,
  },
  dotNumber: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(10),
    lineHeight: normalize(12),
    letterSpacing: normalize(0.5),
  },
  white: colors.white,

  // default constant values from victory native. can't use normalize here
  graphPadding: {
    top: 40,
    left: 50,
    right: 50,
    bottom: 40,
  },
};
