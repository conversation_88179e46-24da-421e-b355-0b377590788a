// modules
import React from 'react';
import {
  VictoryLine,
  VictoryChart,
  VictoryScatter,
  VictoryAxis,
  VictoryLabel,
} from 'victory-native';
import { LinearGradient, Stop, Defs } from 'react-native-svg';

// styles
import styles from './styles';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';

const RoboticMilkMultiAxisGraph = ({
  data,
  width,
  height,
  leftYAxisLabel,
  rightYAxisLabel,
  horizontalLabelStyles,
  graphConfig,
  showXAxis,
  customLeftAxisTickFormat,
  customRightAxisTickFormat,
}) => {
  const GradientId = '#GraphGradient';

  return (
    <VictoryChart
      height={height && height}
      width={width && width}
      padding={styles.graphPadding}>
      <VictoryAxis
        style={{
          axis: styles.axisStyles,
          tickLabels: {
            ...styles.horizontalLabels,
            ...horizontalLabelStyles,
          },
        }}
        domainPadding={{ x: [0, 15] }}
      />

      <VictoryAxis
        dependentAxis
        key={'0'}
        label={leftYAxisLabel}
        style={{
          axis: styles.axisStyles,
          tickLabels: {
            ...styles.horizontalLabels,
            ...horizontalLabelStyles,
          },
          axisLabel: styles.axisLabelStyles,
        }}
        tickFormat={customLeftAxisTickFormat ? t => t?.toFixed(0) : null}
        axisLabelComponent={<VictoryLabel dy={-5} />}
      />

      {data?.length > 1 && (
        <VictoryAxis
          dependentAxis
          key={'1'}
          label={rightYAxisLabel}
          style={{
            axis: styles.axisStyles,
            tickLabels: {
              ...styles.horizontalLabels,
              ...horizontalLabelStyles,
            },
            axisLabel: styles.axisLabelStyles,
          }}
          orientation="right"
          tickFormat={customRightAxisTickFormat ? t => t?.toFixed(0) : null}
          axisLabelComponent={<VictoryLabel dy={5} />}
        />
      )}

      {graphConfig?.map((d, i) => (
        <Defs key={i}>
          <LinearGradient id={d?.gradientId || GradientId} x1={1} y2={1}>
            {d?.gradientStyles?.map((style, index) => (
              <Stop {...style} key={index} />
            ))}
          </LinearGradient>
        </Defs>
      ))}

      {data?.map((d, i) => (
        <VictoryLine
          key={i}
          data={d}
          interpolation="monotoneX"
          style={{
            data: {
              fillOpacity: 0.3,
              ...graphConfig[i],
              fill: `url(#${graphConfig[i]?.gradientId || GradientId})`,
            },
          }}
        />
      ))}

      {data?.map((d, i) => (
        <VictoryScatter
          key={i}
          data={d}
          size={6}
          style={{
            data: {
              ...graphConfig[i],
              stroke: styles.white,
              fill: graphConfig[i]?.stroke,
            },
          }}
          labelComponent={
            <VictoryLabel
              dy={-10}
              dx={0}
              style={{ ...styles.dotNumber, fill: graphConfig[i]?.stroke }}
            />
          }
          // labels={({ datum }) => datum.y?.toFixed(2)}
          labels={({ datum }) =>
            convertInputNumbersToRegionalBasis(datum.y, 2, true)
          }
        />
      ))}

      {showXAxis && showXAxis}
    </VictoryChart>
  );
};

export default RoboticMilkMultiAxisGraph;
