// modules
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  FlatList,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  Text,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// reusable components
import { showToast } from '../../../../common/CustomToast';
import NumberFormInput from '../../../../common/NumberFormInput';
import HerdAnalysisResults from './HerdAnalysisResults';
import ToolAlert from '../../common/ToolAlert';
import EmptyListComponent from '../../../../common/EmptyListComponent';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  BCS_HERD_ANALYSIS_FIELDS,
  CONTENT_TYPE,
  KEYBOARD_TYPE,
  TOAST_TYPE,
} from '../../../../../constants/FormConstants';
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
  NEXT_FIELD_TEXT,
  TOOL_ANALYSIS_TYPES,
  TOOL_TYPES,
  UNIT_OF_MEASURE,
} from '../../../../../constants/AppConstants';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../../../../store/actions/tool';
import {
  resetUpdatePenRequest,
  updatePenRequest,
} from '../../../../../store/actions/pen';
import { hideBCSToastRequest } from '../../../../../store/actions/userPreferences';
import {
  getBCSPenAnalysisRequest,
  saveBCSHerdRequest,
  updateBCSHerdRequest,
} from '../../../../../store/actions/tools/bcs';

// helpers
import {
  getAllBCSData,
  getBCSAvg,
  mapGraphDataForBCSHerdAnalysisExport,
  parseBCSGoals,
  saveBCSHerdData,
} from '../../../../../helpers/toolHelper';
import {
  convertNumberToString,
  convertStringToNumber,
  stringIsEmpty,
  validateNumber,
} from '../../../../../helpers/alphaNumericHelper';
import {
  convertWeightToImperial,
  convertWeightToMetric,
  getWeightUnitByMeasure,
} from '../../../../../helpers/appSettingsHelper';

//services
import { isOnline } from '../../../../../services/netInfoService';

//lodash
import _ from 'lodash';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';

const HerdAnalysis = props => {
  const {
    penList,
    screenDisabled,
    currentStep,
    totalSteps,
    refreshPensData,
    goalsData,
    scaleList,
    shouldEnableResultsButton,
    penDataForHerdSum,
    enableResults,
    isDirty,
    setIsDirty,
  } = props;
  const [penData, setPenData] = useState([]);
  const [initialPenData, setInitialPenData] = useState([]);
  const [toolsData, setToolsData] = useState([]);
  const [refresh, setRefresh] = useState(false);
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  // const navigation = useNavigation();
  const dispatch = useDispatch();

  //refs
  const DIMRef = useRef([]);
  const milkProductionRef = useRef([]);

  const visitState = useSelector(state => state.visit);
  const bcsState = useSelector(state => state.bcs);
  const penState = useSelector(state => state.pen);
  const enumState = useSelector(state => state.enums);
  const userPreferencesState = useSelector(state => state.userPreferences);

  const unitOfMeasure = visitState?.visit?.unitOfMeasure || {};
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  // const [isDirty, setIsDirty] = useState(false);

  const { isEditable = false, id } = visitState?.visit || {};
  const getPenAnalysisData = () => {
    const { id } = visitState.visit || {};
    dispatch(
      getBCSPenAnalysisRequest({
        localId: id,
        enumState: enumState,
        isEditable: !screenDisabled,
      }),
    );
  };

  useEffect(() => {
    getPenAnalysisData();
  }, []);

  useEffect(() => {
    if (!bcsState.bcsPenAnalysisLoading) {
      const allData = getAllBCSData(bcsState?.bcsPenAnalysis);
      setToolsData(allData);
    }
  }, [bcsState.bcsPenAnalysisLoading]);

  const populatePenData = (pens, toolsData) => {
    //refactored to only get those pens that were touched in pen-analysis
    const dataTwo = [];
    pens?.filter(pen => {
      let avg = 0;
      let currentPenObject = {};
      let filteredPenArray = [];
      if (!stringIsEmpty(pen?.sv_id)) {
        filteredPenArray = toolsData?.filter(
          penObj =>
            penObj.penId === pen.sv_id || penObj.localPenId === pen.sv_id,
        );
      } else {
        filteredPenArray = toolsData?.filter(
          penObj => penObj.localPenId === pen.id,
        );
      }
      if (filteredPenArray && filteredPenArray.length > 0) {
        currentPenObject = filteredPenArray[0];
        avg = getBCSAvg(currentPenObject).toFixed(2);
        let milkVal = '';
        if (isEditable) {
          milkVal = pen?.milk;
        } else {
          milkVal = currentPenObject?.milk;
        }
        if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
          milkVal = convertWeightToImperial(milkVal, 1);
        }
        dataTwo.push({
          ...pen,
          daysInMilk: convertNumberToString(
            isEditable ? pen?.daysInMilk : currentPenObject?.daysInMilk,
          ),
          milk: convertNumberToString(milkVal),
          avgBCS: avg,
        });
      }
    });

    penDataForHerdSum.current = dataTwo;
    setPenData(dataTwo);
    setInitialPenData(dataTwo);
  };

  useEffect(() => {
    populatePenData(penList, toolsData);
    const herdData = saveBCSHerdData(
      penList,
      bcsState?.bcsPenAnalysis,
      enumState?.simpleEnum,
      scaleList[0],
    );
    let obj = {
      isEditable,
      pens: [...herdData],
      localVisitId: id,
    };

    isEditable && isDirty && dispatch(saveBCSHerdRequest(obj));
  }, [penList, toolsData, refresh]);

  useEffect(() => {
    if (penState.updateSuccess) {
      dispatch(resetUpdatePenRequest());
      refreshPensData();
    }
  }, [penState.updateSuccess]);

  useEffect(() => {
    if (penState.updateError) {
      showToast(
        TOAST_TYPE.ERROR,
        penState.updateError || i18n.t('somethingWentWrongError'),
      );
    }
  }, [penState.updateError]);

  //disables results button if pen's values sum to 0
  useEffect(() => {
    if (!enableResults) {
      shouldEnableResultsButton(TOOL_ANALYSIS_TYPES.HERD_ANALYSIS, penData);
    }
  }, [penData]);

  const onDIMChange = (val, index) => {
    setIsDirty(true);
    let tempPenData = [...penData];
    tempPenData[index] = { ...penData[index], daysInMilk: val };
    penDataForHerdSum.current = tempPenData;

    setPenData(tempPenData);
  };

  const onMilkChange = (val, index) => {
    setIsDirty(true);

    let tempPenData = [...penData];
    if (!validateNumber(val, false) && val !== '') {
      return;
    }

    let milkVal = val;
    if (val === '') {
      milkVal = '0';
    }
    tempPenData[index] = {
      ...penData[index],
      milk: milkVal,
    };
    penDataForHerdSum.current = tempPenData;

    setPenData(tempPenData);
  };

  // update pen fields on submit
  const updatePen = pen => {
    let milkVal = parseFloat(pen?.milk);
    if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
      milkVal = convertWeightToMetric(milkVal);
    }
    const payload = {
      ...pen,
      daysInMilk:
        pen?.daysInMilk == '' || pen?.daysInMilk == null
          ? null
          : parseInt(pen?.daysInMilk),
      milk: milkVal || 0,
      localId: pen.id,
    };
    delete payload.avgBCS;
    dispatch(updatePenRequest(payload));

    isEditable &&
      isDirty &&
      dispatch(
        updateBCSHerdRequest({
          ...payload,
          isEditable,
          localVisitId: id,
        }),
      );
  };

  // set dim/milk data and open alert
  const onBlurInput = (item, inputKey) => {
    const penFilteredList = initialPenData.filter(
      initialPen => initialPen.id === item.id,
    );
    if (penFilteredList && penFilteredList.length > 0) {
      const initialPenObj = penFilteredList[0];
      if (inputKey === BCS_HERD_ANALYSIS_FIELDS.DIM) {
        if (item.daysInMilk === null && initialPenObj.daysInMilk === null) {
          return;
        }
        if (
          item.daysInMilk != null &&
          initialPenObj.daysInMilk != null &&
          item.daysInMilk.toString() === initialPenObj?.daysInMilk.toString()
        ) {
          return;
        }
      } else if (inputKey === BCS_HERD_ANALYSIS_FIELDS.MILK) {
        if (item.milk == initialPenObj.milk) {
          return;
        }
        item.milk = convertStringToNumber(item.milk);
      }
    }
    updatePen(item);
  };

  const renderItem = ({ item, index }) => {
    return (
      <View style={styles.listItemRow}>
        <Text style={styles.titleText}>
          {`${i18n.t('pen')}: `}
          <Text style={styles.penNameText}>{item?.name}</Text>
          {` (${i18n.t('BCS')}: ${convertInputNumbersToRegionalBasis(
            item?.avgBCS,
            2,
          )})`}
        </Text>
        <View style={[styles.fieldsRow]}>
          <View style={[styles.flexOne, styles.marginRight, styles.labelAlign]}>
            <NumberFormInput
              disabled={screenDisabled}
              label={i18n.t('DIM')}
              unit={''}
              minValue={-100}
              maxValue={999}
              isNegative={true}
              isInteger={true}
              keyboardType={
                Platform.OS === 'ios'
                  ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
                  : KEYBOARD_TYPE.NUMBER_PAD
              }
              placeholder={i18n.t('numberPlaceholder')}
              value={item?.daysInMilk?.toString() || ''}
              hasCommas={true}
              customInputContainerStyle={styles.customInputStyle}
              onChange={val => onDIMChange(val, index)}
              skipBlurValidation={true}
              onBlur={() => onBlurInput(item, BCS_HERD_ANALYSIS_FIELDS.DIM)}
              reference={e => {
                DIMRef.current[index] = e;
              }}
              onSubmitEditing={() => {
                // Keyboard?.dismiss();
                milkProductionRef?.current[index]?.focus();
              }}
              // returnKeyType={"next"}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: milkProductionRef?.current[index],
                });
              }}
            />
          </View>
          <View style={[styles.flexOne]}>
            <NumberFormInput
              disabled={screenDisabled}
              label={`${i18n.t('milkProductionKg')} (${weightUnit})`}
              unit={''}
              placeholder={i18n.t('singleDecimalNumberPlaceholder')}
              value={item?.milk?.toString() || ''}
              minValue={0}
              maxValue={999}
              isInteger={false}
              decimalPoints={1}
              customInputContainerStyle={styles.customInputStyle}
              onChange={val => onMilkChange(val, index)}
              onBlur={() => onBlurInput(item, BCS_HERD_ANALYSIS_FIELDS.MILK)}
              reference={e => {
                milkProductionRef.current[index] = e;
              }}
              onSubmitEditing={() => {
                Keyboard?.dismiss();
                DIMRef?.current[index + 1]?.focus();
              }}
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.DONE}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef: DIMRef?.current[index + 1],
                  dismiss: DIMRef?.current[index + 1] ? false : true,
                });
              }}
            />
          </View>
        </View>
      </View>
    );
  };

  const downloadHerdAnalysisData = async (graphData, type, lactationStages) => {
    if (await isOnline()) {
      const model = mapGraphDataForBCSHerdAnalysisExport(
        visitState?.visit,
        graphData,
        lactationStages,
      );
      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.BCS_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType: EXPORT_REPORT_TYPES.BCS_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareHerdAnalysisData = async (
    graphData,
    type,
    lactationStages,
    exportMethod,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForBCSHerdAnalysisExport(
        visitState?.visit,
        graphData,
        lactationStages,
      );

      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod === GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.BCS_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.BCS_HERD_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const renderHerdAnalysisResults = () => {
    return (
      <HerdAnalysisResults
        goalsData={goalsData} //convert back to normal number where needed
        penData={penData}
        onDownloadPress={downloadHerdAnalysisData}
        onShareHerdAnalysisData={onShareHerdAnalysisData}
      />
    );
  };

  const getToolToast = () => {
    const {
      userPreferences: { defaultValues },
    } = userPreferencesState || {};
    const { [TOOL_TYPES.BODY_CONDITION]: bodyConditionToast } =
      defaultValues || false;
    return !screenDisabled ? bodyConditionToast : false;
  };

  const onCloseToast = () => {
    dispatch(hideBCSToastRequest(true));
  };

  const emptyComponent = () => {
    if (!isEditable) {
      return (
        <EmptyListComponent
          title={i18n.t('noResultShow')}
          description={i18n.t('noPensToShow')}
        />
      );
    }
    return (
      <EmptyListComponent
        title={i18n.t('noPensAdded')}
        description={i18n.t('addPensForHerdData')}
      />
    );
  };

  return (
    <>
      {currentStep === totalSteps ? (
        renderHerdAnalysisResults()
      ) : (
        <>
          <KeyboardAvoidingView style={styles.container} behavior="padding">
            <CustomInputAccessoryView doneAction={action} type={type} />
            <View style={styles.container}>
              <FlatList
                style={styles.flatlist}
                data={penData}
                keyExtractor={({ item }) => item?.id}
                showsVerticalScrollIndicator={false}
                renderItem={renderItem}
                ListEmptyComponent={emptyComponent}
              />
            </View>
          </KeyboardAvoidingView>
          {!!getToolToast() && (
            <View style={styles.alert}>
              <ToolAlert onCloseToast={onCloseToast} />
            </View>
          )}
        </>
      )}
    </>
  );
};

export default HerdAnalysis;
