import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  flexOne: { flex: 1 },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  flatlist: {
    width: '100%',
    marginTop: normalize(14),
  },
  listItemRow: {
    flexDirection: 'column',
    paddingVertical: normalize(12),
    marginHorizontal: normalize(20),
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(24),
    color: colors.grey1,
  },
  penNameText: {
    fontWeight: '700',
  },
  fieldsRow: {
    flexDirection: 'row',
    marginTop: normalize(12),
  },
  marginRight: {
    marginRight: normalize(19),
  },
  customInputStyle: {
    width: '100%',
  },
  labelAlign: {
    alignSelf: 'flex-end',
  },
  alert:{
    backgroundColor: colors.white
  }
};
