import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../constants/theme/variables/platform';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import DeviceInfo from 'react-native-device-info';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  legendRow: {
    flexDirection: 'row',
    marginHorizontal: normalize(20),
    marginTop: normalize(26),
    marginBottom: normalize(16),
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: normalize(16),
  },
  milkLegend: {
    width: normalize(10),
    height: normalize(10),
    backgroundColor: colors.purple,
    borderRadius: normalize(42),
  },
  bcsAvgLegend: {
    width: normalize(10),
    height: normalize(10),
    backgroundColor: colors.secondary2,
    borderRadius: normalize(42),
  },
  minLegend: {
    width: normalize(10),
    height: normalize(10),
    borderWidth: normalize(1),
    borderStyle: 'dashed',
    borderColor: colors.minBCSGoalColor,
    borderRadius: normalize(52),
  },
  maxLegend: {
    width: normalize(10),
    height: normalize(10),
    borderWidth: normalize(1),
    borderStyle: 'dashed',
    borderColor: colors.error4,
    borderRadius: normalize(52),
  },
  legendText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(10),
    lineHeight: normalize(12),
    color: colors.grey1,
    marginLeft: normalize(6),
  },
  dataLineColor: colors.secondary2,
  minGoalLineColor: colors.minBCSGoalColor,
  maxGoalLineColor: colors.error4,
  rightAxisTicks: {
    fill: colors.purple,
  },
  customXAxisLabelStyles: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(11),
    lineHeight: normalize(13),
    color: colors.alphabetIndex,
    textAlign: 'center',
  },
  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight) - 50
          : Platform.deviceHeight - 60,
    },
    android: {
      width: normalize(
        DeviceInfo.isTablet()
          ? Platform.deviceHeight * 0.75
          : Platform.deviceHeight,
      ),
    },
  }),
  graphWidth: {
    width: Platform.deviceWidth,
  },
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 100)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.45)
          : normalize(Platform.deviceWidth - 110),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 360 : 120),
      ),
    },
  }),
  graphHeight: {
    height: normalize(Platform.deviceHeight * 0.45),
  },
};
