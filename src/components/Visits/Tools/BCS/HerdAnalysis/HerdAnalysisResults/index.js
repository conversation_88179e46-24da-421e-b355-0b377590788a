// modules
import React, { useState, useEffect, useMemo } from 'react';
import { View, Text, ScrollView, SafeAreaView } from 'react-native';
import { useSelector } from 'react-redux';

// components
import ToolGraph from '../../../common/ToolGraph';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// helpers
import {
  getBCSAvgForLactationStage,
  getDomain,
  getFormattedLactationStage,
  getLactationStageKey,
  getMilkYieldAvgForLactationStage,
  parseBCSGoals,
} from '../../../../../../helpers/toolHelper';
import { truncateString } from '../../../../../../helpers/alphaNumericHelper';

// actions
import BCSHerdAnalysisGraph from '../../../../../common/BCSHerdAnalysisGraph';

const HerdAnalysisResults = ({
  goalsData,
  penData,
  onDownloadPress,
  onShareHerdAnalysisData,
}) => {
  const _goalsData = useMemo(() => parseBCSGoals(goalsData), [goalsData]);

  const [avgBCSData, setAvgBCSData] = useState([]);
  const [milkData, setMilkData] = useState([]);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  const enumState = useSelector(state => state.enums);

  useEffect(() => {
    let dict = {};
    if (enumState?.enum && penData && penData.length > 0) {
      penData.map(penObj => {
        if (penObj.daysInMilk || penObj.daysInMilk == 0) {
          const stage = getFormattedLactationStage(
            enumState,
            getLactationStageKey(penObj.daysInMilk),
          );
          if (stage in dict) {
            dict[stage].push(penObj);
          } else {
            dict[stage] = [penObj];
          }
        }
      });
    }
    const goalsArray = [..._goalsData];
    const lactationStages = enumState?.enum
      ? [
          ...goalsArray.map(goal =>
            getFormattedLactationStage(enumState, goal.lactationStage),
          ),
        ]
      : [];

    const avgBCSData = lactationStages.map(stage => {
      if (Object.keys(dict).includes(stage)) {
        return getBCSAvgForLactationStage(dict[stage]);
      }
      return null;
    });

    setAvgBCSData(avgBCSData);

    const avgMilkYieldData = lactationStages?.map(stage => {
      if (Object.keys(dict).includes(stage)) {
        return getMilkYieldAvgForLactationStage(dict[stage], true);
      }
      return null;
    });

    setMilkData(avgMilkYieldData);
  }, [penData, goalsData]);

  const getLeftAxisDomain = () => {
    const minGoalsData = [..._goalsData.map(goal => goal.goalMin)];
    const maxGoalsData = [..._goalsData.map(goal => goal.goalMax)];
    const _avgBCSData = [...avgBCSData];
    let domain = getDomain([...minGoalsData, ...maxGoalsData, ..._avgBCSData]);
    domain[1] = 5;

    return domain;
  };

  const getRightAxisDomain = () => {
    return getDomain([...milkData]);
  };

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <ToolGraph
          showExpandIcon
          handleExpandIconPress={onExpandIconPress}
          showDownloadIcon={!landscapeModalVisible}
          showShareIcon={!landscapeModalVisible}
          landscapeModalVisible={landscapeModalVisible}
          onDownloadPress={option =>
            onDownloadPress(
              {
                avgBCSData: avgBCSData,
                minGoalsData: [..._goalsData.map(goal => goal.goalMin)],
                maxGoalsData: [..._goalsData.map(goal => goal.goalMax)],
                milkData: milkData,
              },
              option,
              [..._goalsData.map(goal => goal.lactationStage)],
            )
          }
          onSharePress={(option, exportMethod) =>
            onShareHerdAnalysisData(
              {
                avgBCSData: avgBCSData,
                minGoalsData: [..._goalsData.map(goal => goal.goalMin)],
                maxGoalsData: [..._goalsData.map(goal => goal.goalMax)],
                milkData: milkData,
              },
              option,
              [..._goalsData?.map(goal => goal?.lactationStage)],
              exportMethod,
            )
          }
          customGraphTitleComponent={<View></View>}
          graphComponent={
            <>
              <SafeAreaView>
                <View>
                  <BCSHerdAnalysisGraph
                    labels={
                      enumState?.enum
                        ? [
                            ..._goalsData.map(goal =>
                              getFormattedLactationStage(
                                enumState,
                                goal.lactationStage,
                              ),
                            ),
                          ]
                        : []
                    }
                    minGoalsData={[..._goalsData?.map(goal => goal.goalMin)]}
                    maxGoalsData={[..._goalsData?.map(goal => goal.goalMax)]}
                    avgBCSData={avgBCSData}
                    milkData={milkData}
                    width={
                      landscapeModalVisible
                        ? styles.graphWidthLandscape.width
                        : styles.graphWidth.width
                    }
                    height={
                      landscapeModalVisible
                        ? styles.graphHeightLandscape.height
                        : styles.graphHeight.height
                    }
                    showRightAxis={true}
                    leftYAxisLabel={i18n.t('BCS')}
                    rightYAxisLabel={i18n.t('milk')}
                    leftAxisDomain={getLeftAxisDomain()}
                    rightAxisDomain={getRightAxisDomain()}
                    customXAxisLabelStyles={styles.customXAxisLabelStyles}
                    customRightYAxisTickStyle={styles.rightAxisTicks}
                    xAxisFormatter={t => {
                      //move to three lines so that its properly visible. did this specially for BCS as it has
                      //left and right axis and cant add scroll here
                      return typeof t == 'string'
                        ? t?.replaceAll(' ', '\n')
                        : t;
                    }}
                  />
                </View>
                <View>
                  <Text style={styles.customXAxisLabelStyles}>
                    {i18n.t('lactationStages')}
                  </Text>
                </View>
                <View style={styles.legendRow}>
                  <View style={styles.legendItem}>
                    <View style={styles.milkLegend}></View>
                    <Text style={styles.legendText}>
                      {truncateString(i18n.t('milkHdDay'), 15)}
                    </Text>
                  </View>
                  <View style={styles.legendItem}>
                    <View style={styles.bcsAvgLegend}></View>
                    <Text style={styles.legendText}>
                      {truncateString(i18n.t('BCSAvg'), 15)}
                    </Text>
                  </View>
                  <View style={styles.legendItem}>
                    <View style={styles.minLegend}></View>
                    <Text style={styles.legendText}>{i18n.t('min')}</Text>
                  </View>
                  <View style={styles.legendItem}>
                    <View style={styles.maxLegend}></View>
                    <Text style={styles.legendText}>{i18n.t('max')}</Text>
                  </View>
                </View>
              </SafeAreaView>
            </>
          }
        />
      </ScrollView>
    </View>
  );
};

export default HerdAnalysisResults;
