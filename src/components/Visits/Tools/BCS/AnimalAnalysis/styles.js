import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  flexOne: { flex: 1 },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(24),
    paddingTop: normalize(23),
    paddingBottom: normalize(20),
  },
  dropdownTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dropdownText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: 0.15,
    marginRight: normalize(8),
  },
  penNameLimit: {
    maxWidth: normalize(140),
  },
  scaleIconContainer: {
    marginRight: normalize(6),
  },
  flatlist: {
    width: '100%',
    paddingHorizontal: normalize(19),
  },
  animalCardContainer: {
    marginBottom: normalize(14),
  },
  buttonContainer: {
    flexDirection: 'row',
    height: normalize(48),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.primaryMain,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  plusIcon: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(19),
    lineHeight: normalize(16),
    textAlign: 'center',
    color: colors.primaryMain,
    // textTransform: 'uppercase',
    letterSpacing: 1.25,
    paddingTop: normalize(1),
    marginRight: normalize(7),
  },
  addAnimalText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    // textTransform: 'uppercase',
  },
  scaleBottomSheetContainer: {
    height: normalize(312),
  },
  dataLineColor: colors.secondary2,
  toastContainer: {
    alignSelf: 'center',
    flexDirection: 'row',
    width: normalize(337),
    height: normalize(73),
    paddingHorizontal: normalize(24),
    paddingVertical: normalize(11),
    marginBottom: normalize(16),
    borderRadius: normalize(4),
    backgroundColor: colors.userAvatarBackground,
  },
  toastMessageText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.white,
    marginLeft: normalize(14),
    flex: 1,
  },
  infoIconContainer: {
    marginTop: normalize(2),
  },
  closeIconStyle: {
    marginLeft: normalize(32),
    marginTop: normalize(4),
  },
  disableColor: { color: colors.grey2 },
};
