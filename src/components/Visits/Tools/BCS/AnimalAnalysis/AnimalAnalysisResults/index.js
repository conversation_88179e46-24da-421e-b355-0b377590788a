// modules
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  useWindowDimensions,
  SafeAreaView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';
import ScatterGraph from '../../../../../common/ScatterGraph';
import ToolGraph from '../../../common/ToolGraph';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import { CHEVRON_DOWN_BLUE_ICON } from '../../../../../../constants/AssetSVGConstants';
import { BOTTOM_SHEET_TYPE } from '../../../../../../constants/FormConstants';
import { normalize } from '../../../../../../constants/theme/variables/customFont';
import {
  DATE_FORMATS,
  VISIT_TABLE_FIELDS,
} from '../../../../../../constants/AppConstants';

// helpers
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';
import {
  getAllAnimalAnalysisData,
  getAnimalBCSCategory,
  getAnimalBCSDIM,
} from '../../../../../../helpers/toolHelper';
import { getFormattedDate } from '../../../../../../helpers/dateHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../../store/actions/tool';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';
import { parseEarTags } from '../../../../../../helpers/animalHelper';

const AnimalAnalysisResults = ({
  selectedPen,
  animalData,
  selectedVisits,
  isBCS,
  isLocomotion,
  onDownloadPress,
  onShareAnimalAnalysisData,
}) => {
  const { width, height } = useWindowDimensions();
  const dispatch = useDispatch();
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);

  const [bcsSelected, setBcsSelected] = useState(isBCS);
  const [selectedAnimal, setSelectedAnimal] = useState(null);
  const [showAnimalBottomSheet, setShowAnimalBottomSheet] = useState(false);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  const [recentVisits, setRecentVisits] = useState([]);
  const [chartData, setChartData] = useState([]);
  const [currentVisitDIM, setCurrentVisitDIM] = useState(null);

  useEffect(() => {
    setBcsSelected(isBCS);
  }, [isBCS]);

  useEffect(() => {
    const visit = visitState.visit || {};
    const siteId = !stringIsEmpty(visit.siteId)
      ? visit.siteId
      : visit.localSiteId;
    const accountId = !stringIsEmpty(visit.customerId)
      ? visit.customerId
      : visit.localCustomerId;

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visit?.id,
        tool: VISIT_TABLE_FIELDS.ANIMAL_ANALYSIS,
      }),
    );
  }, []);

  useEffect(() => {
    //TODO: MARYAM add to helper
    if (!toolState.recentVisitsLoading) {
      const data = toolState?.recentVisits?.map(visitObj => {
        const allData = visitObj?.animalAnalysis
          ? getAllAnimalAnalysisData(JSON.parse(visitObj?.animalAnalysis))
          : [];
        if (allData.length > 0 && selectedPen) {
          let filteredPenArray = [];
          if (!stringIsEmpty(selectedPen.sv_id)) {
            filteredPenArray = allData.filter(
              penObj => penObj.penId === selectedPen.sv_id,
            );
          } else {
            filteredPenArray = allData.filter(
              penObj => penObj.localPenId === selectedPen.id,
            );
          }
          if (filteredPenArray && filteredPenArray.length > 0) {
            const currentPenObject = filteredPenArray[0];
            return {
              ...currentPenObject,
              visitId: visitObj.id,
              date: visitObj.visitDate,
            };
          }
          return { visitId: visitObj.id, date: visitObj.visitDate };
        }
        return { visitId: visitObj.id, date: visitObj.visitDate };
      });
      setRecentVisits(data);
    }
  }, [toolState.recentVisitsLoading]);

  useEffect(() => {
    //TODO: Maryam add to helper
    let selectedRecentVisits = recentVisits?.slice(1);
    selectedRecentVisits = selectedRecentVisits.filter(visit =>
      selectedVisits?.includes(visit.visitId),
    );
    selectedRecentVisits.push(animalData);

    const xaxisData = selectedRecentVisits.map(visit =>
      getAnimalBCSDIM(visit, selectedAnimal),
    );
    const yaxisData = selectedRecentVisits.map(visit =>
      getAnimalBCSCategory(visit, selectedAnimal, !bcsSelected),
    );

    const bcsCategory = selectedRecentVisits.map(visit =>
      getAnimalBCSCategory(visit, selectedAnimal, false),
    );
    const locomotionCategory = selectedRecentVisits.map(visit =>
      getAnimalBCSCategory(visit, selectedAnimal, true),
    );
    let dataPoints = [];
    for (let i = 0; i < xaxisData.length; i++) {
      if (xaxisData[i] !== -1000) {
        dataPoints.push({
          x: xaxisData[i],
          y: yaxisData[i],
          bcs: bcsCategory[i],
          locomotionScore: locomotionCategory[i],
          date: getFormattedDate(
            selectedRecentVisits[i].date,
            DATE_FORMATS.MM_dd,
          ),
        });
      }
    }
    setCurrentVisitDIM(getAnimalBCSDIM(animalData, selectedAnimal));
    setChartData(dataPoints);
  }, [animalData, recentVisits, selectedVisits, selectedAnimal, bcsSelected]);

  useEffect(() => {
    if (
      animalData &&
      animalData.animalDetails &&
      animalData.animalDetails.length > 0
    ) {
      setSelectedAnimal(animalData.animalDetails[0]);
    }
  }, [animalData]);

  const openAnimalBottomSheet = () => {
    setShowAnimalBottomSheet(true);
  };

  const closeAnimalBottomSheet = () => {
    setShowAnimalBottomSheet(false);
  };

  const onAnimalChange = item => {
    setSelectedAnimal(item);
    closeAnimalBottomSheet();
  };

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <ToolGraph
          showExpandIcon
          onDownloadPress={option =>
            onDownloadPress(
              chartData,
              option,
              selectedAnimal,
              selectedPen,
              bcsSelected,
              currentVisitDIM,
            )
          }
          onSharePress={(option, exportMethod) =>
            onShareAnimalAnalysisData(
              chartData,
              option,
              selectedAnimal,
              selectedPen,
              bcsSelected,
              currentVisitDIM,
              exportMethod,
            )
          }
          handleExpandIconPress={onExpandIconPress}
          showDownloadIcon={!landscapeModalVisible}
          showShareIcon={!landscapeModalVisible}
          landscapeModalVisible={landscapeModalVisible}
          customGraphTitleComponent={
            <View style={styles.infoColumn}>
              {selectedAnimal &&
                (!landscapeModalVisible ? (
                  <TouchableOpacity
                    style={styles.dropdownTextContainer}
                    onPress={openAnimalBottomSheet}>
                    <Text
                      style={[styles.dropdownText, styles.earTagLimit]}
                      noOfLines={1}>
                      {convertInputNumbersToRegionalBasis(
                        selectedAnimal?.name || '',
                      )}
                    </Text>
                    <CHEVRON_DOWN_BLUE_ICON
                      width={normalize(12)}
                      height={normalize(8)}
                    />
                  </TouchableOpacity>
                ) : (
                  <View style={styles.dropdownTextContainer}>
                    <Text
                      style={[styles.dropdownText, styles.earTagLimit]}
                      noOfLines={1}>
                      {convertInputNumbersToRegionalBasis(
                        selectedAnimal?.name || '',
                      )}
                    </Text>
                  </View>
                ))}

              <Text style={styles.penAnalysisTitle}>
                {i18n.t('pen') || ''}:{' '}
                <Text style={styles.penAnalysisValue}>
                  {selectedPen?.name || ''}
                </Text>
              </Text>
            </View>
          }
          graphComponent={
            <SafeAreaView>
              <View style={styles.leftPadding}>
                <ScatterGraph
                  width={
                    landscapeModalVisible
                      ? styles.graphWidthLandscape.width
                      : styles.graphWidth.width
                  }
                  height={
                    landscapeModalVisible
                      ? styles.graphHeightLandscape.height
                      : styles.graphHeight.height
                  }
                  data={chartData}
                  xAxisLabel={i18n.t('DIM')}
                  yAxisLabel={
                    bcsSelected
                      ? i18n.t('BodyCondition')
                      : i18n.t('locomotionScore')
                  }
                  currentVisitVal={currentVisitDIM}
                />
              </View>
            </SafeAreaView>
          }
        />

        {selectedAnimal && (
          <View style={styles.tabsRow}>
            <TouchableOpacity
              style={bcsSelected ? styles.selectedTabContainer : null}
              onPress={() => {
                setBcsSelected(true);
              }}>
              <Text
                style={[
                  styles.tabItemText,
                  bcsSelected
                    ? styles.selectedTabItemText
                    : styles.unSelectedTabItemText,
                ]}>
                {i18n.t('BCS')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.locomotionContainer,
                !bcsSelected ? styles.selectedTabContainer : null,
              ]}
              onPress={() => {
                setBcsSelected(false);
              }}>
              <Text
                style={[
                  styles.tabItemText,
                  !bcsSelected
                    ? styles.selectedTabItemText
                    : styles.unSelectedTabItemText,
                ]}>
                {i18n.t('locomotion')}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {showAnimalBottomSheet && (
        <CustomBottomSheet
          type={BOTTOM_SHEET_TYPE.SIMPLE}
          selectLabel={i18n.t('selectAnimal')}
          searchPlaceHolder={i18n.t('searchAnimal')}
          data={parseEarTags(animalData.animalDetails || [])}
          onChange={onAnimalChange}
          onClose={closeAnimalBottomSheet}
        />
      )}
    </View>
  );
};

export default AnimalAnalysisResults;
