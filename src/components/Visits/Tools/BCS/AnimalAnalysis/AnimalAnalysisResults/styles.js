import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../constants/theme/variables/platform';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import DeviceInfo from 'react-native-device-info';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  infoColumn: {
    flexDirection: 'column',
  },
  dropdownTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dropdownText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: 0.15,
    marginRight: normalize(4),
  },
  earTagLimit: {
    maxWidth: normalize(160),
  },
  penAnalysisTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(20),
    color: colors.alphabetIndex,
    letterSpacing: normalize(0.15),
    marginTop: normalize(6),
  },
  penAnalysisValue: {
    fontFamily: fonts.HelveticaNeueBold,
  },
  leftPadding: { paddingLeft: normalize(10) },
  tabsRow: {
    flexDirection: 'row',
    marginHorizontal: normalize(20),
    marginVertical: normalize(11),
  },
  tabItemText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    paddingBottom: normalize(4),
  },
  selectedTabItemText: {
    color: colors.primaryMain,
  },
  unSelectedTabItemText: {
    color: colors.alphabetIndex,
  },
  locomotionContainer: {
    marginLeft: normalize(38),
  },
  selectedTabContainer: {
    borderBottomWidth: normalize(2),
    borderBottomColor: colors.primaryMain,
  },
  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight) - 50
          : Platform.deviceHeight - 60,
    },
    android: {
      width: normalize(
        DeviceInfo.isTablet()
          ? Platform.deviceHeight * 0.75
          : Platform.deviceHeight,
      ),
    },
  }),
  graphWidth: {
    width: Platform.deviceWidth,
  },
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 100)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.5)
          : normalize(Platform.deviceWidth - 110),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 300 : 100),
      ),
    },
  }),
  graphHeight: {
    height: normalize(Platform.deviceHeight * 0.4),
  },
};
