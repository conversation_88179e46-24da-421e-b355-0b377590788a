import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../constants/theme/variables/platform';
import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import DeviceInfo from 'react-native-device-info';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  infoColumn: {
    flexDirection: 'column',
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: normalize(6),
  },
  statsTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  statsValue: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: colors.grey9,
  },
  leftMargin: {
    marginLeft: normalize(20),
  },
  penAnalysisTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(20),
    color: colors.alphabetIndex,
    letterSpacing: normalize(0.15),
  },
  penAnalysisValue: {
    fontFamily: fonts.HelveticaNeueBold,
  },
  dataLineColor: colors.secondary2,
  domainPadding: {
    x: [0, 35],
  },
  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight + 30)
          : Platform.deviceHeight,
    },
    android: {
      width: normalize(
        DeviceInfo.isTablet()
          ? Platform.deviceHeight * 0.75
          : normalize(Platform.deviceHeight) - 20,
      ),
    },
  }),
  graphWidth: RNPlatform.select({
    ios: {
      width: Platform.deviceWidth + 40,
    },
    android: {
      width: Platform.deviceWidth + 40,
    },
  }),
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 60)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.5)
          : normalize(Platform.deviceWidth - 110),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 300 : 60),
      ),
    },
  }),
  graphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? Platform.deviceHeight * 0.5
          : Platform.deviceHeight * 0.53,
    },
    android: {
      height: normalize(Platform.deviceHeight / 2),
    },
  }),
  axisStyles: {
    stroke: colors.grey1,
    strokeWidth: 1,
    strokeDasharray: '1, 5',
  },
  leftAndroidPadding: RNPlatform.select({
    android: {
      marginLeft: normalize(20),
    },
  }),
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: normalize(10),
    marginHorizontal: normalize(18),
  },
};
