// modules
import React, { useState, useEffect } from 'react';
import {
  View,
  useWindowDimensions,
  Text,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { VictoryAxis } from 'victory-native';

// components
import CustomLineGraph from '../../../../../common/LineGraph';
import ToolGraph from '../../../common/ToolGraph';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import {
  DATE_FORMATS,
  VISIT_TABLE_FIELDS,
} from '../../../../../../constants/AppConstants';
import colors from '../../../../../../constants/theme/variables/customColor';

// helpers
import {
  getAllBCSData,
  getBCSAvg,
  getBCSStdDev,
  sortRecentVisitsForGraph,
} from '../../../../../../helpers/toolHelper';
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';
import { getFormattedDate } from '../../../../../../helpers/dateHelper';
import { addSpace } from '../../../../../../helpers/rumenHealthHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../../store/actions/tool';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';
import { getTotalCowsCountBCS } from '../../../../../../helpers/bcsHelper';

const PenAnalysisResults = ({
  selectedPen,
  penData,
  selectedVisits,
  onDownloadPress,
  onSharePenAnalysisData,
}) => {
  const { width, height } = useWindowDimensions();
  const dispatch = useDispatch();
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);

  const [recentVisits, setRecentVisits] = useState([]);
  const [lineGraphData, setLineGraphData] = useState({
    data: [],
    gradientId: 'gradient1',
    gradientStyles: [
      {
        offset: '0%',
        stopColor: colors.activeTabColor,
      },
      {
        offset: '100%',
        stopColor: colors.white,
      },
    ],
  });
  const [currentXVal, setCurrentXVal] = useState(null);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  useEffect(() => {
    const visit = visitState.visit || {};
    const siteId = !stringIsEmpty(visit.siteId)
      ? visit.siteId
      : visit.localSiteId;
    const accountId = !stringIsEmpty(visit.customerId)
      ? visit.customerId
      : visit.localCustomerId;

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visit?.id,
        tool: VISIT_TABLE_FIELDS.BODY_CONDITION,
      }),
    );
  }, []);

  useEffect(() => {
    if (!toolState.recentVisitsLoading) {
      const data = toolState?.recentVisits?.map(visitObj => {
        const allData = visitObj.bodyCondition
          ? getAllBCSData(JSON.parse(visitObj.bodyCondition))
          : [];
        if (allData.length > 0 && selectedPen) {
          let filteredPenArray = [];
          if (!stringIsEmpty(selectedPen?.sv_id)) {
            filteredPenArray = allData.filter(
              penObj => penObj.penId === selectedPen?.sv_id,
            );
          } else {
            filteredPenArray = allData.filter(
              penObj => penObj.localPenId === selectedPen?.id,
            );
          }
          if (filteredPenArray && filteredPenArray.length > 0) {
            const currentPenObject = filteredPenArray[0];
            return {
              ...currentPenObject,
              visitId: visitObj.id,
              date: visitObj.visitDate,
            };
          }
          return { visitId: visitObj.id, date: visitObj.visitDate };
        }
        return { visitId: visitObj.id, date: visitObj.visitDate };
      });
      setRecentVisits(data);
    }
  }, [toolState.recentVisitsLoading]);

  useEffect(() => {
    let selectedRecentVisits = recentVisits.slice(1);
    selectedRecentVisits = selectedRecentVisits.filter(visit =>
      selectedVisits?.includes(visit.visitId),
    );
    selectedRecentVisits.push(penData);
    selectedRecentVisits = sortRecentVisitsForGraph(selectedRecentVisits);
    const datesDict = {};
    let currentVal = null;
    let maxVal = 0;
    const graphData = selectedRecentVisits?.map(visit => {
      const point = {
        x: getFormattedDate(visit?.date, DATE_FORMATS.MM_dd),
        y: getBCSAvg(visit),
      };
      if (point.x in datesDict) {
        const tempXVal = point.x + addSpace(datesDict[point.x]);
        datesDict[point.x] = datesDict[point.x] + 1;
        point.x = tempXVal;
      } else {
        datesDict[point.x] = 1;
      }
      if (visit.visitId === penData.visitId) {
        currentVal = point.x;
      }
      if (maxVal < point.y) {
        maxVal = point.y;
      }
      return point;
    });
    setCurrentXVal(currentVal);

    const graphDataObj = { ...lineGraphData, data: graphData };
    if (maxVal === 0) {
      graphDataObj.domain = { y: [0, 5] };
    }
    setLineGraphData(graphDataObj);
  }, [penData, recentVisits, selectedVisits]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <ToolGraph
          showExpandIcon
          handleExpandIconPress={onExpandIconPress}
          showDownloadIcon={!landscapeModalVisible}
          onDownloadPress={option =>
            onDownloadPress(
              [lineGraphData],
              option,
              getBCSAvg(penData).toFixed(2),
              getBCSStdDev(penData).toFixed(2),
            )
          }
          onSharePress={(option, exportMethod) =>
            onSharePenAnalysisData(
              [lineGraphData],
              option,
              getBCSAvg(penData).toFixed(2),
              getBCSStdDev(penData).toFixed(2),
              exportMethod,
            )
          }
          showShareIcon={!landscapeModalVisible}
          landscapeModalVisible={landscapeModalVisible}
          customGraphTitleComponent={
            <View style={styles.infoColumn}>
              <View style={styles.statsRow}>
                <Text style={styles.statsTitle}>
                  {`${i18n.t('avg')}: `}
                  <Text style={styles.statsValue}>
                    {convertInputNumbersToRegionalBasis(
                      getBCSAvg(penData).toFixed(2),
                      2,
                      true,
                    )}
                  </Text>
                </Text>
                <Text style={[styles.statsTitle, styles.leftMargin]}>
                  {`${i18n.t('std')}: `}
                  <Text style={styles.statsValue}>
                    {convertInputNumbersToRegionalBasis(
                      getBCSStdDev(penData).toFixed(2),
                      2,
                      true,
                    )}
                  </Text>
                </Text>
              </View>
            </View>
          }
          graphComponent={
            <SafeAreaView>
              <View style={styles.textContainer}>
                <Text style={styles.penAnalysisTitle}>
                  {i18n.t('pen') || ''}:{' '}
                  <Text style={styles.penAnalysisValue}>
                    {selectedPen?.name || ''}
                  </Text>
                </Text>
                <Text style={styles.penAnalysisTitle}>
                  {i18n.t('animalsObserved') || ''}:{' '}
                  <Text style={styles.penAnalysisValue}>
                    {getTotalCowsCountBCS(penData)}
                  </Text>
                </Text>
              </View>
              <View
                style={
                  landscapeModalVisible ? styles.leftAndroidPadding : null
                }>
                <CustomLineGraph
                  data={[lineGraphData]}
                  showVictory={true}
                  showVerticalYAxis
                  showLabelsValue
                  verticalAxisDomain
                  interpolation={'monotoneX'}
                  verticalAxisLabel={i18n.t('category')}
                  xAxisDomainPadding={
                    !landscapeModalVisible ? styles.domainPadding : null
                  }
                  width={
                    landscapeModalVisible
                      ? styles.graphWidthLandscape.width
                      : styles.graphWidth.width
                  }
                  height={
                    landscapeModalVisible
                      ? styles.graphHeightLandscape.height
                      : styles.graphHeight.height
                  }
                  showXAxis={
                    <VictoryAxis
                      dependentAxis
                      axisValue={currentXVal}
                      tickFormat={t => ''}
                      domainPadding={
                        !landscapeModalVisible ? styles.domainPadding : null
                      }
                      style={{
                        axis: styles.axisStyles,
                      }}
                    />
                  }
                />
              </View>
            </SafeAreaView>
          }
        />
      </ScrollView>
    </View>
  );
};

export default PenAnalysisResults;
