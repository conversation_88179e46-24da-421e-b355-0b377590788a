// modules
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Keyboard,
  Text,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// reusable components
import CustomBottomSheet from '../../../../common/CustomBottomSheet';
import CircularCounter from '../../../../common/CircularCounter';
import { showToast } from '../../../../common/CustomToast';
import { showAlertMsg } from '../../../../common/Alerts';
import PenAnalysisResults from './PenAnalysisResults';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  GRAPH_EXPORT_OPTIONS,
  EXPORT_REPORT_TYPES,
  GRAPH_HEADER_OPTIONS,
  NEXT_FIELD_TEXT,
  TOOL_ANALYSIS_TYPES,
} from '../../../../../constants/AppConstants';

import {
  BCS_SCALE_ICON,
  CHEVRON_DOWN_BLUE_ICON,
  CHEVRON_DOWN_GRAY_ICON,
} from '../../../../../constants/AssetSVGConstants';
import {
  BOTTOM_SHEET_TYPE,
  CONTENT_TYPE,
  TOAST_TYPE,
} from '../../../../../constants/FormConstants';
import { normalize } from '../../../../../constants/theme/variables/customFont';

// actions
import { getEnumRequest } from '../../../../../store/actions/enum';
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
  setSelectedPenRequest,
} from '../../../../../store/actions/tool';
import { updateScaleInSite } from '../../../../../store/actions/site';
import {
  getBCSPenAnalysisRequest,
  resetSaveBCSPenAnalysisRequest,
  saveBCSPenAnalysisRequest,
} from '../../../../../store/actions/tools/bcs';

// helpers
import {
  getAllBCSData,
  getPenBCSData,
  getPenPercent,
  mapGraphDataForBCSPenAnalysisExport,
  penExistsInPublishedVisit,
} from '../../../../../helpers/toolHelper';
import {
  pickPenInReducerFromPensList,
  saveSelectedPenInReducer,
} from '../../../../../helpers/visitHelper';
import { stringIsEmpty } from '../../../../../helpers/alphaNumericHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';
import { getTotalCowsCountBCS } from '../../../../../helpers/bcsHelper';

//services
import { isOnline } from '../../../../../services/netInfoService';

//lodash
import _ from 'lodash';

let DATA = [];
let localSelectedScale = null;

const PenAnalysis = props => {
  const {
    penList,
    scaleList,
    screenDisabled,
    currentStep,
    totalSteps,
    selectedVisits,
    healthCurrentActivePen,
    penAnalysis,
    setPenAnalysis,
    setEnableResults,
    shouldEnableResultsButton,
    setIsDirty,
  } = props;
  const [selectedPen, setSelectedPen] = useState(null);
  const [selectedScale, setSelectedScale] = useState(null);
  const [showPenBottomSheet, setShowPenBottomSheet] = useState(false);
  const [showScaleBottomSheet, setShowScaleBottomSheet] = useState(false);
  const [totalCowsCount, setTotalCowsCount] = useState(0);
  // allPenAnalysis -> This state variable will contain pen analysis data for all the pens
  const [allPenAnalysis, setAllPenAnalysis] = useState([]);
  // penAnalysis -> This state variable will contain pen analysis data for the selected pen on screen
  // const [penAnalysis, setPenAnalysis] = useState(null);
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit);
  const enumState = useSelector(state => state.enums);
  const bcsState = useSelector(state => state.bcs);

  const inputReferences = useRef([]);
  const { isEditable = false } = visitState?.visit || false;

  const penAnalysisData = !stringIsEmpty(visitState?.visit?.bodyCondition)
    ? JSON.parse(visitState?.visit?.bodyCondition)
    : {};

  const isDirty = useRef(false);
  const penFinalArray = useRef([]);

  const getPenAnalysisData = () => {
    const { id } = visitState.visit || {};
    // Fetch pen analysis data from database
    dispatch(
      getBCSPenAnalysisRequest({
        localId: id,
        enumState: enumState,
        isEditable: !screenDisabled,
      }),
    );
  };

  useEffect(() => {
    dispatch(getEnumRequest());
    getPenAnalysisData();

    // Save data when screen is exited
    return () => {
      saveBCSPenAnalysis();
    };
  }, []);

  useEffect(() => {
    if (penList.length > 0) {
      // setSelectedPen(penList[0]);
      if (healthCurrentActivePen) {
        pickPenInReducerFromPensList(
          penList,
          healthCurrentActivePen,
          setSelectedPen,
        );
      } else {
        setSelectedPen(penList[0]);
      }
    }
  }, [penList]);

  //saves selected pen in reducer on pen change
  useEffect(() => {
    dispatch(setSelectedPenRequest(selectedPen));
  }, [selectedPen?.id, selectedPen?.sv_id]);

  useEffect(() => {
    if (scaleList.length > 0) {
      const selectedKey = visitState.visit.selectedPointScale;
      let scaleSet = false;
      for (const scale of scaleList) {
        if (scale.key === selectedKey) {
          setSelectedScale(scale);
          scaleSet = true;
          localSelectedScale = scale;
          break;
        }
      }
      if (!scaleSet) {
        setSelectedScale(scaleList[0]);
        localSelectedScale = scaleList[0];
      }
    }
  }, [scaleList]);

  useEffect(() => {
    // When save operation is successful, reset save reducer and fetch pen analysis data again
    if (bcsState.saveBCSPenAnalysisSuccess) {
      dispatch(resetSaveBCSPenAnalysisRequest());
      getPenAnalysisData();
    }
  }, [bcsState.saveBCSPenAnalysisSuccess]);

  useEffect(() => {
    if (bcsState.saveBCSPenAnalysisError) {
      showToast(
        TOAST_TYPE.ERROR,
        bcsState.saveBCSPenAnalysisError || i18n.t('somethingWentWrongError'),
      );
    }
  }, [bcsState.saveBCSPenAnalysisError]);

  useEffect(() => {
    debounce_fun(penAnalysis);
    setTotalCowsCount(getTotalCowsCountBCS(penAnalysis));
  }, [penAnalysis]);

  //disables results button if pen's values sum to 0
  const debounce_fun = _.debounce(penAnalysisArray => {
    shouldEnableResultsButton(
      TOOL_ANALYSIS_TYPES.PEN_ANALYSIS,
      penAnalysisArray,
    );
  }, 1000);

  const saveBCSPenAnalysis = (
    scale = null,
    reset = false,
    isScaleUpdate = false,
  ) => {
    const { id } = visitState.visit || {};
    if (
      isDirty.current ||
      isScaleUpdate ||
      !stringIsEmpty(visitState?.visit?.bodyCondition) ||
      penFinalArray.current.length > 0
    ) {
      const model = {
        penAnalysis: reset ? null : penFinalArray.current,
        localVisitId: id,
        enumState: enumState,
        isEditable: !screenDisabled,
      };
      if (scale || localSelectedScale) {
        model.selectedPointScale = scale || localSelectedScale?.key;
      }

      dispatch(saveBCSPenAnalysisRequest(model));
    }
  };

  useEffect(() => {
    if (!bcsState.bcsPenAnalysisLoading) {
      const allData = getAllBCSData(bcsState.bcsPenAnalysis);
      setAllPenAnalysis(allData);
      penFinalArray.current = allData;
      DATA = allData;
      if (enumState?.simpleEnum && selectedPen) {
        const penData = getPenBCSData(
          allData,
          selectedPen,
          enumState?.simpleEnum,
          selectedScale,
        );
        setPenAnalysis(penData);
      }
    }
  }, [bcsState.bcsPenAnalysisLoading]);

  useEffect(() => {
    if (enumState?.simpleEnum && selectedPen) {
      const penData = getPenBCSData(
        allPenAnalysis,
        selectedPen,
        enumState?.simpleEnum,
        selectedScale,
      );
      setPenAnalysis(penData);
    }
  }, [enumState?.simpleEnum, selectedPen]);

  /**
   * When any data in selected pen state variable is changed,
   * make the same changes in all pens data variable
   */
  useEffect(() => {
    if (penAnalysis && penAnalysis.bodyConditionScores) {
      let index = -1;
      if (penAnalysis.penId) {
        index = allPenAnalysis.findIndex(
          pen => pen?.penId === penAnalysis?.penId,
        );
      } else {
        index = allPenAnalysis.findIndex(
          pen => pen?.localPenId === penAnalysis?.localPenId,
        );
      }
      const tempAllPenAnalysis = [...allPenAnalysis];
      if (index === -1) {
        tempAllPenAnalysis.push(penAnalysis);
      } else {
        tempAllPenAnalysis[index] = penAnalysis;
      }
      setAllPenAnalysis(tempAllPenAnalysis);
      DATA = tempAllPenAnalysis;
    }
    recomputePenFinalArray();
  }, [penAnalysis]);

  const recomputePenFinalArray = () => {
    if (isDirty.current) {
      const tempAllPenAnalysis = [...penFinalArray.current];
      let index = -1;
      if (penAnalysis.penId) {
        index = tempAllPenAnalysis.findIndex(
          pen => pen.penId === penAnalysis.penId,
        );
      } else {
        index = tempAllPenAnalysis.findIndex(
          pen => pen.localPenId === penAnalysis.localPenId,
        );
      }
      if (index === -1) {
        tempAllPenAnalysis.push(penAnalysis);
      } else {
        tempAllPenAnalysis[index] = penAnalysis;
      }
      penFinalArray.current = [...tempAllPenAnalysis];
    }
  };

  const openPenBottomSheet = () => {
    setShowPenBottomSheet(true);
  };

  const closePenBottomSheet = () => {
    setShowPenBottomSheet(false);
  };

  const onPenChange = item => {
    saveSelectedPenInReducer(dispatch, item);
    setSelectedPen(item);
    closePenBottomSheet();
    isDirty.current = false;
    setEnableResults(false);
  };

  const openScaleBottomSheet = () => {
    setShowScaleBottomSheet(true);
  };

  const closeScaleBottomSheet = () => {
    setShowScaleBottomSheet(false);
  };

  const onScaleChange = item => {
    if (item.key !== selectedScale.key) {
      showAlertMsg('', i18n.t('scaleChangeMessage'), [
        {
          text: i18n.t('no'),
          onPress: () => {},
        },
        {
          text: i18n.t('yes'),
          onPress: () => {
            localSelectedScale = item;
            setEnableResults(false);
            setSelectedScale(item);
            saveBCSPenAnalysis(item.key, true, true);
            // Update scale in site
            dispatch(
              updateScaleInSite({
                localSiteId: visitState.visit.localSiteId,
                siteId: visitState.visit.siteId,
                scale: item.key,
              }),
            );
          },
        },
      ]);
    }
    closeScaleBottomSheet();
  };

  const onAnimalIncrement = index => {
    isDirty.current = true;
    let newBodyConditionScoresArray = [...penAnalysis.bodyConditionScores];
    let object = newBodyConditionScoresArray[index];
    object.animalsObserved += 1;
    setPenAnalysis({
      ...penAnalysis,
      bodyConditionScores: newBodyConditionScoresArray,
    });
    setIsDirty(true);
    setTotalCowsCount(totalCowsCount + 1);
  };

  const onAnimalDecrement = index => {
    isDirty.current = true;
    let newBodyConditionScoresArray = [...penAnalysis.bodyConditionScores];
    let object = newBodyConditionScoresArray[index];
    object.animalsObserved -= 1;
    setPenAnalysis({
      ...penAnalysis,
      bodyConditionScores: newBodyConditionScoresArray,
    });
    setIsDirty(true);
    setTotalCowsCount(totalCowsCount - 1);
  };

  const onAnimalCountChange = (index, value) => {
    isDirty.current = true;
    let newBodyConditionScoresArray = [...penAnalysis.bodyConditionScores];
    let object = newBodyConditionScoresArray?.[index];
    let _categoryCount = object.animalsObserved; //used below
    object.animalsObserved = parseInt(value);
    setPenAnalysis({
      ...penAnalysis,
      bodyConditionScores: newBodyConditionScoresArray,
    });
    setIsDirty(true);
    let _totalCowsCount = totalCowsCount - _categoryCount;
    _totalCowsCount += parseInt(value);
    setTotalCowsCount(_totalCowsCount);
  };

  const renderItem = ({ item, index }) => {
    return (
      <View style={styles.listItemRow}>
        <Text style={[styles.listItemText, styles.flexOne]}>
          {item?.bcsCategory}
        </Text>
        <Text style={[styles.listItemText, styles.flexOne]}>
          {convertInputNumbersToRegionalBasis(
            getPenPercent(item, penAnalysis?.bodyConditionScores).toFixed(2),
            2,
          )}
        </Text>
        <View style={[styles.flexByDeviceType, styles.counterStyle]}>
          <CircularCounter
            disabled={screenDisabled}
            count={
              penExistsInPublishedVisit(
                isEditable,
                penAnalysisData,
                selectedPen,
              )
                ? item?.animalsObserved
                : '-'
            }
            onIncrementClick={() => onAnimalIncrement(index)}
            onDecrementClick={() => onAnimalDecrement(index)}
            onChangeText={count => onAnimalCountChange(index, count)}
            showInput={true}
            reference={input => {
              inputReferences.current[index] = input;
            }}
            onSubmitEditing={() => {
              if (index < inputReferences.current.length - 1) {
                inputReferences?.current[index + 1]?.focus();
              } else {
                Keyboard?.dismiss();
              }
            }}
            inputAccessoryViewID="customInputAccessoryView"
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({
                currentRef: inputReferences?.current[index + 1],
                dismiss:
                  index < inputReferences.current.length - 1 ? false : true,
              });
            }}
            returnKeyType={
              index < inputReferences.current.length - 1
                ? NEXT_FIELD_TEXT.NEXT
                : NEXT_FIELD_TEXT.DONE
            }
          />
        </View>
      </View>
    );
  };

  const downloadPenAnalysisData = async (graphData, type, avg, stdDev) => {
    if (await isOnline()) {
      const model = mapGraphDataForBCSPenAnalysisExport(
        visitState?.visit,
        graphData,
        selectedPen?.sv_id || null,
        selectedPen?.name,
        avg,
        stdDev,
      );
      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.BCS_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType: EXPORT_REPORT_TYPES.BCS_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onSharePenAnalysisData = async (
    graphData,
    type,
    avg,
    stdDev,
    exportMethod,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForBCSPenAnalysisExport(
        visitState?.visit,
        graphData,
        selectedPen?.sv_id || null,
        selectedPen?.name,
        avg,
        stdDev,
      );

      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType: EXPORT_REPORT_TYPES.BCS_PEN_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.BCS_PEN_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const renderPenAnalysisResults = () => {
    const { id, visitDate } = visitState.visit || {};

    return (
      <PenAnalysisResults
        selectedPen={selectedPen || {}}
        penData={{ ...penAnalysis, visitId: id, date: visitDate }}
        selectedVisits={selectedVisits}
        onSharePenAnalysisData={onSharePenAnalysisData}
        onDownloadPress={downloadPenAnalysisData}
      />
    );
  };

  return (
    <>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        // behavior={Platform.OS === 'ios' ? 'position' : 'padding'}
        behavior="padding"
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : -120}>
        {currentStep === totalSteps ? (
          renderPenAnalysisResults()
        ) : (
          <View style={styles.flexOne}>
            <CustomInputAccessoryView doneAction={action} type={type} />
            <ScrollView
              keyboardDismissMode="on-drag"
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}>
              <View style={styles.container1}>
                <View style={styles.headerContainer}>
                  <View>
                    <TouchableOpacity
                      style={styles.dropdownTextContainer}
                      onPress={openPenBottomSheet}>
                      <Text
                        style={[styles.dropdownText, styles.penNameLimit]}
                        noOfLines={1}>
                        {selectedPen?.name || ''}
                      </Text>
                      <CHEVRON_DOWN_BLUE_ICON
                        width={normalize(12)}
                        height={normalize(8)}
                      />
                    </TouchableOpacity>
                  </View>
                  <View>
                    <TouchableOpacity
                      style={styles.dropdownTextContainer}
                      onPress={openScaleBottomSheet}
                      disabled={screenDisabled}>
                      <View style={styles.scaleIconContainer}>
                        <BCS_SCALE_ICON
                          width={normalize(13)}
                          height={normalize(13)}
                        />
                      </View>
                      <Text
                        style={[
                          styles.dropdownText,
                          !isEditable && styles.disableColor,
                        ]}>
                        {selectedScale?.name || ''}
                      </Text>
                      {isEditable ? (
                        <CHEVRON_DOWN_BLUE_ICON
                          width={normalize(12)}
                          height={normalize(8)}
                        />
                      ) : (
                        <CHEVRON_DOWN_GRAY_ICON
                          width={normalize(12)}
                          height={normalize(8)}
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={styles.flexOne}>
                  <View style={styles.tableHeader}>
                    <Text style={[styles.headerCategoryText, styles.flexOne]}>
                      {i18n.t('category')}
                    </Text>
                    <Text style={[styles.headerCategoryText, styles.flexOne]}>
                      {i18n.t('penPercent')}
                    </Text>
                    <Text
                      style={[
                        styles.headerCategoryText,
                        styles.flexByDeviceType,
                      ]}>
                      {`${i18n.t('animalsObserved')}: ${totalCowsCount}`}
                    </Text>
                  </View>
                  <View style={styles.flexOne}>
                    <FlatList
                      style={styles.flatlist}
                      keyboardShouldPersistTaps="always"
                      keyboardDismissMode="on-drag"
                      data={
                        penAnalysis && penAnalysis.bodyConditionScores
                          ? penAnalysis.bodyConditionScores
                          : []
                      }
                      showsVerticalScrollIndicator={false}
                      renderItem={renderItem}
                      scrollEnabled={false}
                    />
                  </View>
                </View>
                {showPenBottomSheet && (
                  <CustomBottomSheet
                    type={BOTTOM_SHEET_TYPE.SIMPLE}
                    selectLabel={i18n.t('selectPen')}
                    searchPlaceHolder={i18n.t('searchPen')}
                    data={penList || []}
                    onChange={onPenChange}
                    onClose={closePenBottomSheet}
                  />
                )}
                {showScaleBottomSheet && (
                  <CustomBottomSheet
                    type={BOTTOM_SHEET_TYPE.SIMPLE}
                    selectLabel={i18n.t('selectScale')}
                    data={scaleList || []}
                    disableSearch
                    onChange={onScaleChange}
                    onClose={closeScaleBottomSheet}
                    customContainerStyle={styles.scaleBottomSheetContainer}
                  />
                )}
              </View>
            </ScrollView>
          </View>
        )}
      </KeyboardAvoidingView>
    </>
  );
};

export default PenAnalysis;
