import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';
import DeviceInfo from 'react-native-device-info';

export default {
  flexOne: { flex: 1 },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container1: {
    width: '100%',
    backgroundColor: colors.white,
  },
  keyboardContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(24),
    paddingTop: normalize(23),
    paddingBottom: normalize(20),
  },
  dropdownTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dropdownText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: 0.15,
    marginRight: normalize(8),
  },
  penNameLimit: {
    maxWidth: normalize(140),
  },
  scaleIconContainer: {
    marginRight: normalize(6),
  },
  scaleBottomSheetContainer: {
    height: normalize(312),
  },
  tableHeader: {
    flex: 1,
    flexDirection: 'row',
    height: normalize(40),
    backgroundColor: colors.grey12,
    paddingHorizontal: normalize(24),
    borderWidth: normalize(1),
    borderColor: colors.grey13,
    alignItems: 'center',
    // justifyContent: 'space-between',
  },
  headerCategoryText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    color: colors.grey1,
    textAlign: 'center',
  },
  flatlist: {
    width: '100%',
    marginTop: normalize(5),
  },
  listItemRow: {
    flexDirection: 'row',
    paddingVertical: normalize(13),
    paddingHorizontal: normalize(24),
    alignItems: 'center',
  },
  listItemText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(16),
    lineHeight: normalize(19),
    letterSpacing: 0.15,
    color: colors.grey1,
    textAlign: 'center',
  },
  flexTwo: { flex: 2 },
  disableColor: { color: colors.grey2 },
  flexByDeviceType: {
    flex: DeviceInfo.isTablet() ? 1 : 2,
  },
  counterStyle: { width: 167, alignItems: 'center' },
};
