// modules
import { useCallback } from 'react';

//redux
import { useDispatch } from 'react-redux';

// localization
import i18n from '../../../../../localization/i18n';

// constants
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
} from '../../../../../constants/AppConstants';

// actions
import { isOnline } from '../../../../../services/netInfoService';
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../../../../store/actions/tool';

import { TOAST_TYPE } from '../../../../../constants/FormConstants';
import { showToast } from '../../../../common/CustomToast';

const useExport = () => {
  //api calling
  const dispatch = useDispatch();

  //download graph data
  const downloadPenTimeBudgetGraph = useCallback(
    async (model, type, exportType) => {
      if (await isOnline()) {
        if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
          dispatch(
            downloadToolExcelRequest({
              exportType: exportType,
              model,
            }),
          );
        } else {
          dispatch(
            downloadToolImageRequest({
              exportType: exportType,
              model,
            }),
          );
        }
      } else {
        showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
      }
    },
    [dispatch],
  );

  const onSharePenTimeBudgetGraphData = useCallback(
    async (model, type, exportMethod, exportType) => {
      if (await isOnline()) {
        //share excel
        if (
          type === GRAPH_EXPORT_OPTIONS.EXCEL &&
          exportMethod == GRAPH_HEADER_OPTIONS.SHARE
        ) {
          dispatch(
            emailToolExcelRequest({
              exportType: exportType,
              model,
            }),
          );
          return;
        }
        // share image
        dispatch(
          emailToolImageRequest({
            exportType: exportType,
            model,
          }),
        );
      } else {
        showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
      }
    },
    [dispatch],
  );

  return {
    downloadPenTimeBudgetGraph,
    onSharePenTimeBudgetGraphData,
  };
};

export default useExport;
