import { useNavigation } from '@react-navigation/native';
import { useState, useEffect } from 'react';

//redux
import { useDispatch } from 'react-redux';
import { PEN_TIME_BUDGET_KEYS } from '../../../../../constants/FormConstants';

//helpers
import { parseTimeBudgetData } from '../../../../../helpers/penTimeBudgetHelper';
import { updatePenRequest } from '../../../../../store/actions/pen';

//actions
import { clearActiveCategoryTool } from '../../../../../store/actions/tool';
import { savePenTimeBudgetAnalysisRequest } from '../../../../../store/actions/tools/penTimeBudget';
import { convertStringToNumber } from '../../../../../helpers/alphaNumericHelper';

const usePenTimeBudgetSteps = (
  initialStep,
  totalToolSteps,
  backToolListingStep,
  isEditable = false,
  formRef,
  visitState,
  penList,
) => {
  //api calling
  const dispatch = useDispatch();

  //navigation
  const navigation = useNavigation();

  //local states
  const [currentStep, setCurrentStep] = useState(1);
  const [summaryScreen, setSummaryScreen] = useState(false);
  const [compareButtonVisible, setCompareButtonVisible] = useState(true);

  const [isDirty, setIsDirty] = useState(false);

  //hooks
  useEffect(() => {
    if (currentStep === initialStep) {
      dispatch(clearActiveCategoryTool());
    }
  }, [currentStep]);

  //handlers
  const onNextStepClick = values => {
    if (currentStep === totalToolSteps) {
      savePenTimeBudgetData && savePenTimeBudgetData(values);
      dispatch(clearActiveCategoryTool());
      setSummaryScreen(true);
      setCurrentStep(1);
      setCompareButtonVisible(true);
    } else {
      setSummaryScreen(true);
      savePenTimeBudgetData && savePenTimeBudgetData(values);
      setCurrentStep(prevState => prevState + 1);
    }
  };

  const onPrevStepClick = () => {
    if (currentStep === totalToolSteps) {
      setCurrentStep(prevState => prevState - 1);
      setCompareButtonVisible(true);
    } else if (currentStep === 2) {
      setCurrentStep(prevState => prevState - 1);
    }
  };

  const showCompareButton = value => {
    setCompareButtonVisible(value || false);
  };

  const showSummaryScreen = value => {
    setSummaryScreen(value || false);
  };

  const onDropdownStepSelect = (selectedStep, values, isValid) => {
    if (!isValid) {
      return;
    }
    if (currentStep === selectedStep?.step) return;
    else if (selectedStep?.step === initialStep) {
      dispatch(clearActiveCategoryTool());
      setCurrentStep(prevState => (prevState = selectedStep?.step));
    } else if (selectedStep?.step === totalToolSteps) {
      savePenTimeBudgetData(values);
      setSummaryScreen(true);
      setCurrentStep(prevState => (prevState = selectedStep?.step));
    }
  };

  const onToolListingClick = selectedStep => {
    if (selectedStep?.step == backToolListingStep) {
      navigation.goBack();
    } else if (selectedStep?.step === initialStep) {
      setSummaryScreen(true);
      setCompareButtonVisible(true);
      setCurrentStep(prevState => (prevState = selectedStep?.step));
    }
  };

  const savePenTimeBudgetData = values => {
    const { isEditable = false } = visitState?.visit;
    if (!!penList?.length && isEditable && isDirty) {
      const data = parseTimeBudgetData(values, penList);
      dispatch(
        savePenTimeBudgetAnalysisRequest({
          localVisitId: visitState?.visit?.id,
          pen: data,
        }),
      );
      updatePenDataInSite(values);
    }
  };

  const updatePenDataInSite = values => {
    const stalls = values?.[PEN_TIME_BUDGET_KEYS.NO_OF_STALLS];
    const animals = values?.[PEN_TIME_BUDGET_KEYS.ANIMALS_IN_PENS];
    const milkFrequency = values?.[PEN_TIME_BUDGET_KEYS.MILKING_FREQUENCY];
    if (true) {
      const pen =
        !!penList?.length &&
        penList?.find(
          e =>
            e.id == values?.selectedPenId || e?.sv_id === values?.selectedPenId,
        );
      if (!!pen) {
        const payload = {
          name: pen?.name,
          numberOfStalls: +stalls ? parseFloat(stalls) : null,
          animals: +animals ? parseFloat(animals) : null,
          milkingFrequency: milkFrequency
            ? parseFloat(convertStringToNumber(milkFrequency))
            : null,
          localId: pen?.id,
          updated: true,
        };

        dispatch(updatePenRequest(payload));
      }
    }
  };

  return [
    currentStep,
    compareButtonVisible,
    onNextStepClick,
    onPrevStepClick,
    onDropdownStepSelect,
    showCompareButton,
    savePenTimeBudgetData,
    summaryScreen,
    showSummaryScreen,
    onToolListingClick,
    isDirty,
    setIsDirty,
  ];
};

export default usePenTimeBudgetSteps;
