// modules
import React, { useEffect, useState } from 'react';
import { Keyboard, View } from 'react-native';

//react-redux
import { useDispatch, useSelector } from 'react-redux';

// constants
import {
  CONTENT_TYPE,
  PEN_TIME_BUDGET_KEYS,
  TOAST_TYPE,
} from '../../../../../../constants/FormConstants';
import { PEN_TIME_BUDGET_FIELDS } from '../../../../../../constants/toolsConstants/PenTimeBudget';

// components
import NumberFormInput from '../../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// helpers
import {
  getFormattedCommaNumber,
  getKeyboardType,
  stringIsEmpty,
} from '../../../../../../helpers/alphaNumericHelper';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

//actions
import {
  resetUpdatePenRequest,
  updatePenRequest,
} from '../../../../../../store/actions/pen';
import { showToast } from '../../../../../common/CustomToast';
import { penExistsInPublishedVisit } from '../../../../../../helpers/penTimeBudgetHelper';

const PenTimeBudgetForm = ({
  screenDisabled,
  values,
  handleChange,
  pensList,
  data,
  refreshPensData,
  penAnalysisData,
}) => {
  //api calling
  const dispatch = useDispatch();

  //redux states
  const penState = useSelector(state => state.pen);

  //local state
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  useEffect(() => {
    if (penState.updateSuccess) {
      dispatch(resetUpdatePenRequest());
      refreshPensData && refreshPensData();
    }
  }, [penState.updateSuccess]);

  useEffect(() => {
    if (penState.updateError) {
      showToast(TOAST_TYPE.ERROR, i18n.t('somethingWentWrongError'));
      dispatch(resetUpdatePenRequest());
    }
  }, [penState.updateError]);

  //handlers
  const handleChangeInput = (value, field) => {
    const previousValue = parseFloat(data?.[field]);
    if (
      value !== previousValue &&
      (field === PEN_TIME_BUDGET_KEYS.NO_OF_STALLS ||
        field === PEN_TIME_BUDGET_KEYS.MILKING_FREQUENCY ||
        field === PEN_TIME_BUDGET_KEYS.ANIMALS_IN_PENS)
    ) {
      const pen =
        !!pensList?.length &&
        pensList?.find(
          e =>
            e.id == values?.selectedPenId || e?.sv_id === values?.selectedPenId,
        );
      const fieldKey =
        field === PEN_TIME_BUDGET_KEYS.NO_OF_STALLS ? 'numberOfStalls' : field;
      const payload = {
        name: pen?.name,
        [fieldKey]: value ? parseFloat(value) : null,
        localId: pen?.id,
        updated: true,
      };
      dispatch(updatePenRequest(payload));
    }
  };

  const handleSubmitEditing = index => {
    if (PEN_TIME_BUDGET_FIELDS[index + 1]?.inputRef) {
      PEN_TIME_BUDGET_FIELDS[index + 1]?.inputRef?.current?.focus();
    } else {
      Keyboard.dismiss();
    }
  };

  return (
    <View>
      <CustomInputAccessoryView doneAction={action} type={type} />
      {PEN_TIME_BUDGET_FIELDS?.map((field, index) => {
        return (
          <View key={field?.key} style={styles.container}>
            <NumberFormInput
              label={field.label || ''}
              required={field?.required}
              placeholder={field?.placeholder}
              keyboardType={getKeyboardType(field?.decimalPoints)}
              returnKeyType={field?.returnKeyType}
              maxLength={field?.maxLimit}
              minValue={field?.minValue}
              maxValue={field?.maxValue}
              isInteger={field?.isInteger}
              reference={field?.inputRef}
              decimalPoints={field?.decimalPoints}
              forceOnBlur={true}
              hasCommas={field?.hasCommas}
              onBlur={() => {
                if (
                  field.key === PEN_TIME_BUDGET_KEYS.NO_OF_STALLS ||
                  field.key === PEN_TIME_BUDGET_KEYS.MILKING_FREQUENCY ||
                  field.key === PEN_TIME_BUDGET_KEYS.ANIMALS_IN_PENS
                ) {
                  handleChangeInput(values?.[field.key], field?.key);
                }
              }}
              onChange={handleChange(`${field?.key}`)}
              customInputContainerStyle={styles.customInputContainerStyle}
              textAlign={'left'}
              value={
                penExistsInPublishedVisit(
                  screenDisabled,
                  penAnalysisData,
                  !stringIsEmpty(values?.id)
                    ? values?.id
                    : values?.selectedPenId,
                )
                  ? // ? getFormattedCommaNumber(values[field?.key]?.toString())

                    values[field?.key]?.toString()
                  : '-'
              }
              onSubmitEditing={() => handleSubmitEditing(index)}
              blurOnSubmit={false}
              disabled={!screenDisabled}
              inputAccessoryViewID="customInputAccessoryView"
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  currentRef:
                    PEN_TIME_BUDGET_FIELDS[index + 1]?.inputRef?.current,
                  dismiss: PEN_TIME_BUDGET_FIELDS[index + 1]?.inputRef
                    ? false
                    : true,
                });
              }}
            />
          </View>
        );
      })}
    </View>
  );
};

export default PenTimeBudgetForm;
