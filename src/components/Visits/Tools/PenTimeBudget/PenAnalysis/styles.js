import colors from '../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import Platform from '../../../../../constants/theme/variables/platform';

export default {
  customContainerStyles: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: normalize(20),
    paddingVertical: normalize(10),
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    height: normalize(48),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.primaryMain,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  plusIcon: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(19),
    lineHeight: normalize(16),
    textAlign: 'center',
    color: colors.primaryMain,
    // textTransform: 'uppercase',
    letterSpacing: 1.25,
    paddingTop: normalize(1),
    marginRight: normalize(7),
  },
  createPSPS: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    // textTransform: 'uppercase',
  },

  customFieldLabel: {
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    // textTransform: 'capitalize',
    marginBottom: normalize(8),
  },
  flexOne: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
    borderColor: colors.grey8,
    borderTopWidth: 0.7,
  },
  contentContainerStyle: {
    paddingBottom: normalize(30),
  },
  alert: {
    backgroundColor: colors.white,
  },
  dropdownStyles: {
    marginTop: normalize(5),
    marginHorizontal: normalize(20),
    width: 144,
  },
  // keyboardVerticalOffset: normalize(Platform.deviceHeight < 700 ? 170 : 200),
};
