import React from 'react';

//constants
import { BOTTOM_SHEET_TYPE } from '../../../../../../constants/FormConstants';

//localization
import i18n from '../../../../../../localization/i18n';

//components
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';

//styles
import styles from './styles';

const PenDropdown = ({ value, data, onChangePen, isEditable = false }) => {
  return (
    <CustomBottomSheet
      value={value}
      type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
      selectLabel={i18n.t('selectPen')}
      infoText={i18n.t('selectOne')}
      data={data}
      onChange={onChangePen}
      customInputStyle={styles.firstDropdown}
      customValueStyle={styles.customValue}
      customIconStyles={styles.dropdownIcon}
      iconStroke={styles.iconStrokeColor}
      customLabelStyle={styles.customFieldLabel}
      selectFieldProps={{
        numberOfLines: 2,
      }}
    />
  );
};

export default PenDropdown;
