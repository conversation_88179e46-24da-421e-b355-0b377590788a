import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  customValue: {
    fontSize: normalize(14),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.primaryMain,
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    paddingLeft: normalize(0),
    paddingRight: normalize(0),
  },
  secondDropdown: {
    width: '80%',
    alignSelf: 'flex-end',
    borderWidth: normalize(0),
  },
  firstDropdown: {
    width: '70%',
    borderWidth: normalize(0),
    alignSelf: 'flex-start',
  },
  dropdownIcon: {
    width: normalize(20),
  },
  customFieldLabel: {
    // textTransform: 'capitalize',
    marginBottom: normalize(0),
    height: normalize(0),
  },
  iconStrokeColor: colors.primaryMain,
  // keyboardVerticalOffset: normalize(Platform.deviceHeight < 700 ? 170 : 200),
};
