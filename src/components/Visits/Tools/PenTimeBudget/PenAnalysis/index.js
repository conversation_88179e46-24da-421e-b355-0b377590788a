// modules
import React, { useRef, forwardRef, useCallback, useEffect } from 'react';
import { View, Keyboard, KeyboardAvoidingView, ScrollView } from 'react-native';

//redux
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// reusable components
import Results from './Results';
import PenDropdown from './PenList';
import PenTimeBudgetForm from './BudgetForm';
import ToolAlert from '../../../../../components/Visits/Tools/common/ToolAlert';

//constants
import { TOOL_TYPES } from '../../../../../constants/AppConstants';
import { PEN_TIME_BUDGET_KEYS } from '../../../../../constants/FormConstants';

//helpers
import { parseTimeBudgetData } from '../../../../../helpers/penTimeBudgetHelper';

//actions
import { hidePenTimeBudgetToastRequest } from '../../../../../store/actions/userPreferences';
import { savePenTimeBudgetAnalysisRequest } from '../../../../../store/actions/tools/penTimeBudget';
import { updatePenRequest } from '../../../../../store/actions/pen';

const PenTimeBudgetAnalysis = forwardRef(
  (
    {
      currentStep,
      totalSteps,
      selectedVisits,
      values,
      errors,
      touched,
      handleChange,
      handleBlur,
      setFieldValue,
      toolData,
      setFieldError,
      showCompareButton,
      onPenChange,
      dirty,
      data,
      refreshPensData,
      setSummaryScreen,
      setSelectedVisits,
      isDirty,
      setIsDirty,
    },
    ref,
  ) => {
    let scrollViewRef = useRef();

    useEffect(() => {
      setIsDirty(dirty);
    }, [dirty]);

    //Api calling
    const dispatch = useDispatch();

    //redux states
    const visitState = useSelector(state => state.visit);
    const pensList = useSelector(state => state.tool.pensList);
    const userPreferencesState = useSelector(state => state.userPreferences);

    const { isEditable = false } = visitState?.visit || false;
    const penAnalysisData = visitState?.visit?.penTimeBudgetTool
      ? JSON.parse(visitState?.visit?.penTimeBudgetTool)
      : [];

    const getToolToast = () => {
      const {
        userPreferences: { defaultValues },
      } = userPreferencesState || {};
      const { [TOOL_TYPES.PEN_TIME_BUDGET_TOOL]: penBudgetToast } =
        defaultValues || false;
      return isEditable ? penBudgetToast : false;
    };

    const onCloseToast = useCallback(() => {
      dispatch(hidePenTimeBudgetToastRequest());
    }, [dispatch]);

    const showResults = step => {
      return step === totalSteps;
    };

    const ShowToolToast = () => {
      return (
        getToolToast() && (
          <View>
            <ToolAlert
              onCloseToast={onCloseToast}
              message={i18n.t('sitePenValueChange')}
            />
          </View>
        )
      );
    };

    const onChangePen = pen => {
      if (dirty) {
        const data = parseTimeBudgetData(values, pensList);
        dispatch(
          savePenTimeBudgetAnalysisRequest({
            localVisitId: visitState?.visit.id,
            pen: data,
          }),
        );
      }
      isEditable && updatePenDataInSite(values);
      onPenChange(pen);
    };

    const updatePenDataInSite = values => {
      const stalls = values?.[PEN_TIME_BUDGET_KEYS.NO_OF_STALLS];
      const animals = values?.[PEN_TIME_BUDGET_KEYS.ANIMALS_IN_PENS];
      const milkFrequency = values?.[PEN_TIME_BUDGET_KEYS.MILKING_FREQUENCY];
      if (true) {
        const pen =
          !!pensList?.length &&
          pensList?.find(
            e =>
              e.id == values?.selectedPenId ||
              e?.sv_id === values?.selectedPenId,
          );
        if (!!pen) {
          const payload = {
            name: pen?.name,
            numberOfStalls: +stalls ? parseFloat(stalls) : null,
            animals: +animals ? parseFloat(animals) : null,
            milkingFrequency: +milkFrequency ? parseFloat(milkFrequency) : null,
            localId: pen?.id,
            updated: true,
          };
          dispatch(updatePenRequest(payload));
        }
      }
    };

    return (
      <>
        {showResults(currentStep) ? (
          <Results
            herdData={values}
            toolData={toolData}
            selectedVisits={selectedVisits}
            selectedPen={values}
            setSelectedVisits={setSelectedVisits}
            setSummaryScreen={setSummaryScreen}
            showCompareButton={showCompareButton}
          />
        ) : (
          <>
            <KeyboardAvoidingView style={styles.container} behavior="padding">
              <View style={styles.dropdownStyles}>
                <PenDropdown
                  data={pensList || []}
                  value={values?.selectedPenId || pensList?.[0]?.id}
                  onChangePen={onChangePen}
                  isEditable={isEditable}
                />
              </View>
              <ScrollView
                keyboardDismissMode="onDrag"
                keyboardShouldPersistTaps="always"
                contentContainerStyle={styles.contentContainerStyle}
                ref={scrollViewRef}>
                <View
                // onTouchStart={() => Keyboard.dismiss()}
                >
                  <PenTimeBudgetForm
                    values={values}
                    errors={errors}
                    touched={touched}
                    handleBlur={handleBlur}
                    handleChange={handleChange}
                    setFieldValue={setFieldValue}
                    setFieldError={setFieldError}
                    screenDisabled={isEditable}
                    pensList={pensList}
                    refreshPensData={refreshPensData}
                    data={data}
                    penAnalysisData={penAnalysisData}
                  />
                </View>
              </ScrollView>
            </KeyboardAvoidingView>

            {/* show toast message */}
            <ShowToolToast />
          </>
        )}
      </>
    );
  },
);

export default PenTimeBudgetAnalysis;
