import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

//styles
import styles from '../styles';

//constants
import { CHEVRON_DOWN_BLUE_ICON } from '../../../../../../../../constants/AssetSVGConstants';

//fonts
import { normalize } from '../../../../../../../../constants/theme/variables/customFont';

const CustomGraphTitleComponent = ({
  landscapeModalVisible,
  selectedGraph,
  openGraphBottomSheet,
}) => {
  return (
    <View style={styles.infoColumn}>
      {!landscapeModalVisible ? (
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.dropdownTextContainer}
          onPress={openGraphBottomSheet}>
          <Text style={[styles.dropdownText, styles.textLimit]} noOfLines={1}>
            {selectedGraph?.name || ''}
          </Text>
          <CHEVRON_DOWN_BLUE_ICON width={normalize(12)} height={normalize(8)} />
        </TouchableOpacity>
      ) : (
        <View style={styles.dropdownTextContainer}>
          <Text style={[styles.dropdownText, styles.textLimit]} noOfLines={1}>
            {selectedGraph?.name || ''}
          </Text>
        </View>
      )}
    </View>
  );
};

export default CustomGraphTitleComponent;
