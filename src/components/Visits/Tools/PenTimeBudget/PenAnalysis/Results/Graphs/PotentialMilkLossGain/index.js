import React from 'react';
//modules
import { View, Text } from 'react-native';

//victory graph
import { VictoryAxis } from 'victory-native';

//styles
import styles from '../styles';

//localization
import i18n from '../../../../../../../../localization/i18n';

//constants
import {
  DATE_FORMATS,
  UNIT_OF_MEASURE,
} from '../../../../../../../../constants/AppConstants';

//helpers
import { getFormattedDate } from '../../../../../../../../helpers/dateHelper';

//components
import PenTimeBudgetAnalysisGraph from '../../../../../../../common/PenTimeBudgetGraphs';
import { addSpace } from '../../../../../../../../helpers/rumenHealthHelper';

const PotentialMilkLossGainGraph = ({
  labels,
  graphData,
  recentVisitsData,
  landscapeModalVisible,
  unitOfMeasure,
}) => {
  //handlers
  const getLine = recentVisits => {
    const data = recentVisits?.map(
      (visit, index) =>
        getFormattedDate(visit.date || visit.visitDate, DATE_FORMATS.MM_dd) +
        addSpace(index),
    );
    const { length } = data || [];
    const value = data?.[length - 1] || '';
    return value?.toString();
  };

  return (
    <>
      <View>
        <PenTimeBudgetAnalysisGraph
          labels={labels || []}
          potentialMilkLoss={
            graphData?.map(e => parseFloat(e?.potentialMilkLossGain || 0)) || []
          }
          width={
            landscapeModalVisible
              ? styles.graphWidthLandscape.width
              : styles.graphWidth.width
          }
          height={
            landscapeModalVisible
              ? styles.graphHeightLandscape.height
              : styles.graphHeight.height
          }
          leftYAxisLabel={
            unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
              ? i18n.t('lbs')
              : i18n.t('kgs')
          }
          xAxisFormatter={t => {
            return !!t?.length && t?.slice(0, 5);
          }}
          showXAxis={
            <VictoryAxis
              dependentAxis
              axisValue={getLine(recentVisitsData)}
              tickFormat={t => ''}
              domainPadding={
                !landscapeModalVisible ? styles.domainPadding : null
              }
              style={{
                axis: styles.axisStyles,
              }}
            />
          }
          leftAxisDomain={null}
          customXAxisLabelStyles={styles.customXAxisLabelStyles}
          customRightYAxisTickStyle={styles.rightAxisTicks}
        />
      </View>
      <View style={styles.legendRow}>
        <View style={styles.legendItem}>
          <View style={styles.potentialMilkLoss}></View>
          <Text style={styles.legendText}>
            {i18n.t('potentialMilkDifference')}
          </Text>
        </View>
      </View>
    </>
  );
};

export default PotentialMilkLossGainGraph;
