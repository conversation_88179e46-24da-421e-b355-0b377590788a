import React, { useEffect, useState, useMemo } from 'react';

//modules
import { SafeAreaView, View, ScrollView } from 'react-native';

//react-redux
import { useSelector } from 'react-redux';

//styles
import styles from './styles';

//localization
import i18n from '../../../../../../../localization/i18n';

//constants
import {
  DATE_FORMATS,
  EXPORT_REPORT_TYPES,
} from '../../../../../../../constants/AppConstants';
import { BOTTOM_SHEET_TYPE } from '../../../../../../../constants/FormConstants';
import {
  PEN_TIME_BUDGET_GRAPH_TYPES,
  PEN_TIME_BUDGET_GRAPHS,
} from '../../../../../../../constants/toolsConstants/PenTimeBudget';

//helpers
import { getFormattedDate } from '../../../../../../../helpers/dateHelper';
import { sortRecentVisitsForGraph } from '../../../../../../../helpers/toolHelper';
import {
  getTimeRequiredGraphData,
  parseExportPotentialMilkLoss,
  parseExportTimeAvailableForResting,
} from '../../../../../../../helpers/penTimeBudgetHelper';

//components
import ToolGraph from '../../../../common/ToolGraph';
import CustomGraphTitleComponent from './CustomGraphTitle';
import TimeAvailableRestingGraph from './TimeAvailableResting';
import PotentialMilkLossGainGraph from './PotentialMilkLossGain';
import CustomBottomSheet from '../../../../../../common/CustomBottomSheet';
import { addSpace } from '../../../../../../../helpers/rumenHealthHelper';

const PenAnalysisGraph = ({
  onSharePress,
  onDownloadPress,
  recentVisits,
  selectedVisits,
  selectedPen,
  unitOfMeasure,
}) => {
  //local states
  const [recentVisitsData, setRecentVisitsData] = useState([]);
  const [showGraphBottomSheet, setShowGraphBottomSheet] = useState(false);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);
  const [selectedGraph, setSelectedGraph] = useState(PEN_TIME_BUDGET_GRAPHS[0]);

  //redux states
  const visitState = useSelector(state => state?.visit) || {};

  //data parsing
  const labels = useMemo(
    () =>
      recentVisitsData?.map(
        (visit, index) =>
          getFormattedDate(visit.date, DATE_FORMATS.MM_dd) + addSpace(index),
      ),
    [recentVisitsData],
  );

  const graphData = useMemo(
    () =>
      getTimeRequiredGraphData(recentVisitsData, selectedPen, unitOfMeasure),
    [recentVisitsData],
  );

  //hooks
  useEffect(() => {
    recentVisits = recentVisits.filter(visit =>
      selectedVisits?.includes(visit.visitId),
    );
    recentVisits = sortRecentVisitsForGraph(recentVisits);
    setRecentVisitsData(recentVisits);
  }, [recentVisits, selectedVisits]);

  //handlers
  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const onGraphChange = item => {
    setSelectedGraph(item);
    closeGraphBottomSheet();
  };
  const openGraphBottomSheet = () => {
    setShowGraphBottomSheet(true);
  };

  const closeGraphBottomSheet = () => {
    setShowGraphBottomSheet(false);
  };

  return (
    <View style={styles.container}>
      <ScrollView showsHorizontalScrollIndicator={true}>
        <ToolGraph
          showExpandIcon
          handleExpandIconPress={onExpandIconPress}
          showDownloadIcon={!landscapeModalVisible}
          showShareIcon={!landscapeModalVisible}
          landscapeModalVisible={landscapeModalVisible}
          onSharePress={(option, exportMethod) => {
            if (
              selectedGraph?.id ===
              PEN_TIME_BUDGET_GRAPH_TYPES.TIME_AVAILABLE_FOR_RESTING
            ) {
              const model = parseExportTimeAvailableForResting(
                visitState,
                graphData,
              );

              onSharePress(
                model,
                option,
                exportMethod,
                (exportType = EXPORT_REPORT_TYPES.PEN_TIME_BUDGET_RESTING),
              );
            } else {
              const model = parseExportPotentialMilkLoss(
                visitState,
                graphData,
                unitOfMeasure,
              );
              onSharePress(
                model,
                option,
                exportMethod,
                (exportType = EXPORT_REPORT_TYPES.PEN_TIME_BUDGET_POTENTIAL),
              );
            }
          }}
          onDownloadPress={option => {
            if (
              selectedGraph?.id ===
              PEN_TIME_BUDGET_GRAPH_TYPES.TIME_AVAILABLE_FOR_RESTING
            ) {
              const model = parseExportTimeAvailableForResting(
                visitState,
                graphData,
              );

              onDownloadPress(
                model,
                option,
                (exportType = EXPORT_REPORT_TYPES.PEN_TIME_BUDGET_RESTING),
              );
            } else {
              const model = parseExportPotentialMilkLoss(
                visitState,
                graphData,
                unitOfMeasure,
              );
              onDownloadPress(
                model,
                option,
                (exportType = EXPORT_REPORT_TYPES.PEN_TIME_BUDGET_POTENTIAL),
              );
            }
          }}
          customGraphTitleComponent={
            <CustomGraphTitleComponent
              landscapeModalVisible={landscapeModalVisible}
              selectedGraph={selectedGraph}
              openGraphBottomSheet={openGraphBottomSheet}
            />
          }
          graphComponent={
            <SafeAreaView>
              <View>
                {selectedGraph?.id ===
                PEN_TIME_BUDGET_GRAPH_TYPES.TIME_AVAILABLE_FOR_RESTING ? (
                  <TimeAvailableRestingGraph
                    labels={labels || []}
                    graphData={graphData || []}
                    recentVisitsData={recentVisitsData}
                    landscapeModalVisible={landscapeModalVisible}
                  />
                ) : (
                  <PotentialMilkLossGainGraph
                    labels={labels || []}
                    graphData={graphData || []}
                    unitOfMeasure={unitOfMeasure}
                    recentVisitsData={recentVisitsData}
                    landscapeModalVisible={landscapeModalVisible}
                  />
                )}
              </View>
            </SafeAreaView>
          }
        />
      </ScrollView>
      {showGraphBottomSheet && (
        <CustomBottomSheet
          type={BOTTOM_SHEET_TYPE.SIMPLE}
          selectLabel={i18n.t('selectGraph')}
          data={PEN_TIME_BUDGET_GRAPHS}
          disableSearch
          onChange={onGraphChange}
          onClose={closeGraphBottomSheet}
          customContainerStyle={styles.scaleBottomSheetContainer}
        />
      )}
    </View>
  );
};

export default PenAnalysisGraph;
