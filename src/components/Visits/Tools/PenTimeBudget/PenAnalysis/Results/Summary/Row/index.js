// modules
import React from 'react';

//modules
import { View, Text } from 'react-native';

//animation
import Animated from 'react-native-reanimated';

// styles
import styles from './styles';

// helpers
import { convertInputNumbersToRegionalBasis } from '../../../../../../../../helpers/genericHelper';

const SummaryRow = ({ row }) => {
  const { category = '', currentValue = '', previousValue = '' } = row || {};

  let decimalsAllowed = 0;
  if (currentValue && currentValue?.toString()?.split('.')?.length > 0) {
    decimalsAllowed = currentValue?.toString()?.split('.')?.[1]?.length || 0;
  }

  // const currentFormattedValue =
  //   getCommaSeparatedValues(currentValue, decimalsAllowed) || currentValue;
  // const previousFormattedValue =
  //   getCommaSeparatedValues(previousValue, decimalsAllowed) || previousValue;

  const currentFormattedValue =
    convertInputNumbersToRegionalBasis(currentValue, decimalsAllowed, true) ||
    currentValue;
  const previousFormattedValue =
    convertInputNumbersToRegionalBasis(previousValue, decimalsAllowed, true) ||
    previousValue;

  return (
    <View style={styles.rowContainer}>
      <Animated.View style={styles.rowLabelBox}>
        <Text style={styles.rowLabelText}>{category}</Text>
      </Animated.View>

      <Animated.View style={[styles.rowDataBox, styles.currentRowBox]}>
        <Animated.Text style={styles.rowDataText}>
          {currentFormattedValue}
        </Animated.Text>
      </Animated.View>

      <Animated.View style={[styles.rowDataBox, styles.previousValue]}>
        <Animated.Text style={styles.rowDataText}>
          {previousFormattedValue}
        </Animated.Text>
      </Animated.View>
    </View>
  );
};

export default SummaryRow;
