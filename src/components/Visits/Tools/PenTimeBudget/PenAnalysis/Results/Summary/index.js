// modules
import React, { useMemo } from 'react';
import { FlatList } from 'react-native';

//react-redux
import { useSelector } from 'react-redux';

//components
import SummaryHeader from './Header';
import SummaryRow from './Row';

//helpers
import {
  parsePenTimeBudgetSummaryData,
  parseSummaryHeader,
} from '../../../../../../../helpers/penTimeBudgetHelper';

const PenSummary = ({
  recentVisits,
  selectedVisits,
  selectedPen,
  unitOfMeasure,
}) => {
  //data parsing

  const tableHeader = useMemo(
    () => parseSummaryHeader(recentVisits, selectedVisits),
    [recentVisits, selectedVisits],
  );

  const summary = useMemo(
    () =>
      parsePenTimeBudgetSummaryData(
        recentVisits,
        selectedPen,
        selectedVisits,
        unitOfMeasure,
      ),
    [recentVisits, selectedVisits, selectedPen, unitOfMeasure],
  );

  return (
    <>
      <SummaryHeader dates={tableHeader || []} />
      <FlatList
        keyExtractor={(item, index) => item?.category + index}
        keyboardShouldPersistTaps="always"
        data={!!summary?.length ? summary : []}
        showsVerticalScrollIndicator={false}
        renderItem={({ item, index }) => (
          <SummaryRow row={item} index={index} />
        )}
      />
    </>
  );
};

export default PenSummary;
