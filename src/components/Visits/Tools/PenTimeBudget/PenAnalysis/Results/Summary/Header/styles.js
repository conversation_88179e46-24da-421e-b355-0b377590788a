import fonts, {
  normalize,
} from '../../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../../constants/theme/variables/customColor';

export default {
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginBottom: normalize(10),
    marginTop: normalize(10),
  },

  headerRowContainer: {
    flexDirection: 'row',
    marginHorizontal: normalize(20),
    paddingVertical: normalize(10),
    backgroundColor: colors.primaryMain,
    borderRadius: normalize(4),
    marginBottom: normalize(8),
    marginTop: normalize(16),
  },
  headerRowText: {
    fontFamily: fonts.HelveticaNeueBold,
    fontWeight: '500',
    fontSize: normalize(13),
    lineHeight: normalize(14),
    color: colors.white,
    textAlign: 'center',
    paddingHorizontal: 10,
  },
  firstHeaderRowText: {
    textAlign: 'left',
  },
  rowCell: { width: 77, alignItems: 'center', justifyContent: 'center' },
  firstRowCell: {
    alignItems: 'flex-start',
    justifyContent: 'center',
    width: normalize(177),
  },
};
