import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
  },
  contentContainerStyle: {
    flexGrow: 1,
  },
  flexDirectionRow: {
    flexDirection: 'row',
  },
  headerContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: normalize(20),
    marginHorizontal: normalize(16),
    borderRadius: normalize(4),
    backgroundColor: colors.primaryMain,
  },
  headerBox: {
    width: normalize(88),
  },
  initialHeaderBox: {
    width: normalize(143),
  },
  headerTitle: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(13),
    lineHeight: normalize(14),
    color: colors.white,
    textAlign: 'center',
  },
  tableBody: {
    paddingHorizontal: normalize(16),
    flexDirection: 'row',
  },
  tableRowLabelContainer: {
    flexDirection: 'column',
  },
  rowLabelBox: {
    width: normalize(143),
    backgroundColor: colors.primaryMain,
    marginTop: normalize(8),
    height: normalize(48),
    justifyContent: 'center',
    paddingHorizontal: normalize(8),
    borderTopLeftRadius: normalize(4),
    borderBottomLeftRadius: normalize(4),
  },
  rowLabelText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    lineHeight: normalize(18),
    letterSpacing: normalize(0.2),
    color: colors.white,
  },
  rowDataBox: {
    width: normalize(88),
    height: normalize(48),
    backgroundColor: colors.grey10,
    marginTop: normalize(8),
    justifyContent: 'center',
    alignItems: 'center',
  },
  currentRowBox: {
    backgroundColor: colors.currentBoxColor,
  },
  rowDataText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(13),
    lineHeight: normalize(14),
    letterSpacing: normalize(0.2),
    color: colors.grey9,
  },
};
