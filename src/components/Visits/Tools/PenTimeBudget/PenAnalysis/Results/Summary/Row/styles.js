import fonts, {
  normalize,
} from '../../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../../constants/theme/variables/customColor';

export default {
  rowContainer: {
    flexDirection: 'row',
    marginHorizontal: normalize(20),
    marginBottom: normalize(8),
    backgroundColor: colors.grey10,
    borderRadius: normalize(4),
  },

  tableBody: {
    paddingHorizontal: normalize(16),
    flexDirection: 'row',
  },
  tableRowLabelContainer: {
    flexDirection: 'column',
  },

  rowText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(15),
    color: colors.grey1,
    width: normalize(66),
    paddingLeft: normalize(20),
  },
  firstRowText: {
    textAlign: 'left',
    fontSize: normalize(12),
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    color: colors.grey1,
    width: 120,
    marginRight: 3,
  },
  rowLabelBox: {
    width: normalize(177),
    backgroundColor: colors.primaryMain,
    height: normalize(48),
    justifyContent: 'center',
    paddingHorizontal: normalize(8),
    borderTopLeftRadius: normalize(4),
    borderBottomLeftRadius: normalize(4),
  },
  rowLabelText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(12),
    fontWeight: '400',
    lineHeight: normalize(18),
    letterSpacing: normalize(0.2),
    color: colors.white,
  },
  rowDataBox: {
    width: normalize(77),
    height: normalize(48),
    backgroundColor: colors.grey10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  currentRowBox: {
    backgroundColor: colors.currentBoxColor,
  },
  previousValue: {
    backgroundColor: colors.grey10,
  },
  rowDataText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(13),
    fontWeight: '500',
    lineHeight: normalize(14),
    letterSpacing: normalize(0.2),
    color: colors.grey9,
  },
};
