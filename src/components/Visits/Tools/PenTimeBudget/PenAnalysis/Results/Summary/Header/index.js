// modules
import React from 'react';
import { View, Text } from 'react-native';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../../../localization/i18n';

const SummaryHeader = ({ dates }) => {
  return (
    <View>
      {!!dates?.length && (
        <View style={styles.headerRowContainer}>
          {dates.map((heading, index) => {
            return (
              <View
                style={[index === 0 ? styles.firstRowCell : styles.rowCell]}>
                <Text style={[styles.headerRowText]}>{heading}</Text>
              </View>
            );
          })}
        </View>
      )}
    </View>
  );
};

export default SummaryHeader;
