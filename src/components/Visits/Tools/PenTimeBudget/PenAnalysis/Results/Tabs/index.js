import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

//styles
import styles from '../styles';

//localization
import i18n from '../../../../../../../localization/i18n';

//constants
import { TOOL_RESULTS_TABS } from '../../../../../../../constants/AppConstants';

const PenTimeBudgetTabs = ({
  selectedTab,
  setSelectedTab,
  setSummaryScreen,
  showVisits,
  shouldEnableResultsButton,
}) => {
  return (
    <View style={styles.tabsRow}>
      <TouchableOpacity
        activeOpacity={0.7}
        style={
          selectedTab === TOOL_RESULTS_TABS.SUMMARY
            ? styles.selectedTabContainer
            : null
        }
        onPress={() => {
          setSelectedTab(TOOL_RESULTS_TABS.SUMMARY);
          setSummaryScreen(true);
          showVisits();
        }}>
        <Text
          style={[
            styles.tabItemText,
            selectedTab === TOOL_RESULTS_TABS.SUMMARY
              ? styles.selectedTabItemText
              : styles.unSelectedTabItemText,
          ]}>
          {i18n.t('summary')}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        activeOpacity={0.7}
        style={[
          styles.tabMarginLeft,
          selectedTab === TOOL_RESULTS_TABS.GRAPH
            ? styles.selectedTabContainer
            : null,
        ]}
        onPress={() => {
          if (shouldEnableResultsButton) {
            setSelectedTab(TOOL_RESULTS_TABS.GRAPH);
            setSummaryScreen(false);
            showVisits();
          }
        }}>
        <Text
          style={[
            styles.tabItemText,
            selectedTab === TOOL_RESULTS_TABS.GRAPH
              ? styles.selectedTabItemText
              : shouldEnableResultsButton
              ? styles.unSelectedTabItemText
              : styles.disabledTabItemText,
          ]}>
          {i18n.t('graph')}
        </Text>
      </TouchableOpacity>
    </View>
  );
};
export default PenTimeBudgetTabs;
