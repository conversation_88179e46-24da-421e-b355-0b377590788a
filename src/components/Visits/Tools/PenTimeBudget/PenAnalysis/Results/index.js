import React, { useEffect, useState } from 'react';
//modules
import { View, ScrollView } from 'react-native';

//react-redux
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

//constants
import { TOOL_RESULTS_TABS } from '../../../../../../constants/AppConstants';
import { VISIT_TABLE_FIELDS } from '../../../../../../constants/AppConstants';

//custom hook
import useExport from '../../Hooks/useExport';

//actions
import { getRecentVisitsForToolRequest, setSelectedPenRequest } from '../../../../../../store/actions/tool';

//components
import PenSummary from './Summary';
import PenAnalysisGraph from './Graphs';
import PenTimeBudgetTabs from './Tabs';

//helpers
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';
import {
  checkIfGraphDependentValuesAreZero,
  getFormattedPenTimeBudgetRecentVisits,
  parsePenTimeBudgetSummaryData,
} from '../../../../../../helpers/penTimeBudgetHelper';

const Results = ({
  showCompareButton,
  selectedVisits,
  selectedPen,
  setSummaryScreen,
  setSelectedVisits,
}) => {
  //api calling
  const dispatch = useDispatch();
  const [shouldEnableResultsButton, setShouldEnableResultsButton] = useState(false);
  const [summaryDataForGraphTab, setSummaryDataForGraphTab] = useState([]);

  //STEP 1: SUMMARY DATA FOR GRAPH TAB
  useEffect(() => {
    setSummaryDataForGraphTab(
      parsePenTimeBudgetSummaryData(
        recentVisits,
        selectedPen,
        selectedVisits,
        unitOfMeasure,
      ),
    );
  }, [recentVisits, selectedPen, selectedVisits, unitOfMeasure]);

  //STEP 2: CHECKS IF SUMMARY DATA SET IN STEP 1 HAS ALL GRAPH VALUES AS 0
  useEffect(() => {
    //checks for values that graph is dependent on and disables graph tab if they ALL are 0
    checkIfGraphDependentValuesAreZero(
      summaryDataForGraphTab,
      setShouldEnableResultsButton,
    );
  }, [summaryDataForGraphTab]);

  //saves selected pen in reducer on pen change
  useEffect(() => {
    dispatch(setSelectedPenRequest(selectedPen));
  }, [selectedPen?.id, selectedPen?.selectedPenId]);

  //custom hooks
  const { downloadPenTimeBudgetGraph, onSharePenTimeBudgetGraphData } =
    useExport();

  //local states
  const [recentVisits, setRecentVisits] = useState(null);
  const [selectedTab, setSelectedTab] = useState(TOOL_RESULTS_TABS.SUMMARY);

  //redux states
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);
  const visits = toolState?.recentVisits || [];
  const unitOfMeasure = visitState?.visit?.unitOfMeasure || {};

  useEffect(() => {
    const visit = visitState.visit || {};
    const siteId = !stringIsEmpty(visit.siteId)
      ? visit.siteId
      : visit.localSiteId;
    const accountId = !stringIsEmpty(visit.customerId)
      ? visit.customerId
      : visit.localCustomerId;

    setTimeout(() => {
      dispatch(
        getRecentVisitsForToolRequest({
          siteId,
          accountId,
          localVisitId: visit?.id,
          tool: VISIT_TABLE_FIELDS.PEN_TIME_BUDGET_TOOL,
        }),
      );
    }, 700);
  }, []);

  useEffect(() => {
    if (!toolState.recentVisitsLoading) {
      let data = getFormattedPenTimeBudgetRecentVisits(toolState?.recentVisits);
      setRecentVisits(data);
    }
  }, [toolState.recentVisitsLoading]);

  const showVisits = () => {
    const visitsIds = visits?.map(visit => visit?.id);
    setSelectedVisits(visitsIds);
  };

  return (
    <>
      <View style={styles.container}>
        {
          {
            [TOOL_RESULTS_TABS.SUMMARY]: (
              <PenSummary
                unitOfMeasure={unitOfMeasure}
                selectedPen={selectedPen}
                recentVisits={recentVisits}
                selectedVisits={selectedVisits}
              />
            ),
            [TOOL_RESULTS_TABS.GRAPH]: (
              <ScrollView
                keyboardDismissMode="none"
                keyboardShouldPersistTaps="always">
                <PenAnalysisGraph
                  unitOfMeasure={unitOfMeasure}
                  selectedPen={selectedPen}
                  recentVisits={recentVisits || []}
                  selectedVisits={selectedVisits}
                  onSharePress={onSharePenTimeBudgetGraphData}
                  onDownloadPress={downloadPenTimeBudgetGraph}
                />
              </ScrollView>
            ),
          }[selectedTab]
        }
        <PenTimeBudgetTabs
          showVisits={showVisits}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          setSummaryScreen={setSummaryScreen}
          shouldEnableResultsButton={shouldEnableResultsButton}
        />
      </View>
    </>
  );
};

export default Results;
