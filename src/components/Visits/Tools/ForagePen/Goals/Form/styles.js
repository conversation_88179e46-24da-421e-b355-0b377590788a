import colors from '../../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  flexOne: {
    flex: 1,
    backgroundColor: colors.white,
    marginBottom: normalize(90)
  },
  customHeaderStyle: {
    height: normalize(106),
  },
  silageType: {
    marginTop: normalize(24),
    marginBottom: normalize(16),
    marginHorizontal: normalize(20),
  },
  titleText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(18),
    color: colors.primaryMain,
  },
  goalsFormView: {
    flex: 1,
    marginHorizontal: normalize(20),
    // marginBottom: normalize(100),
  },
  bottomButtonView: {
    position: 'absolute',
    width: '100%',
    paddingHorizontal: normalize(24),
    bottom: normalize(40),
  },
  button: {
    borderWidth: 0,
  },
  buttonText: {
    letterSpacing: 1.25,
  },
  headerView: {
    backgroundColor: colors.grey7,
    flexDirection: 'row',
    height: normalize(40),
    paddingHorizontal: normalize(20),
    justifyContent: 'space-around',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.grey8,
  },
  headerText: {
    fontFamily: customFont.HelveticaNeueBold,
    fontSize: normalize(12),
    lineHeight: normalize(16),
    color: colors.grey1,
  },
  buttonView: {
    marginVertical: normalize(16),
    paddingHorizontal: normalize(26),
  },
  itemHeading: {
    marginTop: normalize(16),
    alignSelf: 'center',
    fontFamily: customFont.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(15),
    color: colors.grey1,
    letterSpacing: 0.25,
    // borderWidth: 2
  },
  inputContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: normalize(20),
    // marginTop:-3
  },
  labelStyle: {
    marginTop: normalize(12),
    marginBottom: 0,
    height: 0,
  },
  customInputContainer: {
    width: normalize(158),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
    paddingHorizontal: normalize(12),
  },
};
