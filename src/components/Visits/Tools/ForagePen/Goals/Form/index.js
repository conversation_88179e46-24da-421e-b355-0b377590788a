import React, { useRef, useState } from 'react';

import { Keyboard, Text, View } from 'react-native';

//components
import NumberFormInput from '../../../../../../components/common/NumberFormInput';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

//localization
import i18n from '../../../../../../localization/i18n';

//styles
import styles from './styles';

//constants
import {
  FORAGE_PENN_GOALS,
  GOALS_DECIMAL_POINTS,
  GOALS_MAX_VALUE,
  GOALS_MIN_VALUE,
} from '../../../../../../constants/toolsConstants/ForagePenState';
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
} from '../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../constants/AppConstants';

const GoalsForm = ({
  herdGoal,
  handleBlur,
  handleChange,
  values,
  silageType,
  silageIndex,
  silageTypes,
  isEditable = false,
}) => {
  // ref
  const goalMinReference = useRef([]);
  const goalMaxReference = useRef([]);

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const goalsHeaderLabels = [i18n.t('goalMin%'), i18n.t('goalMax%')];
  const keys = !!silageType?.length
    ? silageType.map(el => el.key.toLowerCase())
    : [];

  const getSilageType = type => {
    if (!keys?.includes(type?.toLowerCase())) {
      const newSilage = silageTypes?.filter(el => el?.key == type);
      if (newSilage?.length) {
        return newSilage[0]?.value;
      }
    }
    const result = silageTypes?.find(el => el.key === type)?.value;
    return result || '';
  };

  return (
    <View key={'silage' + silageIndex}>
      <CustomInputAccessoryView
        doneAction={action}
        type={type}
        nativeViewId={'customInputAccessoryView' + silageIndex}
      />
      <View style={styles.silageType}>
        <Text style={styles.titleText}>
          {getSilageType(herdGoal?.silageType)}
        </Text>
      </View>
      <View style={styles.headerView}>
        {goalsHeaderLabels?.map(text => (
          <Text key={text} style={styles.headerText}>
            {text}
          </Text>
        ))}
      </View>

      {herdGoal?.goals?.map((item, goalIndex) => {
        const incrementalRef = `${silageIndex}${goalIndex + 1}`;

        return (
          <View key={`herd${silageIndex}${goalIndex}`}>
            <Text style={styles.itemHeading}>{item?.silageRange}</Text>

            <View style={styles.inputContainer}>
              <NumberFormInput
                placeholder={i18n.t('numberPlaceholder')}
                value={values[FORAGE_PENN_GOALS]?.[silageIndex]?.['goals']?.[
                  goalIndex
                ]?.['goalMin']?.toString()}
                isInteger={false}
                minValue={GOALS_MIN_VALUE}
                maxValue={GOALS_MAX_VALUE}
                decimalPoints={GOALS_DECIMAL_POINTS}
                blurOnSubmit={false}
                disabled={!isEditable}
                onChange={handleChange(
                  `${FORAGE_PENN_GOALS}.${silageIndex}.goals.${goalIndex}.goalMin`,
                )}
                onBlur={handleBlur(
                  `${FORAGE_PENN_GOALS}.${silageIndex}.goals.${goalIndex}.goalMin`,
                )}
                customInputContainerStyle={styles.customInputContainer}
                keyboardType={KEYBOARD_TYPE.DECIMAL}
                reference={input => {
                  goalMinReference.current[`${silageIndex}${goalIndex}`] =
                    input;
                }}
                onSubmitEditing={() => {
                  goalMaxReference?.current[
                    `${silageIndex}${goalIndex}`
                  ]?.focus();
                }}
                inputAccessoryViewID={'customInputAccessoryView' + silageIndex}
                returnKeyType={NEXT_FIELD_TEXT.NEXT}
                onFocus={() => {
                  setType(CONTENT_TYPE.NUMBER);
                  setAction({
                    currentRef:
                      goalMaxReference?.current[`${silageIndex}${goalIndex}`],
                  });
                }}
              />
              <NumberFormInput
                placeholder={i18n.t('numberPlaceholder')}
                value={values[FORAGE_PENN_GOALS]?.[silageIndex]?.['goals']?.[
                  goalIndex
                ]?.['goalMax']?.toString()}
                isInteger={false}
                minValue={GOALS_MIN_VALUE}
                maxValue={GOALS_MAX_VALUE}
                decimalPoints={GOALS_DECIMAL_POINTS}
                blurOnSubmit={false}
                disabled={!isEditable}
                onChange={handleChange(
                  `${FORAGE_PENN_GOALS}.${silageIndex}.goals.${goalIndex}.goalMax`,
                )}
                onBlur={handleBlur(
                  `${FORAGE_PENN_GOALS}.${silageIndex}.goals.${goalIndex}.goalMax`,
                )}
                customInputContainerStyle={styles.customInputContainer}
                keyboardType={KEYBOARD_TYPE.DECIMAL}
                reference={input => {
                  goalMaxReference.current[`${silageIndex}${goalIndex}`] =
                    input;
                }}
                onSubmitEditing={() => {
                  //   if (goalIndex < goalMinReference.current.length - 1) {
                  if (goalMinReference?.current[incrementalRef]) {
                    goalMinReference?.current[incrementalRef]?.focus();
                  } else {
                    Keyboard?.dismiss();
                  }
                }}
                inputAccessoryViewID={'customInputAccessoryView' + silageIndex}
                returnKeyType={
                  // goalIndex < goalMinReference.current.length - 1
                  herdGoal.goals.length - 1 === goalIndex
                    ? NEXT_FIELD_TEXT.DONE
                    : NEXT_FIELD_TEXT.NEXT
                }
                onFocus={() => {
                  setType(CONTENT_TYPE.NUMBER);
                  setAction({
                    currentRef: goalMinReference?.current[incrementalRef],
                    dismiss:
                      herdGoal.goals.length - 1 === goalIndex ? true : false,
                    // goalIndex < goalMinReference.current.length - 1
                    //   ? false
                    //   : true,
                  });
                }}
              />
            </View>
          </View>
        );
      })}
    </View>
  );
};

export default GoalsForm;
