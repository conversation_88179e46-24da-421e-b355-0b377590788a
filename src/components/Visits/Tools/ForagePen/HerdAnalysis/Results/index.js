// modules
import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, TouchableOpacity, Text } from 'react-native';

//redux
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../localization/i18n';

// reusable components
import HerdAnalysisSummary from './Summary';
import HerdAnalysisGraphs from './Graphs';

// constants
import { TOOL_RESULTS_TABS } from '../../../../../../constants/AppConstants';

// actions
import { getForagePennGoalsRequest } from '../../../../../../store/actions/tools/foragePennState';

//custom hook
import useExport from '../Hooks/useExport';

const Results = ({
  herdData,
  selectedVisits,
  showCompareButton,
  toolData,
  silageTypes,
}) => {
  //api calling
  const dispatch = useDispatch();

  //local states
  const [goalsData, setGoalsData] = useState([]);
  const [selectedTab, setSelectedTab] = useState(TOOL_RESULTS_TABS.SUMMARY);

  //redux states
  const visitState = useSelector(state => state.visit);
  const foragePenn = useSelector(state => state.foragePennState);

  const { downloadForagePennGraph, onShareForagePennData } = useExport();

  //hooks
  useEffect(() => {
    getGoals();
  }, []);

  useEffect(() => {
    if (!foragePenn?.goalsLoading) {
      setGoalsData(foragePenn?.goals);
    }
  }, [foragePenn?.goalsLoading]);

  //handlers
  const getGoals = useCallback(() => {
    const { id } = visitState.visit || {};
    const obj = { localId: id };
    dispatch(getForagePennGoalsRequest(obj));
  }, [dispatch]);

  return (
    <>
      <View style={styles.container}>
        {
          {
            [TOOL_RESULTS_TABS.SUMMARY]: (
              <HerdAnalysisSummary
                inputs={herdData}
                goals={goalsData}
                silageTypes={silageTypes}
              />
            ),
            [TOOL_RESULTS_TABS.GRAPH]: (
              <ScrollView
                keyboardDismissMode="none"
                keyboardShouldPersistTaps="always">
                <HerdAnalysisGraphs
                  toolData={toolData}
                  scorerData={herdData}
                  goalsData={goalsData}
                  silageTypes={silageTypes}
                  selectedVisits={selectedVisits}
                  onSharePress={onShareForagePennData}
                  onDownloadPress={downloadForagePennGraph}
                  showCompareButton={showCompareButton}
                />
              </ScrollView>
            ),
          }[selectedTab]
        }
        <View style={styles.tabsRow}>
          <TouchableOpacity
            activeOpacity={0.7}
            style={
              selectedTab === TOOL_RESULTS_TABS.SUMMARY
                ? styles.selectedTabContainer
                : null
            }
            onPress={() => {
              setSelectedTab(TOOL_RESULTS_TABS.SUMMARY);
              showCompareButton(false);
            }}>
            <Text
              style={[
                styles.tabItemText,
                selectedTab === TOOL_RESULTS_TABS.SUMMARY
                  ? styles.selectedTabItemText
                  : styles.unSelectedTabItemText,
              ]}>
              {i18n.t('summary')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.7}
            style={[
              styles.tabMarginLeft,
              selectedTab === TOOL_RESULTS_TABS.GRAPH
                ? styles.selectedTabContainer
                : null,
            ]}
            onPress={() => {
              setSelectedTab(TOOL_RESULTS_TABS.GRAPH);
              showCompareButton(true);
            }}>
            <Text
              style={[
                styles.tabItemText,
                selectedTab === TOOL_RESULTS_TABS.GRAPH
                  ? styles.selectedTabItemText
                  : styles.unSelectedTabItemText,
              ]}>
              {i18n.t('graph')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
};

export default Results;
