import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../../constants/theme/variables/platform';
import colors from '../../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  infoColumn: {
    flexDirection: 'column',
  },
  dropdownTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dropdownText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: 0.15,
    marginRight: normalize(8),
  },
  textLimit: {
    maxWidth: normalize(200),
  },
  scaleBottomSheetContainer: {
    height: normalize(500),
  },
  leftPadding: { paddingLeft: normalize(18) },
  legendContainer: landscapeModalVisible => ({
    flexDirection: 'row',
    marginTop: normalize(10),
    marginBottom: landscapeModalVisible ? normalize(12) : normalize(42),
    marginLeft: normalize(6),
    flexWrap: 'wrap',
  }),
  legendItem: {
    flexDirection: 'row',
    marginRight: normalize(20),
    alignItems: 'center',
  },
  legendItemBottomSpacing: {
    marginBottom: normalize(6),
  },
  legendCircle: backgroundColor => ({
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(50),
    backgroundColor: backgroundColor,
    marginRight: normalize(12),
  }),
  legendText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(15),
    color: colors.grey1,
    letterSpacing: 0.15,
  },
  disorderGraphLabels: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(9),
    lineHeight: normalize(11),
    fill: colors.primaryMain,
    padding: normalize(-1),
  },
  scorer: {
    marginBottom: normalize(12),
  },
  scorerTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  incidenceGraphYAxisLabel: { margin: normalize(-25) },

  customContainerStyles: {
    marginTop: normalize(16),
    paddingHorizontal: normalize(18),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    zIndex: 1000,
  },
};
