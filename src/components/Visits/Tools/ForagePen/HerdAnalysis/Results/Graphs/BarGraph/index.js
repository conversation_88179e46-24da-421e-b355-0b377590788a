// modules
import React from 'react';
import { View, Text, ScrollView } from 'react-native';

// components
import CategoryBarGraph from '../../../../../../../common/CategoryBarGraph';

// localization
import i18n from '../../../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import { LEGENDS_ITEMS } from '../../../../../../../../constants/toolsConstants/ForagePenState';
import { convertInputNumbersToRegionalBasis } from '../../../../../../../../helpers/genericHelper';
import { SCORER_ENUMS } from '../../../../../../../../constants/AppConstants';

const ForageBarGraph = ({
  data,
  labels,
  landscapeModalVisible,
  inputScore,
}) => {
  const legendItems =
    inputScore !== SCORER_ENUMS.THREE_SCREEN
      ? LEGENDS_ITEMS
      : LEGENDS_ITEMS.filter(item => item.key !== 'mid2');
  return (
    <View>
      <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
        <CategoryBarGraph
          useTiltedLabels
          hasYOffset
          barWidth={9}
          barOffset={18}
          verticalTickCount={5}
          xAxisFormatter={t => {
            if (!!t?.length) {
              return !!t?.length && t?.slice(0, 5);
            } else {
              return t;
            }
          }}
          valuesFormatter={t => {
            return !!t >= 0
              ? convertInputNumbersToRegionalBasis(t, 1, true)
              : '';
          }}
          yAxisFormatter={t => {
            if (Math.round(t) == 0 || Math.round(t) == '0') {
              return Math.round(t);
            } else {
              return Math.round(t) + '%';
            }
          }}
          // verticalAxisLabel={i18n.t('onScreen')}
          verticalAxisLabel={i18n.t('onScreen')}
          labels={labels?.length ? labels : []}
          data={data || []}
          width={
            landscapeModalVisible
              ? styles.graphWidthLandscape(data?.[0].dataPoints?.length).width
              : styles.graphWidth(data?.[0].dataPoints?.length).width
          }
          xDomainPadding={{ x: [17, 15] }}
          height={
            landscapeModalVisible
              ? styles.graphHeightLandscape.height
              : styles.forageGraphHeight.height
          }
          customYAxisLabelStyle={styles.forageGraphYAxisLabel}
        />
      </ScrollView>
      <View style={styles.legendContainer(landscapeModalVisible)}>
        {legendItems.map((legend, index) => {
          return (
            <View style={styles.legendItem} key={`${legend.title}-${index}`}>
              <View style={styles.legendCircle(legend.color)}></View>
              <Text style={styles.legendText}>{legend.title}</Text>
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default ForageBarGraph;
