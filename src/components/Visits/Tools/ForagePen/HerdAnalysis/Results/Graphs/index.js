// modules
import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';

//react-redux
import { useDispatch, useSelector } from 'react-redux';

// components
import ToolGraph from '../../../../common/ToolGraph';
import CustomBottomSheet from '../../../../../../common/CustomBottomSheet';

// localization
import i18n from '../../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import { BOTTOM_SHEET_TYPE } from '../../../../../../../constants/FormConstants';
import {
  SCORER_ENUMS,
  VISIT_TABLE_FIELDS,
} from '../../../../../../../constants/AppConstants';
import { normalize } from '../../../../../../../constants/theme/variables/customFont';
import { CHEVRON_DOWN_BLUE_ICON } from '../../../../../../../constants/AssetSVGConstants';
import ForageBarGraph from './BarGraph';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../../../store/actions/tool';

// helpers
import { stringIsEmpty } from '../../../../../../../helpers/alphaNumericHelper';
import {
  exportForagePennGraphData,
  getForagePennStateGraphs,
  getFormattedRecentVisits,
  getGraphLegends,
  parseForagePennGraphData,
} from '../../../../../../../helpers/foragePennHelper';
import { sortRecentVisitsForGraph } from '../../../../../../../helpers/toolHelper';
import { PEN_ANALYSIS_TABLE_CONSTANTS } from '../../../../../../../constants/toolsConstants/TMRParticleScoreConstants';

const HerdAnalysisGraphs = ({
  toolData,
  scorerData,
  selectedVisits,
  silageTypes,
  onDownloadPress,
  onSharePress,
  showCompareButton,
}) => {
  //api calling
  const dispatch = useDispatch();

  //local states
  const [data, setData] = useState([]);
  const [recentVisits, setRecentVisits] = useState([]);
  const [selectedGraph, setSelectedGraph] = useState(null);
  const [showGraphBottomSheet, setShowGraphBottomSheet] = useState(false);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  //redux states
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);
  const enumState = useSelector(state => state.enums.enum);
  const { scorers = [] } = enumState || [];

  //data parsing
  const labels = useMemo(() => getGraphLegends(recentVisits), [recentVisits]);
  const graphsData = useMemo(
    () => getForagePennStateGraphs(scorerData, enumState, silageTypes),
    [scorerData, silageTypes],
  );

  useEffect(() => {
    const visit = visitState.visit;
    const siteId = !stringIsEmpty(visit.siteId)
      ? visit.siteId
      : visit.localSiteId;
    const accountId = !stringIsEmpty(visit.customerId)
      ? visit.customerId
      : visit.localCustomerId;

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visit.id,
        tool: VISIT_TABLE_FIELDS.FORAGE_PENN_STATE,
      }),
    );
  }, []);

  //hooks
  useEffect(() => {
    if (!toolState.recentVisitsLoading) {
      let data = getFormattedRecentVisits(toolState.recentVisits);
      data = sortRecentVisitsForGraph(data);
      setRecentVisits(data);
    }
  }, [toolState.recentVisitsLoading]);

  useEffect(() => {
    if (recentVisits?.length) {
      const visits = recentVisits?.filter(el =>
        selectedVisits?.includes(el.visitId),
      );
      const results = parseForagePennGraphData(
        visits,
        selectedGraph || graphsData[0],
        enumState,
        scorerData,
      );
      setData(results);
    }
  }, [recentVisits, selectedVisits, toolData, selectedGraph]);

  //handlers
  const openGraphBottomSheet = () => {
    setShowGraphBottomSheet(true);
  };

  const closeGraphBottomSheet = () => {
    setShowGraphBottomSheet(false);
  };

  const onGraphChange = item => {
    setSelectedGraph(item);
  };

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const getScorer = key => {
    const scorer =
      scorers &&
      scorers?.length > 0 &&
      scorers.find(el => el.key == key)?.value;
    return scorer || '';
  };

  const getScorerName = () => {
    const name = `${i18n.t('scorer')}: ${getScorer(scorerData?.scorer)}`;
    return name;
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <ToolGraph
          showExpandIcon
          handleExpandIconPress={onExpandIconPress}
          showDownloadIcon={!landscapeModalVisible}
          showShareIcon={!landscapeModalVisible}
          onDownloadPress={option => {
            const model = exportForagePennGraphData(
              visitState.visit,
              recentVisits,
              enumState,
              selectedGraph || graphsData[0],
              getScorerName(),
              scorerData,
            );
            onDownloadPress(model, option);
          }}
          onSharePress={(option, exportMethod) => {
            const model = exportForagePennGraphData(
              visitState.visit,
              recentVisits,
              enumState,
              selectedGraph || graphsData[0],
              getScorerName(),
              scorerData,
            );
            onSharePress(model, option, exportMethod);
          }}
          landscapeModalVisible={landscapeModalVisible}
          customGraphTitleComponent={
            <View style={styles.infoColumn}>
              {!landscapeModalVisible ? (
                <>
                  <View style={styles.scorer}>
                    <Text style={styles.scorerTitle}>{getScorerName()}</Text>
                  </View>
                  {!!scorerData?.foragePennState?.length && (
                    <TouchableOpacity
                      activeOpacity={0.8}
                      style={styles.dropdownTextContainer}
                      onPress={openGraphBottomSheet}>
                      <Text
                        style={[styles.dropdownText, styles.textLimit]}
                        noOfLines={1}>
                        {selectedGraph?.name || graphsData[0]?.name}
                      </Text>
                      <CHEVRON_DOWN_BLUE_ICON
                        width={normalize(12)}
                        height={normalize(8)}
                      />
                    </TouchableOpacity>
                  )}
                </>
              ) : (
                <>
                  <View style={styles.scorer}>
                    <Text style={styles.scorerTitle}>{getScorerName()}</Text>
                  </View>
                  {
                    <View style={styles.dropdownTextContainer}>
                      <Text
                        style={[styles.dropdownText, styles.textLimit]}
                        noOfLines={1}>
                        {selectedGraph?.name || graphsData[0]?.name}
                      </Text>
                    </View>
                  }
                </>
              )}
            </View>
          }
          customContainerStyles={styles.customContainerStyles}
          graphComponent={
            <SafeAreaView>
              <View style={styles.leftPadding}>
                {!!data?.length && !!scorerData?.foragePennState?.length && (
                  <ForageBarGraph
                    data={data || []}
                    labels={labels || []}
                    landscapeModalVisible={landscapeModalVisible}
                    inputScore={scorerData?.scorer}
                  />
                )}
              </View>
            </SafeAreaView>
          }
        />
      </ScrollView>

      <View>
        {showGraphBottomSheet && (
          <CustomBottomSheet
            type={BOTTOM_SHEET_TYPE.SIMPLE}
            selectLabel={i18n.t('selectGraph')}
            data={graphsData || []}
            onChange={onGraphChange}
            onClose={closeGraphBottomSheet}
          />
        )}
      </View>
    </View>
  );
};

export default HerdAnalysisGraphs;
