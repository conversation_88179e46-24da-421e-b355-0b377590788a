import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../../../constants/theme/variables/platform';
import colors from '../../../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../../../constants/theme/variables/customFont';
import DeviceInfo from 'react-native-device-info';

export default {
  scaleBottomSheetContainer: {
    height: normalize(500),
  },
  leftPadding: { paddingLeft: normalize(18) },
  legendContainer: landscapeModalVisible => ({
    flexDirection: 'row',
    marginTop: normalize(24),
    marginBottom: landscapeModalVisible ? normalize(12) : normalize(42),
    marginLeft: normalize(6),
    flexWrap: 'wrap',
  }),
  legendItem: {
    flexDirection: 'row',
    marginRight: normalize(20),
    alignItems: 'center',
  },
  legendItemBottomSpacing: {
    marginBottom: normalize(6),
  },
  legendCircle: backgroundColor => ({
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(50),
    backgroundColor: backgroundColor,
    marginRight: normalize(12),
  }),
  legendText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(15),
    color: colors.grey1,
    letterSpacing: 0.15,
  },
  graphHeightLandscape: RNPlatform.select({
    ios: {
      height:
        Platform.deviceWidth < 400
          ? normalize(Platform.deviceWidth - 140)
          : DeviceInfo.isTablet()
          ? normalize(Platform.deviceWidth * 0.45)
          : normalize(Platform.deviceWidth - 130),
    },
    android: {
      height: normalize(
        Platform.deviceWidth - (DeviceInfo.isTablet() ? 370 : 130),
      ),
    },
  }),
  graphHeight: {
    height: normalize(Platform.deviceHeight * 0.45),
  },
  graphWidthLandscape: RNPlatform.select({
    ios: {
      width:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight - 40)
          : normalize(Platform.deviceHeight - 120),
    },
    android: {
      width: normalize(720),
    },
  }),
  // forageGraphHeight: {
  //   height: normalize(Platform.deviceHeight * 0.38),
  // },
  forageGraphHeight: RNPlatform.select({
    ios: {
      height:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight * 0.35)
          : normalize(Platform.deviceHeight * 0.4),
    },
    android: {
      height:
        Platform.deviceHeight < 700
          ? normalize(Platform.deviceHeight * 0.38)
          : normalize(Platform.deviceHeight * 0.4),
    },
  }),
  graphWidthLandscape: dataLength =>
    RNPlatform.select({
      ios: {
        width:
          Platform.deviceHeight < 700
            ? Math.max(
                normalize(500 + dataLength * 90),
                normalize(Platform.deviceHeight - 40),
              )
            : Math.max(
                normalize(500 + dataLength * 90),
                normalize(Platform.deviceHeight - 120),
              ),
      },
      android: {
        width: Math.max(normalize(500 + dataLength * 90), normalize(720)),
      },
    }),
  graphWidth: dataLength => ({
    width:
      dataLength === 0
        ? normalize(Platform.deviceWidth - 50)
        : normalize(200 + dataLength * 90),
  }),
  scorer: {
    marginHorizontal: normalize(15),
  },

  scorerTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  forageGraphYAxisLabel: { margin: normalize(-28), marginLeft: normalize(-24) },
};
