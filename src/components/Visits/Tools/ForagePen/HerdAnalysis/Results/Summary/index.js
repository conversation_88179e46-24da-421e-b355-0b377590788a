// modules
import React, { useMemo } from 'react';
import { View, FlatList, Text } from 'react-native';

//react-redux
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

//components
import SummaryTable from './Table';

//helpers
import { parseSummaryData } from '../../../../../../../helpers/foragePennHelper';

//localization
import i18n from '../../../../../../../localization/i18n';
import { SCORER_ENUMS } from '../../../../../../../constants/AppConstants';
import { PEN_ANALYSIS_TABLE_CONSTANTS } from '../../../../../../../constants/toolsConstants/TMRParticleScoreConstants';

const Summary = ({ goals, inputs, silageTypes }) => {
  //redux states
  const { silageType = [], scorers = [] } = useSelector(
    state => state?.enums?.enum,
  );
  //parsing
  const data = useMemo(() => {
    const summaryData = parseSummaryData(
      inputs,
      goals,
      silageTypes,
      silageType,
      inputs?.scorer,
    );

    return summaryData.map(item => {
      const filteredSummary =
        inputs?.scorer === SCORER_ENUMS.THREE_SCREEN
          ? item.summary.filter(
              sub =>
                ![i18n.t('mid2_4mm'), i18n.t('mid2(4mm)')].includes(
                  sub.silageRange,
                ),
            )
          : item.summary;

      return {
        ...item,
        summary: filteredSummary,
      };
    });
  }, [inputs, goals, silageType, silageTypes]);

  //handlers
  const getScorer = key => {
    const scorer =
      scorers &&
      scorers?.length > 0 &&
      scorers.find(el => el.key == key)?.value;
    return scorer || '';
  };

  const getScorerName = () => {
    const name = `${i18n.t('scorer')}: ${getScorer(inputs?.scorer)}`;
    return name;
  };

  return (
    <View style={styles.container}>
      <View style={styles.scorer}>
        <Text style={styles.scorerTitle}>{getScorerName()}</Text>
      </View>

      <FlatList
        keyExtractor={(item, index) => item.title + index}
        style={styles.flatList}
        keyboardShouldPersistTaps="always"
        data={data.length > 0 ? data : []}
        showsVerticalScrollIndicator={false}
        renderItem={({ item, index }) => (
          <SummaryTable table={item} index={index} />
        )}
      />
    </View>
  );
};

export default Summary;
