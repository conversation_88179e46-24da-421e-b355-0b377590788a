import fonts, {
  normalize,
} from '../../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../../constants/theme/variables/customColor';

export default {
  rowContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: normalize(5),
    paddingVertical: normalize(8),
    backgroundColor: colors.grey10,
    borderRadius: normalize(4),
    marginTop: normalize(12),
    justifyContent: 'space-between',
  },
  rowText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(15),
    color: colors.grey1,
    width: normalize(66),
    paddingLeft: normalize(20),
  },
  firstRowText: {
    textAlign: 'left',
    fontSize: normalize(12),
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    color: colors.grey1,
    width: 120,
    marginRight: 3,
  },
};
