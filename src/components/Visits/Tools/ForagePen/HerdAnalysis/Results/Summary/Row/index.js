// modules
import React from 'react';
import { View, Text } from 'react-native';

// styles
import styles from './styles';

import colors from '../../../../../../../../constants/theme/variables/customColor';
import { convertInputNumbersToRegionalBasis } from '../../../../../../../../helpers/genericHelper';

const SummaryRow = ({ row, index }) => {
  const {
    silageRange = '',
    goalMin = '',
    goalMax = '',
    onScreenValue = '',
  } = row || {};

  // Fixes in color combination
  const max = typeof goalMax === 'string' ? parseFloat(goalMax) : goalMax;
  const min = typeof goalMin === 'string' ? parseFloat(goalMin) : goalMin;

  const onScreenColor =
    onScreenValue >= min && onScreenValue <= max
      ? colors.pileGreenSlope
      : colors.tempRedColor;

  return (
    <View style={styles.rowContainer} key={silageRange + index}>
      <Text style={[styles.firstRowText]}>{silageRange}</Text>
      <Text style={[styles.rowText, { color: onScreenColor }]}>
        {convertInputNumbersToRegionalBasis(onScreenValue, 1)}
      </Text>
      <Text style={[styles.rowText]}>
        {convertInputNumbersToRegionalBasis(goalMin, 1)}
      </Text>
      <Text style={[styles.rowText]}>
        {convertInputNumbersToRegionalBasis(goalMax, 1)}
      </Text>
    </View>
  );
};

export default SummaryRow;
