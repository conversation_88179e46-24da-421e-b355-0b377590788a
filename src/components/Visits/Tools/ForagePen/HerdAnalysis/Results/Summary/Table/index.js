// modules
import React from 'react';
import { View, FlatList } from 'react-native';

//components
import SummaryRow from '../Row';
import SummaryHeader from '../Header';

//styles
import styles from './styles';

const SummaryTable = ({ table, index }) => {
  //destructuring
  const { summary = [] } = table || {};

  return (
    <View key={table?.title} style={styles.marginBottom}>
      <SummaryHeader key={table?.title} title={table?.title} index={index} />
      <FlatList
        key={index}
        keyExtractor={(item, index) => item?.silageRange + index}
        keyboardShouldPersistTaps="always"
        data={summary?.length > 0 ? summary : []}
        showsVerticalScrollIndicator={false}
        renderItem={({ item, index }) => (
          <SummaryRow row={item} index={index} />
        )}
      />
    </View>
  );
};

export default SummaryTable;
