// modules
import React from 'react';
import { View, Text } from 'react-native';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../../../localization/i18n';

const SummaryHeader = ({ title, index }) => {
  const HEADER_ROWS = [
    `${i18n.t('On')} \n ${i18n.t('ScreenTwo')} (%)`,
    `${i18n.t('goal')} % \n (${i18n.t('min').toLowerCase()})`,
    `${i18n.t('goal')} %\n (${i18n.t('max').toLowerCase()})`,
  ];

  return (
    <View key={title + index}>
      <Text style={styles.titleText}>{title || ''}</Text>

      {!!HEADER_ROWS?.length && (
        <View style={styles.headerRowContainer}>
          {HEADER_ROWS.map((heading, index) => {
            return (
              <View
                style={[index === 0 ? styles.firstRowCell : styles.rowCell]}>
                <Text style={[styles.headerRowText]}>{heading}</Text>
              </View>
            );
          })}
        </View>
      )}
    </View>
  );
};

export default SummaryHeader;
