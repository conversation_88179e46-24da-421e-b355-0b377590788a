import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    flexDirection: 'column',
    marginTop: normalize(16),
    marginHorizontal: normalize(5),
    marginBottom: normalize(5),
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    marginBottom: normalize(10),
    marginTop: normalize(10)
  },
  titleMarginTop: {
    marginTop: normalize(24),
  },
  flatList: {
    width: '100%',
    flex: 1,
    // height: '70%',
    paddingHorizontal: normalize(20),
    marginTop: normalize(1),
  },
  flatlistContainer: {
    width: '100%',
  },

  headerRowContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    backgroundColor: colors.primaryMain,
    borderRadius: normalize(4),
  },
  headerRowText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    color: colors.white,
    textAlign: 'left',
  },
  firstHeaderRowText: {
    textAlign: 'left',
  },
  rowCell: { flex: 2, alignItems: 'center' },
  firstRowCell: {
    alignItems: 'flex-end',
   flexGrow: 5,
    
  },
  rowContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: normalize(5),
    paddingVertical: normalize(8),
    backgroundColor: colors.grey10,
    borderRadius: normalize(4),
    marginTop: normalize(12),
    alignItems: 'center',
  },
  rowText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.grey1,
    textAlign: 'left',
    // width: 80,
  },
  firstRowText: {
    textAlign: 'left',
    fontSize: normalize(12),
  },
  scorer: {
    marginHorizontal: normalize(20)
  },
  scorerTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(20),
    letterSpacing: 0.2,
    color: colors.primaryMain,
  }
};
