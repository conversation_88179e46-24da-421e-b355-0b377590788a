import fonts, {
  normalize,
} from '../../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../../constants/theme/variables/customColor';

export default {
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginBottom: normalize(10),
    marginTop: normalize(10),
  },

  headerRowContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    backgroundColor: colors.primaryMain,
    borderRadius: normalize(4),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerRowText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    color: colors.white,
    textAlign: 'center',
  },
  firstHeaderRowText: {
    textAlign: 'left',
  },
  rowCell: { width: normalize(66), alignItems: 'center' },
  firstRowCell: {
    alignItems: 'flex-end',
    width: normalize(186),
  },
};
