import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  tabsRow: {
    flexDirection: 'row',
    marginHorizontal: normalize(25),
    marginVertical: normalize(12),
  },
  selectedTabContainer: {
    borderBottomWidth: normalize(2),
    borderBottomColor: colors.primaryMain,
  },
  tabItemText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    paddingBottom: normalize(4),
  },
  selectedTabItemText: {
    color: colors.primaryMain,
  },
  unSelectedTabItemText: {
    color: colors.alphabetIndex,
  },
  tabMarginLeft: {
    marginLeft: normalize(20),
  },
};
