// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import {
  ACCORDION_DOWN_ICON,
  ACCORDION_RIGHT_ICON,
} from '../../../../../../constants/AssetSVGConstants';

//fonts
import { normalize } from '../../../../../../constants/theme/variables/customFont';

const Accordion = props => {
  const { isSelected, onOpenAccordion, index, name, children, accordionRefs } =
    props;

  const setAccordionName = index => {
    const number = index + 1;
    const value = `${i18n.t('PSP')} ${number} - ${name}`;
    return value;
  };

  return (
    <View style={styles.container} key={index}>
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={() => onOpenAccordion(index)}>
        <View style={styles.accordionContainer}>
          <Text numberOfLines={2} ellipsizeMode='tail' style={styles.accordionTitle}>{setAccordionName(index)}</Text>
          {isSelected ? (
            <ACCORDION_DOWN_ICON width={normalize(14)} height={normalize(14)} />
          ) : (
            <ACCORDION_RIGHT_ICON
              width={normalize(14)}
              height={normalize(14)}
            />
          )}
        </View>
      </TouchableOpacity>
      {!!children && <>{children}</>}
    </View>
  );
};

export default Accordion;
