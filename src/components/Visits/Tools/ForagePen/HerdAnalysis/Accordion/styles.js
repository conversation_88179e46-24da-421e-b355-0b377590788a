import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    flexDirection: 'column',
    paddingHorizontal: normalize(20),
  },
  accordionContainer: {
    width: '100%',
    height: normalize(40),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.primaryMain,
    borderRadius: normalize(4),
    paddingLeft: normalize(8),
    paddingRight: normalize(11),
    marginBottom: normalize(12),
  },
  accordionTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.15,
    color: colors.white,
    width: normalize(300)
  },
};
