// modules
import React, { useRef, useState } from 'react';
import { Keyboard, View } from 'react-native';

//redux
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../localization/i18n';

// reusable components
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';
import NumberFormInput from '../../../../../common/NumberFormInput';
import FormInput from '../../../../../common/FormInput';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  BOTTOM_SHEET_TYPE,
  FORAGE_PENN_STATE_FIELDS,
  FORAGE_PENN_STATE,
  INPUT_TYPE,
  CONTENT_TYPE,
} from '../../../../../../constants/FormConstants';
import {
  NEXT_FIELD_TEXT,
  SCORER_ENUMS,
} from '../../../../../../constants/AppConstants';
import { FORAGE_PENN_VALIDATIONS } from '../../../../../../constants/toolsConstants/ForagePenState';

//hooks
import useSilage from '../Hooks/useSilage';

const HerdForm = ({
  index,
  values,
  errors,
  touched,
  handleChange,
  handleBlur,
  screenDisabled,
  silageType,
  setFieldValue,
  setFieldError,
  getUpdatedSilagesByAccount,
}) => {
  //refs
  const inputReferences = useRef([]);

  // State
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  //redux states
  const visitState = useSelector(state => state.visit);
  const silageState = useSelector(state => state.silage);

  // hooks
  const { createNewSilage, handleChangeSilageType, getSilageType } = useSilage(
    index,
    values,
    visitState,
    silageState,
    setFieldValue,
    setFieldError,
    silageType,
    getUpdatedSilagesByAccount,
  );

  return (
    <View key={index} style={styles.flexOne}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.formInputView}>
        <CustomBottomSheet
          type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
          selectLabel={i18n.t('silage')}
          label={i18n.t('silage')}
          infoText={i18n.t('selectOne')}
          disabled={!screenDisabled}
          data={silageType || []}
          value={values?.[FORAGE_PENN_STATE]?.[index]?.[
            FORAGE_PENN_STATE_FIELDS.SILAGE
          ]?.toString()}
          placeholder={i18n.t('selectOne')}
          searchPlaceHolder={i18n.t('search')}
          newRequiredDesign
          onChange={handleChangeSilageType}
          customLabelStyles={styles.customLabelStyles}
          selectFieldProps={{
            numberOfLines: 1,
          }}
        />
      </View>

      {getSilageType(index) && (
        <View style={styles.formInputView}>
          <FormInput
            label={i18n.t('silageName')}
            type={INPUT_TYPE.TEXT}
            required
            onChange={handleChange(
              `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.SILAGE_NAME}`,
            )}
            value={values?.[FORAGE_PENN_STATE]?.[index]?.[
              FORAGE_PENN_STATE_FIELDS.SILAGE_NAME
            ]?.toString()}
            onBlur={(e, value) => {
              createNewSilage();
            }}
            disabled={!screenDisabled}
            placeholder={i18n.t('inputPlaceholder')}
            // maxLength={30}
            customLabelStyle={styles.customFieldLabel}
            reference={input => {
              inputReferences.current[index] = input;
            }}
            onSubmitEditing={() => {
              inputReferences?.current[
                index + FORAGE_PENN_STATE_FIELDS.TOP
              ]?.focus();
            }}
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            inputAccessoryViewID="customInputAccessoryView"
            onFocus={() => {
              setType(CONTENT_TYPE.TEXT);
              setAction({
                currentRef:
                  inputReferences?.current[
                    index + FORAGE_PENN_STATE_FIELDS.TOP
                  ],
              });
            }}
          />
        </View>
      )}

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('top(19mm)')}
          disabled={!screenDisabled}
          placeholder={i18n.t('decimalNumberPlaceholder')}
          blurOnSubmit={false}
          isInteger={false}
          forceOnBlur
          minValue={FORAGE_PENN_VALIDATIONS.MIN}
          maxValue={FORAGE_PENN_VALIDATIONS.MAX}
          decimalPoints={FORAGE_PENN_VALIDATIONS.DECIMAL_POINTS}
          value={values?.[FORAGE_PENN_STATE]?.[index]?.[
            FORAGE_PENN_STATE_FIELDS.TOP
          ]}
          onBlur={handleBlur(
            `${FORAGE_PENN_STATE}.${index}?.${FORAGE_PENN_STATE_FIELDS.TOP}`,
          )}
          onChange={handleChange(
            `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.TOP}`,
          )}
          error={
            touched[
              `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.TOP}`
            ] &&
            errors[
              `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.TOP}`
            ]
          }
          customLabelStyle={styles.customFieldLabel}
          customInputContainerStyle={styles.customInputContainerStyle}
          customContainerStyle={styles.customContainerStyles}
          reference={input => {
            inputReferences.current[index + FORAGE_PENN_STATE_FIELDS.TOP] =
              input;
          }}
          onSubmitEditing={() => {
            inputReferences?.current[
              index + FORAGE_PENN_STATE_FIELDS.MID1
            ]?.focus();
          }}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                inputReferences?.current[index + FORAGE_PENN_STATE_FIELDS.MID1],
            });
          }}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('mid1(18mm)')}
          disabled={!screenDisabled}
          placeholder={i18n.t('decimalNumberPlaceholder')}
          minValue={FORAGE_PENN_VALIDATIONS.MIN}
          maxValue={FORAGE_PENN_VALIDATIONS.MAX}
          decimalPoints={FORAGE_PENN_VALIDATIONS.DECIMAL_POINTS}
          isInteger={false}
          value={values?.[FORAGE_PENN_STATE]?.[index]?.[
            FORAGE_PENN_STATE_FIELDS.MID1
          ]}
          onBlur={handleBlur(
            `${FORAGE_PENN_STATE}.${index}?.${FORAGE_PENN_STATE_FIELDS.MID1}`,
          )}
          onChange={handleChange(
            `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.MID1}`,
          )}
          error={
            touched[
              `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.MID1}`
            ] &&
            errors[
              `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.MID1}`
            ]
          }
          blurOnSubmit={false}
          customLabelStyle={styles.customFieldLabel}
          customInputContainerStyle={styles.customInputContainerStyle}
          customContainerStyle={styles.customContainerStyles}
          reference={input => {
            inputReferences.current[index + FORAGE_PENN_STATE_FIELDS.MID1] =
              input;
          }}
          onSubmitEditing={() => {
            inputReferences?.current[
              index + FORAGE_PENN_STATE_FIELDS.MID2
            ]?.focus();
          }}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                inputReferences?.current[index + FORAGE_PENN_STATE_FIELDS.MID2],
            });
          }}
        />
      </View>

      {values[FORAGE_PENN_STATE_FIELDS.SCORER] !== SCORER_ENUMS.THREE_SCREEN && (
      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('mid2(4mm)')}
          disabled={!screenDisabled}
          placeholder={i18n.t('decimalNumberPlaceholder')}
          minValue={FORAGE_PENN_VALIDATIONS.MIN}
          maxValue={FORAGE_PENN_VALIDATIONS.MAX}
          decimalPoints={FORAGE_PENN_VALIDATIONS.DECIMAL_POINTS}
          isInteger={false}
          value={values?.[FORAGE_PENN_STATE]?.[index]?.[
            FORAGE_PENN_STATE_FIELDS.MID2
          ]}
          onBlur={handleBlur(
            `${FORAGE_PENN_STATE}.${index}?.${FORAGE_PENN_STATE_FIELDS.MID2}`,
          )}
          onChange={handleChange(
            `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.MID2}`,
          )}
          error={
            touched[
              `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.MID2}`
            ] &&
            errors[
              `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.MID2}`
            ]
          }
          blurOnSubmit={false}
          customLabelStyle={styles.customFieldLabel}
          customInputContainerStyle={styles.customInputContainerStyle}
          customContainerStyle={styles.customContainerStyles}
          reference={input => {
            inputReferences.current[index + FORAGE_PENN_STATE_FIELDS.MID2] =
              input;
          }}
          onSubmitEditing={() => {
            inputReferences?.current[
              index + FORAGE_PENN_STATE_FIELDS.TRAY
            ]?.focus();
          }}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                inputReferences?.current[index + FORAGE_PENN_STATE_FIELDS.TRAY],
            });
          }}
        />
      </View>
      )}

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('tray(g)')}
          disabled={!screenDisabled}
          placeholder={i18n.t('decimalNumberPlaceholder')}
          minValue={FORAGE_PENN_VALIDATIONS.MIN}
          maxValue={FORAGE_PENN_VALIDATIONS.MAX}
          decimalPoints={FORAGE_PENN_VALIDATIONS.DECIMAL_POINTS}
          isInteger={false}
          value={values?.[FORAGE_PENN_STATE]?.[index]?.[
            FORAGE_PENN_STATE_FIELDS.TRAY
          ]}
          onBlur={handleBlur(
            `${FORAGE_PENN_STATE}.${index}?.${FORAGE_PENN_STATE_FIELDS.TRAY}`,
          )}
          onChange={handleChange(
            `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.TRAY}`,
          )}
          error={
            touched[
              `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.TRAY}`
            ] &&
            errors[
              `${FORAGE_PENN_STATE}.${index}.${FORAGE_PENN_STATE_FIELDS.TRAY}`
            ]
          }
          blurOnSubmit={false}
          customLabelStyle={styles.customFieldLabel}
          customInputContainerStyle={styles.customInputContainerStyle}
          customContainerStyle={styles.customContainerStyles}
          reference={input => {
            inputReferences.current[index + FORAGE_PENN_STATE_FIELDS.TRAY] =
              input;
          }}
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
          returnKeyType={NEXT_FIELD_TEXT.DONE}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              dismiss: true,
            });
          }}
        />
      </View>
    </View>
  );
};

export default HerdForm;
