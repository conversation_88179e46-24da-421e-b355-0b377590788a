import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  customContainerStyles: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // borderWidth: 2,
    // marginVertical: normalize(10)
  },
  customInputContainerStyle: {
    marginRight: normalize(-10),
    width: normalize(110),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
  },
  customFieldLabel: {
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    paddingLeft: normalize(5)
    // textTransform: 'capitalize',
  },
  container: {
    flex: 1,
    backgroundColor: colors.white
  },
  formInputView: {
    marginBottom: normalize(16)
  },
  flexOne: {
    // flex: 1,
  },
};
