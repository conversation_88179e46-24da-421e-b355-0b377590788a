// modules
import React from 'react';
import { View } from 'react-native';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../localization/i18n';

// reusable components
import { showAlertMsg } from '../../../../../common/Alerts';
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';

// constants
import {
  BOTTOM_SHEET_TYPE,
  FORAGE_PENN_STATE_FIELDS,
} from '../../../../../../constants/FormConstants';

//lodash
import _ from 'lodash';

const Scorer = ({
  isEditable = false,
  scorers,
  values,
  setFieldValue,
  changeScorerType,
}) => {
  const sortScorers = scorers => {
    if (!!scorers?.length) {
      scorers = _.sortBy(scorers, [str => str?.value?.toLowerCase()]);
      scorers = scorers.filter(e => e?.key !== 'NoneSelected');
      return scorers;
    }
    return scorers || [];
  };

  //handlers
  const handleChangeScorerType = el => {
    if (el?.key !== values[FORAGE_PENN_STATE_FIELDS.SCORER]) {
      showAlertMsg('', i18n.t('scorerChangeMessage'), [
        {
          text: i18n.t('no'),
          onPress: () => {
            return;
          },
        },
        {
          text: i18n.t('yes'),
          onPress: () => {
            onConfirmClick(el);
          },
        },
      ]);
    }
  };

  const onConfirmClick = el => {
    let { scorer } = { ...values };
    scorer = el?.key;
    setFieldValue([FORAGE_PENN_STATE_FIELDS.SCORER], scorer);
    changeScorerType(el?.key);
  };

  return (
    <View style={styles.container}>
      <CustomBottomSheet
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        selectLabel={i18n.t('scorer')}
        label={i18n.t('scorer')}
        infoText={i18n.t('selectOne')}
        disabled={!isEditable}
        value={values[FORAGE_PENN_STATE_FIELDS.SCORER]}
        data={sortScorers(scorers)}
        placeholder={i18n.t('selectOne')}
        searchPlaceHolder={i18n.t('search')}
        newRequiredDesign
        onChange={handleChangeScorerType}
        customLabelStyles={styles?.customLabelStyles}
        selectFieldProps={{
          numberOfLines: 1,
        }}
      />
    </View>
  );
};

export default Scorer;
