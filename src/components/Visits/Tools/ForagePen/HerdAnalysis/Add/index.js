// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../localization/i18n';

const AddButton = ({ addAccordion, screenDisabled }) => {
  return (
    <TouchableOpacity
      onPress={addAccordion}
      style={styles.container}
      >
      <View style={styles.buttonContainer}>
        <Text style={styles.plusIcon}>{i18n.t('plusSign')}</Text>
        <Text style={styles.createPSPS}>{i18n.t('addPSPS')}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default AddButton;
