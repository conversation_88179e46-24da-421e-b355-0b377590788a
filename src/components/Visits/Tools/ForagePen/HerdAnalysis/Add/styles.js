import colors from '../../../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';

export default {
  
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    height: normalize(48),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.primaryMain,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: normalize(16)
  },
  plusIcon: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(19),
    lineHeight: normalize(16),
    textAlign: 'center',
    color: colors.primaryMain,
    // textTransform: 'uppercase',
    letterSpacing: 1.25,
    paddingTop: normalize(1),
    marginRight: normalize(7),
  },
  createPSPS: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    // textTransform: 'uppercase',
  },
  container: {
    paddingHorizontal: normalize(20)
  }
 
};
