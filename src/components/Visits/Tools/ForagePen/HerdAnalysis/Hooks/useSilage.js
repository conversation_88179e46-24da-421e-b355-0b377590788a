//redux
import { useDispatch, useSelector } from 'react-redux';

import { useEffect, useCallback } from 'react';

//actions
import {
  createSilageRequest,
  resetCreateSilageRequest,
  updateSilageRequest,
} from '../../../../../../store/actions/silage';

//helpers
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';

//localization
import i18n from '../../../../../../localization/i18n';

//constants
import {
  FORAGE_PENN_STATE,
  FORAGE_PENN_STATE_FIELDS,
  TOAST_TYPE,
} from '../../../../../../constants/FormConstants';
import { OTHER } from '../../../../../../constants/toolsConstants/ForagePenState';

//components
import { showToast } from '../../../../../common/CustomToast';
import { showLoader, hideLoader } from '../../../../../../store/actions/view';

export default (
  index,
  values,
  visitState,
  silageState,
  setFieldValue,
  setFieldError,
  silageType,
  getUpdatedSilagesByAccount,
) => {
  //api calling
  const dispatch = useDispatch();

  const authenticatedUser = useSelector(state => state.authentication.user);

  //hooks
  useEffect(() => {
    if (silageState?.addedSilage) {
      const id = silageState?.addedSilage?.id;
      let newItems = [...values?.[FORAGE_PENN_STATE]];
      newItems[index].silageId = id;
      setFieldValue(FORAGE_PENN_STATE, [...newItems]);
    }
  }, [silageState.addedSilage]);

  useEffect(() => {
    if (silageState.createError) {
      showToast(
        TOAST_TYPE.ERROR,
        silageState?.createErrorMsg || i18n.t('somethingWentWrongError'),
      );
      setFieldError(FORAGE_PENN_STATE_FIELDS.SILAGE, true);
      dispatch(resetCreateSilageRequest());
      dispatch(hideLoader());
    }
  }, [silageState?.createError]);

  useEffect(() => {
    if (silageState.createSuccess) {
      // showToast(TOAST_TYPE.SUCCESS, i18n.t('silageCreatedSuccess'));
      dispatch(resetCreateSilageRequest());
      getUpdatedSilagesByAccount && getUpdatedSilagesByAccount();
      dispatch(hideLoader());
    }

    if (silageState.updateSuccess) {
      // showToast(TOAST_TYPE.SUCCESS, i18n.t('silageUpdatedSuccess'));
      dispatch(resetCreateSilageRequest());
      getUpdatedSilagesByAccount && getUpdatedSilagesByAccount();
      dispatch(hideLoader());
    }
  }, [silageState?.createSuccess, silageState?.updateSuccess]);

  //handlers
  const createNewSilage = () => {
    const silageName =
      values?.[FORAGE_PENN_STATE]?.[index]?.[
        FORAGE_PENN_STATE_FIELDS.SILAGE_NAME
      ]?.toString();

    const id =
      values?.[FORAGE_PENN_STATE]?.[index]?.[
        FORAGE_PENN_STATE_FIELDS.SILAGE_ID
      ];
    const silage = silageType?.find(e => e?.key === id);

    if (!silageName?.length || silageName === silage?.value) {
      return;
    }

    const { siteId, localSiteId, customerId, localCustomerId } =
      visitState.visit;
    let silageObj = {
      silageName: silageName,
      accessIdentifier: authenticatedUser?.email || null,
    };
    if (siteId && !stringIsEmpty(siteId)) {
      silageObj.siteId = siteId;
    } else {
      silageObj.localSiteId = localSiteId;
    }
    if (customerId && !stringIsEmpty(customerId)) {
      silageObj.accountId = customerId;
    } else {
      silageObj.localAccountId = localCustomerId;
    }

    if (!!id) {
      //id may be local id or server id
      const sv_id = /-/.test(id);
      if (sv_id) {
        silageObj.silageId = id;
      } else {
        silageObj.localSilageId = id;
      }
      silageObj.updated = true;
      dispatch(updateSilageRequest(silageObj));
      return;
    }
    dispatch(showLoader());
    dispatch(createSilageRequest(silageObj));
  };

  const handleChangeSilageType = silageType => {
    let newItems = [...values?.[FORAGE_PENN_STATE]];
    newItems[index].silage = silageType?.key;

    if (silageType?.key === OTHER) {
      newItems[index].silageId = null;
      newItems[index].silageName = '';
    }
    setFieldValue(FORAGE_PENN_STATE, [...newItems]);
  };

  const getSilageType = index => {
    const silage =
      values?.[FORAGE_PENN_STATE]?.[index]?.[
        FORAGE_PENN_STATE_FIELDS.SILAGE
      ]?.toString();

    return silage === OTHER ? true : false;
  };

  return { createNewSilage, handleChangeSilageType, getSilageType };
};
