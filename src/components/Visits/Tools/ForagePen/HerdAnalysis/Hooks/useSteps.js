import { useNavigation } from '@react-navigation/native';
import { useState, useEffect } from 'react';

//redux
import { useDispatch } from 'react-redux';
import { TOOL_TYPES } from '../../../../../../constants/AppConstants';
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';

//helpers
import {
  calculateForagePennArrayLength,
  parseForageHerdInputValues,
} from '../../../../../../helpers/foragePennHelper';
import { updateSiteRequest } from '../../../../../../store/actions/site';

//actions
import { clearActiveCategoryTool } from '../../../../../../store/actions/tool';
import { saveForagePennRequest } from '../../../../../../store/actions/tools/foragePennState';

const useSteps = (
  initialStep,
  totalToolSteps,
  isEditable = false,
  formRef,
  silageType,
  silageTypes,
  visitState,
  site,
) => {
  //api calling
  const dispatch = useDispatch();

  //navigation
  const navigation = useNavigation();

  //local states
  const [currentStep, setCurrentStep] = useState(1);
  const [compareButtonVisible, setCompareButtonVisible] = useState(false);

  //hooks
  useEffect(() => {
    if (currentStep === initialStep) {
      dispatch(clearActiveCategoryTool());
    }
  }, [currentStep]);

  //handlers
  const onNextStepClick = values => {
    if (currentStep === totalToolSteps) {
      dispatch(clearActiveCategoryTool());
      setCurrentStep(1);
      setCompareButtonVisible(false);
      saveForagePennData && saveForagePennData(values);
      navigation.goBack();
    } else {
      const localSum = calculateForagePennArrayLength(values?.foragePennState);
      localSum && setCurrentStep(prevState => prevState + 1);
      // setCurrentStep(prevState => prevState + 1);
      saveForagePennData && saveForagePennData(values);
    }
  };

  const onPrevStepClick = () => {
    if (currentStep === totalToolSteps) {
      setCurrentStep(prevState => prevState - 1);
      setCompareButtonVisible(false);
    } else if (currentStep === 2) {
      setCurrentStep(prevState => prevState - 1);
    }
  };

  const showCompareButton = value => {
    setCompareButtonVisible(value || false);
  };

  const onDropdownStepSelect = (selectedStep, values, isValid) => {
    if (!isValid) {
      return;
    }
    if (currentStep === selectedStep?.step) return;
    else if (selectedStep?.step === initialStep) {
      dispatch(clearActiveCategoryTool());
      setCurrentStep(prevState => (prevState = selectedStep?.step));
    } else if (selectedStep?.step === totalToolSteps) {
      const localSum = calculateForagePennArrayLength(values?.foragePennState);
      if (localSum) {
        setCurrentStep(prevState => (prevState = selectedStep?.step));
        saveForagePennData(values);
      }
    }
  };

  //save forage penn data in local db
  const saveForagePennData = values => {
    if (isEditable) {
      if (formRef?.current?.isValid && formRef?.current?.dirty) {
        const { id } = visitState.visit;
        const data = parseForageHerdInputValues(
          values,
          visitState?.visit,
          silageType,
          silageTypes,
        );

        dispatch(
          saveForagePennRequest({
            localVisitId: id,
            forageData: { ...data },
            visitData: visitState.visit,
          }),
        );

        saveScorerOnSite(values);
      }
    }
  };

  const saveScorerOnSite = data => {
    let keys = site.keys;
    if (!stringIsEmpty(keys) && typeof keys === 'string') {
      keys = JSON.parse(keys);
    }
    const siteObj = {
      ...site,
      keys: {
        ...keys,
        [TOOL_TYPES.FORAGE_PENN_STATE]: {
          scorer: data?.scorer,
        },
      },
      updated: true,
    };
    dispatch(updateSiteRequest(siteObj));
  };

  return [
    currentStep,
    compareButtonVisible,
    onNextStepClick,
    onPrevStepClick,
    onDropdownStepSelect,
    showCompareButton,
    saveForagePennData,
  ];
};

export default useSteps;
