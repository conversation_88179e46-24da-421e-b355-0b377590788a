// modules
import React, { useState, useRef, forwardRef, useCallback } from 'react';
import { View, Keyboard, KeyboardAvoidingView, ScrollView } from 'react-native';

//redux
import { useDispatch, useSelector } from 'react-redux';

//forms
import { FieldArray } from 'formik';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// reusable components
import HerdForm from './Form';
import Scorer from './Scorer';
import AddButton from './Add';
import Results from './Results';
import Accordion from './Accordion';
import ToolAlert from '../../../../../components/Visits/Tools/common/ToolAlert';

//helpers
import { getInitialAccordionModelValues } from '../../../../../helpers/foragePennHelper';

//constants
import { TOOL_TYPES } from '../../../../../constants/AppConstants';
import { FORAGE_PENN_STATE, FORAGE_PENN_STATE_FIELDS } from '../../../../../constants/FormConstants';
import { OTHER } from '../../../../../constants/toolsConstants/ForagePenState';

//actions
import { changeForagePennScorerRequest } from '../../../../../store/actions/tools/foragePennState';
import { hideForagePenStateToastRequest } from '../../../../../store/actions/userPreferences';

const ForageHerdAnalysis = forwardRef(
  (
    {
      currentStep,
      totalSteps,
      selectedVisits,
      screenDisabled,
      values,
      errors,
      touched,
      handleChange,
      handleBlur,
      setFieldValue,
      toolData,
      silageTypes,
      setFieldError,
      showCompareButton,
      getUpdatedSilagesByAccount,
      setPensSum,
    },
    ref,
  ) => {
    let scrollViewRef = useRef();

    //Api calling
    const dispatch = useDispatch();

    //refs
    const accordionRefs = useRef([]);

    //local states
    const [activeIndex, setActiveIndex] = useState(null);
    const [selectedScorer, setSelectedScorer] = useState(values[FORAGE_PENN_STATE_FIELDS.SCORER]);

    //redux states
    const visitState = useSelector(state => state.visit);
    const enumState = useSelector(state => state.enums);
    const userPreferencesState = useSelector(state => state.userPreferences);

    const { isEditable = false } = visitState.visit || false;
    const { silageType = [], scorers = [] } = enumState?.enum || [];

    const handleAccordionPress = index => {
      scrollToAccordion(index);
      if (index !== activeIndex) {
        setActiveIndex(index);
        return;
      }
      setActiveIndex(null);
    };

    const isAccordionOpen = index => {
      return index === activeIndex;
    };

    //add new model of accordion
    const addAccordion = (form, index) => {
      const scorer = values[FORAGE_PENN_STATE_FIELDS.SCORER];
      const model = getInitialAccordionModelValues(silageType, scorer);
      form.push({ ...model });
      setActiveIndex(values?.[FORAGE_PENN_STATE]?.length);
    };

    const showButton = arr => {
      return arr?.length < 10 &&
        isEditable &&
        !!silageType?.length &&
        !!scorers?.length
        ? true
        : false;
    };

    const scrollToAccordion = index => {
      const fy = (index + 1) * 60;
      scrollViewRef?.current?.scrollTo({
        x: 0,
        y: fy,
        animated: true,
      });
    };

    const changeScorerType = useCallback(
      key => {
        setSelectedScorer(key);
        setPensSum(false);
        const { id } = visitState.visit || {};
        dispatch(
          changeForagePennScorerRequest({ localVisitId: id, scorer: key }),
        );
      },
      [dispatch],
    );

    const getSilageName = el => {
      const data = silageTypes?.find(silage => silage.key == el?.silage);
      if (el?.silage == OTHER) {
        const value = `${data?.value || ''} - ${el?.silageName || ''}`;
        return value || '';
      }
      return data?.value || '';
    };

    const getToolToast = () => {
      const {
        userPreferences: { defaultValues },
      } = userPreferencesState || {};
      const { [TOOL_TYPES.FORAGE_PENN_STATE]: foragePennToast = false } =
        defaultValues || false;
      return isEditable ? foragePennToast : false;
    };

    const onCloseToast = useCallback(() => {
      dispatch(hideForagePenStateToastRequest());
    }, [dispatch]);

    return (
      <>
        {currentStep === totalSteps ? (
          <Results
            herdData={values}
            toolData={toolData}
            silageTypes={silageTypes}
            selectedVisits={selectedVisits}
            showCompareButton={showCompareButton}
          />
        ) : (
          <>
            <KeyboardAvoidingView style={styles.container} behavior="padding">
              <ScrollView
                keyboardDismissMode="none"
                keyboardShouldPersistTaps="always"
                contentContainerStyle={styles.contentContainerStyle}
                ref={scrollViewRef}>
                <View onTouchStart={() => Keyboard.dismiss()}>
                  {/* Scorer dropdown */}
                  <Scorer
                    values={values}
                    scorers={scorers}
                    isEditable={isEditable}
                    setFieldValue={setFieldValue}
                    changeScorerType={changeScorerType}
                  />
                  {/* Add PSPS */}
                  <FieldArray
                    name={FORAGE_PENN_STATE}
                    render={arrayHelpers => (
                      <>
                        {values?.[FORAGE_PENN_STATE]?.map(
                          (foragePen, index) => {
                            return (
                              <Accordion
                                index={index}
                                accordionRefs={accordionRefs}
                                onOpenAccordion={handleAccordionPress}
                                isSelected={isAccordionOpen(index)}
                                name={getSilageName(foragePen)}>
                                {isAccordionOpen(index) && (
                                  <HerdForm
                                    index={index}
                                    values={values}
                                    errors={errors}
                                    touched={touched}
                                    foragePen={foragePen}
                                    handleBlur={handleBlur}
                                    handleChange={handleChange}
                                    silageType={silageTypes}
                                    setFieldValue={setFieldValue}
                                    setFieldError={setFieldError}
                                    screenDisabled={isEditable}
                                    getUpdatedSilagesByAccount={
                                      getUpdatedSilagesByAccount
                                    }
                                  />
                                )}
                              </Accordion>
                            );
                          },
                        )}
                        {!!showButton(values?.[FORAGE_PENN_STATE]) && (
                          <AddButton
                            addAccordion={() => addAccordion(arrayHelpers)}
                            arrayHelpers={arrayHelpers}
                          />
                        )}
                      </>
                    )}
                  />
                </View>
              </ScrollView>
            </KeyboardAvoidingView>

            {/* show toast message */}
            {!!getToolToast() && (
              <View>
                <ToolAlert
                  onCloseToast={onCloseToast}
                  message={i18n.t('foragePenToastMessage')}
                />
              </View>
            )}
          </>
        )}
      </>
    );
  },
);

export default ForageHerdAnalysis;
