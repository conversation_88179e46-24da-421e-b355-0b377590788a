// modules
import React, { Suspense } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { useSelector } from 'react-redux';

// constants
import { TOOL_TYPES } from '../../../../../constants/AppConstants';
import customColor from '../../../../../constants/theme/variables/customColor';

// components
import VisitReportIndex from '../../VisitReportIndex';
import CudChewingPageComponent from '../../Tools/RumenHealthCudChewing';
import BCSPageComponent from '../../Tools/BodyConditionScore';
import LocomotionScorePageComponent from '../../Tools/LocomotionScore';
import MilkSoldEvaluationPageComponent from '../../Tools/MilkSoldEvaluation';
import ForageInventoriesPageComponent from '../../Tools/ForageInventories';
import RumenHealthManureScorePageComponent from '../../Tools/RumenHealthManureScore';
import RumenHealthManureScreening from '../../Tools/RumenHealthManureScreening';
import RumenFill from '../../Tools/RumenFill';
import HeatStress from '../../Tools/HeatStress';
import ForagePenState from '../../Tools/ForagePennState';
import TMRPenState from '../../Tools/TMRPenState';
import MetabolicIncidence from '../../Tools/MetabolicIncidence';
import PenTimeBudget from '../../Tools/PenTimeBudget';
import RoboticMilk from '../../Tools/RoboticMilk';
import ForageAuditPageComponent from '../../Tools/ٖForageAudit';
import ProfitabilityAnalysisReport from '../../Tools/ProfitabilityAnalysis';
import CalfHeiferScorecard from '../../Tools/CalfHeiferScorecard';
import ReturnOverFeedPageComponent from '../../Tools/ROF';
import VisitReportComments from '../../VisitReportComments';
import VisitReportGeneralNotes from '../../VisitReportGeneralNotes';

const PdfToolsBodyListing = () => {
  const addedTools = useSelector(state => state.visitReport.addedTools);

  if (addedTools?.length > 0) {
    return (
      <Suspense
        fallback={
          <View style={{ height: 200, width: 200, borderWidth: 1 }}>
            <ActivityIndicator size="large" color={customColor.primaryMain} />
          </View>
        }>
        <VisitReportIndex />
        {addedTools?.map((tool, index) => {
          switch (tool?.basicInfo?.toolId) {
            case TOOL_TYPES.RUMEN_HEALTH:
              return <CudChewingPageComponent key={index} tool={tool} />;

            case TOOL_TYPES.BODY_CONDITION:
              return <BCSPageComponent key={index} tool={tool} />;

            case TOOL_TYPES.LOCOMOTION_SCORE:
              return <LocomotionScorePageComponent key={index} tool={tool} />;

            case TOOL_TYPES.MILK_SOLD_EVALUATION:
              return (
                <MilkSoldEvaluationPageComponent key={index} tool={tool} />
              );

            case TOOL_TYPES.PILE_AND_BUNKER:
              return <ForageInventoriesPageComponent key={index} tool={tool} />;

            case TOOL_TYPES.RUMEN_HEALTH_MANURE_SCORE:
              return (
                <RumenHealthManureScorePageComponent key={index} tool={tool} />
              );

            case TOOL_TYPES.MANURE_SCREENER:
              return <RumenHealthManureScreening key={index} tool={tool} />;

            case TOOL_TYPES.RUMEN_FILL:
              return <RumenFill key={index} tool={tool} />;

            case TOOL_TYPES.HEAT_STRESS:
              return <HeatStress key={index} tool={tool} />;

            case TOOL_TYPES.FORAGE_PENN_STATE:
              return <ForagePenState key={index} tool={tool} />;

            case TOOL_TYPES.TMR_PARTICLE_SCORE:
              return <TMRPenState key={index} tool={tool} />;

            case TOOL_TYPES.METABOLIC_INCIDENCE:
              return <MetabolicIncidence key={index} tool={tool} />;

            case TOOL_TYPES.PEN_TIME_BUDGET_TOOL:
              return <PenTimeBudget key={index} tool={tool} />;

            case TOOL_TYPES.ROBOTIC_MILK_EVALUATION:
              return <RoboticMilk key={index} tool={tool} />;

            case TOOL_TYPES.FORAGE_AUDIT_SCORECARD:
              return <ForageAuditPageComponent key={index} tool={tool} />;

            case TOOL_TYPES.PROFITABILITY_ANALYSIS:
              return <ProfitabilityAnalysisReport key={index} tool={tool} />;

            case TOOL_TYPES.CALF_HEIFER_SCORECARD:
              return <CalfHeiferScorecard key={index} tool={tool} />;

            case TOOL_TYPES.ROF:
              return <ReturnOverFeedPageComponent key={index} tool={tool} />;

            default:
              return <></>;
          }
        })}
        <VisitReportGeneralNotes />
        <VisitReportComments />
      </Suspense>
    );
  }

  return <></>;
};

export default PdfToolsBodyListing;
