import { Platform, StyleSheet } from 'react-native';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default StyleSheet.create({
  scrollViewStyles: {
    paddingVertical: normalize(20),
  },
  scrollViewContainer: {
    flexGrow: 1,
  },
  mainContainer: {
    paddingHorizontal: normalize(20),
    flexDirection: 'row',
  },
  bodyContainer: {
    width: normalize(1000),
  },
  graphContainer: {
    marginVertical: normalize(20),
  },
  graphTitleContainer: {
    alignItems: 'center',
    marginBottom: normalize(15),
  },
  graphTitle: {
    color: colors.grey1,
    fontSize: normalize(15),
    fontFamily: customFont.HelveticaNeueMedium,
    fontWeight: '700',
  },
  graphWidth: normalize(1000),
  graphHeight: normalize(450),
  domainPadding: { x: [30, 30] },
  yAxisLabel: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(11),
    lineHeight: normalize(13),
    fill: colors.alphabetIndex,
    padding: normalize(40),
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: normalize(200),
    backgroundColor: colors.lightGray,
    borderRadius: normalize(8),
    marginVertical: normalize(20),
  },
  noDataText: {
    fontSize: normalize(16),
    color: colors.darkGray,
    fontFamily: customFont.HelveticaNeueRegular,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginTop: normalize(25),
    marginLeft: normalize(50),
    flexWrap: 'wrap',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: normalize(25),
    marginVertical: normalize(5),
  },
  legendCircle: {
    width: normalize(12),
    height: normalize(12),
    borderRadius: normalize(2),
    marginRight: normalize(8),
  },
  legendText: {
    fontSize: normalize(13),
    color: colors.black,
    fontFamily: customFont.HelveticaNeueRegular,
  },
  infoColumn: {
    flexDirection: 'column',
  },
  penAnalysisTitle: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontSize: normalize(14),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    letterSpacing: normalize(0.15),
    fontWeight: '500',
  },
  extraHeight: Platform.select({
    ios: normalize(200),
    android: normalize(200),
  }),
  axisStyles: {
    stroke: colors.grey14,
    strokeWidth: 1,
    strokeDasharray: '3, 3',
  },
  horizontalLabels: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontSize: normalize(11),
    lineHeight: normalize(13),
    letterSpacing: 0.15,
    fill: colors.alphabetIndex,
    padding: normalize(16),
  },
  verticalLabels: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontSize: normalize(11),
    lineHeight: normalize(13),
    letterSpacing: normalize(0.15),
    fill: colors.alphabetIndex,
    padding: normalize(8),
  },
  axisLabelStyles: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(11),
    lineHeight: normalize(13),
    fill: colors.alphabetIndex,
    padding: normalize(30),
  },
  barLabels: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(10),
    lineHeight: normalize(12),
    letterSpacing: 0.5,
    fill: colors.alphabetIndex,
  },
});
