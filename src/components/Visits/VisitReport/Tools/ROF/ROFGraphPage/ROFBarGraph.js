// modules
import React from 'react';
import { View } from 'react-native';
import {
  Victory<PERSON>hart,
  VictoryGroup,
  VictoryAxis,
  VictoryBar,
} from 'victory-native';

// styles
import styles from './styles';

import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const ROFBarGraph = ({
  data,
  labels,
  width,
  height,
  barWidth,
  barOffset,
  verticalTickCount,
  verticalAxisLabel,
  customXAxisStyle,
  customYAxisStyle,
  customYAxisLabelStyle,
  customHorizontalLabelStyle,
  customVerticalLabelStyle,
  customBarLabelStyle,
  xAxisFormatter,
  yAxisFormatter,
  valuesFormatter,
  xDomainPadding,
  domainPadding,
}) => {
  const renderVerticalAxis = graphData => {
    if (graphData?.length > 0) {
      return (
        <VictoryAxis
          dependentAxis
          label={verticalAxisLabel}
          style={{
            axis: { ...styles.axisStyles, ...customYAxisStyle },
            axisLabel: { ...styles.axisLabelStyles, ...customYAxisLabelStyle },
            tickLabels: {
              ...styles.verticalLabels,
              ...customVerticalLabelStyle,
            },
          }}
          tickCount={verticalTickCount || 10}
          tickFormat={
            yAxisFormatter
              ? t =>
                  convertInputNumbersToRegionalBasis(yAxisFormatter(t), 1, true)
              : t => convertInputNumbersToRegionalBasis(t, 1, true)
          }
        />
      );
    }
  };

  const renderHorizontalAxis = data => {
    if (data?.length > 0) {
      return (
        <VictoryAxis
          orientation="bottom"
          offsetY={50}
          style={{
            axis: {
              ...styles.axisStyles,
              ...customXAxisStyle,
              strokeWidth: 0,
            },
            tickLabels: {
              ...styles.horizontalLabels,
              ...customHorizontalLabelStyle,
            },
          }}
          domainPadding={xDomainPadding ? xDomainPadding : null}
          tickFormat={xAxisFormatter ? t => xAxisFormatter(t) : t => t}
        />
      );
    }
  };

  const renderHorizontalZeroAxis = () => {
    return (
      <VictoryAxis
        tickFormat={() => ''}
        axisValue={0}
        style={{
          axis: { ...styles.axisStyles, ...customXAxisStyle },
          tickLabels: {
            ...styles.horizontalLabels,
            ...customHorizontalLabelStyle,
          },
        }}
      />
    );
  };

  return (
    <View style={styles.container}>
      <VictoryChart
        height={height && height}
        width={width && width}
        domainPadding={domainPadding ? domainPadding : { x: [20, 25] }}>
        {renderHorizontalAxis(labels)}
        {renderVerticalAxis(data)}
        {renderHorizontalZeroAxis()}
        <VictoryGroup offset={barOffset || 22}>
          {data?.length > 0
            ? data.map((item, index) => (
                <VictoryBar
                  data={item.dataPoints}
                  key={index}
                  style={{
                    data: { fill: item.barColor },
                    labels: { ...styles.barLabels, ...customBarLabelStyle },
                  }}
                  barWidth={barWidth || 12}
                  labels={
                    valuesFormatter
                      ? ({ datum }) =>
                          convertInputNumbersToRegionalBasis(
                            valuesFormatter(datum.y),
                            1,
                            true,
                          )
                      : ({ datum }) =>
                          convertInputNumbersToRegionalBasis(datum.y, 1, true)
                  }
                />
              ))
            : null}
        </VictoryGroup>
      </VictoryChart>
    </View>
  );
};

export default ROFBarGraph;
