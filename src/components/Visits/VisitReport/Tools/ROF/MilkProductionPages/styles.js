import { StyleSheet } from 'react-native';
import { normalize } from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default StyleSheet.create({
  scrollViewStyles: {
    paddingVertical: normalize(20),
  },
  scrollViewContainer: {
    flexGrow: 1,
  },
  mainContainer: {
    paddingHorizontal: normalize(20),
    flexDirection: 'row',
  },
  bodyContainer: {
    width: normalize(1000),
  },
  spacer: {
    width: normalize(20),
    height: normalize(20),
  },
});
