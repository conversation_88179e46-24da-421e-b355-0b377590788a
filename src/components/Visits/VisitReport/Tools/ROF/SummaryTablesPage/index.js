// modules
import React, { useEffect, useRef } from 'react';
import { View, ScrollView } from 'react-native';
import { Table } from 'react-native-reanimated-table';
import { useDispatch } from 'react-redux';

// styles
import styles from './styles';

// components
import Too<PERSON>Header from '../../../Components/ToolHeader';
import SummaryTablesAnalysis from './SummaryTablesAnalysis';
import ToolFooter from '../../../Components/ToolFooter';

// constants
import {
  TOOL_ANALYSIS_TYPES,
  TOOL_TYPES,
} from '../../../../../../constants/AppConstants';
import { ROF_FORM_TYPES } from '../../../../../../constants/toolsConstants/ROFConstants';

// actions
import { addNewPagePdfPageReference } from '../../../../../../store/actions/visitReport';

// localization
import i18n from '../../../../../../localization/i18n';

const SummaryTablesPage = ({
  tool,
  formType,
  summaryTablesData,
  startingPageNumber = 3,
}) => {
  const pageRef = useRef([]);
  const dispatch = useDispatch();

  useEffect(() => {
    setTimeout(() => {
      const pageInfo = {
        pageRef: pageRef,
        toolType: TOOL_TYPES.ROF,
        analysisType: TOOL_ANALYSIS_TYPES.HERD_ANALYSIS,
        pageNumber: startingPageNumber,
      };

      dispatch(addNewPagePdfPageReference(pageInfo));
    }, 100);
  }, [startingPageNumber]);

  const summaryTables = summaryTablesData || {};

  return (
    <ScrollView
      horizontal
      style={styles.scrollViewStyles}
      contentContainerStyle={styles.scrollViewContainer}>
      <View ref={viewRef => (pageRef[0] = viewRef)} collapsable={false}>
        <ToolHeader
          basicInfo={tool?.basicInfo}
          analysisType={
            formType === ROF_FORM_TYPES.TMR
              ? i18n.t('tmr')
              : i18n.t('individualCow')
          }
          isNotesAvailable={tool?.notes?.length}
        />

        <View style={styles.mainContainer}>
          <View style={styles.bodyContainer}>
            <Table>
              <SummaryTablesAnalysis summaryTablesData={summaryTables} />
            </Table>
          </View>
        </View>

        <ToolFooter isNotesAvailable={false} />
      </View>
    </ScrollView>
  );
};

export default SummaryTablesPage;
