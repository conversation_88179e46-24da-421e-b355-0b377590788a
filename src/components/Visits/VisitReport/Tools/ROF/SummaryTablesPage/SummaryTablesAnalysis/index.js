import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

import styles from './styles';

import TableRowWrapper from '../../../../Components/TableRowWrapper';
import TableRowHeader from '../../../../Components/TableRowHeader';

import i18n from '../../../../../../../localization/i18n';

import { ROF_FIELDS } from '../../../../../../../constants/FormConstants';

import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../../helpers/appSettingsHelper';

const renderSection = (section = [], headerLabel) => {
  if (!section || section.length === 0) return null;

  const unitOfMeasure = useSelector(
    state => state.visitReport.visitDetails?.unitOfMeasure,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visitReport.visitDetails?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  return (
    <View style={styles.tableContainer}>
      <TableRowHeader
        headerLabel={i18n.t(headerLabel)}
        numberOfLines={1}
        textStyles={styles.headerTextStyle}
        rowStyles={styles.headerRowStyle}
        cellWidth={'100%'}
      />
      {section.map(([label, value], index) => (
        <View key={index}>
          <TableRowWrapper
            customLabel={i18n
              .t(label)
              .replaceAll('$', currencySymbol)
              .replaceAll('kg', weightUnit)}
            rowData={[convertInputNumbersToRegionalBasis(value, 2, true)]}
            customCellStyles={styles.flexOne}
            customRowStyles={{ flex: 0.5 }}
            isRowEven={index % 2 !== 0}
            numberOfLines={2}
          />
        </View>
      ))}
    </View>
  );
};

const SummaryTablesAnalysis = ({ summaryTablesData = {} }) => {
  const {
    feedCostsSection = [],
    revenueSection = [],
    currentReturnSection = [],
    previousReturnSection = [],
  } = summaryTablesData;

  return (
    <View>
      <View style={styles.topRow}>
        <View style={styles.flexOne}>
          {renderSection(feedCostsSection, ROF_FIELDS.FEED_COSTS)}
        </View>

        <View style={styles.spacer} />

        <View style={styles.flexOne}>
          {renderSection(revenueSection, ROF_FIELDS.REVENUE)}
        </View>
      </View>

      <View style={styles.verticalSpacer} />

      <View style={styles.bottomRow}>
        <View style={styles.flexOne}>
          {renderSection(
            currentReturnSection,
            ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS,
          )}
        </View>

        <View style={styles.spacer} />

        <View style={styles.flexOne}>
          {renderSection(
            previousReturnSection,
            ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS,
          )}
        </View>
      </View>
    </View>
  );
};

export default SummaryTablesAnalysis;
