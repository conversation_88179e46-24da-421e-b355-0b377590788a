import { StyleSheet } from 'react-native';
import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default StyleSheet.create({
  topRow: {
    flexDirection: 'row',
  },
  bottomRow: {
    flexDirection: 'row',
  },
  spacer: {
    width: normalize(20),
  },
  verticalSpacer: {
    height: normalize(20),
  },
  flexOne: {
    flex: 1,
  },
  tableContainer: {
    marginBottom: normalize(10),
  },
  headerTextStyle: {
    color: colors.accordionBorder,
    fontSize: normalize(14),
    fontWeight: '500',
    fontFamily: customFont.HelveticaNeueRegular,
  },
  headerRowStyle: {
    backgroundColor: 'red',
    height: normalize(40),
  },
});
