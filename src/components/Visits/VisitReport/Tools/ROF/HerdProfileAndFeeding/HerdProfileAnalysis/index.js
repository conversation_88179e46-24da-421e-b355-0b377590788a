import React, { useRef } from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

import TableRowHeader from '../../../../Components/TableRowHeader';
import TableRowWrapper from '../../../../Components/TableRowWrapper';

import styles from './styles';

import i18n from '../../../../../../../localization/i18n';

import { ROF_FIELDS } from '../../../../../../../constants/FormConstants';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../../helpers/appSettingsHelper';

const HerdProfileAnalysis = ({ tool = {}, herdAnalysis = [] }) => {
  const pageRefs = useRef([]);

  const unitOfMeasure = useSelector(
    state => state.visitReport.visitDetails?.unitOfMeasure,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visitReport.visitDetails?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  // Defensive: if no data, show empty arrays
  const data = herdAnalysis?.tableData || [];

  // Split data into two halves
  const half = Math.ceil(data.length / 2);
  const firstHalf = data.slice(0, half);
  const secondHalf = data.slice(half, data.length);

  return (
    <View ref={viewRef => (pageRefs[0] = viewRef)} collapsable={false}>
      <TableRowHeader headerLabel={i18n.t(ROF_FIELDS.HERD_PROFILE)} />

      <View style={styles.section}>
        <View style={styles.flexOne}>
          {firstHalf.map(([title, value], index) => (
            <TableRowWrapper
              key={index}
              customLabel={i18n
                .t(title)
                .replaceAll('$', currencySymbol)
                .replaceAll('kg', weightUnit)}
              customCellStyles={styles.flexOne}
              customRowStyles={{
                flex: 0.5,
              }}
              rowData={[value]}
              isRowEven={index % 2 === 0 ? false : true}
              numberOfLines={2}
            />
          ))}
        </View>

        <View style={styles.spacer} />

        <View style={styles.flexOne}>
          {secondHalf.map(([title, value], index) => (
            <TableRowWrapper
              key={index}
              customCellStyles={styles.flexOne}
              customRowStyles={{
                flex: 0.5,
              }}
              customLabel={i18n
                .t(title)
                .replaceAll('$', currencySymbol)
                .replaceAll('kg', weightUnit)}
              rowData={[value]}
              isRowEven={index % 2 === 0 ? false : true}
              numberOfLines={2}
            />
          ))}
        </View>
      </View>
    </View>
  );
};

export default HerdProfileAnalysis;
