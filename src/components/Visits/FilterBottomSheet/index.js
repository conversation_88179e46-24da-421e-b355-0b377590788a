import React from 'react';
import Modal from 'react-native-modal';
import { View, Text, SafeAreaView, TouchableOpacity } from 'react-native';
//styles
import styles from './styles';
// translation
import i18n from '../../../localization/i18n';

import {
  VISIT_REPORT_ICON,
  DELETE_VISIT_ICON,
} from '../../../constants/AssetSVGConstants';
import { normalize } from '../../../constants/theme/variables/customFont';

const VisitFilterBottomSheet = props => {
  const {
    isVisible,
    setIsVisible,
    modalTitle,
    onDeleteVisitClick,
    shouldShowDeleteVisit,
    shouldDisableVisitReport,
    onVisitReportDownloadClick,
  } = props;
  const handleCancel = () => {
    setIsVisible(false);
  };

  return (
    <>
      <Modal
        testID={'modal'}
        isVisible={isVisible}
        onSwipeComplete={handleCancel}
        propagateSwipe={true}
        animationInTiming={200}
        animationOutTiming={200}
        onBackdropPress={handleCancel}
        style={styles.modalStyle}>
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.topDraggerBarParent}>
            <View style={styles.topDraggerBar} />
          </View>
          <View style={styles.bottomSheetContentParent}>
            <Text style={styles.modalTitle}>{modalTitle}</Text>
          </View>

          <View style={styles.listContainer}>
            <TouchableOpacity
              onPress={onVisitReportDownloadClick}
              disabled={shouldDisableVisitReport}>
              <View style={styles.listItemContainer}>
                <View style={styles.iconBackgroundView}>
                  <VISIT_REPORT_ICON
                    width={normalize(13)}
                    height={normalize(13)}
                  />
                </View>
                <View style={styles.column}>
                  <Text
                    style={[
                      styles.toolReportText,
                      shouldDisableVisitReport
                        ? styles.disabledTextColor
                        : null,
                    ]}>
                    {i18n.t('visitReport')}
                  </Text>
                  {shouldDisableVisitReport && (
                    <Text style={styles.visitReportErrorText}>
                      {i18n.t('unsyncedVisitReportGenerationError')}
                    </Text>
                  )}
                </View>
              </View>
            </TouchableOpacity>

            {shouldShowDeleteVisit && (
              <TouchableOpacity onPress={onDeleteVisitClick}>
                <View style={styles.listItemContainer}>
                  <View style={styles.iconBackgroundView}>
                    <DELETE_VISIT_ICON
                      width={normalize(13)}
                      height={normalize(13)}
                    />
                  </View>
                  <Text style={styles.deleteText}>{i18n.t('deleteVisit')}</Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </SafeAreaView>
      </Modal>
    </>
  );
};

export default VisitFilterBottomSheet;
