import colors from '../../../../constants/theme/variables/customColor';
import fonts, {
  normalize,
} from '../../../../constants/theme/variables/customFont';

export default {
  flexOne: {
    flex: 1,
    marginTop: normalize(1),
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  container: {
    marginBottom: normalize(10),
    flex: 1,
    backgroundColor: colors.white,
  },
  searchFilterContainer: {
    flexDirection: 'row',
    marginVertical: normalize(16),
    marginHorizontal: normalize(20),
  },
  flex1: {
    flex: 1,
  },
  categoriesRow: {
    flexDirection: 'row',
    marginBottom: normalize(14),
    marginHorizontal: normalize(20),
  },
  categoryContainer: {
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: normalize(1),
    borderColor: colors.grey4,
    borderRadius: normalize(4),
    paddingHorizontal: normalize(14),
    paddingVertical: normalize(10),
    marginRight: normalize(12),
  },
  categoryText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(13),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  selectedCategoryContainer: {
    backgroundColor: colors.todayBackGroundColor,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: normalize(1),
    borderColor: colors.primaryMain,
    borderRadius: normalize(4),
    paddingHorizontal: normalize(14),
    paddingVertical: normalize(10),
    marginRight: normalize(12),
  },
  selectedCategoryText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(13),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.primaryMain,
  },
  disabledCategoryContainer: {
    // backgroundColor: colors.disablePrimaryButtonBackgroundColor,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: normalize(1),
    borderColor: colors.grey4,
    borderRadius: normalize(4),
    paddingHorizontal: normalize(14),
    paddingVertical: normalize(10),
    marginRight: normalize(12),
  },
  disabledCategoryText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(13),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.disablePrimaryButtonTextColor,
  },
  toolsListContainer: {
    marginHorizontal: normalize(20),
    justifyContent: 'space-between',
  },
  bottomButtonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 60,
    padding: 10,
    marginBottom: 30,
    marginHorizontal: 16,
  },
  cardWidth: { width: normalize(161) },
  sectionHeader: {
    marginHorizontal: normalize(20),
    backgroundColor: colors.primaryMain,
    borderRadius: 2,
    paddingHorizontal: normalize(10),
    paddingVertical: normalize(6),
    marginBottom: normalize(15),
    alignSelf: 'flex-start',
    alignItems: 'center',
  },
  sectionTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(12),
    letterSpacing: 0.18,
    color: colors.white,
  },
  emptyContainerStyles: {
    marginTop: normalize(50),
  },
};
