// modules
import React from 'react';
import { <PERSON>, Text, FlatList, ScrollView } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/core';

// constants
import {
  TOOL_CATEGORIES,
  TOOL_TYPES,
  VISIT_STATUS,
} from '../../../../constants/AppConstants';
import ROUTE_CONSTANTS from '../../../../constants/RouteConstants';
import {
  EMPTY_LIST_ICON,
  NO_ACCOUNT_FOUND_ICON,
  NO_RESULT_FOUND_ICON,
} from '../../../../constants/AssetSVGConstants';

// components
import ToolCard from '../../Tools/common/ToolCard';
import EmptyListComponent from '../../../common/EmptyListComponent';

// actions
import { hideLoader, showLoader } from '../../../../store/actions/view';
import { resetToolSwitchRequest } from '../../../../store/actions/toolSwitching';
import { clearRoboticMilkData } from '../../../../store/actions/tools/roboticMilk';
import { setActiveToolRequest } from '../../../../store/actions/tool';

// styles
import styles from './styles';

// helpers
import { stringIsEmpty } from '../../../../helpers/alphaNumericHelper';
import { groupUsedUnusedTools } from '../../../../helpers/toolHelper';

// localization
import i18n from '../../../../localization/i18n';

const ToolSelectionToolsList = ({
  filteredCountryTools,
  selectedIndex,
  searchTerm,
  selectedCategories,
}) => {
  const { navigate } = useNavigation();
  const dispatch = useDispatch();

  const visit = useSelector(state => state?.visit?.visit);

  const handleToolSelect = tool => {
    dispatch(showLoader());
    //save selected tool in reducer if tool is of health
    if (tool?.toolGroupId !== TOOL_CATEGORIES?.HEALTH) {
      dispatch(resetToolSwitchRequest());
    }
    if (tool?.toolId === TOOL_TYPES.ROBOTIC_MILK_EVALUATION) {
      // TODO: move to tool screen for clearing robotic milk reducer
      dispatch(clearRoboticMilkData());
    }
    dispatch(setActiveToolRequest(tool));
    navigate(ROUTE_CONSTANTS.TOOL);
    dispatch(hideLoader());
  };

  const EmptyCard = () => <View style={styles.cardWidth}></View>;

  const emptyComponent = () => {
    if (
      selectedIndex == 0 &&
      stringIsEmpty(searchTerm) &&
      selectedCategories.length == 0
    ) {
      return (
        <EmptyListComponent
          title={i18n.t('noRecordFound')}
          description={i18n.t('noRecordFoundDescription')}
          image={<NO_RESULT_FOUND_ICON />}
          button={false}
        />
      );
    } else {
      if (selectedIndex == 1) {
        return (
          <EmptyListComponent
            title={i18n.t('noFavoritesFound')}
            description={i18n.t('noFavoritesFoundDescription')}
            image={<EMPTY_LIST_ICON />}
            button={false}
          />
        );
      } else {
        return (
          <EmptyListComponent
            title={i18n.t('noResultShow')}
            description={i18n.t('noResultShowDescription')}
            image={<NO_ACCOUNT_FOUND_ICON />}
            button={false}
          />
        );
      }
    }
  };

  const _renderToolCard = ({ item }) => {
    if (item.name) {
      return <ToolCard item={item} visit={visit} onPress={handleToolSelect} />;
    } else {
      return <EmptyCard />;
    }
  };

  const numColumns = DeviceInfo.isTablet() ? 3 : 2;

  const _renderFlatList = data => (
    <FlatList
      numColumns={numColumns}
      data={data || []}
      renderItem={_renderToolCard}
      showsVerticalScrollIndicator={false}
      columnWrapperStyle={styles.toolsListContainer}
      keyExtractor={item => 'tool' + item.name}
      ListEmptyComponent={emptyComponent}
      scrollEnable={false}
    />
  );

  switch (visit?.visitStatus) {
    case VISIT_STATUS.IN_PROGRESS:
      return _renderFlatList(filteredCountryTools);

    case VISIT_STATUS.PUBLISHED: {
      const { usedToolsList, unusedToolsList } = groupUsedUnusedTools(
        visit,
        filteredCountryTools,
      );

      return (
        <ScrollView>
          {usedToolsList?.length > 0 && (
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{i18n.t('used')}</Text>
            </View>
          )}

          {usedToolsList?.length > 0 && _renderFlatList(usedToolsList)}

          {unusedToolsList?.length > 0 && selectedIndex !== 2 && (
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{i18n.t('unused')}</Text>
            </View>
          )}

          {unusedToolsList?.length > 0 &&
            selectedIndex !== 2 &&
            _renderFlatList(unusedToolsList)}

          {usedToolsList?.length <= 0 && unusedToolsList?.length <= 0 && (
            <View style={styles.emptyContainerStyles}>
              <EmptyListComponent
                title={i18n.t('noResultShow')}
                description={i18n.t('noResultShowDescription')}
                image={<NO_ACCOUNT_FOUND_ICON />}
                button={false}
              />
            </View>
          )}
        </ScrollView>
      );
    }

    default:
      return <></>;
  }
};

export default ToolSelectionToolsList;
