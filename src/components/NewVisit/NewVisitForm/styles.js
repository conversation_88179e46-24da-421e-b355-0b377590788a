import { normalize } from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import DeviceInfo from 'react-native-device-info';

export default {
  formContainer: {
    marginBottom: normalize(80),
  },
  formInputView: {
    marginBottom: normalize(22),
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: colors.grey1,
    marginBottom: normalize(10),
  },
  customInputStyle: {
    backgroundColor: colors.white,
  },
  flexOne: {
    flex: 1,
    backgroundColor: colors.searchBoxBorder,
  },
  newVisitFormView: {
    flex: 1,
    width: DeviceInfo.isTablet() ? '95%' : '90%',
    alignSelf: 'center',
    marginTop: normalize(30),
  },
  placeholderColor: colors.alphabetIndex,
};
