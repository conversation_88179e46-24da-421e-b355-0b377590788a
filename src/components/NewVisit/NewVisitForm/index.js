// modules
import React, { useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import { useFormikContext } from 'formik';
import { useDispatch } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../../localization/i18n';

// reusable components
import FormInput from '../../common/FormInput';
import CustomBottomSheet from '../../common/CustomBottomSheet';

// constants
import {
  INPUT_TYPE,
  BOTTOM_SHEET_TYPE,
  VISIT_FIELDS,
} from '../../../constants/FormConstants';
import { GENERAL_INPUT_MAX_LIMIT } from '../../../constants/AppConstants';

// actions
import { getAccountSitesRequest } from '../../../store/actions/visit';

const NewVisitForm = ({
  getVisitName,
  getPenList,
  sitesList,
  customersProspectsList,
}) => {
  const dispatch = useDispatch();

  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setFieldValue,
    setFieldTouched,
  } = useFormikContext();

  useEffect(() => {
    if (sitesList?.length === 1) {
      const firstSite = sitesList[0];
      setFieldValue(VISIT_FIELDS.SITE, firstSite.id);
      getPenList(firstSite);
      setTimeout(() => setFieldTouched(VISIT_FIELDS.SITE, true));
      autofillVisitName(values[VISIT_FIELDS.CUSTOMER_PROSPECT], firstSite?.id);
    }
  }, [sitesList]);

  const autofillVisitName = (accountId, siteId) => {
    const visitName = getVisitName(accountId, siteId, sitesList);
    if (visitName) {
      setFieldValue(VISIT_FIELDS.VISIT_NAME, visitName);
    }
  };

  return (
    <View style={styles.flexOne}>
      <ScrollView
        keyboardDismissMode="none"
        keyboardShouldPersistTaps="handled">
        <View style={styles.newVisitFormView}>
          <View style={styles.formContainer}>
            <View style={styles.formInputView}>
              <CustomBottomSheet
                type={BOTTOM_SHEET_TYPE.CUSTOMER_PROSPECT}
                required
                label={i18n.t('customerProspect')}
                selectLabel={i18n.t('selectCustomerProspect')}
                placeholder={i18n.t('selectOne')}
                searchPlaceHolder={i18n.t('search')}
                data={customersProspectsList}
                value={values[VISIT_FIELDS.CUSTOMER_PROSPECT]}
                error={
                  touched[VISIT_FIELDS.CUSTOMER_PROSPECT] &&
                  errors[VISIT_FIELDS.CUSTOMER_PROSPECT]
                }
                onChange={item => {
                  setFieldValue(VISIT_FIELDS.CUSTOMER_PROSPECT, item.id);
                  setFieldValue(VISIT_FIELDS.SITE, '');
                  setFieldValue(VISIT_FIELDS.VISIT_NAME, '');
                  setTimeout(() =>
                    setFieldTouched(VISIT_FIELDS.CUSTOMER_PROSPECT, true),
                  );
                  dispatch(
                    getAccountSitesRequest({
                      accountId: item.sv_id,
                      localAccountId: item.id,
                    }),
                  );
                }}
                customLabelStyle={styles.customFieldLabel}
                customInputStyle={styles.customInputStyle}
              />
            </View>

            <View style={styles.formInputView}>
              <CustomBottomSheet
                type={BOTTOM_SHEET_TYPE.IMAGE_LIST_ITEM}
                required
                label={i18n.t('site')}
                selectLabel={i18n.t('selectSite')}
                placeholder={i18n.t('selectOne')}
                searchPlaceHolder={i18n.t('searchSite')}
                infoText={i18n.t('siteSelectorInfoText')}
                data={sitesList}
                value={values[VISIT_FIELDS.SITE]}
                error={touched[VISIT_FIELDS.SITE] && errors[VISIT_FIELDS.SITE]}
                onChange={item => {
                  setFieldValue(VISIT_FIELDS.SITE, item.id);
                  setTimeout(() => setFieldTouched(VISIT_FIELDS.SITE, true));
                  autofillVisitName(
                    values[VISIT_FIELDS.CUSTOMER_PROSPECT],
                    item.id,
                  );
                  getPenList(item);
                }}
                customLabelStyle={styles.customFieldLabel}
                customInputStyle={styles.customInputStyle}
              />
            </View>

            <View style={styles.formInputView}>
              <FormInput
                label={i18n.t('visitName')}
                type={INPUT_TYPE.TEXT}
                required
                placeholder={i18n.t('visitNamePlaceholder')}
                placeholderColor={styles.placeholderColor}
                maxLength={GENERAL_INPUT_MAX_LIMIT}
                value={values[VISIT_FIELDS.VISIT_NAME]}
                error={
                  touched[VISIT_FIELDS.VISIT_NAME] &&
                  errors[VISIT_FIELDS.VISIT_NAME]
                }
                onChange={handleChange(VISIT_FIELDS.VISIT_NAME)}
                onBlur={handleBlur(VISIT_FIELDS.VISIT_NAME)}
                customLabelStyle={styles.customFieldLabel}
                customInputContainer={styles.customInputStyle}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default NewVisitForm;
