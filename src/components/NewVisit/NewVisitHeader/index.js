// modules
import React from 'react';
import { useFormikContext } from 'formik';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// components
import TopBar from '../../TopBar';
import { showAlertMsg } from '../../common/Alerts';

// localization
import i18n from '../../../localization/i18n';

// styles
import styles from './styles';

// actions
import {
  resetCreateVisitRequest,
  resetVisitDataRequest,
} from '../../../store/actions/visit';

const VisitFormHeader = () => {
  const dispatch = useDispatch();
  const { goBack } = useNavigation();
  const { dirty } = useFormikContext();

  const handleGoBack = () => {
    dispatch(resetVisitDataRequest());
    dispatch(resetCreateVisitRequest());
    goBack();
  };

  const onCrossClick = () => {
    if (dirty) {
      showAlertMsg('', i18n.t('dataLossMessage'), [
        {
          text: i18n.t('no'),
          onPress: () => {},
        },
        {
          text: i18n.t('yes'),
          onPress: handleGoBack,
        },
      ]);
    } else {
      handleGoBack();
    }
  };

  return (
    <TopBar
      crossIcon
      showBottomBorder
      crossClick={onCrossClick}
      title={i18n.t('newVisit')}
      titleStyles={styles.titleText}
      customHeaderStyle={styles.customHeaderStyle}
    />
  );
};

export default VisitFormHeader;
