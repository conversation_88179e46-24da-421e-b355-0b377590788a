import DeviceInfo from 'react-native-device-info';
import colors from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';

export default {
  customHeaderStyle: {
    height: normalize(117),
  },
  titleText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(24),
    color: colors.grey1,
    marginLeft: DeviceInfo.isTablet() ? normalize(0) : normalize(-8),
  },
};
