import colors from '../../../constants/theme/variables/customColor';
import { normalize } from '../../../constants/theme/variables/customFont';

export default {
  flex1: {
    flex: 1,
  },
  bottomButtonView: {
    position: 'absolute',
    width: '100%',
    paddingHorizontal: normalize(24),
    bottom: normalize(35),
  },
  button: {
    borderWidth: 0,
  },
  errorView: {
    borderRadius: normalize(4),
    backgroundColor: colors.graphHeaderBullet3,
    paddingVertical: normalize(10),
    marginBottom: normalize(20),
    flexDirection: 'row',
  },
  iconView: {
    marginHorizontal: normalize(10),
    justifyContent: 'center',
  },
  iconStyles: {
    width: normalize(30),
    height: normalize(30),
    fill: colors.white,
  },
  textError: {
    color: colors.white,
    fontSize: normalize(12),
    paddingRight: normalize(10),
  },
};
