// modules
import React from 'react';
import { View, Text } from 'react-native';
import { useSelector } from 'react-redux';
import { useFormikContext } from 'formik';

// styles
import styles from './styles';

// components
import FormButton from '../../common/FormButton';

// constants
import { FILL_INFO_ICON } from '../../../constants/AssetSVGConstants';
import { BUTTON_TYPE } from '../../../constants/FormConstants';

// localization
import i18n from '../../../localization/i18n';

// helpers
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';

const NewVisitFormFooter = () => {
  const { handleSubmit, isValid, isSubmitting, values } = useFormikContext();

  const pensList = useSelector(state => state.tool.pensList);
  const pensLoading = useSelector(state => state.tool.pensLoading);

  const showCreateVisitError =
    !pensLoading && pensList?.length === 0 && !stringIsEmpty(values?.site);

  return (
    <View style={styles.bottomButtonView}>
      {showCreateVisitError && (
        <View style={styles.errorView}>
          <View style={styles.iconView}>
            <FILL_INFO_ICON {...styles.iconStyles} />
          </View>
          <View style={styles.flex1}>
            <Text style={styles.textError}>{i18n.t('createVisitError')}</Text>
          </View>
        </View>
      )}

      <FormButton
        type={BUTTON_TYPE.PRIMARY}
        label={i18n.t('continue')}
        onPress={handleSubmit}
        disabled={isSubmitting || !isValid || pensList?.length == 0}
        customButtonStyle={styles.button}
      />
    </View>
  );
};

export default NewVisitFormFooter;
