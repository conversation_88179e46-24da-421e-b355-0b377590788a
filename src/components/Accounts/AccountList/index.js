// modules
import React, { useRef, useEffect } from 'react';
import {
  View,
  RefreshControl,
  FlatList,
  ActivityIndicator,
} from 'react-native';

import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';
import { getBase64ImageConverted } from '../../../helpers/fileSystemHelper';
import { getMediaAbsolutePath } from '../../../helpers/genericHelper';

import i18n from '../../../localization/i18n';

// components
import AccountListItem from '../AccountListItem';
import EmptyListComponent from '../../common/EmptyListComponent';

// styles
import styles from './styles';

import {
  NO_ACCOUNT_FOUND_ICON,
  NO_RESULT_FOUND_ICON,
} from '../../../constants/AssetSVGConstants';

const AccountList = ({
  isLoading,
  syncLoader,
  searchTerm,
  data,
  onRefresh,
  onPress,
  emptyListTitle,
  emptyListDescription,
  onEndReached,
  loader,
  tabChange,
  accountFavourites,
}) => {
  const flatListRef = useRef();

  const emptyComponent = () => {
    if (stringIsEmpty(searchTerm) && !isLoading && !syncLoader && !loader) {
      return (
        <EmptyListComponent
          title={emptyListTitle}
          description={emptyListDescription}
          image={<NO_RESULT_FOUND_ICON {...styles.noProspectsFound} />}
          button={false}
        />
      );
    } else if (!isLoading && !syncLoader && !loader) {
      return (
        <EmptyListComponent
          title={i18n.t('noResultShow')}
          description={i18n.t('noResultShowDescription')}
          image={<NO_ACCOUNT_FOUND_ICON {...styles.noProspectsFound} />}
          button={false}
        />
      );
    } else {
      scrollToTop();
      return null;
    }
  };

  const scrollToTop = () => {
    if (flatListRef && flatListRef.current?.scrollToOffset) {
      flatListRef?.current?.scrollToOffset({ animated: false, y: 0 });
    }
  };

  useEffect(() => {
    scrollToTop();
  }, [tabChange]);

  const ListFooter = () => {
    if (isLoading) {
      return (
        <View style={styles.footerLoaderView}>
          <ActivityIndicator />
        </View>
      );
    } else {
      return null;
    }
  };

  const renderItem = ({ item, index }) => {
    let isFavourite = false;
    isFavourite =
      accountFavourites != '' &&
      (accountFavourites?.includes(item?.id) ||
        accountFavourites?.includes(item?.localId));

    return (
      <View>
        <AccountListItem
          data={data}
          isFavourite={isFavourite}
          index={index}
          localId={item.localId}
          businessName={item.value}
          imageURL={
            (item.imageToUploadBase64 &&
              getBase64ImageConverted(item.imageToUploadBase64)) ||
            getMediaAbsolutePath(item.localMediaUrl) ||
            item.imageURL
          }
          siteCount={item.siteCount}
          isPinned={isFavourite}
          lastVisited={item.dateOfLastVisit}
          onPress={() => onPress(item)}
          accountType={item.accountType}
        />
      </View>
    );
  };

  return (
    <>
      {data?.length > 0 ? (
        <View style={data?.length > 0 ? styles.accountListView : {}}>
          <FlatList
            data={data}
            ref={flatListRef}
            showsVerticalScrollIndicator={false}
            renderItem={renderItem}
            // initialNumToRender={10}
            onEndReachedThreshold={0.7}
            onEndReached={onEndReached}
            keyExtractor={item => item.localId}
            ListEmptyComponent={emptyComponent}
            contentContainerStyle={styles.contentFlatListContainer}
            refreshControl={
              <RefreshControl
                refreshing={syncLoader || loader || isLoading}
                onRefresh={onRefresh}
              />
            }
            ListFooterComponent={ListFooter}
          />
        </View>
      ) : (
        emptyComponent()
      )}
    </>
  );
};

export default AccountList;
