import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import { Dimensions } from 'react-native';

const width = Dimensions.get('window').width;

export default {
  indexLetterStyle: {
    fontFamily: fonts.RobotoMedium,
    fontWeight: '400',
    fontSize: normalize(10),
    lineHeight: normalize(18),
    letterSpacing: normalize(1),
    // textTransform: 'uppercase',
    color: colors.alphabetIndex,
  },
  accountListView: { backgroundColor: 'white', borderRadius: 12, padding: 8 },
  sectionHeaderContainer: {
    marginLeft: normalize(20),
    marginTop: normalize(15),
    marginBottom: normalize(10),
  },
  sectionHeaderLabel: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: normalize(0.2),
    color: colors.grey2,
  },
  contentFlatListContainer: {
    flexGrow: 1,
    borderRadius: normalize(12),
    // marginHorizontal: normalize(20),
  },
  footerLoaderView: {
    height: normalize(100),
    alignItems: 'center',
    justifyContent: 'center',
  },
  noProspectsFound: {
    width: width * 0.3,
    height: width * 0.3,
  },
};
