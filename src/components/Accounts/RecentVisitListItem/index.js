// modules
import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';

// components
import VisitStatusTag from '../../common/VisitStatusTag';

// styles
import styles from './styles';
import { normalize } from '../../../constants/theme/variables/customFont';

// language translations
import i18n from '../../../localization/i18n';

// constants
import {
  CALENDAR_ICON,
  MAP_PIN_ICON,
  GEAR_ICON,
  CUSTOMER_ICON,
} from '../../../constants/AssetSVGConstants';
import { DATE_FORMATS, VISIT_STATUS } from '../../../constants/AppConstants';

//helper
import { dateHelper } from '../../../helpers/dateHelper';
import {
  inProgressToolCount,
  VisitStatusLabel,
} from '../../../helpers/visitHelper';

const RecentVisitListItem = props => {
  const {
    visitName,
    siteName,
    visitDate,
    visitStatus,
    onPress,
    customerName,
    prospectName,
    mobileLastUpdatedTime,
    createUser,
  } = props.item;

  const renderVisitDate = () => {
    if (dateHelper.isCurrentYear(mobileLastUpdatedTime)) {
      return dateHelper.getFormattedDate(
        mobileLastUpdatedTime,
        DATE_FORMATS.MMM_DD_H_MM,
      );
    } else {
      return dateHelper.getFormattedDate(
        mobileLastUpdatedTime,
        DATE_FORMATS.MMM_DD_YY_H_MM,
      );
    }
  };

  const createUserTextColor =
    visitStatus === VISIT_STATUS.PUBLISHED
      ? styles.publishedVisitColor
      : styles.inProgressVisitColor;

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={() => props?.onPress(props?.item)}
      style={[styles.container, props?.customContainerStyle]}>
      <VisitStatusTag
        status={VisitStatusLabel(visitDate, visitStatus)}
        customViewStyle={styles.statusTagContainer}
      />
      <Text style={styles.visitName} numberOfLines={1}>
        {visitName}
      </Text>

      <Text style={[styles.creatorName, createUserTextColor]} numberOfLines={1}>
        {i18n.t('createdBy')}: {createUser ? createUser : i18n.t('NA')}
      </Text>

      {props?.shouldShowCustomer && (
        <View style={styles.siteDateRow}>
          <View style={styles.siteContainer}>
            <CUSTOMER_ICON
              width={normalize(16)}
              height={normalize(16)}
              style={styles.mapPinIcon}
            />
            <Text style={styles.siteName} noOfLines={2}>
              {customerName ?? prospectName ?? 'N/A'}
            </Text>
          </View>
        </View>
      )}

      <View
        style={
          props?.shouldShowCustomer ? styles.breadcrumb : styles.siteDateRow
        }>
        <View style={styles.dateContainer}>
          <MAP_PIN_ICON
            width={normalize(16)}
            height={normalize(16)}
            style={styles.mapPinIcon}
          />
          <Text style={styles.siteName} noOfLines={2}>
            {siteName}
          </Text>
        </View>
      </View>

      <View style={styles.breadcrumb}>
        <View style={styles.dateContainer}>
          <CALENDAR_ICON width={normalize(16)} height={normalize(16)} />
          <Text style={styles.visitDate}>
            {mobileLastUpdatedTime
              ? renderVisitDate(mobileLastUpdatedTime)
              : 'N/A'}
          </Text>
        </View>
      </View>

      <View style={styles.breadcrumb}>
        <View style={styles.siteContainer}>
          <GEAR_ICON
            width={normalize(16)}
            height={normalize(16)}
            style={styles.mapPinIcon}
          />
          <Text style={styles.siteName} noOfLines={2}>
            {i18n.t('toolsUsed')}: {inProgressToolCount(props.item)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default RecentVisitListItem;
