import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    // height: normalize(153),
    flexDirection: 'column',
    borderRadius: normalize(8),
    backgroundColor: colors.white,
    borderWidth: normalize(1),
    borderColor: colors.searchBoxBorder,
    marginVertical: normalize(7),
    // marginHorizontal: normalize(10),
    borderTopRightRadius: 13,
    paddingBottom: normalize(15),
  },
  statusTagContainer: {
    position: 'absolute',
    height: normalize(26),
    right: 0,
    top: 0,
  },
  visitName: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginTop: normalize(22),
    marginHorizontal: normalize(16),
  },
  creatorName: {
    marginTop: normalize(5),
    marginHorizontal: normalize(16),
    marginBottom: normalize(10),
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
  },
  publishedVisitColor: {
    color: colors.secondary2,
  },
  inProgressVisitColor: {
    color: colors.inProgressBackgroundYellow,
  },
  siteDateRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginHorizontal: normalize(16),
  },
  siteContainer: {
    width: '100%',
    flexDirection: 'row',
  },
  mapPinIcon: {
    width: normalize(16),
    marginTop: normalize(2),
  },
  siteName: {
    height: '100%',
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginLeft: normalize(11),
    marginRight: normalize(11),
    flexShrink: 1,
  },
  breadcrumb: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: normalize(15),
    marginRight: normalize(5),
    marginTop: normalize(15),
    // marginVertical:normalize(15)
  },
  dateContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  visitDate: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginLeft: normalize(11),
  },
  toolsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.toolsLabelBackground,
    borderRadius: normalize(4),
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingVertical: normalize(8),
    marginTop: normalize(16),
    marginBottom: normalize(19),
    marginHRight: normalize(16),
  },
  toolsText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: 0,
    color: colors.primaryMain,
    // textTransform: 'capitalize',
    marginHorizontal: normalize(8),
  },
};
