import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';

export default {
  modalStyle: {
    margin: 0,
  },
  modalContainer: {
    flex: 0.5,
    width: '100%',
    backgroundColor: colors.white,
    bottom: 0,
    position: 'absolute',
    borderTopRightRadius: normalize(30),
    borderTopLeftRadius: normalize(30),
  },
  topDraggerBarParent: {
    justifyContent: 'center',
    alignSelf: 'center',
    alignItems: 'center',
    height: normalize(30),
  },
  topDraggerBar: {
    height: normalize(6),
    width: normalize(50),
    borderRadius: normalize(3),
    backgroundColor: colors.grey3,
    marginTop: normalize(3),
    marginBottom: normalize(3),
  },
  bottomSheetContentParent: {
    marginVertical: normalize(10),
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: normalize(15),
  },
  modalTitle: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueMedium,
    paddingRight: normalize(5),
  },
  formInputView: {
    marginHorizontal: normalize(20),
    marginTop: normalize(20),
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: colors.grey1,
    // textTransform: 'capitalize',
    marginBottom: normalize(10),
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: normalize(29),
    marginHorizontal: normalize(20),
    marginBottom: normalize(20),
    flex: 1,
  },
  buttonsStyle: {
    flex: 1,
    height: normalize(52),
  },
  spacer: {
    width: normalize(20),
  },
  errorButtonsStyle: {
    backgroundColor: colors.error4,
  },
};
