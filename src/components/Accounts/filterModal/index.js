import React from 'react';
import Modal from 'react-native-modal';
import { View, Text, SafeAreaView } from 'react-native';
//styles
import styles from './styles';
// translation
import i18n from '../../../localization/i18n';

// reusable component
import RangeDateInput from '../../common/RangeDateInput';
import FormButton from '../../common/FormButton';
import { BUTTON_TYPE } from '../../../constants/FormConstants';

// helper
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';

//date helper
import { getFormattedDate } from '../../../helpers/dateHelper';
import { DATE_FORMATS } from '../../../constants/AppConstants';
const FilterModal = props => {
  const {
    isVisible,
    setIsVisible,
    modalTitle,
    value,
    onPress,
    onDateClick,
    onResetPress,
  } = props;
  const handleCancel = () => {
    setIsVisible(false);
  };

  const displayDateRangeValue = () => {
    let dateValue = i18n.t('selectDateRange');
    if (!stringIsEmpty(value?.startDate) && !stringIsEmpty(value?.endDate)) {
      dateValue = `${getFormattedDate(
        value?.startDate,
        DATE_FORMATS.EEE_MMM_d,
      )} - ${getFormattedDate(value?.endDate, DATE_FORMATS.EEE_MMM_d)}`;
    }
    return dateValue;
  };

  return (
    <>
      <Modal
        testID={'modal'}
        isVisible={isVisible}
        onSwipeComplete={handleCancel}
        propagateSwipe={true}
        animationInTiming={200}
        animationOutTiming={200}
        onBackdropPress={handleCancel}
        style={styles.modalStyle}>
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.topDraggerBarParent}>
            <View style={styles.topDraggerBar} />
          </View>
          <View style={styles.bottomSheetContentParent}>
            <Text style={styles.modalTitle}>{modalTitle}</Text>
          </View>
          <View style={styles.formInputView}>
            <RangeDateInput
              label={i18n.t('dateRange')}
              value={displayDateRangeValue()}
              onPress={onDateClick}
              placeholder={i18n.t('inputPlaceholder')}
              customLabelStyle={styles.customFieldLabel}
            />
          </View>

          <View style={styles.optionsContainer}>
            <FormButton
              type={BUTTON_TYPE.SECONDARY}
              label={i18n.t('reset')}
              onPress={onResetPress}
              customButtonStyle={styles.buttonsStyle}
            />
            <View style={styles.spacer} />
            <FormButton
              type={BUTTON_TYPE.PRIMARY}
              label={i18n.t('apply')}
              onPress={onPress}
              customButtonStyle={styles.buttonsStyle}
            />
          </View>
        </SafeAreaView>
      </Modal>
    </>
  );
};

export default FilterModal;
