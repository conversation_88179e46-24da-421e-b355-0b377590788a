import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import customFont from '../../../constants/theme/variables/customFont';
import { ACCOUNT_TYPE } from '../../../constants/AppConstants';

export default {
  container: accountType => ({
    width: '100%',
    paddingVertical: normalize(15),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor:
      accountType === ACCOUNT_TYPE.CUSTOMER
        ? colors.customerColor
        : colors.prospectColor,
    paddingHorizontal: normalize(10),
    // margin: normalize(5),
    borderRadius: normalize(12),
    // borderColor: colors.white,
    // borderWidth: normalize(5),
  }),
  accountTypeView: accountType => ({
    backgroundColor:
      accountType === ACCOUNT_TYPE.CUSTOMER
        ? colors.screenBackgroundColor1
        : colors.screenBackgroundColor2,
    minWidth: normalize(35),
    maxWidth: normalize(175),
    paddingHorizontal: normalize(5),
    paddingVertical: normalize(2),
    borderRadius: normalize(4),
    marginLeft: normalize(5),
  }),
  accountTypeText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    letterSpacing: 0.5,
    color: colors.white,
  },
  customerInfoContainer: {
    marginHorizontal: normalize(8),
    flex: 1,
    flexDirection: 'column',
  },
  customerNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: normalize(5),
  },
  customerName: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(19),
    letterSpacing: 0.5,
    color: colors.grey1,
  },
  pinIconContainer: {
    marginLeft: normalize(8),
  },
  sitesContainer: {
    height: normalize(14),
    flexDirection: 'row',
    marginTop: normalize(7),
    alignItems: 'center',
  },

  lastVisitedContainer: {
    flexDirection: 'row',
    paddingLeft: normalize(5),
    // alignItems: 'flex-end',
  },
  lastVisitedText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '400',
    fontSize: normalize(10),
    lineHeight: normalize(10),
    letterSpacing: normalize(0.2),
    color: colors.grey1,
  },
  lastVisitedDate: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(12),
    lineHeight: normalize(15),
    letterSpacing: 0.2,
    color: colors.alphabetIndex,
    marginTop: normalize(3),
  },
  speratorLine: {
    flex: 1,
    backgroundColor: colors.grey5,
    height: 1,
    marginHorizontal: normalize(15),
  },
};
