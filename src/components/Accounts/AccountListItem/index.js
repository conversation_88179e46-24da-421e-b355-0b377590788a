// modules
import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Text } from 'native-base';

// components
import UserAvatar from '../../common/UserAvatar';
import PinIcon from '../../common/PinIcon';

// styles
import styles from './styles';

// language translations
import i18n from '../../../localization/i18n';

// helpers
import { getAvatarFallbackText } from '../../../helpers/genericHelper';
import { getFormattedDate } from '../../../helpers/dateHelper';
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';
import { ACCOUNT_TYPE } from '../../../constants/AppConstants';

const AccountListItem = props => {
  const {
    businessName,
    imageURL,
    siteCount,
    isPinned,
    lastVisited,
    onPress,
    localId,
    data,
    index,
    accountType,
  } = props;

  return (
    <View>
      <TouchableOpacity
        key={localId}
        onPress={() => onPress(props)}
        style={[
          styles.container(accountType),
          {
            marginBottom: data.length - 1 == index ? 0 : 10,
            // borderTopLeftRadius: index === 0 ? 12 : 0,
            // borderTopRightRadius: index === 0 ? 12 : 0,
            // borderBottomLeftRadius: index === data.length - 1 ? 12 : 0,
            // borderBottomRightRadius: index === data.length - 1 ? 12 : 0,
          },
        ]}>
        <View>
          <UserAvatar
            imageUri={imageURL != '' ? imageURL : null}
            size={46}
            fallback={getAvatarFallbackText(businessName)}
          />
        </View>
        <View style={styles.customerInfoContainer}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <View style={styles.accountTypeView(accountType)}>
              <Text style={styles.accountTypeText} numberOfLines={1}>
                {accountType === ACCOUNT_TYPE.CUSTOMER
                  ? i18n.t('customer')
                  : i18n.t('prospect')}
              </Text>
            </View>
            {isPinned && (
              <View style={styles.pinIconContainer}>
                <PinIcon selected={isPinned} size={14} />
              </View>
            )}
          </View>
          <View style={styles.customerNameContainer}>
            <Text style={styles.customerName} noOfLines={1}>
              {businessName}
            </Text>
          </View>
          <View style={styles.lastVisitedContainer}>
            <Text style={styles.lastVisitedDate}>
              {i18n.t('lastVisit')}:{' '}
              {!stringIsEmpty(lastVisited)
                ? getFormattedDate(lastVisited)
                : i18n.t('NA')}
            </Text>
            <Text style={styles.lastVisitedDate}>
              {'  |  '}
              {siteCount} {siteCount > 1 ? i18n.t('sites') : i18n.t('site')}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
      {/* <View style={styles.speratorLine} /> */}
    </View>
  );
};

export default AccountListItem;
