import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';

export default {
  container: {
    width: '100%',
    height: normalize(74),
    flexDirection: 'row',
    alignItems: 'center',
  },
  userIconContainer: {
    width: normalize(67),
    height: normalize(67),
    backgroundColor: colors.primaryLight2,
    borderRadius: normalize(100),
    alignItems: 'center',
    justifyContent: 'center',
  },
  changeImageText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: normalize(0.2),
    color: colors.grey2,
    marginLeft: normalize(16),
  },
  addImageText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: normalize(0.2),
    color: colors.primaryMain,
    marginLeft: normalize(16),
  },
};
