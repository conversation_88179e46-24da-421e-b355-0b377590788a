// modules
import React, { useState } from 'react';
import { TouchableOpacity, View, Keyboard } from 'react-native';
import { Text } from 'native-base';

// components
import UserAvatar from '../../common/UserAvatar';
import ImageUploader from '../../common/ImageUploader';

// styles
import styles from './styles';

// language translations
import i18n from '../../../localization/i18n';

// constants
import { USER_IMAGE_ICON } from '../../../constants/AssetSVGConstants';
import { normalize } from '../../../constants/theme/variables/customFont';

const ImageUpload = props => {
  const { image, onRemove, setImage } = props;
  const [showImageUploader, setShowImageUploader] = useState(false);

  const openImageUploader = () => {
    Keyboard.dismiss();
    setShowImageUploader(true);
  };

  const closeImageUploader = () => {
    setShowImageUploader(false);
  };

  return (
    <View style={styles.container}>
      <View style={styles.userIconContainer}>
        {image ? (
          <UserAvatar
            imageUri={image.uri || `data:image/jpg;base64,${image.data}`}
            size={64}
          />
        ) : (
          <USER_IMAGE_ICON width={normalize(20)} height={normalize(20)} />
        )}
      </View>
      <TouchableOpacity onPress={openImageUploader}>
        {image ? (
          <Text style={styles.changeImageText}>{i18n.t('changeImage')}</Text>
        ) : (
          <Text style={styles.addImageText}>{i18n.t('addImage')}</Text>
        )}
      </TouchableOpacity>
      <ImageUploader
        title={i18n.t('profilePhoto')}
        isOpen={showImageUploader}
        onClose={closeImageUploader}
        onRemove={() => {
          closeImageUploader();
          onRemove();
        }}
        setImage={imageObj => {
          closeImageUploader();
          setImage(imageObj);
        }}
        closeImageUploader={closeImageUploader}
      />
    </View>
  );
};

export default ImageUpload;
