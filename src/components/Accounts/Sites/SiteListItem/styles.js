import fonts, {
  normalize,
} from '../../../../constants/theme/variables/customFont';
import colors from '../../../../constants/theme/variables/customColor';

export default {
  container: {
    height: normalize(77),
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: normalize(13),
    borderBottomWidth: normalize(1),
    borderBottomColor: colors.lightWhite,
  },
  avatorView: {
    width: normalize(42),
    height: normalize(42),
    borderRadius: normalize(21),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.userAvatarBackground,
  },
  listCenter: {
    flex: 1,
    marginLeft: normalize(10),
  },
  listTitle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(15),
    lineHeight: normalize(17),
    letterSpacing: 0.18,
    color: colors.grey1,
  },
  subtitleView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(6),
  },
  pipe: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(14),
    color: colors.grey3,
    marginRight: normalize(8),
  },
  lastVisitedDate: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: 0.2,
    color: colors.alphabetIndex,
  },
};
