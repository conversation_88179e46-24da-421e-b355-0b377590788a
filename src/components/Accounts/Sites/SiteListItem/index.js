// modules
import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';

// styles
import styles from './styles';

// language translations
import i18n from '../../../../localization/i18n';

// constants
import { BADGE_ICON } from '../../../../constants/AssetSVGConstants';
import { normalize } from '../../../../constants/theme/variables/customFont';
import colors from '../../../../constants/theme/variables/customColor';
import { DATE_FORMATS } from '../../../../constants/AppConstants';

// helpers
import { stringIsEmpty } from '../../../../helpers/alphaNumericHelper';
import { getFormattedDate } from '../../../../helpers/dateHelper';

const SiteListItem = props => {
  const { siteName, penCount, lastVisited, onPress, siteId, localSiteId } =
    props;

  return (
    <TouchableOpacity
      onPress={() => onPress(siteId, localSiteId)}
      style={styles.container}>
      <View style={styles.avatorView}>
        <BADGE_ICON
          height={normalize(24)}
          width={normalize(24)}
          color={colors.white}
        />
      </View>
      <View style={styles.listCenter}>
        <Text style={styles.listTitle}>{siteName}</Text>
        <View style={styles.subtitleView}>
          <Text style={styles.lastVisitedDate}>
            {i18n.t('lastVisit')}:{' '}
            {!stringIsEmpty(lastVisited)
              ? getFormattedDate(lastVisited, DATE_FORMATS.dd_MMM_yy)
              : i18n.t('NA')}
          </Text>
          <Text style={styles.pipe}>{'  |'}</Text>
          <Text style={styles.lastVisitedDate}>
            {penCount} {penCount > 1 ? i18n.t('pens') : i18n.t('pen')}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default SiteListItem;
