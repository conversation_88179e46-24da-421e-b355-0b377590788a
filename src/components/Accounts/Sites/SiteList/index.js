// modules
import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, FlatList, RefreshControl } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';

// styles
import styles from './styles';

// components
import SiteListItem from '../../../Accounts/Sites/SiteListItem';
import FAB from '../../../common/FAB';
import SearchBar from '../../../common/SearchBar';
import FilterIconButton from '../../../common/FilterIconButton';
import EmptyListComponent from '../../../common/EmptyListComponent';
import FilterModal from '../../../Accounts/filterModal';
import CustomerCalenderRangePicker from '../../../common/CustomCalenderRangePicker';

// translations
import i18n from '../../../../localization/i18n';

// constants
import {
  NO_ACCOUNT_FOUND_ICON,
  NO_SITES_ICON,
} from '../../../../constants/AssetSVGConstants';
import ROUTE_CONSTANTS from '../../../../constants/RouteConstants';

// helpers
import { dateHelper } from '../../../../helpers/dateHelper';
import { getSearchBoxVisible } from '../../../../helpers/siteHelper';
import { stringIsEmpty } from '../../../../helpers/alphaNumericHelper';

// store
import {
  resetSiteState,
  getSitesRequest,
  resetSiteDetail,
  resetGetSitesRequest,
  getSiteByIdRequest,
} from '../../../../store/actions/site';

// models
import { siteFilterModel } from '../../../../models/site';

const SiteList = props => {
  const dispatch = useDispatch();
  const sitesState = useSelector(state => state.site);
  const { filters } = useSelector(state => state.site);
  const [searchTerm, setSearchTerm] = useState(filters?.search);
  const [pageNo, setPageNo] = useState(filters.pageNo);
  const [filterModal, setFilterModal] = useState(false);
  const [dataRangeModal, setDateRangeModal] = useState(false);
  const [filterDate, setFilterDate] = useState({
    startDate: null,
    endDate: null,
  });
  const { type, accountId, localAccountId } = props;
  useFocusEffect(
    useCallback(() => {
      resetPageAndList();
      dispatch(resetSiteDetail());
      const filtersDate = {
        startDate: filters?.startDate,
        endDate: filters?.endDate,
      };
      dispatch(
        getSitesRequest(
          siteFilterModel(
            filters.search,
            filtersDate,
            filters.accountId,
            stringIsEmpty(filters.accountId) ? filters.localAccountId : '',
            pageNo,
          ),
        ),
      );
    }, [filters.search, filters.accountId, filters.localAccountId]),
  );

  useEffect(() => {
    setSearchTerm(filters.search);
  }, [filters.search]);

  useEffect(() => {
    resetPageAndList(); //CDEA-1660 - sites duplicating after applying filter
    getSites();
  }, [searchTerm, filterDate, sitesState.syncLoader]);

  const getSites = useCallback(() => {
    dispatch(
      getSitesRequest(
        siteFilterModel(
          searchTerm,
          filterDate,
          accountId,
          stringIsEmpty(accountId) ? localAccountId : '',
          pageNo,
        ),
      ),
    );
  }, [searchTerm, filterDate]);

  const resetPageAndList = () => {
    dispatch(resetGetSitesRequest());
  };

  const emptyResultsComponent = () => {
    if (
      stringIsEmpty(searchTerm) &&
      !sitesState.sitesLoading &&
      !sitesState.syncLoader
    ) {
      return (
        <View style={styles.emptyListView}>
          <View style={styles.emptyListSection1}>
            <NO_SITES_ICON {...styles.noSitesFound} />
            <Text style={styles.emptyTextLabel}>{i18n.t('noSitesFound')}</Text>
            <Text style={styles.emptyTextDescription}>
              {i18n.t('noSitesFoundDescription')}
            </Text>
          </View>
          <View style={styles.plusFABContainer}>
            <FAB onPress={moveToAddSiteScreen} />
          </View>
        </View>
      );
    } else if (!sitesState.sitesLoading && !sitesState.syncLoader) {
      return (
        <EmptyListComponent
          title={i18n.t('noResultShow')}
          description={i18n.t('noResultShowDescription')}
          image={<NO_ACCOUNT_FOUND_ICON />}
          button={false}
        />
      );
    } else {
      return null;
    }
  };

  const navigateToPens = (siteId, localSiteId) => {
    dispatch(getSiteByIdRequest({ id: localSiteId }));
    props.navigation.navigate(ROUTE_CONSTANTS.PENS, {
      type: type,
      siteId: siteId,
      localSiteId: localSiteId,
      accountId: accountId,
      localAccountId: localAccountId,
    });
  };

  const renderSiteListItem = ({ item }) => {
    return (
      <SiteListItem
        siteName={item.value}
        siteId={item.id}
        localSiteId={item.localId}
        penCount={item.penCount}
        lastVisited={item.dateOfLastVisit}
        onPress={navigateToPens}
      />
    );
  };

  const searchTermChangeHandler = val => {
    resetPageAndList();
    setSearchTerm(val);
  };

  const dateClick = () => {
    setFilterModal(false);
    setTimeout(() => {
      setDateRangeModal(true);
    }, 1000);
  };

  const setFilterDates = ({ startDate, endDate }) => {
    setFilterModal(false);
    setDateRangeModal(false);
    setFilterDate(prevState => ({
      ...prevState,
      startDate: dateHelper.getUnixTimestamp(startDate?.toISOString()),
      endDate: dateHelper.getUnixTimestamp(
        dateHelper.getCurrentDateWithCurrentTime(endDate).toISOString(),
      ),
    }));
  };

  const resetFilter = () => {
    setFilterModal(false);
    setDateRangeModal(false);
    setFilterDate({
      startDate: null,
      endDate: null,
    });
  };

  const onRefresh = () => {
    dispatch(resetGetSitesRequest());
    setPageNo(1);
    dispatch(
      getSitesRequest(
        siteFilterModel(
          searchTerm,
          filterDate,
          accountId,
          stringIsEmpty(accountId) ? localAccountId : '',
          1,
        ),
      ),
    );
  };

  const onEndReached = () => {
    if (sitesState.sitesList.length > 15) {
      setPageNo(pageNo + 1);
      dispatch(
        getSitesRequest(
          siteFilterModel(
            searchTerm,
            filterDate,
            accountId,
            stringIsEmpty(accountId) ? localAccountId : '',
            pageNo + 1,
          ),
        ),
      );
    }
  };

  const moveToAddSiteScreen = () => {
    dispatch(resetSiteState());
    props.navigation.navigate(ROUTE_CONSTANTS.ADD_UPDATE_SITE, {
      type: type,
      accountId: accountId,
      localAccountId: localAccountId,
      update: false,
    });
  };

  const searchBoxVisible = () => {
    return getSearchBoxVisible(filters, sitesState?.sitesList);
  };

  return (
    <>
      <View style={styles.flex1}>
        {/* {sitesState.siteCount > 0 && ( */}
        {searchBoxVisible() && (
          <View style={styles.header}>
            <View style={styles.searchFilterContainer}>
              <View style={styles.flex1}>
                <SearchBar
                  searchTerm={searchTerm}
                  onChange={searchTermChangeHandler}
                />
              </View>
              <FilterIconButton
                onPress={() => setFilterModal(true)}
                customButtonStyle={styles.filterIconButton}
              />
            </View>

            <TouchableOpacity
              onPress={moveToAddSiteScreen}
              style={styles.addSiteButton}>
              <Text style={styles.plusIcon}>{i18n.t('plusSign')}</Text>
              <Text style={styles.newSiteLabel}>{i18n.t('newSite')}</Text>
            </TouchableOpacity>
          </View>
        )}
        {sitesState.sitesList.length > 0 ? (
          <View style={styles.listStyle}>
            <FlatList
              data={sitesState.sitesList}
              renderItem={renderSiteListItem}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.sitesFlatListContainer}
              keyExtractor={item => 'sites_' + item?.localId}
              refreshControl={
                <RefreshControl
                  refreshing={sitesState.sitesLoading || sitesState.syncLoader}
                  onRefresh={onRefresh}
                />
              }
              onEndReachedThreshold={0.7}
              onEndReached={onEndReached}
            />
          </View>
        ) : (
          emptyResultsComponent()
        )}

        <FilterModal
          isVisible={filterModal}
          setIsVisible={() => setFilterModal(false)}
          modalTitle={i18n.t('filters')}
          value={filterDate}
          onPress={() => setFilterModal(false)}
          onDateClick={dateClick}
          onResetPress={resetFilter}
        />

        <CustomerCalenderRangePicker
          isVisible={dataRangeModal}
          setIsVisible={() => setDateRangeModal(!dataRangeModal)}
          customDateSelection={setFilterDates}
          resetFilter={resetFilter}
          backButtonLabel={i18n.t('close')}
          resetSelectedDates={
            filterDate?.startDate === null && filterDate?.endDate === null
          }
        />
      </View>
    </>
  );
};

const SiteListWrapper = props => {
  return <SiteList {...props} />;
};

export default SiteListWrapper;
