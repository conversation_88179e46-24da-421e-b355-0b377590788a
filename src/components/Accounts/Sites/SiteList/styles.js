import fonts, {
  normalize,
} from '../../../../constants/theme/variables/customFont';
import colors from '../../../../constants/theme/variables/customColor';
import { Dimensions } from 'react-native';

const width = Dimensions.get('window').width;

export default {
  header: {
    paddingTop: normalize(17),
    paddingBottom: normalize(18),
    paddingLeft: normalize(20),
    paddingRight: normalize(19),
    alignItems: 'flex-end',
  },
  searchFilterContainer: {
    flexDirection: 'row',
  },
  flex1: {
    flex: 1,
  },
  filterIconButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: normalize(38),
    height: normalize(38),
    borderRadius: normalize(6),
    backgroundColor: colors.white,
    marginLeft: normalize(6),
  },
  addSiteButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: normalize(27),
    marginRight: normalize(12),
  },
  plusIcon: {
    fontFamily: fonts.HelveticaNeueBold,
    fontWeight: '900',
    fontSize: normalize(19),
    lineHeight: normalize(16),
    textAlign: 'center',
    color: colors.primaryMain,
    // textTransform: 'uppercase',
    letterSpacing: 1.25,
    paddingTop: normalize(1),
  },
  newSiteLabel: {
    fontFamily: fonts.HelveticaNeueBold,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    textAlign: 'center',
    color: colors.primaryMain,
    // textTransform: 'uppercase',
    letterSpacing: 1.25,
    marginLeft: normalize(8),
  },
  sitesFlatListContainer: {
    flexGrow: 1,
    backgroundColor: colors.white,
    borderRadius: normalize(12),
    marginHorizontal: normalize(19),
  },
  emptyListView: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  emptyListSection1: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: normalize(40),
    marginRight: normalize(40),
    marginBottom: normalize(122),
  },
  emptyTextLabel: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(25),
    lineHeight: normalize(28),
    color: colors.primaryMain,
    marginTop: normalize(39),
  },
  emptyTextDescription: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontSize: normalize(18),
    lineHeight: normalize(25),
    color: colors.grey1,
    marginTop: normalize(16),
    textAlign: 'center',
  },
  plusFABContainer: {
    position: 'absolute',
    right: normalize(20),
    bottom: normalize(40),
  },
  listStyle: {
    marginBottom: normalize(120),
  },
  noSitesFound: {
    width: width * 0.4,
    height: width * 0.4,
  },
};
