// modules
import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Text } from 'native-base';
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

// translations
import i18n from '../../../../../localization/i18n';

// reusable components
import PenPill from '../PenPill';

const CreatePenField = props => {
  const { moveToAddPenScreen, moveToEditPenScreen, label, required } = props;
  const { customContainerStyle, customLabelStyle } = props;
  const { error } = props;
  const [expandedView, setExpandedView] = useState(false);

  const siteState = useSelector(state => state.site);

  const getExpandedView = () => {
    return siteState.addedPens.map((pen, index) => {
      return (
        <PenPill title={pen.name} onPress={() => moveToEditPenScreen(index)} />
      );
    });
  };

  const getSummaryView = () => {
    const pens = siteState.addedPens.slice(0, 2);
    let pensUI = pens.map((pen, index) => (
      <PenPill title={pen.name} onPress={() => moveToEditPenScreen(index)} />
    ));
    pensUI.push(
      <TouchableOpacity
        onPress={() => {
          setExpandedView(true);
        }}>
        <Text style={styles.moreText}>{`${i18n.t('plusSign')}${
          siteState.addedPens.length - 2
        } ${i18n.t('more')}`}</Text>
      </TouchableOpacity>,
    );
    return pensUI;
  };

  return (
    <View style={[styles.container, customContainerStyle]}>
      <View style={styles.labelContainerStyle}>
        {/* {required && (
          <Text style={styles.starSignStyle}>{i18n.t('starSign')}</Text>
        )} */}
        <Text style={[styles.labelStyle, customLabelStyle]}>{label}</Text>
        {required && (
          <Text style={styles.requiredLabelStyle}>{i18n.t('starSign')}</Text>
        )}
      </View>
      <View style={styles.pillsContainer}>
        {expandedView
          ? getExpandedView()
          : siteState.addedPens.length > 2
          ? getSummaryView()
          : getExpandedView()}
      </View>
      <TouchableOpacity onPress={moveToAddPenScreen}>
        <View style={styles.buttonContainer}>
          <Text style={styles.plusIcon}>{i18n.t('plusSign')}</Text>
          <Text style={styles.createPenText}>{i18n.t('createPen')}</Text>
        </View>
      </TouchableOpacity>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

export default CreatePenField;
