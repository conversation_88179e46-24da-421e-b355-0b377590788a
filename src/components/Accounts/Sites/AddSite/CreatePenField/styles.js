import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flexDirection: 'column',
  },
  labelContainerStyle: {
    flexDirection: 'row',
  },
  starSignStyle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: normalize(0.2),
    color: colors.error4,
    marginRight: normalize(2),
  },
  labelStyle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: normalize(0.2),
    color: colors.grey1,
    marginBottom: normalize(10),
  },
  requiredLabelStyle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontStyle: 'italic',
    fontWeight: '400',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: normalize(0.2),
    color: colors.error4,
    // marginLeft: normalize(12),
    marginLeft: normalize(5)
  },
  pillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: normalize(20),
    alignItems: 'center',
  },
  moreText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(10),
    lineHeight: normalize(12),
    letterSpacing: normalize(1),
    // textTransform: 'uppercase',
    color: colors.grey9,
    marginLeft: normalize(10),
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(2),
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    height: normalize(48),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.primaryMain,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  plusIcon: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(19),
    lineHeight: normalize(16),
    textAlign: 'center',
    color: colors.primaryMain,
    // textTransform: 'uppercase',
    letterSpacing: 1.25,
    paddingTop: normalize(1),
    marginRight: normalize(7),
  },
  createPenText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.primaryMain,
    // textTransform: 'uppercase',
  },
  errorText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '400',
    fontSize: normalize(14),
    marginTop: normalize(2),
    color: colors.error3,
  },
};
