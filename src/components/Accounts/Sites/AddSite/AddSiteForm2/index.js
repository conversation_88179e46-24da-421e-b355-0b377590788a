// modules
import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Platform, Keyboard } from 'react-native';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import NumberFormInput from '../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../AddEdit/CustomInput/index';

// constants
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
  SITE_FIELDS,
} from '../../../../../constants/FormConstants';
import {
  BACTERIA_CELL_COUNT_DECIMAL_PLACE,
  BACTERIA_CELL_COUNT_MAX_VALUE,
  DAYS_IN_MILK_MAX_VALUE,
  DAYS_IN_MILK_MIN_VALUE,
  LACTATING_ANIMALS_MAX_VALUE,
  LACTATING_ANIMALS_MIN_VALUE,
  SOMATIC_CELL_COUNT_MAX_VALUE,
} from '../../../../../constants/AppConstants';

// helpers
import { getWeightUnit } from '../../../../../helpers/appSettingsHelper';

const AddSiteForm2 = props => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    scrollToTop,
    userData,
  } = props;

  const weightUnit = getWeightUnit(userData);

  useEffect(() => {
    scrollToTop();
  }, []);

  //refs
  let lactatingAnimalRef = useRef();
  let daysInMilkRef = useRef();
  let milkYieldRef = useRef();
  let milkFatRef = useRef();
  let milkProtein = useRef();
  let milkOtherSolidsRef = useRef();
  let somaticRef = useRef();
  let bacteriaRef = useRef();

  //helpers
  const focusDaysInMilkRef = () => {
    daysInMilkRef?.focus?.();
  };
  const focusMilkYieldRef = () => {
    milkYieldRef?.focus?.();
  };
  const focusMilkFatRef = () => {
    milkFatRef?.focus?.();
  };
  const focusMilkProtein = () => {
    milkProtein?.focus?.();
  };
  const focusMilkOtherSolidsRef = () => {
    milkOtherSolidsRef?.focus?.();
  };
  const focusSomaticRef = () => {
    somaticRef?.focus?.();
  };
  const focusBacteriaRef = () => {
    bacteriaRef?.focus?.();
  };

  const [type, setType] = useState(CONTENT_TYPE.NUMBER);
  const [action, setAction] = useState(() => {});

  return (
    <View style={styles.siteFormContainer}>
      <Text style={styles.formHeading}>{i18n.t('animalInputSite')}</Text>
      <View style={styles.requiredLabelRow}>
        <Text style={styles.starIcon}>{i18n.t('starSign')}</Text>
        <Text style={styles.requiredLabel}>{i18n.t('requiredFieldMsg')}</Text>
      </View>
      <CustomInputAccessoryView doneAction={action} type={type} />

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('lactatingAnimals')}
          // unit={i18n.t('animals')}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.LACTATING_ANIMALS]}
          error={
            touched[SITE_FIELDS.LACTATING_ANIMALS] &&
            errors[SITE_FIELDS.LACTATING_ANIMALS]
          }
          minValue={LACTATING_ANIMALS_MIN_VALUE}
          maxValue={LACTATING_ANIMALS_MAX_VALUE}
          reference={ref => (lactatingAnimalRef = ref)}
          onSubmitEditing={focusDaysInMilkRef}
          onChange={handleChange(SITE_FIELDS.LACTATING_ANIMALS)}
          onBlur={handleBlur(SITE_FIELDS.LACTATING_ANIMALS)}
          isInteger={true}
          keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: daysInMilkRef });
          }}
          returnKeyType={'next'}
          hasCommas={true}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('daysInMilk')}
          unit={i18n.t('days')}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.DAYS_IN_MILK]}
          error={
            touched[SITE_FIELDS.DAYS_IN_MILK] &&
            errors[SITE_FIELDS.DAYS_IN_MILK]
          }
          onChange={handleChange(SITE_FIELDS.DAYS_IN_MILK)}
          onBlur={handleBlur(SITE_FIELDS.DAYS_IN_MILK)}
          isInteger={true}
          minValue={DAYS_IN_MILK_MIN_VALUE}
          maxValue={DAYS_IN_MILK_MAX_VALUE}
          isNegative={true}
          keyboardType={
            Platform.OS === 'ios'
              ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
              : KEYBOARD_TYPE.NUMBER_PAD
          }
          reference={ref => (daysInMilkRef = ref)}
          onSubmitEditing={() => {
            // Keyboard?.dismiss();
            focusMilkYieldRef();
          }}
          blurOnSubmit={false}
          returnKeyType={'next'}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: milkYieldRef });
          }}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('milkYield')}
          unit={weightUnit}
          reference={ref => (milkYieldRef = ref)}
          onSubmitEditing={focusMilkFatRef}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.MILK_YIELD]}
          // value={values[SITE_FIELDS.MILK_YIELD]}
          error={
            touched[SITE_FIELDS.MILK_YIELD] && errors[SITE_FIELDS.MILK_YIELD]
          }
          minValue={0}
          maxValue={999}
          isInteger={false}
          decimalPoints={2}
          onChange={handleChange(SITE_FIELDS.MILK_YIELD)}
          onBlur={handleBlur(SITE_FIELDS.MILK_YIELD)}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: milkFatRef });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('milkFat')}
          unit={i18n.t('%')}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.MILK_FAT]}
          // value={values[SITE_FIELDS.MILK_FAT]}
          error={touched[SITE_FIELDS.MILK_FAT] && errors[SITE_FIELDS.MILK_FAT]}
          reference={ref => (milkFatRef = ref)}
          onSubmitEditing={focusMilkProtein}
          minValue={0}
          maxValue={100}
          isInteger={false}
          decimalPoints={2}
          onChange={handleChange(SITE_FIELDS.MILK_FAT)}
          onBlur={handleBlur(SITE_FIELDS.MILK_FAT)}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: milkProtein });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('milkProtein')}
          unit={i18n.t('%')}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.MILK_PROTEIN]}
          // value={values[SITE_FIELDS.MILK_PROTEIN]}
          error={
            touched[SITE_FIELDS.MILK_PROTEIN] &&
            errors[SITE_FIELDS.MILK_PROTEIN]
          }
          reference={ref => (milkProtein = ref)}
          onSubmitEditing={focusMilkOtherSolidsRef}
          minValue={0}
          maxValue={100}
          isInteger={false}
          decimalPoints={2}
          onChange={handleChange(SITE_FIELDS.MILK_PROTEIN)}
          onBlur={handleBlur(SITE_FIELDS.MILK_PROTEIN)}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: milkOtherSolidsRef });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('milkOtherSolids')}
          unit={i18n.t('%')}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.MILK_OTHER_SOLIDS]}
          // value={values[SITE_FIELDS.MILK_OTHER_SOLIDS]}
          error={
            touched[SITE_FIELDS.MILK_OTHER_SOLIDS] &&
            errors[SITE_FIELDS.MILK_OTHER_SOLIDS]
          }
          reference={ref => (milkOtherSolidsRef = ref)}
          onSubmitEditing={focusSomaticRef}
          minValue={0}
          maxValue={100}
          isInteger={false}
          decimalPoints={2}
          onChange={handleChange(SITE_FIELDS.MILK_OTHER_SOLIDS)}
          onBlur={handleBlur(SITE_FIELDS.MILK_OTHER_SOLIDS)}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: somaticRef });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('somaticCellCount')}
          unit={i18n.t('somaticCellCountUnit')}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.SOMATIC_CELL_COUNT]}
          error={
            touched[SITE_FIELDS.SOMATIC_CELL_COUNT] &&
            errors[SITE_FIELDS.SOMATIC_CELL_COUNT]
          }
          reference={ref => (somaticRef = ref)}
          onSubmitEditing={focusBacteriaRef}
          minValue={0}
          maxValue={SOMATIC_CELL_COUNT_MAX_VALUE}
          onChange={handleChange(SITE_FIELDS.SOMATIC_CELL_COUNT)}
          onBlur={handleBlur(SITE_FIELDS.SOMATIC_CELL_COUNT)}
          isInteger={true}
          keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: bacteriaRef });
          }}
          returnKeyType={'next'}
          hasCommas={true}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('bacteriaCellCount')}
          unit={i18n.t('bacteriaCellCountUnit')}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.BACTERIA_CELL_COUNT]}
          // value={values[SITE_FIELDS.BACTERIA_CELL_COUNT]}
          error={
            touched[SITE_FIELDS.BACTERIA_CELL_COUNT] &&
            errors[SITE_FIELDS.BACTERIA_CELL_COUNT]
          }
          minValue={0}
          maxValue={BACTERIA_CELL_COUNT_MAX_VALUE}
          isInteger={false}
          decimalPoints={BACTERIA_CELL_COUNT_DECIMAL_PLACE}
          reference={ref => (bacteriaRef = ref)}
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
          onChange={handleChange(SITE_FIELDS.BACTERIA_CELL_COUNT)}
          onBlur={handleBlur(SITE_FIELDS.BACTERIA_CELL_COUNT)}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ dismiss: true });
          }}
          hasCommas={true}
        />
      </View>
    </View>
  );
};

export default AddSiteForm2;
