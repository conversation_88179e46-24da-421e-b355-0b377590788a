// modules
import React from 'react';
import { View, TouchableOpacity } from 'react-native';

// styles
import styles from './styles';

// reusable components
import TextLimit from '../../../../common/TextLimit';

// constants
import { PEN_PILL_MAX_LIMIT } from '../../../../../constants/AppConstants';

const PenPill = props => {
  const { title, onPress } = props;

  return (
    <TouchableOpacity onPress={onPress}>
      <View style={styles.pill}>
        <TextLimit
          title={title}
          charLimit={PEN_PILL_MAX_LIMIT}
          customStyle={styles.penName}
        />
      </View>
    </TouchableOpacity>
  );
};

export default PenPill;
