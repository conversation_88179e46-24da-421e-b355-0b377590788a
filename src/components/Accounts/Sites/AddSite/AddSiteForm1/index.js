// modules
import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Keyboard } from 'react-native';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import NumberFormInput from '../../../../common/NumberFormInput';
import HorizontalSingleSelect from '../../../../common/HorizontalSingleSelect';
import FormInput from '../../../../common/FormInput';
import CustomInputAccessoryView from '../../../AddEdit/CustomInput/index';
import CreatePenField from '../CreatePenField';

// constants
import {
  CONTENT_TYPE,
  INPUT_TYPE,
  KEYBOARD_TYPE,
  SITE_FIELDS,
} from '../../../../../constants/FormConstants';
import {
  ENUM_CONSTANTS,
  MILKING_SYSTEM_CONSTANTS,
  SITE_NAME_MAX_LIMIT,
} from '../../../../../constants/AppConstants';

import { capitalizeFirstLetter } from '../../../../../helpers/alphaNumericHelper';
import { stringIsEmpty } from '../../../../../helpers/alphaNumericHelper';
import { getCurrencyByWeightUnit } from '../../../../../helpers/appSettingsHelper';

const AddSiteForm1 = props => {
  const {
    showPensField,
    pensError,
    moveToAddPenScreen,
    moveToEditPenScreen,
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setFieldValue,
    enums,
    scrollToTop,
    sitesFormRef,
    userData,
    currency,
  } = props;

  useEffect(() => {
    scrollToTop();
  }, []);

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  //refs
  let milkPriceRef = useRef();
  let totalStallsParlorRef = useRef();

  //helpers
  const focusMilkPrice = () => milkPriceRef?.focus?.();
  const focusTotalStallParlorRef = () => totalStallsParlorRef?.focus?.();

  return (
    <View style={styles.siteFormContainer}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <Text style={styles.formHeading}>
        {i18n.t('generalCustomerSiteSetup')}
      </Text>
      <View style={styles.requiredLabelRow}>
        <Text style={styles.starIcon}>{i18n.t('starSign')}</Text>
        <Text style={styles.requiredLabel}>{i18n.t('requiredFieldMsg')}</Text>
      </View>

      <View style={styles.formInputView}>
        <FormInput
          label={i18n.t('siteName')}
          type={INPUT_TYPE.TEXT}
          required
          placeholder={i18n.t('inputPlaceholder')}
          maxLength={SITE_NAME_MAX_LIMIT}
          value={values[SITE_FIELDS.SITE_NAME]}
          error={
            touched[SITE_FIELDS.SITE_NAME] && errors[SITE_FIELDS.SITE_NAME]
          }
          reference={ref => (siteNameRef = ref)}
          onSubmitEditing={() => {
            Keyboard?.dismiss();
            focusMilkPrice();
          }}
          onChange={e => {
            sitesFormRef?.current?.setSubmitting(false);
            setFieldValue(SITE_FIELDS.SITE_NAME, e);
          }}
          onBlur={handleBlur(SITE_FIELDS.SITE_NAME)}
          customLabelStyle={styles.customFieldLabel}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.TEXT);
            setAction({ currentRef: milkPriceRef });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={`${i18n.t('currentMilkPrice')} ${getCurrencyByWeightUnit(
            currency,
            userData,
          )}`}
          unit={currency}
          required
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.CURRENT_MILK_PRICE]}
          // value={values[SITE_FIELDS.CURRENT_MILK_PRICE]}
          error={
            touched[SITE_FIELDS.CURRENT_MILK_PRICE] &&
            errors[SITE_FIELDS.CURRENT_MILK_PRICE]
          }
          decimalPoints={3}
          isInteger={false}
          minValue={0}
          maxValue={1000}
          onChange={handleChange(SITE_FIELDS.CURRENT_MILK_PRICE)}
          onBlur={handleBlur(SITE_FIELDS.CURRENT_MILK_PRICE)}
          reference={ref => (milkPriceRef = ref)}
          onSubmitEditing={focusTotalStallParlorRef}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: totalStallsParlorRef });
          }}
          returnKeyType={'next'}
          hasCommas={true}
        />
      </View>

      <View style={styles.formInputView}>
        <HorizontalSingleSelect
          label={i18n.t('milkingSystem')}
          required
          options={
            !stringIsEmpty(enums?.enum)
              ? enums?.enum[ENUM_CONSTANTS.MILKING_SYSTEM]
              : []
          } //[i18n.t('parlor'), i18n.t('robot'), i18n.t('other')]}
          value={values[SITE_FIELDS.MILKING_SYSTEM]}
          error={
            touched[SITE_FIELDS.MILKING_SYSTEM] &&
            errors[SITE_FIELDS.MILKING_SYSTEM]
          }
          onChange={item => {
            setFieldValue(SITE_FIELDS.MILKING_SYSTEM, item);
            //when its not parlor reset TOTAL_STALLS value to 0
            if (item !== MILKING_SYSTEM_CONSTANTS.PARLOR) {
              setFieldValue(SITE_FIELDS.TOTAL_STALLS, 0);
            }
          }}
        />
      </View>

      {values[SITE_FIELDS.MILKING_SYSTEM] ===
      MILKING_SYSTEM_CONSTANTS.PARLOR ? (
        <View style={styles.formInputView}>
          <NumberFormInput
            label={i18n.t('totalStallsInParlor')}
            unit={capitalizeFirstLetter(i18n.t('stalls'))}
            placeholder={i18n.t('numberPlaceholder')}
            value={values[SITE_FIELDS.TOTAL_STALLS]}
            error={
              touched[SITE_FIELDS.TOTAL_STALLS] &&
              errors[SITE_FIELDS.TOTAL_STALLS]
            }
            minValue={0}
            maxValue={1000000}
            onChange={handleChange(SITE_FIELDS.TOTAL_STALLS)}
            onBlur={handleBlur(SITE_FIELDS.TOTAL_STALLS)}
            isInteger={true}
            reference={ref => (totalStallsParlorRef = ref)}
            blurOnSubmit={false}
            onSubmitEditing={() => {
              Keyboard.dismiss();
            }}
            inputAccessoryViewID="customInputAccessoryView"
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({ dismiss: true });
            }}
            keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
            hasCommas={true}
          />
        </View>
      ) : null}

      {showPensField ? (
        <CreatePenField
          label={i18n.t('pens')}
          required
          error={pensError}
          moveToAddPenScreen={moveToAddPenScreen}
          moveToEditPenScreen={moveToEditPenScreen}
        />
      ) : null}
    </View>
  );
};

export default AddSiteForm1;
