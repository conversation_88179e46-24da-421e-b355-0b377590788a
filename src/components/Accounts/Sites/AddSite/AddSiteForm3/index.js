// modules
import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Keyboard } from 'react-native';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import NumberFormInput from '../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../AddEdit/CustomInput/index';

// constants
import {
  SITE_FIELDS,
  CONTENT_TYPE,
} from '../../../../../constants/FormConstants';

// helpers
import { getWeightUnit } from '../../../../../helpers/appSettingsHelper';
import { getNELDairyUnit } from '../../../../../helpers/siteHelper';
import { RATION_COST_MAX_VALUE } from '../../../../../constants/AppConstants';

const AddSiteForm3 = props => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    scrollToTop,
    userData,
    currency,
  } = props;

  const weightUnit = getWeightUnit(userData);

  useEffect(() => {
    scrollToTop();
  }, []);

  const [type, setType] = useState(CONTENT_TYPE.NUMBER);
  const [action, setAction] = useState(() => {});

  //refs
  let fedIntakeRef = useRef();
  let nelDairy = useRef();
  let rationCostRef = useRef();

  //helpers
  const focusFedIntakeRef = () => fedIntakeRef?.focus?.();
  const focusNelDairy = () => nelDairy?.focus?.();
  const focusRationCostRef = () => rationCostRef?.focus();

  return (
    <View style={styles.siteFormContainer}>
      <Text style={styles.formHeading}>{i18n.t('dietInputSite')}</Text>
      <View style={styles.requiredLabelRow}>
        <Text style={styles.starIcon}>{i18n.t('starSign')}</Text>
        <Text style={styles.requiredLabel}>{i18n.t('requiredFieldMsg')}</Text>
      </View>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('dryMatterIntake')}
          unit={weightUnit}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.DRY_MATTER_INTAKE]}
          // value={values[SITE_FIELDS.DRY_MATTER_INTAKE]}
          error={
            touched[SITE_FIELDS.DRY_MATTER_INTAKE] &&
            errors[SITE_FIELDS.DRY_MATTER_INTAKE]
          }
          minValue={0}
          maxValue={100}
          isInteger={false}
          decimalPoints={2}
          // reference={ref => (dryMatterRef = ref)}
          onSubmitEditing={focusFedIntakeRef}
          onChange={handleChange(SITE_FIELDS.DRY_MATTER_INTAKE)}
          onBlur={handleBlur(SITE_FIELDS.DRY_MATTER_INTAKE)}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: fedIntakeRef });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('asFedIntake')}
          unit={weightUnit}
          placeholder={i18n.t('numberPlaceholder')}
          value={
            values[SITE_FIELDS.AS_FED_INTAKE]
            // value={values[SITE_FIELDS.AS_FED_INTAKE]}
          }
          minValue={0}
          maxValue={999}
          isInteger={false}
          decimalPoints={1}
          reference={ref => (fedIntakeRef = ref)}
          onSubmitEditing={focusNelDairy}
          onChange={handleChange(SITE_FIELDS.AS_FED_INTAKE)}
          onBlur={handleBlur(SITE_FIELDS.AS_FED_INTAKE)}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: nelDairy });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('NELDairy')}
          unit={getNELDairyUnit(userData)}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.NEL_DAIRY]}
          // value={values[SITE_FIELDS.NEL_DAIRY]}
          error={
            touched[SITE_FIELDS.NEL_DAIRY] && errors[SITE_FIELDS.NEL_DAIRY]
          }
          minValue={0}
          maxValue={99}
          isInteger={false}
          decimalPoints={2}
          reference={ref => (nelDairy = ref)}
          onSubmitEditing={focusRationCostRef}
          onChange={handleChange(SITE_FIELDS.NEL_DAIRY)}
          onBlur={handleBlur(SITE_FIELDS.NEL_DAIRY)}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: rationCostRef });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('rationCostPerAnimal')}
          unit={currency}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[SITE_FIELDS.RATION_COST]}
          // value={values[SITE_FIELDS.RATION_COST]}
          error={
            touched[SITE_FIELDS.RATION_COST] && errors[SITE_FIELDS.RATION_COST]
          }
          minValue={0}
          maxValue={RATION_COST_MAX_VALUE}
          isInteger={false}
          decimalPoints={2}
          onChange={handleChange(SITE_FIELDS.RATION_COST)}
          onBlur={handleBlur(SITE_FIELDS.RATION_COST)}
          reference={ref => (rationCostRef = ref)}
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
          blurOnSubmit={false}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ dismiss: true });
          }}
          hasCommas={true}
        />
      </View>
    </View>
  );
};

export default AddSiteForm3;
