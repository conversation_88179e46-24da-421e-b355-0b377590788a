// modules
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation, useRoute } from '@react-navigation/native';

// components
import TopBar from '../../../../TopBar';

// localization
import i18n from '../../../../../localization/i18n';

// constants
import { ACCOUNT_TYPE } from '../../../../../constants/AppConstants';

// models
import { generalFilterModelForNotebook } from '../../../../../models/noteBook';

// actions
import {
  notesListResetRequest,
  getNotebookTrailDataRequest,
} from '../../../../../store/actions/notebook';

// styles
import styles from './styles';

const PensTopBar = () => {
  const dispatch = useDispatch();
  const { goBack } = useNavigation();
  const { params } = useRoute();

  const accountsState = useSelector(state => state.accounts.account);
  const siteState = useSelector(state => state.site.site);

  const navigateToNoteBook = () => {
    const notebookPayload = {
      pageNo: 1,
      filterAccounts: [
        {
          businessName: accountsState?.businessName,
          accountId: params?.accountId,
          localAccountId: params?.localAccountId,
          localId: '',
        },
      ],
      filterSites: [
        {
          siteId: params?.siteId,
          localSiteId: params?.localSiteId,
          name: siteState?.siteName,
          localId: '',
          accountId: params?.accountId,
          localAccountId: params?.localAccountId,
        },
      ],
    };

    const _noteBookFilterModel = generalFilterModelForNotebook(notebookPayload);

    dispatch(notesListResetRequest());
    dispatch(
      getNotebookTrailDataRequest({
        model: _noteBookFilterModel,
        showTrail: true,
        requestList: true,
      }),
    );
  };

  const tobBarTitle =
    params?.type === ACCOUNT_TYPE.CUSTOMER
      ? i18n.t('customers')
      : i18n.t('prospects');

  return (
    <TopBar
      backButton
      backButtonClick={goBack}
      notificationIcon
      notesIcon
      onNotesClick={navigateToNoteBook}
      titleStyles={styles.titleText}
      title={tobBarTitle}
    />
  );
};

export default PensTopBar;
