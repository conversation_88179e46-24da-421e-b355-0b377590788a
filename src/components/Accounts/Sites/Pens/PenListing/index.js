// modules
import React, { useRef, useState } from 'react';
import { RefreshControl, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import Animated, { CurvedTransition } from 'react-native-reanimated';

// common component
import PenListItem from './PenListItem';
import PensFilterClassContainer from './PensFilterContainer';
import EmptyListComponent from '../../../../common/EmptyListComponent';
import DeletePenModal from './DeletePenModal';

//styles
import styles from './styles';

// actions
import { getPensBySiteIdRequest } from '../../../../../store/actions/pen';

// helpers
import {
  filterPensByAnimalClass,
  searchPensByPenName,
} from '../../../../../helpers/penHelper';
import { stringIsEmpty } from '../../../../../helpers/alphaNumericHelper';

// localization
import i18n from '../../../../../localization/i18n';

// constants
import RouteConstants from '../../../../../constants/RouteConstants';

// icons
import {
  NO_ACCOUNT_FOUND_ICON,
  NO_PENS_ICON,
} from '../../../../../constants/AssetSVGConstants';

// models
import { generalFilterModelForNotebook } from '../../../../../models/noteBook';

const PenList = ({ syncLoader, params }) => {
  const dispatch = useDispatch();
  const { navigate } = useNavigation();

  const itemRef = useRef({});

  const penList = useSelector(state => state.pen.penList);
  const siteState = useSelector(state => state.site.site);
  const pensLoading = useSelector(state => state.pen.pensLoading);
  const accountsState = useSelector(state => state.accounts.account);

  const [animalClassFilter, setAnimalClassFilter] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [localPensList, setLocalPensList] = useState(null);

  const _handleSearchTermChange = val => {
    setSearchTerm(val);

    const filteredPens = searchPensByPenName(penList, val);
    setLocalPensList(filteredPens);
  };

  const _handleSelectAnimalClassFilter = selectedAnimalClasses => {
    setAnimalClassFilter(selectedAnimalClasses);

    const filteredPens = filterPensByAnimalClass(
      penList,
      selectedAnimalClasses,
    );
    setLocalPensList(filteredPens);
  };

  const moveToAddPenScreen = () => {
    const notebookPayload = {
      pageNo: 1,
      filterAccounts: [
        {
          businessName: accountsState?.businessName,
          accountId: params?.accountId,
          localAccountId: params?.localAccountId,
          localId: '',
        },
      ],
      filterSites: [
        {
          siteId: params?.siteId,
          localSiteId: params?.localSiteId,
          name: siteState?.siteName,
          localId: '',
          accountId: params?.accountId,
          localAccountId: params?.localAccountId,
        },
      ],
    };

    navigate(RouteConstants.PEN_DETAIL, {
      type: params?.type,
      siteId: params?.siteId,
      localSiteId: params?.localSiteId,
      penId: null,
      update: false,
      _noteBookFilterModel: generalFilterModelForNotebook(notebookPayload),
    });
  };

  const onRefresh = () =>
    dispatch(
      getPensBySiteIdRequest({
        siteId: params?.siteId,
        localSiteId: params?.localSiteId,
      }),
    );

  const totalPens = penList?.length;
  const _renderListItem = ({ item, index }) => (
    <PenListItem
      {...item}
      itemRef={itemRef.current}
      index={index}
      totalPens={totalPens}
    />
  );

  const _renderRefreshControl = (
    <RefreshControl
      refreshing={syncLoader || pensLoading}
      onRefresh={onRefresh}
    />
  );

  const _renderListSeparator = () => <View style={styles.separator} />;

  const _renderEmptyComponent = () => {
    if (
      stringIsEmpty(searchTerm) &&
      animalClassFilter.length === 0 &&
      !pensLoading &&
      !syncLoader
    ) {
      return (
        <EmptyListComponent
          title={i18n.t('noPensFound')}
          description={i18n.t('noPensFoundDescription')}
          image={<NO_PENS_ICON {...styles.noRecordsFound} />}
          plusFab={true}
          onPress={moveToAddPenScreen}
        />
      );
    }

    if (!pensLoading && !syncLoader)
      return (
        <EmptyListComponent
          title={i18n.t('noResultShow')}
          description={i18n.t('noResultShowDescription')}
          image={<NO_ACCOUNT_FOUND_ICON {...styles.noRecordsFound} />}
          button={false}
        />
      );
  };

  return (
    <>
      <PensFilterClassContainer
        searchTerm={searchTerm}
        searchTermChangeHandler={_handleSearchTermChange}
        animalClassFilter={animalClassFilter}
        onAnimalClassFilterSelect={_handleSelectAnimalClassFilter}
        moveToAddPenScreen={moveToAddPenScreen}
        disableSearch={localPensList?.length <= 0 || penList?.length <= 0}
      />

      <Animated.FlatList
        data={localPensList ? localPensList : penList}
        renderItem={_renderListItem}
        ListEmptyComponent={_renderEmptyComponent}
        ItemSeparatorComponent={_renderListSeparator}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentFlatListContainer}
        keyExtractor={(_, index) => 'pens_' + index}
        refreshControl={_renderRefreshControl}
        itemLayoutAnimation={CurvedTransition}
      />

      <DeletePenModal />
    </>
  );
};

export default PenList;
