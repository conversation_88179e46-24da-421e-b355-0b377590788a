// modules
import React, { useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';

// components
import SearchBar from '../../../../../common/SearchBar';
import FilterIconButton from '../../../../../common/FilterIconButton';
import BottomSheetMultiSelection from '../../../../../common/BottomSheetMultiSelection';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../../localization/i18n';

const PensFilterClassContainer = ({
  searchTerm,
  searchTermChangeHandler,
  animalClassFilter,
  onAnimalClassFilterSelect,
  disableSearch,
  moveToAddPenScreen,
}) => {
  const animalClass = useSelector(state => state.animalClass.animalClass);

  const [openFilterModal, setFilterModal] = useState(false);

  const onConfirmPress = selectedAnimalClasses => {
    setFilterModal(false);
    onAnimalClassFilterSelect(selectedAnimalClasses);
  };

  return (
    <>
      <View style={styles.header}>
        <View style={styles.searchFilterContainer}>
          <View style={styles.flex1}>
            <SearchBar
              searchTerm={searchTerm}
              onChange={searchTermChangeHandler}
            />
          </View>
          <FilterIconButton
            onPress={() => setFilterModal(true)}
            customButtonStyle={styles.filterIconButton}
            disabled={disableSearch}
          />
        </View>

        <TouchableOpacity
          onPress={moveToAddPenScreen}
          style={styles.addSiteButton}>
          <Text style={styles.plusIcon}>{i18n.t('plusSign')}</Text>
          <Text style={styles.newSiteLabel}>{i18n.t('newPen')}</Text>
        </TouchableOpacity>
      </View>

      <BottomSheetMultiSelection
        required
        selectLabel={i18n.t('animalClass')}
        label={i18n.t('country')}
        placeholder={i18n.t('selectOne')}
        searchPlaceHolder={i18n.t('search')}
        data={animalClass}
        onPress={onConfirmPress}
        value={animalClassFilter}
        isVisible={openFilterModal}
        onPressCancel={() => setFilterModal(false)}
      />
    </>
  );
};

export default PensFilterClassContainer;
