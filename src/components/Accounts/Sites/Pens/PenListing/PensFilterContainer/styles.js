import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  header: {
    paddingTop: normalize(17),
    paddingBottom: normalize(18),
    paddingLeft: normalize(20),
    paddingRight: normalize(19),
    alignItems: 'flex-end',
  },
  searchFilterContainer: {
    flexDirection: 'row',
  },
  flex1: {
    flex: 1,
  },
  filterIconButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: normalize(38),
    height: normalize(38),
    borderRadius: normalize(6),
    backgroundColor: colors.white,
    marginLeft: normalize(6),
  },
  addSiteButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: normalize(27),
    marginRight: normalize(12),
  },
  plusIcon: {
    fontFamily: fonts.HelveticaNeueBold,
    fontWeight: '900',
    fontSize: normalize(19),
    lineHeight: normalize(16),
    textAlign: 'center',
    color: colors.primaryMain,
    letterSpacing: 1.25,
    paddingTop: normalize(1),
  },
  newSiteLabel: {
    fontFamily: fonts.HelveticaNeueBold,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    textAlign: 'center',
    color: colors.primaryMain,
    letterSpacing: 1.25,
    marginLeft: normalize(8),
  },
};
