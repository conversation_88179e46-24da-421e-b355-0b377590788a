// modules
import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// constants
import {
  DELETE_PEN_ICON,
  MERGE_PENS_ICON,
} from '../../../../../../../constants/AssetSVGConstants';

// localization
import i18n from '../../../../../../../localization/i18n';

// styles
import styles from './styles';

// actions
import {
  setActivePenDeletingId,
  getInProgressVisitsForDeletingPenRequest,
} from '../../../../../../../store/actions/pen';

const PenOptions = props => {
  const dispatch = useDispatch();
  const deletingPenId = useSelector(state => state.pen.deletingPenId);

  const _handlePressDeletePen = () => {
    dispatch(setActivePenDeletingId(props?.penId || props?.localPenId));

    const payloadForVisitsUsedPens = {
      penId: props?.penId,
      localPenId: props?.localPenId,
      localSiteId: props?.localSiteId,
      siteId: props?.siteId,
    };
    dispatch(
      getInProgressVisitsForDeletingPenRequest(payloadForVisitsUsedPens),
    );
  };

  const _handlePressMergePen = () => {};

  return (
    <>
      <View style={styles.container}>
        {/* <TouchableOpacity
          style={[styles.button, styles.mergeButton]}
          onPress={_handlePressMergePen}>
          <MERGE_PENS_ICON {...styles.mergeIcon} />
          <Text style={styles.buttonText}>{i18n.t('merge')}</Text>
        </TouchableOpacity> */}
        <TouchableOpacity
          style={[styles.button, styles.deleteButton]}
          onPress={_handlePressDeletePen}>
          {deletingPenId === props?.localPenId ? (
            <ActivityIndicator color={styles.activityColor} />
          ) : (
            <DELETE_PEN_ICON {...styles.deleteIcon} />
          )}
          <Text style={styles.buttonText}>{i18n.t('delete')}</Text>
        </TouchableOpacity>
      </View>
    </>
  );
};

export default PenOptions;
