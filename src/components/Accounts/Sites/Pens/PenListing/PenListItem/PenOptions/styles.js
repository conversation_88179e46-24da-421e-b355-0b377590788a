import { StyleSheet } from 'react-native';
import customFont, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import customColor from '../../../../../../../constants/theme/variables/customColor';

export default StyleSheet.create({
  container: {
    flexDirection: 'row',
    overflow: 'hidden',
    borderBottomRightRadius: normalize(13),
    borderTopRightRadius: normalize(13),
  },
  button: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  mergeIcon: {
    width: normalize(13),
    height: normalize(13),
  },
  mergeButton: {
    paddingHorizontal: normalize(18),
    backgroundColor: customColor.primaryMain,
  },
  buttonText: {
    fontFamily: customFont.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(13),
    letterSpacing: 0.8,
    color: customColor.grey7,
    marginTop: normalize(4),
  },
  deleteButton: {
    paddingHorizontal: normalize(15),
    backgroundColor: customColor.stericColor,
  },
  deleteIcon: {
    width: normalize(13),
    height: normalize(13),
  },
  activityColor: customColor.white,
});
