import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    height: normalize(80),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingHorizontal: normalize(10),
  },
  borderStyles: (index, totalPens) => {
    return {
      borderTopLeftRadius: index === 0 ? normalize(12) : 0,
      borderTopRightRadius: index === 0 ? normalize(12) : 0,
      borderBottomLeftRadius: index === totalPens - 1 ? normalize(12) : 0,
      borderBottomRightRadius: index === totalPens - 1 ? normalize(12) : 0,
    };
  },
  penIconContainer: {
    width: normalize(46),
    height: normalize(46),
    borderRadius: normalize(23),
    backgroundColor: colors.userAvatarBackground,
    borderWidth: normalize(1),
    borderColor: colors.searchBoxBorder,
    alignItems: 'center',
    justifyContent: 'center',
  },
  penInfoContainer: {
    marginHorizontal: normalize(12),
    flex: 1,
  },
  penSourceContainer: {
    width: '35%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  penName: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(16),
    letterSpacing: 0.5,
    color: colors.grey1,
  },
  penSource: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(14),
    letterSpacing: 0.18,
    color: colors.primaryMain,
  },
  flexRow: {
    flexDirection: 'row',
  },
  penSubType: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '400',
    fontSize: normalize(12),
    lineHeight: normalize(13),
    color: colors.grey2,
    marginTop: normalize(5),
  },
  lastVisitedDate: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(12),
    lineHeight: normalize(15),
    letterSpacing: 0.2,
    color: colors.alphabetIndex,
    marginTop: normalize(3),
  },
  penIcon: {
    width: normalize(20),
    height: normalize(20),
  },
};
