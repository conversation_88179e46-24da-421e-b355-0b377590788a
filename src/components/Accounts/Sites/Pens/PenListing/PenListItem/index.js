// modules
import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';
import { useNavigation, useRoute } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import ReanimatedSwipeable from 'react-native-gesture-handler/ReanimatedSwipeable';

// styles
import styles from './styles';

// components
import PenOptions from './PenOptions';

// constants
import { PEN_ICON } from '../../../../../../constants/AssetSVGConstants';
import { PEN_SOURCES } from '../../../../../../constants/AppConstants';
import RouteConstants from '../../../../../../constants/RouteConstants';

// localization
import i18n from '../../../../../../localization/i18n';

// helpers
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';

// models
import { generalFilterModelForNotebook } from '../../../../../../models/noteBook';

const PenListItem = ({
  id,
  index,
  localId,
  value,
  source = null,
  ddwlastUpdatedDate = '',
  animalClassId = null,
  totalPens,
  itemRef,
}) => {
  const { navigate } = useNavigation();
  const { params } = useRoute();

  const siteState = useSelector(state => state.site.site);
  const accountsState = useSelector(state => state.accounts.account);
  const animalClass = useSelector(state => state.animalClass?.animalClass);

  useEffect(() => {
    closeInitialSlider();
  }, [totalPens]);

  const closeInitialSlider = async () => {
    await itemRef[index]?.close();
  };

  const _handlePenItemSwipe = async () => {
    const keys = Object.keys(itemRef);
    keys.map(async key => {
      if (key != index) {
        await itemRef[key]?.close();
        return;
      }
    });
  };

  const _handlePressPenItem = () => {
    const payload = {
      pageNo: 1,
      filteredAccounts: [
        {
          businessName: accountsState?.businessName,
          accountId: params?.accountId,
          localAccountId: params?.localAccountId,
          localId: '',
        },
      ],
      filterSites: [
        {
          siteId: params?.siteId,
          localSiteId: params?.localSiteId,
          name: siteState?.siteName,
          localId: '',
          accountId: params?.accountId,
          localAccountId: params?.localAccountId,
        },
      ],
    };

    navigate(RouteConstants.PEN_DETAIL, {
      type: params?.type,
      siteId: params?.siteId,
      localSiteId: params?.localSiteId,
      penId: id,
      localPenId: localId,
      update: true,
      _noteBookFilterModel: generalFilterModelForNotebook(payload),
    });
  };

  const penSubType = !stringIsEmpty(animalClassId)
    ? animalClass.find(a => a.id == animalClassId)?.name
    : i18n.t('NA');

  return (
    <GestureHandlerRootView>
      <ReanimatedSwipeable
        key={index}
        friction={2}
        overshootFriction={8}
        rightThreshold={40}
        ref={ref => (itemRef[index] = ref)}
        enabled={source !== PEN_SOURCES.ddw}
        renderRightActions={(prog, transition) => (
          <PenOptions
            penId={id}
            localPenId={localId}
            siteId={params?.siteId}
            localSiteId={params?.localSiteId}
          />
        )}
        onSwipeableWillOpen={_handlePenItemSwipe}>
        <TouchableOpacity
          onPress={_handlePressPenItem}
          activeOpacity={1}
          style={[styles.container, styles.borderStyles(index, totalPens)]}>
          <View style={styles.penIconContainer}>
            <PEN_ICON {...styles.penIcon} />
          </View>
          <View style={styles.penInfoContainer}>
            <Text style={styles.penName} noOfLines={1}>
              {value}
            </Text>
            <View style={styles.flexRow}>
              <Text style={styles.lastVisitedDate}>{penSubType}</Text>
            </View>
          </View>
          <View style={styles.penSourceContainer}>
            <Text style={styles.penSource} noOfLines={1}>
              {i18n.t(source || 'UserCreated')}
            </Text>
            {source == PEN_SOURCES.ddw && (
              <View style={styles.flexRow}>
                <Text style={styles.lastVisitedDate}>{ddwlastUpdatedDate}</Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </ReanimatedSwipeable>
    </GestureHandlerRootView>
  );
};

export default PenListItem;
