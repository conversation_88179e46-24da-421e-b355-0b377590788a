import { Dimensions } from 'react-native';
import { normalize } from '../../../../../constants/theme/variables/customFont';

const width = Dimensions.get('window').width;

export default {
  contentFlatListContainer: {
    flexGrow: 1,
    borderRadius: normalize(8),
    marginTop: normalize(0),
    paddingBottom: normalize(30),
    paddingHorizontal: normalize(15),
  },
  separator: {
    height: normalize(1),
  },
  noRecordsFound: {
    width: width * 0.4,
    height: width * 0.4,
  },
};
