import { StyleSheet, Dimensions } from 'react-native';
import fonts, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';
import { VISIT_TABLE_FIELDS } from '../../../../../../constants/AppConstants';

const { width, height } = Dimensions.get('window');

export default StyleSheet.create({
  modalStyle: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    width: width,
    backgroundColor: colors.white,
    borderTopRightRadius: normalize(30),
    borderTopLeftRadius: normalize(30),
  },
  topDraggerBarParent: {
    justifyContent: 'center',
    alignSelf: 'center',
    height: normalize(40),
  },
  topDraggerBar: {
    height: normalize(8),
    width: normalize(50),
    borderRadius: normalize(40),
    backgroundColor: colors.grey3,
  },
  infoContainer: {
    marginHorizontal: normalize(30),
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: normalize(48),
    height: normalize(48),
    borderRadius: normalize(24),
    borderWidth: normalize(2),
    borderColor: colors.syncErrorIconBorderColor,
    backgroundColor: colors.stericColor,
    marginBottom: normalize(10),
  },
  deleteIcon: {
    width: normalize(22),
    height: normalize(22),
  },
  deleteTitle: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueMedium,
    lineHeight: normalize(24),
    marginBottom: normalize(5),
  },
  confirmDelete: {
    color: colors.grey1,
    fontSize: normalize(14),
    fontWeight: '400',
    fontFamily: fonts.HelveticaNeueMedium,
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    textAlign: 'center',
  },
  lineBreak: {
    width: width - normalize(35),
    height: normalize(0.5),
    backgroundColor: colors.todayBackGroundColor,
    marginVertical: normalize(15),
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginTop: normalize(20),
    marginBottom: normalize(40),
    marginHorizontal: normalize(15),
  },
  button: {
    flex: 0.45,
  },
  buttonText: {
    fontSize: normalize(14),
    lineHeight: normalize(14),
    fontWeight: '700',
    letterSpacing: 1.25,
    fontFamily: fonts.HelveticaNeueMedium,
  },
  loadingColor: colors.primaryMain,
  visitsTextContainer: {
    marginHorizontal: normalize(20),
    marginBottom: normalize(10),
  },
  infoText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  visitContainer: {
    padding: normalize(12),
    paddingHorizontal: normalize(8),
    borderRadius: normalize(8),
    borderWidth: normalize(1),
    borderColor: colors.todayBackGroundColor,
    marginVertical: normalize(7),
  },
  visitItemContainer: {
    flexDirection: 'row',
  },
  expandContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  expandIcon: {
    width: normalize(18),
    height: normalize(18),
  },
  expandedIcon: {
    marginHorizontal: normalize(8),
    width: normalize(14),
    height: normalize(18),
  },
  borderAnimationColors: [colors.grey8, colors.stericColor],
  visitName: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.alphabetIndex,
    maxWidth: '40%',
  },
  expandTextStyle: {
    color: colors.primaryMain,
    fontWeight: '500',
  },
  toolCount: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '400',
    fontSize: normalize(12),
    lineHeight: normalize(18),
    letterSpacing: 0,
    color: colors.grey1,
  },
  toolsName: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '400',
    fontSize: normalize(13),
    lineHeight: normalize(16),
    color: colors.grey1,
    letterSpacing: 0.5,
  },
  visitList: {
    marginHorizontal: normalize(20),
    maxHeight: height / 2.5,
  },
  expandedView: {
    marginHorizontal: normalize(6),
    marginTop: normalize(10),
  },
  expandItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: normalize(3),
  },
  iconContainer: {
    width: normalize(24),
    height: normalize(24),
    borderRadius: normalize(20),
    marginRight: normalize(10),
    alignItems: 'center',
    justifyContent: 'center',
  },
  [VISIT_TABLE_FIELDS.PEN_TIME_BUDGET_TOOL]: colors.comfortBackground,
  [VISIT_TABLE_FIELDS.RUMEN_HEALTH]: colors.healthBackground,
  [VISIT_TABLE_FIELDS.LOCOMOTION_SCORE]: colors.healthBackground,
  [VISIT_TABLE_FIELDS.BODY_CONDITION]: colors.healthBackground,
  [VISIT_TABLE_FIELDS.RUMEN_FILL_MANURE_SCORE]: colors.healthBackground,
  [VISIT_TABLE_FIELDS.MANURE_SCREENER_TOOL]: colors.healthBackground,
  [VISIT_TABLE_FIELDS.RUMEN_HEALTH_MANURE_SCORE]: colors.healthBackground,
  [VISIT_TABLE_FIELDS.TMR_PARTICLE_SCORE]: colors.nutritionBackground,
  comfortIcon: {
    width: normalize(12),
    height: normalize(12),
  },
  healthIcon: {
    stroke: colors.productivityColor,
    fill: colors.productivityColor,
    width: normalize(12),
    height: normalize(12),
  },
  nutritionIcon: {
    stroke: colors.nutritionColor,
    fillWidth: 1,
    width: normalize(12),
    height: normalize(12),
  },
});
