// modules
import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import Animated, {
  FadeInDown,
  FadeOutDown,
  LinearTransition,
} from 'react-native-reanimated';

// styles
import styles from './styles';

// icons
import {
  CHEVRON_DOWN_BLUE_ICON,
  CHEVRON_RIGHT_ICON,
  COMFORT_ICON,
  HEALTH_ICON,
  NUTRITION_ICON,
} from '../../../../../../constants/AssetSVGConstants';

// constants
import {
  DATE_FORMATS,
  VISIT_TABLE_FIELDS,
} from '../../../../../../constants/AppConstants';

// helpers
import { dateHelper } from '../../../../../../helpers/dateHelper';

// localization
import i18n from '../../../../../../localization/i18n';

const DeletingVisitsInfo = ({ visitsUsingDeletingPens }) => {
  const _renderVisitsUsingPens = () => {
    if (visitsUsingDeletingPens?.visitsUsingPens?.length > 0) {
      return visitsUsingDeletingPens?.visitsUsingPens?.map(visit => {
        const visitDate = dateHelper.getFormattedDate(
          visit.visitDate,
          DATE_FORMATS.MM_dd_yyyy,
        );

        return (
          <VisitInfoExpandable
            key={visit.visitDate}
            visitName={visit?.visitName}
            visitDate={visitDate}
            toolsUsingPen={visit?.toolsUsingPen}
          />
        );
      });
    }
  };

  return (
    <>
      {visitsUsingDeletingPens?.isLoading && (
        <ActivityIndicator color={styles.loadingColor} />
      )}

      {visitsUsingDeletingPens?.visitsUsingPens?.length > 0 && (
        <>
          <View style={styles.visitsTextContainer}>
            <Text style={styles.infoText}>
              {i18n.t('followingVisitUsingPen')}
            </Text>
          </View>

          <View style={styles.visitList}>
            <ScrollView showsVerticalScrollIndicator={false}>
              {_renderVisitsUsingPens()}
            </ScrollView>
          </View>
        </>
      )}
    </>
  );
};

function VisitInfoExpandable({ visitName, toolsUsingPen, visitDate }) {
  const [expandDetails, setExpandDetails] = useState(false);

  const _handlePressItem = () => setExpandDetails(!expandDetails);

  const getToolCategoryIcon = toolName => {
    switch (toolName) {
      case VISIT_TABLE_FIELDS.PEN_TIME_BUDGET_TOOL:
        return <COMFORT_ICON {...styles.comfortIcon} />;
      case VISIT_TABLE_FIELDS.RUMEN_HEALTH:
      case VISIT_TABLE_FIELDS.LOCOMOTION_SCORE:
      case VISIT_TABLE_FIELDS.BODY_CONDITION:
      case VISIT_TABLE_FIELDS.RUMEN_HEALTH_MANURE_SCORE:
      case VISIT_TABLE_FIELDS.RUMEN_FILL_MANURE_SCORE:
      case VISIT_TABLE_FIELDS.MANURE_SCREENER_TOOL:
        return <HEALTH_ICON {...styles.healthIcon} />;
      case VISIT_TABLE_FIELDS.TMR_PARTICLE_SCORE:
        return <NUTRITION_ICON {...styles.nutritionIcon} />;
    }
  };

  const filterAnimalAnalysis = toolsUsingPen?.filter(
    item => item !== VISIT_TABLE_FIELDS.ANIMAL_ANALYSIS,
  );

  return (
    <Animated.View style={styles.visitContainer}>
      <TouchableOpacity onPress={_handlePressItem}>
        <View style={styles.visitItemContainer}>
          <View style={styles.expandContainer}>
            {expandDetails ? (
              <CHEVRON_DOWN_BLUE_ICON {...styles.expandedIcon} />
            ) : (
              <CHEVRON_RIGHT_ICON {...styles.expandIcon} />
            )}

            <Text
              style={[
                styles.visitName,
                expandDetails && styles.expandTextStyle,
              ]}
              numberOfLines={1}>
              {visitName}{' '}
            </Text>
            <Text
              style={[
                styles.visitName,
                expandDetails && styles.expandTextStyle,
              ]}>
              {' '}
              {visitDate}
            </Text>
          </View>

          <Text
            style={[styles.toolCount, expandDetails && styles.expandTextStyle]}>
            {i18n.t('tools')}: {filterAnimalAnalysis?.length}
          </Text>
        </View>

        <Animated.View
          style={expandDetails && styles.expandedView}
          layout={LinearTransition}>
          {expandDetails &&
            filterAnimalAnalysis?.map(tool => (
              <Animated.View
                key={`${visitDate}_${tool}`}
                style={styles.expandItem}
                entering={FadeInDown}
                exiting={FadeOutDown}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: styles[tool] },
                  ]}>
                  {getToolCategoryIcon(tool)}
                </View>
                <Text style={styles.toolsName}>{i18n.t(tool)}</Text>
              </Animated.View>
            ))}
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
}

export default DeletingVisitsInfo;
