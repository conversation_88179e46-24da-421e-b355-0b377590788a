// modules
import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ReactNativeModal from 'react-native-modal';
import Animated, { SequencedTransition } from 'react-native-reanimated';

// components
import FormButton from '../../../../../common/FormButton';
import DeletingVisitsInfo from './DeletingVisitsInfo';

// constants
import { BUTTON_TYPE } from '../../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// icons
import { DELETE_PEN_ICON } from '../../../../../../constants/AssetSVGConstants';

// actions
import {
  deletePenByIdRequest,
  setActivePenDeletingId,
} from '../../../../../../store/actions/pen';

const DeletePenModal = () => {
  const dispatch = useDispatch();

  const deletingPenId = useSelector(state => state.pen.deletingPenId);
  const visitsUsingDeletingPens = useSelector(
    state => state.pen.visitsUsingDeletingPens,
  );

  const _handleCancelDeletePen = () => {
    dispatch(setActivePenDeletingId(null));
  };

  const _handleConfirmDeletePen = () => {
    const payload = {
      siteId: visitsUsingDeletingPens?.selectedListItem?.siteId,
      localSiteId: visitsUsingDeletingPens?.selectedListItem?.localSiteId,
      penId: visitsUsingDeletingPens?.selectedListItem?.penId,
      localPenId: visitsUsingDeletingPens?.selectedListItem?.localPenId,
    };
    dispatch(deletePenByIdRequest(payload));
  };

  const deleteLabel = visitsUsingDeletingPens?.deleteLoading ? (
    <ActivityIndicator size={'small'} color={styles.loadingColor} />
  ) : (
    i18n.t('delete')
  );

  return (
    <ReactNativeModal
      isVisible={deletingPenId ? true : false}
      animationIn={'slideInUp'}
      animationOut={'slideOutDown'}
      onBackdropPress={_handleCancelDeletePen}
      onBackButtonPress={_handleCancelDeletePen}
      swipeDirection={['down']}
      swipeThreshold={200}
      useNativeDriver={true}
      hasBackdrop={true}
      propagateSwipe={true}
      style={styles.modalStyle}>
      <Animated.View style={styles.modalContainer} layout={SequencedTransition}>
        <View style={styles.topDraggerBarParent}>
          <View style={styles.topDraggerBar} />
        </View>

        <View style={styles.infoContainer}>
          <View style={styles.deleteIconContainer}>
            <DELETE_PEN_ICON {...styles.deleteIcon} />
          </View>

          <Text style={styles.deleteTitle}>{i18n.t('permanentDelete')}</Text>

          <Text style={styles.confirmDelete}>{i18n.t('confirmDeletePen')}</Text>

          <View style={styles.lineBreak} />
        </View>

        <DeletingVisitsInfo visitsUsingDeletingPens={visitsUsingDeletingPens} />

        <View style={styles.buttonContainer}>
          <FormButton
            type={BUTTON_TYPE.SECONDARY}
            label={i18n.t('cancel')}
            onPress={_handleCancelDeletePen}
            customButtonStyle={styles.button}
            customButtonTextStyle={styles.buttonText}
            disabled={visitsUsingDeletingPens?.deleteLoading}
          />
          <FormButton
            type={BUTTON_TYPE.PRIMARY}
            label={deleteLabel}
            onPress={_handleConfirmDeletePen}
            customButtonStyle={styles.button}
            customButtonTextStyle={styles.buttonText}
            disabled={
              visitsUsingDeletingPens?.isLoading ||
              visitsUsingDeletingPens?.deleteLoading
            }
          />
        </View>
      </Animated.View>
    </ReactNativeModal>
  );
};

export default DeletePenModal;
