// modules
import React from 'react';
import { View } from 'react-native';

// components
import TabSelector from '../../../../common/TabSelector';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

const PensTopTabBar = ({ selectedIndex, onTabChange }) => {
  const tabLabels = [i18n.t('recentVisit'), i18n.t('pens')];

  return (
    <View style={styles.tabsContainer}>
      <TabSelector
        onTabChange={onTabChange}
        selectedIndex={selectedIndex}
        labels={tabLabels}
      />
    </View>
  );
};

export default PensTopTabBar;
