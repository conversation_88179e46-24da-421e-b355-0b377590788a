import { StyleSheet } from 'react-native';
import customColor from '../../../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';

export default StyleSheet.create({
  headerView: {
    backgroundColor: customColor.white,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    justifyContent: 'space-between',
    paddingVertical: normalize(5),
    paddingLeft: normalize(18),
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarView: {
    width: normalize(46),
    height: normalize(46),
    borderRadius: normalize(23),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: customColor.userAvatarBackground,
  },
  badgeIcon: {
    height: normalize(25),
    width: normalize(25),
    fill: customColor.primaryDark1,
  },
  flexColumn: {
    flexDirection: 'column',
    flex: 1,
  },
  avatarText: {
    fontFamily: customFont.HelveticaNeueMedium,
    fontSize: normalize(16),
    lineHeight: normalize(22),
    maxWidth: normalize(250),
    paddingLeft: normalize(14),
    color: customColor.grey1,
    letterSpacing: 0.2,
  },
  siteDescription: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(10),
    lineHeight: normalize(15),
    paddingLeft: normalize(14),
    letterSpacing: 0.25,
    color: customColor.grey1,
    marginTop: normalize(3),
    paddingRight: normalize(30),
  },
  iconsView: {
    flex: 0.6,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
});
