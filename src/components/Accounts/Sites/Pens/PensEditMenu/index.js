// modules
import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation, useRoute } from '@react-navigation/native';

// icons
import { BADGE_ICON } from '../../../../../constants/AssetSVGConstants';

// styles
import styles from './styles';

// components
import VerticalDotIcon from '../../../../common/VerticalDotIcon';
import EditMenu from '../../../../common/EditMenu';

// localization
import i18n from '../../../../../localization/i18n';

// helpers
import { stringIsEmpty } from '../../../../../helpers/alphaNumericHelper';

// constants
import RouteConstants from '../../../../../constants/RouteConstants';
import {
  SHARE_POINT_DETAIL_REPORT_NAME,
  SHARE_POINT_SUMMARY_REPORT_NAME,
} from '../../../../../constants/AppConstants';

// models
import { noteBookFilterModel } from '../../../../../models/noteBook';

// actions
import { downloadSharepointReportRequest } from '../../../../../store/actions/site';

const PensEditMenu = () => {
  const dispatch = useDispatch();
  const { params } = useRoute();
  const { navigate } = useNavigation();

  const accountsState = useSelector(state => state.accounts?.account);
  const site = useSelector(state => state.site?.site);

  const reportDownloaded = useSelector(state => state.site?.reportDownloaded);
  const reportError = useSelector(state => state.site?.reportError);

  const [showEditMenu, setShowEditMenu] = useState(false);

  useEffect(() => {
    if (reportDownloaded || reportError) closeEditMenu();
  }, [reportDownloaded, reportError]);

  const openEditMenu = () => {
    setShowEditMenu(true);
  };

  const closeEditMenu = () => {
    setShowEditMenu(false);
  };

  const openEditScreen = () => {
    closeEditMenu();
    navigate(RouteConstants.ADD_UPDATE_SITE, {
      type: params?.type,
      id: params?.localSiteId,
      update: true,
      _noteBookFilterModel: getNotebookFilterModelData(),
    });
  };

  const getNotebookFilterModelData = () => {
    let accountName = accountsState?.businessName;
    return noteBookFilterModel(
      null,
      1,
      null,
      null,
      null,
      null,
      [
        {
          businessName: accountName,
          accountId: params?.accountId,
          localAccountId: params?.localAccountId,
          localId: '',
        },
      ],
      [
        {
          siteId: params?.siteId,
          localSiteId: params?.localSiteId,
          name: site?.siteName,
          localId: '',
          accountId: params?.accountId,
          localAccountId: params?.localAccountId,
        },
      ],
      null,
      null,
      null,
    );
  };

  const onSharepointReportClick = async isDetailedReport => {
    const fileName = `${site?.siteName || ''} ${
      isDetailedReport
        ? SHARE_POINT_DETAIL_REPORT_NAME
        : SHARE_POINT_SUMMARY_REPORT_NAME
    }`;

    const payload = {
      siteId: params?.siteId,
      fileName: fileName,
      isDetailedReport: isDetailedReport,
    };

    dispatch(downloadSharepointReportRequest(payload));
  };

  return (
    <>
      <View style={styles.headerView}>
        <View style={styles.flexRow}>
          <View style={styles.avatarView}>
            <BADGE_ICON {...styles.badgeIcon} />
          </View>
          <View style={styles.flexColumn}>
            <Text style={styles.avatarText}>{site?.siteName}</Text>
            <Text style={styles.siteDescription}>
              {accountsState?.businessName} / {site?.siteName}
            </Text>
          </View>
        </View>
        <TouchableOpacity style={styles.iconsView} onPress={openEditMenu}>
          <VerticalDotIcon selected={false} size={36} />
        </TouchableOpacity>
      </View>

      <EditMenu
        isOpen={showEditMenu}
        onClose={closeEditMenu}
        hasReport={site?.hasReport}
        title={i18n.t('siteDetails')}
        onUpdateClick={openEditScreen}
        showSharepointResources={!stringIsEmpty(params?.siteId)}
        onSharepointReportClick={onSharepointReportClick}
      />
    </>
  );
};

export default PensEditMenu;
