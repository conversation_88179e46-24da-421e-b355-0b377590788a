// modules
import React, { useRef, useState } from 'react';
import { View, Text, Platform, Keyboard } from 'react-native';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import NumberFormInput from '../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../AddEdit/CustomInput/index';

// constants
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
  PEN_FIELDS,
} from '../../../../../constants/FormConstants';

// helpers
import { getWeightUnit } from '../../../../../helpers/appSettingsHelper';
import { getNELDairyUnit } from '../../../../../helpers/siteHelper';
import { RATION_COST_MAX_VALUE } from '../../../../../constants/AppConstants';

const AddPenForm2 = props => {
  const {
    showPensField,
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    userData,
    currency,
  } = props;

  const weightUnit = getWeightUnit(userData);

  //refs
  let animalsInPen = useRef();
  let daysInMilkRef = useRef();
  let milkProduction = useRef();
  let dryMatterRef = useRef();
  let fedIntakeRef = useRef();
  let nelDairy = useRef();
  let rationCostRef = useRef();

  //helpers

  const focusDaysInMilkRef = () => daysInMilkRef?.focus?.();
  const focusMilkProduction = () => milkProduction?.focus?.();
  const focusDryMatterRef = () => dryMatterRef?.focus?.();
  const focusFedIntakeRef = () => fedIntakeRef?.focus?.();
  const focusNelDairy = () => nelDairy?.focus?.();
  const focusRationCostRef = () => rationCostRef?.focus();

  const [type, setType] = useState(CONTENT_TYPE.NUMBER);
  const [action, setAction] = useState(() => {});

  return (
    <View style={styles.formContainer}>
      <Text style={styles.formHeading}>{i18n.t('animalInputPen')}</Text>
      <View style={styles.requiredLabelRow}>
        <Text style={styles.starIcon}>{i18n.t('starSign')}</Text>
        <Text style={styles.requiredLabel}>{i18n.t('requiredFieldMsg')}</Text>
      </View>
      <CustomInputAccessoryView doneAction={action} type={type} />

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('animalInPen')}
          // unit={i18n.t('number')}
          //   required
          placeholder={i18n.t('numberPlaceholder')}
          value={values[PEN_FIELDS.ANIMLA_PER_PEN]}
          error={
            touched[PEN_FIELDS.ANIMLA_PER_PEN] &&
            errors[PEN_FIELDS.ANIMLA_PER_PEN]
          }
          minValue={0}
          maxValue={9999}
          onChange={handleChange(PEN_FIELDS.ANIMLA_PER_PEN)}
          onBlur={handleBlur(PEN_FIELDS.ANIMLA_PER_PEN)}
          isInteger={true}
          keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
          reference={ref => (animalsInPen = ref)}
          onSubmitEditing={focusDaysInMilkRef}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: daysInMilkRef });
          }}
          returnKeyType={'next'}
          hasCommas={true}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('daysInMilk')}
          unit={i18n.t('days')}
          //   required
          // placeholder={i18n.t('numberPlaceholder')}
          placeholder={'-'}
          value={values[PEN_FIELDS.DAYS_IN_MILK]}
          error={
            touched[PEN_FIELDS.DAYS_IN_MILK] && errors[PEN_FIELDS.DAYS_IN_MILK]
          }
          onChange={handleChange(PEN_FIELDS.DAYS_IN_MILK)}
          onBlur={handleBlur(PEN_FIELDS.DAYS_IN_MILK)}
          isInteger={true}
          minValue={-100}
          maxValue={999}
          keyboardType={
            Platform.OS === 'ios'
              ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
              : KEYBOARD_TYPE.NUMBER_PAD
          }
          isNegative={true}
          reference={ref => (daysInMilkRef = ref)}
          onSubmitEditing={() => {
            Keyboard?.dismiss();
            focusMilkProduction();
          }}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: milkProduction });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('milkYield')}
          unit={weightUnit}
          //   required
          placeholder={i18n.t('numberPlaceholder')}
          value={values[PEN_FIELDS.MILK_YIELD]}
          // value={values[PEN_FIELDS.MILK_YIELD]}
          error={
            touched[PEN_FIELDS.MILK_YIELD] && errors[PEN_FIELDS.MILK_YIELD]
          }
          minValue={0}
          maxValue={999}
          decimalPoints={2}
          isInteger={false}
          onChange={handleChange(PEN_FIELDS.MILK_YIELD)}
          onBlur={handleBlur(PEN_FIELDS.MILK_YIELD)}
          reference={ref => (milkProduction = ref)}
          onSubmitEditing={focusDryMatterRef}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: dryMatterRef });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formContainer2}>
        <Text style={styles.formHeading}>{i18n.t('dietInputPen')}</Text>
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('dryMatterIntake')}
          unit={weightUnit}
          //   required
          placeholder={i18n.t('numberPlaceholder')}
          value={values[PEN_FIELDS.DRY_MATTER_INTAKE]}
          // value={values[PEN_FIELDS.DRY_MATTER_INTAKE]}
          error={
            touched[PEN_FIELDS.DRY_MATTER_INTAKE] &&
            errors[PEN_FIELDS.DRY_MATTER_INTAKE]
          }
          minValue={0}
          maxValue={100}
          decimalPoints={2}
          isInteger={false}
          onChange={handleChange(PEN_FIELDS.DRY_MATTER_INTAKE)}
          onBlur={handleBlur(PEN_FIELDS.DRY_MATTER_INTAKE)}
          reference={ref => (dryMatterRef = ref)}
          onSubmitEditing={focusFedIntakeRef}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: fedIntakeRef });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('asFedIntake')}
          unit={weightUnit}
          //   required
          placeholder={i18n.t('numberPlaceholder')}
          value={values[PEN_FIELDS.AS_FED_INTAKE]}
          // value={values[PEN_FIELDS.AS_FED_INTAKE]}
          error={
            touched[PEN_FIELDS.AS_FED_INTAKE] &&
            errors[PEN_FIELDS.AS_FED_INTAKE]
          }
          minValue={0}
          maxValue={999}
          decimalPoints={1}
          isInteger={false}
          onChange={handleChange(PEN_FIELDS.AS_FED_INTAKE)}
          onBlur={handleBlur(PEN_FIELDS.AS_FED_INTAKE)}
          reference={ref => (fedIntakeRef = ref)}
          onSubmitEditing={focusNelDairy}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: nelDairy });
          }}
          returnKeyType={'next'}
        />
      </View>

      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('NELDairy')}
          minValue={0}
          maxValue={9999}
          decimalPoints={2}
          isInteger={false}
          unit={getNELDairyUnit(userData)}
          //   required
          placeholder={i18n.t('numberPlaceholder')}
          value={values[PEN_FIELDS.NEL_DAIRY]}
          // value={values[PEN_FIELDS.NEL_DAIRY]}
          error={touched[PEN_FIELDS.NEL_DAIRY] && errors[PEN_FIELDS.NEL_DAIRY]}
          onChange={handleChange(PEN_FIELDS.NEL_DAIRY)}
          onBlur={handleBlur(PEN_FIELDS.NEL_DAIRY)}
          reference={ref => (nelDairy = ref)}
          onSubmitEditing={focusRationCostRef}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ currentRef: rationCostRef });
          }}
          returnKeyType={'next'}
          hasCommas={true}
        />
      </View>
      <View style={styles.formInputView}>
        <NumberFormInput
          label={i18n.t('rationCostPerAnimal')}
          unit={currency}
          minValue={0}
          maxValue={RATION_COST_MAX_VALUE}
          decimalPoints={2}
          isInteger={false}
          placeholder={i18n.t('numberPlaceholder')}
          value={values[PEN_FIELDS.RATION_COST]}
          // value={values[PEN_FIELDS.RATION_COST]}
          error={
            touched[PEN_FIELDS.RATION_COST] && errors[PEN_FIELDS.RATION_COST]
          }
          onChange={handleChange(PEN_FIELDS.RATION_COST)}
          onBlur={handleBlur(PEN_FIELDS.RATION_COST)}
          reference={ref => (rationCostRef = ref)}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({ dismiss: true });
          }}
          hasCommas={true}
        />
      </View>
    </View>
  );
};

export default AddPenForm2;
