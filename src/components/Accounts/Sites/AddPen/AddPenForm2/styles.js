import fonts, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

export default {
  siteFormContainer: { flex: 1, marginHorizontal: 0 },
  flexOne: {
    flex: 1,
    marginBottom: normalize(20),
  },
  formContainer: {
    marginBottom: normalize(80),
  },
  formContainer2: {
    marginBottom: normalize(20),
  },
  formHeading: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(24),
    color: colors.primaryMain,
  },
  requiredLabelRow: {
    flexDirection: 'row',
    marginTop: normalize(4),
  },
  starIcon: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.stericColor,
  },
  requiredLabel: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontStyle: 'italic',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginLeft: normalize(6),
    marginBottom: normalize(30),
  },
  formInputView: {
    marginBottom: normalize(24),
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: colors.grey1,
    // textTransform: 'capitalize',
    marginBottom: normalize(10),
  },
};
