// modules
import React, { useRef, useState } from 'react';
import { View, Text } from 'react-native';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import HorizontalSingleSelect from '../../../../common/HorizontalSingleSelect';
import BottomSheet from '../../../../common/BottomSheet';
import FormInput from '../../../../common/FormInput';
import NumberFormInput from '../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../AddEdit/CustomInput/index';

// helpers
import { stringIsEmpty } from '../../../../../helpers/alphaNumericHelper';

// constants
import {
  INPUT_TYPE,
  PEN_FIELDS,
  CONTENT_TYPE,
  KEYBOARD_TYPE,
} from '../../../../../constants/FormConstants';
import {
  ENUM_CONSTANTS,
  NAME_KEY_FOR_FILTERING,
  PEN_SOURCES,
  SITE_NAME_MAX_LIMIT,
} from '../../../../../constants/AppConstants';

const AddPenForm1 = props => {
  const {
    animalClass,
    enums,
    values,
    errors,
    touched,
    handleChange,
    setFieldValue,
    setFieldError,
    handleBlur,
    penFormRef,
    diets,
    currentPen,
  } = props;

  // refs
  let penName = useRef();
  let barnName = useRef();
  let NoOfStalls = useRef();
  let milkingFrequency = useRef();

  //helpers
  const focusBarnName = () => barnName?.focus?.();
  const focusNoOfStalls = () => NoOfStalls?.focus?.();
  const focusMilkingFrequency = () => milkingFrequency?.focus?.();

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const disableAnimalClass = () => {
    if (values[PEN_FIELDS.DIET] && values[PEN_FIELDS.ANIMAL_CLASS]) {
      const isValidDiet = diets?.find(
        item =>
          item?.id === values[PEN_FIELDS.DIET] &&
          item?.animalClassId === values[PEN_FIELDS.ANIMAL_CLASS],
      );
      if (isValidDiet) {
        return true;
      }
      return false;
    } else if (
      (values[PEN_FIELDS.DIET] &&
        stringIsEmpty(values[PEN_FIELDS.ANIMAL_CLASS])) ||
      (values[PEN_FIELDS.ANIMAL_CLASS] &&
        stringIsEmpty(values[PEN_FIELDS.DIET]))
    ) {
      return false;
    }
  };

  return (
    <View style={{ flex: 1, marginHorizontal: 0 }}>
      {/* <KeyboardAwareScrollView
        style={{ flex: 1 }}
        enableOnAndroid
        enableAutomaticScroll
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="always"
        keyboardOpeningTime={0}
        extraHeight={Platform.select({
          android: 200,
        })}> */}
      <View style={styles.formContainer}>
        <Text style={styles.formHeading}>{i18n.t('general')}</Text>
        <View style={styles.requiredLabelRow}>
          <Text style={styles.starIcon}>{i18n.t('starSign')}</Text>
          <Text style={styles.requiredLabel}>{i18n.t('requiredFieldMsg')}</Text>
        </View>
        <CustomInputAccessoryView doneAction={action} type={type} />

        <View style={styles.formInputView}>
          <FormInput
            label={i18n.t('penName')}
            type={INPUT_TYPE.TEXT}
            required
            placeholder={i18n.t('inputPlaceholder')}
            maxLength={SITE_NAME_MAX_LIMIT}
            value={values[PEN_FIELDS.PEN_NAME]}
            error={errors[PEN_FIELDS.PEN_NAME]}
            onChange={e => {
              penFormRef?.current?.setSubmitting(false);
              setFieldValue(PEN_FIELDS.PEN_NAME, e);
            }}
            onBlur={handleBlur(PEN_FIELDS.PEN_NAME)}
            customLabelStyle={styles.customFieldLabel}
            reference={ref => (penName = ref)}
            onSubmitEditing={focusBarnName}
            returnKeyType={'next'}
            inputAccessoryViewID="customInputAccessoryView"
            onFocus={() => {
              setType(CONTENT_TYPE.TEXT);
              setAction({ currentRef: barnName });
            }}
            disabled={
              currentPen && currentPen?.source === PEN_SOURCES.ddw
                ? true
                : false
            }
          />
        </View>

        <View style={styles.formInputView}>
          <BottomSheet
            selectLabel={i18n.t('selecOne')}
            label={i18n.t('diet')}
            placeholder={i18n.t('selectOne')}
            searchPlaceHolder={i18n.t('search')}
            /**
             * @description
             * setting filtered diets directly with out sorting
             */
            data={diets || []}
            onChange={(id, item) => {
              setFieldValue(PEN_FIELDS.DIET, id, true);
              // setting diet optimization type in form values on selecting diet
              setFieldValue(
                PEN_FIELDS.OPTIMIZATION_TYPE,
                item?.[PEN_FIELDS.OPTIMIZATION_TYPE] || null,
                true,
              );
              setFieldValue(PEN_FIELDS.ANIMAL_CLASS, item.animalClassId, true);
              setFieldValue(PEN_FIELDS.DIET_SOURCE, item?.source || null);
              setFieldError(PEN_FIELDS.DIET, null);
              setFieldError(PEN_FIELDS.ANIMAL_CLASS, null);
            }}
            onBlur={handleBlur(PEN_FIELDS.DIET)}
            value={values[PEN_FIELDS.DIET]}
            /**
             * @description
             * only specify for the diets cases
             * @param {string} optimizationType
             */
            optimizationType={values[PEN_FIELDS.OPTIMIZATION_TYPE]}
            error={touched[PEN_FIELDS.DIET] && errors[PEN_FIELDS.DIET]}
            customLabelStyle={styles.customFieldLabel}
            disabled={diets && diets.length > 0 ? false : true}
            extraKeyExtractorKey={NAME_KEY_FOR_FILTERING}
          />
        </View>

        <View style={styles.formInputView}>
          <BottomSheet
            required
            selectLabel={i18n.t('selectAnimalClass')}
            label={i18n.t('animalClass')}
            placeholder={i18n.t('selectOne')}
            searchPlaceHolder={i18n.t('search')}
            data={!stringIsEmpty(animalClass) ? animalClass : []}
            onChange={(id, item) => {
              setFieldValue(PEN_FIELDS.ANIMAL_CLASS, id, true);
              setFieldError(PEN_FIELDS.ANIMAL_CLASS, null);
            }}
            onBlur={handleBlur(PEN_FIELDS.ANIMAL_CLASS)}
            value={values[PEN_FIELDS.ANIMAL_CLASS]}
            error={
              touched[PEN_FIELDS.ANIMAL_CLASS] &&
              errors[PEN_FIELDS.ANIMAL_CLASS]
            }
            errorMessage={errors[PEN_FIELDS.ANIMAL_CLASS]}
            customLabelStyle={styles.customFieldLabel}
            disabled={disableAnimalClass()}
          />
        </View>

        <View style={styles.formInputView}>
          <FormInput
            label={i18n.t('barnName')}
            type={INPUT_TYPE.TEXT}
            //   required
            placeholder={i18n.t('inputPlaceholder')}
            maxLength={SITE_NAME_MAX_LIMIT}
            value={values[PEN_FIELDS.BARN_NAME]}
            error={
              touched[PEN_FIELDS.BARN_NAME] && errors[PEN_FIELDS.BARN_NAME]
            }
            onChange={handleChange(PEN_FIELDS.BARN_NAME)}
            onBlur={handleBlur(PEN_FIELDS.BARN_NAME)}
            customLabelStyle={styles.customFieldLabel}
            reference={ref => (barnName = ref)}
            onSubmitEditing={focusNoOfStalls}
            returnKeyType={'next'}
            inputAccessoryViewID="customInputAccessoryView"
            onFocus={() => {
              setType(CONTENT_TYPE.TEXT);
              setAction({ currentRef: NoOfStalls });
            }}
          />
        </View>

        <View style={styles.formInputView}>
          <HorizontalSingleSelect
            label={i18n.t('housingSystem')}
            // options={[]}
            options={
              !stringIsEmpty(enums?.enum)
                ? enums?.enum[ENUM_CONSTANTS.HOUSING_SYSTEM] //enums?.enum?.[ENUM_CONSTANTS.HOUSING_SYSTEM]
                : []
            }
            value={values[PEN_FIELDS.HOUSING_SYSTEM]}
            error={
              touched[PEN_FIELDS.HOUSING_SYSTEM] &&
              errors[PEN_FIELDS.HOUSING_SYSTEM]
            }
            required={true}
            onChange={handleChange(PEN_FIELDS.HOUSING_SYSTEM)}
          />
        </View>

        <View style={styles.formInputView}>
          <NumberFormInput
            label={i18n.t('number_of_stalls')}
            unit={i18n.t('stalls')}
            //   required
            placeholder={i18n.t('numberPlaceholder')}
            value={values[PEN_FIELDS.NUMBER_OF_STALLS]}
            error={
              touched[PEN_FIELDS.NUMBER_OF_STALLS] &&
              errors[PEN_FIELDS.NUMBER_OF_STALLS]
            }
            minValue={0}
            maxValue={999}
            onChange={handleChange(PEN_FIELDS.NUMBER_OF_STALLS)}
            onBlur={handleBlur(PEN_FIELDS.NUMBER_OF_STALLS)}
            isInteger={true}
            keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
            reference={ref => (NoOfStalls = ref)}
            onSubmitEditing={focusMilkingFrequency}
            inputAccessoryViewID="customInputAccessoryView"
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({ currentRef: milkingFrequency });
            }}
            returnKeyType={'next'}
          />
        </View>

        <View style={styles.formInputView}>
          <HorizontalSingleSelect
            label={i18n.t('feedingSystem')}
            options={
              !stringIsEmpty(enums?.enum)
                ? enums.enum[ENUM_CONSTANTS.FEEDING_SYSTEM]
                : []
            }
            value={values[PEN_FIELDS.FEEDING_SYSTEM]}
            error={
              touched[PEN_FIELDS.FEEDING_SYSTEM] &&
              errors[PEN_FIELDS.FEEDING_SYSTEM]
            }
            required={true}
            onChange={handleChange(PEN_FIELDS.FEEDING_SYSTEM)}
          />
        </View>

        <View style={styles.formInputView}>
          <NumberFormInput
            label={i18n.t('milking_frequency')}
            //   unit={i18n.t('stalls')}
            placeholder={i18n.t('numberPlaceholder')}
            value={values[PEN_FIELDS.MILKING_FREQUNCY]}
            // value={values[PEN_FIELDS.MILKING_FREQUNCY]}
            error={
              touched[PEN_FIELDS.MILKING_FREQUNCY] &&
              errors[PEN_FIELDS.MILKING_FREQUNCY]
            }
            minValue={0}
            maxValue={9}
            isInteger={false}
            decimalPoints={1}
            onChange={handleChange(PEN_FIELDS.MILKING_FREQUNCY)}
            onBlur={handleBlur(PEN_FIELDS.MILKING_FREQUNCY)}
            reference={ref => (milkingFrequency = ref)}
            inputAccessoryViewID="customInputAccessoryView"
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({ dismiss: true });
            }}
          />
        </View>
      </View>
      {/* </KeyboardAwareScrollView> */}
    </View>
  );
};

export default AddPenForm1;
