import React from 'react';

// modules
import {
  View,
  Text,
  Keyboard,
  InputAccessoryView,
  Pressable,
  Platform,
} from 'react-native';

//localization
import i18n from '../../../../localization/i18n';

//styles
import styles from './styles';

import { managePlaceholder } from './constants';
import { PLATFORM } from '../../../../constants/theme/variables/commonColor';

const CustomInputAccessoryView = ({
  doneAction,
  type,
  nativeViewId = null,
}) => {
  const IOS = Platform.OS === PLATFORM.IOS;
  return IOS ? (
    <InputAccessoryView
      nativeID={nativeViewId ? nativeViewId : 'customInputAccessoryView'}>
      <View style={styles.button}>
        <Text />
        <Text style={styles.placeholder}>{managePlaceholder(type)}</Text>
        <Pressable
          onPress={() => {
            doneAction?.dismiss ? Keyboard.dismiss() : null;
            doneAction?.currentRef?.focus();
            doneAction?.accordion && doneAction?.accordion();
            doneAction?.customFunction && doneAction?.customFunction();
          }}>
          <Text style={styles.text}>
            {doneAction?.dismiss ? i18n.t('done') : i18n.t('next')}
          </Text>
        </Pressable>
      </View>
    </InputAccessoryView>
  ) : null;
};

export default CustomInputAccessoryView;
