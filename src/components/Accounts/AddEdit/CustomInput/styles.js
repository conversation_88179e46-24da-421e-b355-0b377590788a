import fonts, {
  normalize,
} from '../../../../constants/theme/variables/customFont';
import colors from '../../../../constants/theme/variables/customColor';

export default {
  button: {
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    borderColor: colors.grey21,
    backgroundColor: colors.grey20,
    borderTopWidth: normalize(1),
  },
  text: {
    fontSize: normalize(16),
    lineHeight: normalize(21),
    fontWeight: 'bold',
    letterSpacing: normalize(0.25),
    color: colors.iosNativeBlue,
    paddingVertical: normalize(11),
    paddingHorizontal: normalize(16),
  },

  placeholder: {
    fontSize: normalize(13),
    lineHeight: normalize(21),
    letterSpacing: normalize(0.25),
    color: colors.grey22,
    marginLeft: normalize(68),
  },
};
