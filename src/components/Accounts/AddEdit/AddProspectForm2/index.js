// modules
import React from 'react';
import { View, Text, Platform } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

// localization
import i18n from '../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import FormInput from '../../../common/FormInput';

// constants
import {
  INPUT_TYPE,
  KEYBOARD_TYPE,
  PROSPECT_FIELDS,
} from '../../../../constants/FormConstants';
import {
  GENERAL_INPUT_MAX_LIMIT,
  NUMERIC_INPUT_MAX_LIMIT,
} from '../../../../constants/AppConstants';

const AddProspectForm2 = props => {
  const { values, errors, touched, handleChange, handleBlur, setFieldValue } =
    props;

  return (
    <View style={styles.formProspectContainer}>
      <KeyboardAwareScrollView
        style={styles.flexOne}
        enableOnAndroid
        enableAutomaticScroll
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="always"
        keyboardOpeningTime={0}
        extraHeight={Platform.select({
          android: 200,
        })}>
        <View style={styles.formContainer}>
          <Text style={styles.formHeading}>
            {i18n.t('primaryContactInformation')}
          </Text>

          <View style={styles.formInputView}>
            <FormInput
              label={i18n.t('fullName')}
              type={INPUT_TYPE.TEXT}
              required
              placeholder={i18n.t('inputPlaceholder')}
              maxLength={GENERAL_INPUT_MAX_LIMIT}
              value={values[PROSPECT_FIELDS.PRIMARY_CONTACT_FULL_NAME]}
              error={
                touched[PROSPECT_FIELDS.PRIMARY_CONTACT_FULL_NAME] &&
                errors[PROSPECT_FIELDS.PRIMARY_CONTACT_FULL_NAME]
              }
              onChange={handleChange(PROSPECT_FIELDS.PRIMARY_CONTACT_FULL_NAME)}
              onBlur={handleBlur(PROSPECT_FIELDS.PRIMARY_CONTACT_FULL_NAME)}
              customLabelStyle={styles.customFieldLabel}
            />
          </View>

          <View style={styles.formInputView}>
            <FormInput
              label={i18n.t('primaryContactEmail')}
              type={INPUT_TYPE.TEXT}
              placeholder={i18n.t('emailPlaceholder')}
              value={values[PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL]}
              error={
                touched[PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL] &&
                errors[PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL]
              }
              onChange={handleChange(PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL)}
              onBlur={handleBlur(PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL)}
              customLabelStyle={styles.customFieldLabel}
            />
          </View>

          <View style={styles.formInputView}>
            <FormInput
              label={i18n.t('primaryContactPhone')}
              type={INPUT_TYPE.TEXT}
              keyboardType={KEYBOARD_TYPE.NUMBER}
              placeholder={i18n.t('phoneNumberPlaceholder')}
              maxLength={NUMERIC_INPUT_MAX_LIMIT}
              value={values[PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE]}
              error={
                touched[PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE] &&
                errors[PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE]
              }
              onChange={handleChange(PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE)}
              onBlur={handleBlur(PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE)}
              customLabelStyle={styles.customFieldLabel}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default AddProspectForm2;
