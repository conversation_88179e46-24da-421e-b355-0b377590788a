import fonts, {
  normalize,
} from '../../../../constants/theme/variables/customFont';
import colors from '../../../../constants/theme/variables/customColor';

export default {
  flexOne: {
    flex: 1,
  },
  container: {
    flexDirection: 'column',
    width: '100%',
  },
  formProspectContainer: { flex: 1, marginHorizontal: 0 },

  formContainer: {
    marginTop: normalize(10),
    marginBottom: normalize(80),
  },
  loginHeader: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(32),
    fontWeight: '700',
    lineHeight: normalize(39),
    color: colors.primaryMain,
    marginBottom: normalize(32),
  },
  formInputView: {
    marginBottom: normalize(26),
  },
  formHeading: {
    fontSize: normalize(18),
    lineHeight: normalize(20),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.primaryMain,
    marginBottom: normalize(16),
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: colors.grey1,
    // textTransform: 'capitalize',
    marginBottom: normalize(10),
  },
};
