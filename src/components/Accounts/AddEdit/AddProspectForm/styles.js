import fonts, {
  normalize,
} from '../../../../constants/theme/variables/customFont';
import colors from '../../../../constants/theme/variables/customColor';
import { Dimensions } from 'react-native';
const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default {
  formProspectContainer: { flex: 1, marginHorizontal: 0 },
  formContainer: {
    flex: 1,
    flexDirection: 'column',
    marginHorizontal: 0,
    marginTop: normalize(10),
    marginBottom: normalize(120),
  },
  loginHeader: {
    fontFamily: fonts.HelveticaNeueBold,
    fontSize: normalize(32),
    fontWeight: '700',
    lineHeight: normalize(39),
    color: colors.primaryMain,
    marginBottom: normalize(32),
  },
  formInputView: {
    marginBottom: normalize(26),
  },
  businessAddressContainer: {
    marginVertical: normalize(10),
  },
  formHeading: {
    fontSize: normalize(18),
    lineHeight: normalize(20),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.primaryMain,
    marginBottom: normalize(16),
  },
  accordionView: {
    borderTopLeftRadius: normalize(8),
    borderTopRightRadius: normalize(8),
    borderRadius: normalize(8),
    borderColor: colors.accordionBorder,
    borderWidth: normalize(1),
    backgroundColor: colors.white,
  },
  accordionButton: {
    height: normalize(60),
    borderRadius: normalize(8),
    backgroundColor: colors.grey5,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: normalize(20),
  },
  accordionOpen: {
    borderRadius: normalize(0),
    borderTopLeftRadius: normalize(8),
    borderTopRightRadius: normalize(8),
  },
  accordionClose: {
    borderRadius: normalize(8),
  },
  accordionText: {
    paddingHorizontal: normalize(10),
    fontSize: normalize(14),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.grey1,
  },
  farmProducerButton: {
    height: normalize(30),
    borderRadius: normalize(3),
    backgroundColor: colors.grey5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(20),
    alignSelf: 'flex-start',
  },
  farmProducerText: {
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: normalize(12),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.primaryMain,
  },
  marginHorizontal: { marginHorizontal: normalize(18) },
  formMarginBottom: { marginBottom: normalize(20) },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: colors.grey1,
    // textTransform: 'capitalize',
    marginBottom: normalize(10),
  },
  countryCustomFieldLabel: {
    letterSpacing: 0.2,
    color: colors.grey1,
    marginBottom: normalize(8),
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(12),
  },
  topAddressMargin: { marginTop: normalize(24) },
  imageUploadContainer: {
    marginTop: normalize(20),
    marginBottom: normalize(20),
  },
  countryMarginTop: {
    marginTop: normalize(-4),
  },
  labelStyle: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(14),
    // letterSpacing: normalize(1.5),
    color: colors.black,
    // textTransform: 'uppercase',
    marginBottom: normalize(12),
  },
  requiredLabelStyle: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(17),
    letterSpacing: normalize(0.2),
    color: colors.stericColor,
    // marginLeft: normalize(12),
    marginLeft: normalize(4),
    // marginTop: normalize(3),
  },
  labelContainerStyle: {
    flexDirection: 'row',
  },
};
