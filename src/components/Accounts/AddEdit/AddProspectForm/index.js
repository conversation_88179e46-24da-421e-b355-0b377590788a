// modules
import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { useSelector } from 'react-redux';

//localization
import i18n from '../../../../localization/i18n';

//styles
import styles from './styles';

// reusable components
import FormInput from '../../../common/FormInput';
import AccordionActiveIcon from '../../../common/AccordionActiveIcon';
import AccordionInActiveIcon from '../../../common/AccordionInActiveIcon';
import BottomSheet from '../../../common/BottomSheet';
import ImageUpload from '../../../Accounts/ImageUpload';
import CustomInputAccessoryView from '../CustomInput';

// constants
import {
  INPUT_TYPE,
  KEYBOARD_TYPE,
  PROSPECT_FIELDS,
  CONTENT_TYPE,
} from '../../../../constants/FormConstants';
import {
  ADDRESS_MAX_LIMIT,
  BRAZIL_COUNTRY_CODE,
  CITY_MAX_LIMIT,
  COUNTRIES_HAVING_STATES,
  GENERAL_INPUT_MAX_LIMIT,
  NEXT_FIELD_TEXT,
  NUMERIC_INPUT_MAX_LIMIT,
  POSTAL_CODE_MAX_LIMIT,
} from '../../../../constants/AppConstants';

import { stringIsEmpty } from '../../../../helpers/alphaNumericHelper';

const AddProspectForm = props => {
  const segmentState = useSelector(state => state.segment);
  const geolocationState = useSelector(state => state.geolocation);

  const { countries, states } = geolocationState;
  const [accordion, setAccordion] = useState(false);
  const [countryState, setCountryState] = useState([]);
  const [isStateRequired, setIsStateRequired] = useState(
    props?.editMode || false,
  );
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setFieldValue,
    setFieldError,
    validateForm,
  } = props;
  const { image, onRemove, setImage } = props;

  useEffect(() => {
    /**
     * @description
     * checking condition state OR stateId as we save initialize form with local stateId
     */
    if (values[PROSPECT_FIELDS.STATE_ID] || values[PROSPECT_FIELDS.STATE]) {
      /**
       * @description
       * filter out country through name that was initialize in form.
       * passing the filtered countryCode to @function setCountryCodeState
       * so that states dropdown list are assigned to state
       */
      const selectedCountry = countries?.find(
        country => country?.countryKey === values[PROSPECT_FIELDS.COUNTRY_NAME],
      );

      setCountryCodeState(
        selectedCountry
          ? selectedCountry?.countryCode
          : values[PROSPECT_FIELDS.STATE_ID],
      );
    }

    isStateProvinceRegionRequired({
      countryCode: values[PROSPECT_FIELDS.COUNTRY_CODE] || '',
    });
  }, [states]);

  const isCustomerCodeRequired = () => {
    let validate = false;
    if (
      !stringIsEmpty(values[PROSPECT_FIELDS.COUNTRY]) &&
      values[PROSPECT_FIELDS.COUNTRY_CODE] === BRAZIL_COUNTRY_CODE
    ) {
      validate = true;
    }
    return validate;
  };

  /**
   * @description
   * @function setCountryCodeState to filter out states through country code and assign filtered states in local state for that dropdown
   * @param {string} countryCode received countryCode to filter states based on same country
   */
  const setCountryCodeState = countryCode => {
    let temp = [];
    temp = states?.filter(a => a?.countryCode == countryCode);
    setCountryState(temp);
  };

  const isStateProvinceRegionRequired = item => {
    const { countryCode } = item;
    if (COUNTRIES_HAVING_STATES?.includes(countryCode)) {
      setIsStateRequired(true);
    } else {
      setIsStateRequired(false);
    }
  };

  //refs
  let customerCode = useRef();
  let contactFirstNameRef = useRef();
  let contactLastNameRef = useRef();
  let primaryContactEmailRef = useRef();
  let primaryContactPhoneRef = useRef();
  let addressRef = useRef();
  let cityRef = useRef();
  let postalZipCodeRef = useRef();

  //helpers
  const focusCustomerCode = () => {
    customerCode?.focus?.();
  };
  const focusContactFirstNameRef = () => {
    contactFirstNameRef?.focus?.();
  };
  const focusContactLastNameRef = () => {
    contactLastNameRef?.focus?.();
  };
  const focusPrimaryContactEmail = () => {
    primaryContactEmailRef?.focus?.();
  };
  const focusPrimaryContactPhone = () => {
    primaryContactPhoneRef?.focus?.();
  };
  const focusAddressRef = () => {
    addressRef?.focus?.();
  };
  const focusCityRef = () => {
    cityRef?.focus?.();
  };
  const focusPostalZipCodeRef = () => postalZipCodeRef?.focus?.();

  const getSegmentId = value => {
    const segment = segmentState.segmentList?.find(el => {
      return el?.name === value;
    });
    return segment?.id;
  };

  return (
    <View style={styles.formContainer}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.formInputView}>
        <FormInput
          label={i18n.t('businessName')}
          type={INPUT_TYPE.TEXT}
          required
          placeholder={i18n.t('inputPlaceholder')}
          maxLength={GENERAL_INPUT_MAX_LIMIT}
          value={values[PROSPECT_FIELDS.BUSINESS_NAME]}
          error={
            touched[PROSPECT_FIELDS.BUSINESS_NAME] &&
            errors[PROSPECT_FIELDS.BUSINESS_NAME]
          }
          onChange={e => {
            props?.accountFormRef?.current.setSubmitting(false);
            setFieldValue(PROSPECT_FIELDS.BUSINESS_NAME, e);
          }}
          onBlur={handleBlur(PROSPECT_FIELDS.BUSINESS_NAME)}
          customLabelStyle={styles.customFieldLabel}
          reference={input => (businessRef = input)}
          onSubmitEditing={focusCustomerCode}
          blurOnSubmit={false}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.TEXT);
            setAction({ currentRef: customerCode });
          }}
        />
      </View>

      <View style={[styles.formInputView, styles.countryMarginTop]}>
        <BottomSheet
          required
          selectLabel={i18n.t('selectCountry')}
          label={i18n.t('country')}
          placeholder={i18n.t('selectOne')}
          searchPlaceHolder={i18n.t('searchCountry')}
          data={countries}
          onChange={(id, item) => {
            if (id != values[PROSPECT_FIELDS.COUNTRY]) {
              /**
               * @description
               * setting the (STATE_ID AND STATE) to null because of B.E requirements of null when not selected
               */
              setFieldValue(PROSPECT_FIELDS.STATE_ID, null);
              setFieldValue(PROSPECT_FIELDS.STATE, null);
            }

            setFieldValue(PROSPECT_FIELDS.COUNTRY, id, true);
            setFieldValue(PROSPECT_FIELDS.COUNTRY_NAME, item.countryKey);
            setFieldValue(PROSPECT_FIELDS.COUNTRY_CODE, item.countryCode);
            setFieldError(PROSPECT_FIELDS.COUNTRY, null);
            if (COUNTRIES_HAVING_STATES.includes(item.countryCode)) {
              setCountryCodeState(item?.countryCode);
            } else {
              /**
               * @description
               * setting country states to empty list so that state list will be disable and does not required
               */
              setCountryState([]);
            }
            isStateProvinceRegionRequired(item);
            //TODO: remove timeout - doing this just for urgent release purpose
            setTimeout(() => {
              validateForm();
            }, 1000);
          }}
          onBlur={handleBlur(PROSPECT_FIELDS.COUNTRY)}
          value={values[PROSPECT_FIELDS.COUNTRY]}
          error={
            touched[PROSPECT_FIELDS.COUNTRY] && errors[PROSPECT_FIELDS.COUNTRY]
          }
          errorMessage={errors[PROSPECT_FIELDS.COUNTRY]}
          customLabelStyle={styles.countryCustomFieldLabel}
          blurOnSubmit={false}
        />
      </View>

      <View style={styles.formInputView}>
        <FormInput
          label={i18n.t('customerCode')}
          type={INPUT_TYPE.TEXT}
          required={isCustomerCodeRequired()}
          placeholder={i18n.t('inputPlaceholder')}
          maxLength={GENERAL_INPUT_MAX_LIMIT}
          value={values[PROSPECT_FIELDS.CUSTOMER_CODE]}
          onChange={handleChange(PROSPECT_FIELDS.CUSTOMER_CODE)}
          onBlur={handleBlur(PROSPECT_FIELDS.CUSTOMER_CODE)}
          customLabelStyle={styles.customFieldLabel}
          reference={input => (customerCode = input)}
          onSubmitEditing={() => {
            focusContactFirstNameRef();
          }}
          onPress={props.scrollToTop}
          blurOnSubmit={false}
          onFocus={() => {
            props.scrollToTop;
            setType(CONTENT_TYPE.TEXT);
            setAction({ currentRef: contactFirstNameRef });
          }}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
        />
      </View>

      <View style={styles.formInputView}>
        <BottomSheet
          selectLabel={i18n.t('selectState/province/region')}
          label={i18n.t('state/province/region')}
          placeholder={i18n.t('selectOne')}
          searchPlaceHolder={i18n.t('searchState/province/region')}
          data={countryState}
          required={isStateRequired}
          error={
            touched[PROSPECT_FIELDS.STATE_ID] &&
            errors[PROSPECT_FIELDS.STATE_ID]
          }
          onChange={(id, item) => {
            handleChange(PROSPECT_FIELDS.STATE);
            setFieldValue(PROSPECT_FIELDS.STATE_ID, id);
            setFieldValue(PROSPECT_FIELDS.STATE, item.stateKey);
            //TODO: remove timeout - doing this just for urgent release purpose
            setTimeout(() => {
              validateForm();
            }, 1000);
          }}
          onBlur={handleBlur(PROSPECT_FIELDS.STATE_ID)}
          value={values[PROSPECT_FIELDS.STATE_ID]}
          customLabelStyle={styles.customFieldLabel}
          onSubmitEditing={() => {
            Keyboard?.dismiss();
            focusContactFirstNameRef();
          }}
          disabled={countryState?.length > 0 ? false : true}
        />
      </View>

      <View style={styles.formInputView}>
        <FormInput
          label={i18n.t('primaryContactFirstName')}
          type={INPUT_TYPE.TEXT}
          required={true}
          placeholder={i18n.t('inputPlaceholder')}
          maxLength={GENERAL_INPUT_MAX_LIMIT}
          value={values[PROSPECT_FIELDS.PRIMARY_CONTACT_FIRST_NAME]}
          error={
            touched[PROSPECT_FIELDS.PRIMARY_CONTACT_FIRST_NAME] &&
            errors[PROSPECT_FIELDS.PRIMARY_CONTACT_FIRST_NAME]
          }
          onChange={handleChange(PROSPECT_FIELDS.PRIMARY_CONTACT_FIRST_NAME)}
          onBlur={handleBlur(PROSPECT_FIELDS.PRIMARY_CONTACT_FIRST_NAME)}
          customLabelStyle={styles.customFieldLabel}
          reference={input => (contactFirstNameRef = input)}
          onSubmitEditing={focusContactLastNameRef}
          blurOnSubmit={false}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.TEXT);
            setAction({ currentRef: contactLastNameRef });
          }}
        />
      </View>

      <View style={styles.formInputView}>
        <FormInput
          label={i18n.t('primaryContactLastName')}
          type={INPUT_TYPE.TEXT}
          required={true}
          placeholder={i18n.t('inputPlaceholder')}
          maxLength={GENERAL_INPUT_MAX_LIMIT}
          value={values[PROSPECT_FIELDS.PRIMARY_CONTACT_LAST_NAME]}
          error={
            touched[PROSPECT_FIELDS.PRIMARY_CONTACT_LAST_NAME] &&
            errors[PROSPECT_FIELDS.PRIMARY_CONTACT_LAST_NAME]
          }
          onChange={handleChange(PROSPECT_FIELDS.PRIMARY_CONTACT_LAST_NAME)}
          onBlur={handleBlur(PROSPECT_FIELDS.PRIMARY_CONTACT_LAST_NAME)}
          customLabelStyle={styles.customFieldLabel}
          reference={input => (contactLastNameRef = input)}
          onSubmitEditing={focusPrimaryContactEmail}
          blurOnSubmit={false}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          inputAccessoryViewID="customInputAccessoryView"
          onFocus={() => {
            setType(CONTENT_TYPE.TEXT);
            setAction({ currentRef: primaryContactEmailRef });
          }}
        />
      </View>

      <View style={styles.formInputView}>
        <BottomSheet
          selectLabel={i18n.t('selectSegment')}
          label={i18n.t('segment')}
          placeholder={i18n.t('selectOne')}
          searchPlaceHolder={i18n.t('searchSegment')}
          data={segmentState.segmentList}
          onChange={(id, item) => {
            setFieldValue(PROSPECT_FIELDS.SEGMENT, item?.name);
            setFieldValue(PROSPECT_FIELDS.SEGMENT_ID, item?.name);
            // setFieldValue(PROSPECT_FIELDS.SEGMENT, id);
            // setFieldValue(PROSPECT_FIELDS.SEGMENT_ID, item.sv_id);
          }}
          value={getSegmentId(values[PROSPECT_FIELDS.SEGMENT])}
          customLabelStyle={styles.customFieldLabel}
        />
      </View>

      <View style={styles.formInputView}>
        <FormInput
          label={i18n.t('primaryContactEmail')}
          type={INPUT_TYPE.TEXT}
          placeholder={i18n.t('emailPlaceholder')}
          value={values[PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL]}
          error={
            touched[PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL] &&
            errors[PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL]
          }
          onChange={handleChange(PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL)}
          onBlur={handleBlur(PROSPECT_FIELDS.PRIMARY_CONTACT_EMAIL)}
          customLabelStyle={styles.customFieldLabel}
          reference={input => (primaryContactEmailRef = input)}
          onSubmitEditing={focusPrimaryContactPhone}
          blurOnSubmit={false}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.EMAIL);
            setAction({ currentRef: primaryContactPhoneRef });
          }}
          inputAccessoryViewID="customInputAccessoryView"
        />
      </View>

      <View style={styles.formInputView}>
        <FormInput
          label={i18n.t('primaryContactPhone')}
          type={INPUT_TYPE.TEXT}
          keyboardType={KEYBOARD_TYPE.NUMBER}
          placeholder={i18n.t('phoneNumberPlaceholder')}
          maxLength={NUMERIC_INPUT_MAX_LIMIT}
          value={values[PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE]}
          error={
            touched[PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE] &&
            errors[PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE]
          }
          onChange={handleChange(PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE)}
          onBlur={handleBlur(PROSPECT_FIELDS.PRIMARY_CONTACT_PHONE)}
          customLabelStyle={styles.customFieldLabel}
          reference={input => (primaryContactPhoneRef = input)}
          onSubmitEditing={() => {
            focusAddressRef();
            setAccordion(true);
          }}
          blurOnSubmit={false}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef: addressRef,
              accordion: () => setAccordion(true),
            });
          }}
          inputAccessoryViewID="customInputAccessoryView"
        />
      </View>

      <View style={styles.formInputView}>
        <View style={styles.labelContainerStyle}>
          <Text style={[styles.labelStyle, styles.customFieldLabel]}>
            {i18n.t('type')}
          </Text>
        </View>
        <TouchableOpacity style={[styles.farmProducerButton]} activeOpacity={1}>
          <Text style={styles.farmProducerText}>
            {values[PROSPECT_FIELDS.TYPE]}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.businessAddressContainer}>
        <View style={styles.accordionView}>
          <TouchableOpacity
            onPress={() => setAccordion(!accordion)}
            style={[
              styles.accordionButton,
              !accordion ? styles.accordionClose : styles.accordionOpen,
            ]}>
            {!accordion ? <AccordionInActiveIcon /> : <AccordionActiveIcon />}

            <Text style={styles.accordionText}>
              {i18n.t('businessAddress')}
            </Text>
          </TouchableOpacity>
          <View
            style={[
              styles.marginHorizontal,
              {
                display: accordion ? 'flex' : 'none',
              },
            ]}>
            <View style={[styles.formInputView, styles.topAddressMargin]}>
              <FormInput
                label={i18n.t('address')}
                type={INPUT_TYPE.TEXT}
                placeholder={i18n.t('inputPlaceholder')}
                maxLength={ADDRESS_MAX_LIMIT}
                value={values[PROSPECT_FIELDS.ADDRESS]}
                onChange={handleChange(PROSPECT_FIELDS.ADDRESS)}
                onBlur={handleBlur(PROSPECT_FIELDS.ADDRESS)}
                customLabelStyle={styles.customFieldLabel}
                reference={input => (addressRef = input)}
                onSubmitEditing={focusCityRef}
                blurOnSubmit={false}
                returnKeyType={NEXT_FIELD_TEXT.NEXT}
                inputAccessoryViewID="customInputAccessoryView"
                onFocus={() => {
                  setType(CONTENT_TYPE.TEXT);
                  setAction({ currentRef: cityRef });
                }}
              />
            </View>

            <View style={styles.formInputView}>
              <FormInput
                label={i18n.t('city')}
                type={INPUT_TYPE.TEXT}
                placeholder={i18n.t('inputPlaceholder')}
                maxLength={CITY_MAX_LIMIT}
                value={values[PROSPECT_FIELDS.CITY]}
                onChange={handleChange(PROSPECT_FIELDS.CITY)}
                onBlur={handleBlur(PROSPECT_FIELDS.CITY)}
                customLabelStyle={styles.customFieldLabel}
                reference={input => (cityRef = input)}
                onSubmitEditing={focusPostalZipCodeRef}
                blurOnSubmit={false}
                returnKeyType={NEXT_FIELD_TEXT.NEXT}
                inputAccessoryViewID="customInputAccessoryView"
                onFocus={() => {
                  setType(CONTENT_TYPE.TEXT);
                  setAction({ currentRef: postalZipCodeRef });
                }}
              />
            </View>

            <View style={[styles.formInputView, styles.formMarginBottom]}>
              <FormInput
                label={i18n.t('postal/zipCode')}
                type={INPUT_TYPE.TEXT}
                placeholder={i18n.t('inputPlaceholder')}
                maxLength={POSTAL_CODE_MAX_LIMIT}
                value={values[PROSPECT_FIELDS.POSTAL]}
                onChange={handleChange(PROSPECT_FIELDS.POSTAL)}
                onBlur={handleBlur(PROSPECT_FIELDS.POSTAL)}
                customLabelStyle={styles.customFieldLabel}
                reference={input => (postalZipCodeRef = input)}
                onSubmitEditing={() => {
                  Keyboard?.dismiss();
                }}
                blurOnSubmit={false}
                inputAccessoryViewID="customInputAccessoryView"
                onFocus={() => {
                  setType(CONTENT_TYPE.TEXT);
                  setAction({ dismiss: true });
                }}
              />
            </View>
          </View>
        </View>
      </View>

      <View style={styles.imageUploadContainer}>
        <ImageUpload image={image} onRemove={onRemove} setImage={setImage} />
      </View>
    </View>
  );
};

export default AddProspectForm;
