// modules
import React from 'react';
import { View } from 'react-native';
import { useFormikContext } from 'formik';

// components
import FormButton from '../../../common/FormButton';

// constants
import { BUTTON_TYPE } from '../../../../constants/FormConstants';

// localization
import i18n from '../../../../localization/i18n';

// styles
import styles from './styles';

/**
 * @param {object} props - The component's props.
 * @param {boolean} props.update - Whether to show update or create button.
 * @returns {JSX.Element} The AccountFooter component.
 */
const AccountFooter = ({ update }) => {
  const { isValid, isSubmitting, handleSubmit } = useFormikContext();

  const label = update ? i18n.t('update') : i18n.t('createProspect');

  return (
    <View style={styles.bottomButtonView}>
      <FormButton
        label={label}
        type={BUTTON_TYPE.PRIMARY}
        onPress={handleSubmit}
        disabled={!isValid || isSubmitting}
        customButtonStyle={styles.button}
      />
    </View>
  );
};

export default AccountFooter;
