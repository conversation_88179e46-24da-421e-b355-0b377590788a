import { StyleSheet } from 'react-native';
import { normalize } from '../../../../constants/theme/variables/customFont';
import customColor from '../../../../constants/theme/variables/customColor';

export default StyleSheet.create({
  bottomButtonView: {
    position: 'absolute',
    bottom: normalize(0),
    height: normalize(110),
    width: '100%',
    borderTopWidth: normalize(2),
    borderTopColor: customColor.searchBoxBorder,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: customColor.white,
    paddingHorizontal: normalize(24),
    justifyContent: 'center',
  },
  button: {
    borderWidth: 0,
    width: '68%',
  },
});
