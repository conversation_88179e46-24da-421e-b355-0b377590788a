// modules
import React from 'react';
import { useDispatch } from 'react-redux';
import { useFormikContext } from 'formik';
import { useNavigation } from '@react-navigation/native';

// components
import TopBar from '../../../TopBar';
import { showAlertMsg } from '../../../common/Alerts';

// localization
import i18n from '../../../../localization/i18n';

// constants
import { EDIT_SCREEN_TYPE } from '../../../../constants/AppConstants';

// actions
import { resetCreateProspectRequest } from '../../../../store/actions/prospect';

/**
 * @description Renders the header for the Accounts screen.
 *
 * @param {boolean} update - Flag indicating whether the screen is in update mode.
 * @param {string} screenType - Type of the edit screen (e.g., 'PROSPECT', 'CUSTOMER').
 * @returns {JSX.Element} - The rendered header component.
 */
const AccountsHeader = ({ update, screenType }) => {
  const dispatch = useDispatch();

  const { goBack } = useNavigation();
  const { dirty } = useFormikContext();

  /**
   * @description Handles the press event on the cross button.
   * If the form is dirty (has unsaved changes), displays an alert to confirm navigation.
   * Otherwise, navigates back to the previous screen.
   */
  const _handleCrossButtonPress = () => {
    // Check if the form has unsaved changes
    if (dirty) {
      // Show an alert to warn the user about data loss
      showAlertMsg('', i18n.t('dataLossMessage'), [
        {
          text: i18n.t('no'),
          onPress: () => {}, // Do nothing if 'No' is pressed
        },
        {
          text: i18n.t('yes'),
          onPress: () => {
            returnToPreviousScreen(); // Navigate back if 'Yes' is pressed
          },
        },
      ]);
    } else {
      // If form is not dirty, navigate back directly
      returnToPreviousScreen();
    }
  };

  // Function to navigate back to the previous screen and reset the create prospect request
  const returnToPreviousScreen = () => {
    dispatch(resetCreateProspectRequest());
    goBack();
  };

  /**
   * @description Determines the title of the header based on the screen type and update mode.
   * @type {string}
   */
  const headerTitle = !update
    ? i18n.t('addProspect')
    : screenType === EDIT_SCREEN_TYPE.PROSPECT
    ? i18n.t('editProspect')
    : i18n.t('editCustomer');

  return (
    <TopBar
      crossIcon
      crossClick={_handleCrossButtonPress}
      title={headerTitle}
      showBottomBorder
    />
  );
};

export default AccountsHeader;
