// modules
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { useSelector } from 'react-redux';
import { Popover } from 'native-base';

// styling constants
import customColor from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';

// localization
import i18n from '../../../localization/i18n';

const NotebookMediaSyncingLoader = () => {
  const syncingNotebookMedia = useSelector(
    state => state.noteBook.syncingNotebookMedia,
  );

  const [isOpen, setIsOpen] = useState(false);

  const _handlePressPopover = () => setIsOpen(!isOpen);

  const countPayload = ` ${syncingNotebookMedia?.currentLoadingIndex || 0}/${
    syncingNotebookMedia?.totalMediaCount || 0
  }`;

  if (!syncingNotebookMedia.loading) return null;

  return (
    <View style={styles.popOverContainer}>
      <Popover
        isOpen={isOpen}
        onClose={_handlePressPopover}
        trigger={triggerProps => (
          <TouchableOpacity {...triggerProps} onPress={_handlePressPopover}>
            <ActivityIndicator size="small" color={styles.loaderColor} />
          </TouchableOpacity>
        )}>
        <Popover.Content style={styles.popoverStyle} overflow={'visible'}>
          <Popover.Arrow style={styles.arrowStyles} />
          <Popover.Body
            style={styles.popoverBodyStyle}
            rounded={'md'}
            shadow={'0'}>
            <View style={styles.innerContainer}>
              <Text style={styles.text}>
                {i18n.t('downloadingNotebookMedia')}
              </Text>

              <Text style={styles.text}>{countPayload}</Text>
            </View>
          </Popover.Body>
        </Popover.Content>
      </Popover>
    </View>
  );
};

const styles = StyleSheet.create({
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  popOverContainer: {
    zIndex: 10,
  },
  popoverStyle: {
    borderWidth: 1,
    borderColor: customColor.popoverBorderColor,
    backgroundColor: customColor.white,
    marginRight: normalize(20),
  },
  popoverBodyStyle: {
    backgroundColor: customColor.white,
    shadowColor: customColor.popoverShadowColor,
    shadowOffset: {
      width: -2,
      height: 15,
    },
    shadowOpacity: 0.35,
    shadowRadius: 20,
    elevation: 7,
  },
  arrowStyles: {
    backgroundColor: customColor.white,
    borderColor: customColor.popoverBorderColor,
  },
  text: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(20),
    letterSpacing: 0.25,
    color: customColor.grey2,
  },
  loaderColor: customColor.primaryMain,
});

export default NotebookMediaSyncingLoader;
