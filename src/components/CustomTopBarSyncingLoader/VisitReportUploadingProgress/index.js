// modules
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  TextInput,
  Platform,
} from 'react-native';
import { useSelector } from 'react-redux';
import { Popover } from 'native-base';
import Animated, {
  useAnimatedProps,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

// constants
import customColor from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';

// localization
import i18n from '../../../localization/i18n';

const AnimatedText = Animated.createAnimatedComponent(TextInput);

const VisitReportUploadingProgress = () => {
  const progress = useSharedValue(0);

  const isReportsSyncing = useSelector(
    state => state.visitReport.isReportsSyncing,
  );
  const uploadProgress = useSelector(
    state => state.visitReport.visitReportUploadProgress,
  );

  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    progress.value = uploadProgress?.uploadedMbs;
  }, [uploadProgress?.uploadedMbs]);

  const _handlePressPopover = () => setIsOpen(!isOpen);

  const animatedText = useDerivedValue(() =>
    withTiming(progress.value, { duration: 500 }),
  );

  const animatedTextProps = useAnimatedProps(() => {
    return {
      text: `${animatedText.value?.toFixed(1)}`,
    };
  });

  if (isReportsSyncing) {
    return (
      <>
        <View style={styles.popOverContainer}>
          <Popover
            isOpen={isOpen}
            onClose={_handlePressPopover}
            trigger={triggerProps => (
              <TouchableOpacity {...triggerProps} onPress={_handlePressPopover}>
                <ActivityIndicator size="small" color={styles.loaderColor} />
              </TouchableOpacity>
            )}>
            <Popover.Content style={styles.popoverStyle} overflow={'visible'}>
              <Popover.Arrow style={styles.arrowStyles} />
              <Popover.Body
                style={styles.popoverBodyStyle}
                rounded={'md'}
                shadow={'0'}>
                <View style={{ flexDirection: 'row' }}>
                  <Text style={styles.text}>
                    {i18n.t('uploadingVisitReport')}
                  </Text>

                  {Platform.OS === 'ios' && (
                    <>
                      <AnimatedText
                        animatedProps={animatedTextProps}
                        style={styles.animatedText}
                      />

                      <Text style={styles.text}>
                        {` / ${uploadProgress?.totalMbs} ${i18n.t('mbs')}`}
                      </Text>
                    </>
                  )}
                </View>
              </Popover.Body>
            </Popover.Content>
          </Popover>
        </View>
      </>
    );
  }

  return null;
};

const styles = StyleSheet.create({
  popOverContainer: {
    zIndex: 10,
  },
  popoverStyle: {
    borderWidth: 1,
    borderColor: customColor.popoverBorderColor,
    backgroundColor: customColor.white,
    marginRight: normalize(18),
  },
  popoverBodyStyle: {
    backgroundColor: customColor.white,
    shadowColor: customColor.popoverShadowColor,
    shadowOffset: {
      width: -2,
      height: 15,
    },
    shadowOpacity: 0.35,
    shadowRadius: 20,
    elevation: 7,
  },
  arrowStyles: {
    backgroundColor: customColor.white,
    borderColor: customColor.popoverBorderColor,
  },
  text: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(20),
    letterSpacing: 0.25,
    color: customColor.grey2,
  },
  animatedText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    letterSpacing: 0.25,
    color: customColor.grey2,
    marginLeft: normalize(10),
  },
  loaderColor: customColor.primaryMain,
});

export default VisitReportUploadingProgress;
