import React from 'react';
import { TouchableOpacity, View, Text } from 'react-native';
import { PLUS_ICON } from '../../constants/AssetSVGConstants';
import customColor from '../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../constants/theme/variables/customFont';

const NewVisitButton = ({ onPress, focused, label }) => {
  return (
    <View style={styles.buttonContainer}>
      <TouchableOpacity style={styles.buttonStyle} onPress={onPress}>
        <PLUS_ICON height={normalize(37)} width={normalize(22)} />
      </TouchableOpacity>
      <Text
        style={[
          styles.tabBarLabel,
          focused ? styles.selectedTabBarLabel : null,
        ]}>
        {label}
      </Text>
    </View>
  );
};

const styles = {
  buttonContainer: {
    width: normalize(90),
    alignItems: 'center',
  },
  buttonStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: normalize(8),
    height: normalize(52),
    width: normalize(52),
    borderRadius: normalize(35),
    borderWidth: normalize(2),
    borderColor: customColor.greenShadowColor,
    backgroundColor: customColor.secondary4,
  },
  tabBarLabel: {
    fontSize: normalize(11),
    letterSpacing: -0.15,
    marginTop: normalize(6),
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.primaryLight1,
    textAlign: 'center',
  },
  selectedTabBarLabel: {
    color: customColor.primaryMain,
  },
};
export default NewVisitButton;
