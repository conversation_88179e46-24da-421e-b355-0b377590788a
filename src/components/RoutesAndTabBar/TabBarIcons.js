import React from 'react';
import { View, Text, Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';

import customColor from '../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../constants/theme/variables/customFont';

let isTablet = DeviceInfo.isTablet();

const TabBarIcons = ({ focused, iconImage, label }) => {
  return (
    <View style={styles.tab}>
      {iconImage}
      <Text
        style={[
          styles.tabBarLabel,
          focused ? styles.selectedTabBarLabel : null,
        ]}>
        {label}
      </Text>
    </View>
  );
};

const styles = {
  tab: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: Platform.OS == 'ios' ? normalize(30) : normalize(15),
    width: isTablet ? 300 : '100%',
  },
  tabBarLabel: {
    fontSize: normalize(11),
    letterSpacing: -0.15,
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.primaryLight1,
  },
  selectedTabBarLabel: {
    fontSize: normalize(11),
    letterSpacing: -0.15,
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.primaryMain,
  },
};

export default TabBarIcons;
