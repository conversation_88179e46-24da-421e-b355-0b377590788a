// modules
import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/core';

//localization
import i18n from '../../../localization/i18n';

//styles
import styles from './styles';

// helpers
import {
  getNoteBookModelFromNotificationKeys,
  getNotificationData,
} from '../../../helpers/notifications';

// actions
import {
  markNotificationAsReadRequest,
  oneDayAndNoSyncNotificationRequest,
} from '../../../store/actions/notifications';
import { getSelectedVisitRequest } from '../../../store/actions/visit';

// constants
import { NOTIFICATION_TYPE } from '../../../constants/AppConstants';
import ROUTE_CONSTANTS from '../../../constants/RouteConstants';

const NotificationListItem = ({ item }) => {
  const dispatch = useDispatch();
  const { navigate } = useNavigation();

  const notificationLoading = useSelector(
    state => state.notifications.notificationLoading,
  );
  const selectedVisitSuccess = useSelector(
    state => state.visit.selectedVisitSuccess,
  );

  useEffect(() => {
    if (selectedVisitSuccess) {
      navigate(ROUTE_CONSTANTS.TOOL_SELECTION);
    }
  }, [selectedVisitSuccess]);

  const _handlePressNotification = () => {
    // action to mark notification as read
    const readNotificationPayload = {
      id: item?.id,
      sv_id: item?.sv_id,
      isRead: true,
    };
    dispatch(markNotificationAsReadRequest(readNotificationPayload));

    const notificationKeys =
      typeof item?.keys === 'string' ? JSON.parse(item.keys) : item?.keys;

    switch (item.type) {
      case NOTIFICATION_TYPE.VISIT_AUTO_PUBLISHED:
        dispatch(
          getSelectedVisitRequest({
            id: notificationKeys.visitId,
            localId: notificationKeys.localVisitId,
          }),
        );

        break;

      case NOTIFICATION_TYPE.ONE_HOUR_BEFORE_ACTION_IS_DUE:
      case NOTIFICATION_TYPE.TWENTY_FOUR_HOURS_BEFORE_ACTION_IS_DUE:
        const model = getNoteBookModelFromNotificationKeys(notificationKeys);
        navigate(ROUTE_CONSTANTS.NOTE_BOOK_EDITOR, { ...model });

        break;

      case NOTIFICATION_TYPE.TWENTY_FOUR_HOURS_AND_NO_SYNC:
        dispatch(oneDayAndNoSyncNotificationRequest());
        navigate(ROUTE_CONSTANTS.DASHBOARD);

        break;

      default:
        break;
    }
  };

  const isReadStyles = item?.isRead ? styles.white : styles.backgroundDark;
  const timeToNotification = `${item?.ago} ${i18n.t('ago')}`;

  const { icon, notificationMessage } = getNotificationData(item);

  return (
    <TouchableOpacity
      onPress={_handlePressNotification}
      disable={notificationLoading}>
      <View style={[styles.listItemContainer, isReadStyles]}>
        {/* icon  */}
        <View style={styles.iconContainer}>{icon}</View>

        {/* notification message */}
        <View style={styles.messageContainer}>
          <View style={styles.marginTop10}>
            <Text numberOfLines={2} style={styles.text}>
              {notificationMessage}
            </Text>
          </View>
          <View style={styles.marginTop}>
            <Text style={styles.timeAgo}>{timeToNotification}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default NotificationListItem;
