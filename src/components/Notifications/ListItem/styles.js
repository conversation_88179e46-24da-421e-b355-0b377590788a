import DeviceInfo from 'react-native-device-info';
import customColor from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';

export default {
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: DeviceInfo.isTablet() ? 'center' : 'space-between',
  },
  iconContainer: {
    width: normalize(48),
    height: normalize(50),
    marginLeft: normalize(10),
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: normalize(14),
    fontFamily: customFont.HelveticaNeueRegular,
    lineHeight: 25,
    color: customColor.grey15,
  },
  timeAgo: {
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueRegular,
    color: customColor.grey16,
    lineHeight: 25,
  },
  messageContainer: {
    flex: 1,
    flexDirection: 'column',
    marginLeft: normalize(10),
    justifyContent: DeviceInfo.isTablet() ? 'flex-start' : 'center',
    paddingVertical: DeviceInfo.isTablet() ? normalize(0) : normalize(5),
  },
  marginTop: { marginTop: 0 },
  marginTop10: { marginTop: 0 },
  backgroundDark: {
    backgroundColor: customColor.disablePrimaryButtonBackgroundColor,
  },
  white: {
    backgroundColor: customColor.white,
  },
};
