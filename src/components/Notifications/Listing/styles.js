import customColor from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';

export default {
  header: {
    // marginTop: normalize(20),
    fontSize: normalize(13),
    fontFamily: customFont.HelveticaNeueMedium,
   
    color: customColor.grey1,
    lineHeight: normalize(16),
    backgroundColor:'white',
    marginHorizontal: 20,
    paddingBottom:normalize(5),
  },
  
  container: {
    paddingBottom: normalize(140)
  }
};
