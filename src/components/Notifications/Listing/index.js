import React, { useEffect } from 'react';

//react native
import { useToast } from 'native-base';
import { Text, SectionList, View } from 'react-native';

//redux
import { useDispatch, useSelector } from 'react-redux';

//styles
import styles from './styles';

//localization
import i18n from '../../../localization/i18n';

//navigation
import { useNavigation } from '@react-navigation/native';

//helpers
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';

//actions
import { getSelectedVisitRequest } from '../../../store/actions/visit';

//constants
import ROUTE_CONSTANTS from '../../../constants/RouteConstants';
import { NOTIFICATION_TYPE } from '../../../constants/AppConstants';
import { EMPTY_LIST_ICON } from '../../../constants/AssetSVGConstants';
import { TOAST_TYPE } from '../../../constants/FormConstants';

// components
import ListItem from '../ListItem/index';
import EmptyListComponent from '../../../components/common/EmptyListComponent';
import { showToast } from '../../common/CustomToast';

//actions
import { markNotificationAsReadRequest } from '../../../store/actions/notifications';
import { syncDataBlockingRequest } from '../../../store/actions/dataSync';

//service
import { isOnline } from '../../../services/netInfoService';

const NotificationListing = ({ list, setList }) => {
  const navigation = useNavigation();

  const dispatch = useDispatch();
  const toast = useToast();

  //redux state
  const visitState = useSelector(state => state.visit);
  const noteBook = useSelector(state => state.noteBook);

  //hooks
  useEffect(() => {
    if (visitState.selectedVisitSuccess) {
      navigateToToolSelectionScreen();
    }
  }, [visitState.selectedVisitSuccess]);

  //handlers
  const navigateToToolSelectionScreen = item => {
    navigation.navigate(ROUTE_CONSTANTS.TOOL_SELECTION, {});
  };

  const navigateToDashboardScreen = () => {
    onSyncPress();
    navigation.navigate(ROUTE_CONSTANTS.DASHBOARD, {});
  };

  const navigateToNote = noteBook => {
    navigation.navigate(ROUTE_CONSTANTS.NOTE_BOOK_EDITOR, {
      noteId: noteBook?.noteId,
      localId: noteBook?.localId,
      accountId: noteBook?.accountId,
      localAccountId: noteBook?.localAccountId,
      visitId: noteBook?.visitId,
      localVisitId: noteBook?.localVisitId,
      siteId: noteBook?.siteId,
      localSiteId: noteBook?.localSiteId,
      section: noteBook?.section,
      sectionTitle: noteBook?.sectionTitle,
    });
  };

  const onSyncPress = async () => {
    if (await isOnline()) {
      dispatch(syncDataBlockingRequest());
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const getNoteBookModel = keys => {
    let model = {
      localId: keys?.localNoteId,
      noteId: keys?.noteId,
      accountId: keys?.accountId,
      localAccountId: keys?.localAccountId,
      visitId: keys?.visitId,
      localVisitId: keys?.localVisitId,
      siteId: keys?.siteId,
      localSiteId: keys?.localSiteId,
      section: keys?.section,
      sectionTitle: keys?.sectionTitle,
    };
    return model;
  };

  const getVisit = (item, index, section) => {
    markNotificationAsRead(item, index, section);

    if (item.type === NOTIFICATION_TYPE.VISIT_AUTO_PUBLISHED) {
      const keys =
        typeof item?.keys == 'string' ? JSON.parse(item.keys) : item?.keys;
      if (!stringIsEmpty(keys?.visitId)) {
        dispatch(
          getSelectedVisitRequest({
            id: keys.visitId,
            localId: keys.localVisitId,
          }),
        );
      }
    }

    if (item.type === NOTIFICATION_TYPE.ONE_HOUR_BEFORE_ACTION_IS_DUE) {
      const keys =
        typeof item?.keys == 'string' ? JSON.parse(item.keys) : item?.keys;
      const model = getNoteBookModel(keys);
      navigateToNote(model);
    }

    if (
      item.type === NOTIFICATION_TYPE.TWENTY_FOUR_HOURS_BEFORE_ACTION_IS_DUE
    ) {
      const keys =
        typeof item?.keys == 'string' ? JSON.parse(item.keys) : item?.keys;
      const model = getNoteBookModel(keys);
      navigateToNote(model);
    }

    // if (item.type === NOTIFICATION_TYPE.TWENTY_FOUR_HOURS_BEFORE_ACTION_IS_DUE) {
    //   const keys =
    //     typeof item?.keys == 'string' ? JSON.parse(item.keys) : item?.keys;
    //   if (!stringIsEmpty(keys?.noteId || keys?.localNoteId)) {
    //     dispatch(getNoteBookByIdRequest(keys.noteId || keys?.localNoteId));
    //   }
    // }

    if (item.type === NOTIFICATION_TYPE.TWENTY_FOUR_HOURS_AND_NO_SYNC) {
      navigateToDashboardScreen();
    }
  };

  const markNotificationAsRead = (item, index, title) => {
    const payload = {
      id: item?.id,
      sv_id: item?.sv_id,
      isRead: true,
      type: item?.type,
    };

    const result =
      !!list.length &&
      list.map(section => {
        if (section.title === title) {
          return {
            title: section.title,
            data: section.data?.map((element, idx) =>
              index == idx ? { ...element, isRead: true } : { ...element },
            ),
          };
        }
        return { ...section };
      });
    setList(result);
    dispatch(markNotificationAsReadRequest(payload));
  };

  return (
    <View style={styles.container}>
      <SectionList
        showsVerticalScrollIndicator={false}
        sections={list || []}
        keyExtractor={(item, index) => item + index}
        renderItem={({ item, index, section }) => (
          <ListItem
            item={item}
            index={index}
            section={section.title}
            onPress={getVisit}
          />
        )}
        renderSectionHeader={({ section: { title } }) => (
          <Text style={styles.header}>{i18n.t(title?.toLowerCase())}</Text>
        )}
        stickySectionHeadersEnabled={false}
        ListEmptyComponent={
          <EmptyListComponent
            title={i18n.t('emptyList')}
            description={i18n.t('noNotification')}
            image={<EMPTY_LIST_ICON />}
            button={false}
          />
        }
      />
    </View>
  );
};

export default NotificationListing;
