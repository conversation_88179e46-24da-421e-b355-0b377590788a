import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import { background } from 'native-base/lib/typescript/theme/styled-system';

export default {
  container: {
    width: '100%',
    flexDirection: 'column',
    backgroundColor: colors.white,
    borderWidth: normalize(1),
    borderColor: colors.grey5,
    borderRadius: normalize(14),
    padding: normalize(20),
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.04,
    shadowRadius: 6.68,

    elevation: 11,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems:'center',
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
  iconContainer: {
    padding: normalize(4),
    backgroundColor: 'red',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(24),
  },
  infoHeading: {
    width: '40%',
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.alphabetIndex,
  },
  infoValue: {
    width: '60%',
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(16),
    letterSpacing: 0.2,
    color: colors.grey1,
  },
};
