import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';

export default {
  animalFormContainer: {
    flex: 1,
    marginHorizontal: 0,
  },
  flexOne: {
    flex: 1,
  },
  formContainer: {},
  formInputView: {
    marginBottom: normalize(30),
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: colors.grey1,
    // textTransform: 'capitalize',
    marginBottom: normalize(10),
  },
  customFieldLabelUppercase: {
    letterSpacing: 0.2,
    color: colors.grey1,
    marginBottom: normalize(10),
  },
};
