// modules
import React, { useState } from 'react';
import { View, Platform } from 'react-native';
import { useSelector } from 'react-redux';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

// styles
import styles from './styles';

// localization
import i18n from '../../../localization/i18n';

// reusable components
import CustomBottomSheet from '../../common/CustomBottomSheet';
import NumberFormInput from '../../common/NumberFormInput';
import CustomInputAccessoryView from '../../Accounts/AddEdit/CustomInput';

// constants
import {
  ENUM_CONSTANTS,
  NEXT_FIELD_TEXT,
  TOOL_TYPES,
} from '../../../constants/AppConstants';
import {
  BOTTOM_SHEET_TYPE,
  ANIMAL_FIELDS,
  KEYBOARD_TYPE,
  CONTENT_TYPE,
} from '../../../constants/FormConstants';
import colors from '../../../constants/theme/variables/customColor';

// helpers
import {
  getFormattedScoreData,
  stringIsEmpty,
} from '../../../helpers/alphaNumericHelper';

const AddAnimalForm = props => {
  const {
    bcsScale,
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setFieldValue,
    setFieldTouched,
    reloadEarTags,
    toolState,
  } = props;

  const earTagState = useSelector(state => state.earTag);
  const enumState = useSelector(state => state.enums);

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const renderField = () => {
    let renderFieldArray = [];
    let bcsField = (
      <View style={styles.formInputView}>
        <CustomInputAccessoryView doneAction={action} type={type} />
        <CustomBottomSheet
          type={BOTTOM_SHEET_TYPE.SINGLE_SELECT_DROPDOWN}
          required={
            toolState?.currentActiveTool?.toolId == TOOL_TYPES.BODY_CONDITION
          }
          disableSearch
          label={i18n.t('BCS')}
          selectLabel={i18n.t('BCS')}
          placeholder={i18n.t('selectCategory')}
          placeholderColor={colors.alphabetIndex}
          data={
            !stringIsEmpty(enumState?.simpleEnum)
              ? getFormattedScoreData(
                  enumState?.simpleEnum[bcsScale?.key] || [],
                )
              : []
          }
          value={values[ANIMAL_FIELDS.BCS]?.toString() || ''}
          error={touched[ANIMAL_FIELDS.BCS] && errors[ANIMAL_FIELDS.BCS]}
          onChange={item => {
            setFieldValue(ANIMAL_FIELDS.BCS, item.name);
            setTimeout(() => setFieldTouched(ANIMAL_FIELDS.BCS, true));
          }}
          customLabelStyle={styles.customFieldLabelUppercase}
        />
      </View>
    );

    let locomotion = (
      <View style={styles.formInputView}>
        <CustomBottomSheet
          type={BOTTOM_SHEET_TYPE.SINGLE_SELECT_DROPDOWN}
          disableSearch
          required={
            toolState?.currentActiveTool?.toolId == TOOL_TYPES.LOCOMOTION_SCORE
          }
          label={i18n.t('locomotionScore')}
          selectLabel={i18n.t('locomotionScore')}
          placeholder={i18n.t('selectCategory')}
          placeholderColor={colors.alphabetIndex}
          data={
            !stringIsEmpty(enumState?.simpleEnum)
              ? getFormattedScoreData(
                  enumState?.simpleEnum[ENUM_CONSTANTS.LOCOMOTION_SCORE] || [],
                )
              : []
          }
          value={values[ANIMAL_FIELDS.LOCOMOTION_SCORE]?.toString() || ''}
          error={
            touched[ANIMAL_FIELDS.LOCOMOTION_SCORE] &&
            errors[ANIMAL_FIELDS.LOCOMOTION_SCORE]
          }
          onChange={item => {
            setFieldValue(ANIMAL_FIELDS.LOCOMOTION_SCORE, item.name);
            setTimeout(() =>
              setFieldTouched(ANIMAL_FIELDS.LOCOMOTION_SCORE, true),
            );
          }}
          customLabelStyle={styles.customFieldLabel}
        />
      </View>
    );
    renderFieldArray.push(bcsField);
    renderFieldArray.push(locomotion);
    return toolState?.currentActiveTool?.toolId == TOOL_TYPES.BODY_CONDITION
      ? renderFieldArray
      : renderFieldArray.reverse();
  };

  return (
    <View style={styles.animalFormContainer}>
      <KeyboardAwareScrollView
        style={styles.flexOne}
        enableOnAndroid
        enableAutomaticScroll
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="always"
        keyboardOpeningTime={0}
        extraHeight={Platform.select({
          android: 0,
        })}>
        <View style={styles.formContainer}>
          <View style={styles.formInputView}>
            <CustomBottomSheet
              type={BOTTOM_SHEET_TYPE.EAR_TAG}
              required
              label={i18n.t('earTag')}
              selectLabel={i18n.t('earTag')}
              placeholder={i18n.t('earTagPlaceholder')}
              placeholderColor={colors.alphabetIndex}
              searchPlaceHolder={i18n.t('enterTagNumber')}
              infoText={i18n.t('newTagInfoText')}
              data={earTagState?.earTagList}
              reloadEarTags={reloadEarTags}
              value={values[ANIMAL_FIELDS.EAR_TAG]}
              error={
                touched[ANIMAL_FIELDS.EAR_TAG] && errors[ANIMAL_FIELDS.EAR_TAG]
              }
              onChange={item => {
                setFieldValue(ANIMAL_FIELDS.EAR_TAG, item.id);
                setTimeout(() => setFieldTouched(ANIMAL_FIELDS.EAR_TAG, true));
              }}
              customLabelStyle={styles.customFieldLabel}
            />
          </View>
          {renderField()}
          <View style={styles.formInputView}>
            <NumberFormInput
              label={i18n.t('DIM')}
              unit={i18n.t('days')}
              placeholder={i18n.t('numberPlaceholder')}
              value={values[ANIMAL_FIELDS.DAYS_IN_MILK]?.toString() || ''}
              error={
                touched[ANIMAL_FIELDS.DAYS_IN_MILK] &&
                errors[ANIMAL_FIELDS.DAYS_IN_MILK]
              }
              minValue={-100}
              maxValue={999}
              isNegative={true}
              onChange={handleChange(ANIMAL_FIELDS.DAYS_IN_MILK)}
              onBlur={handleBlur(ANIMAL_FIELDS.DAYS_IN_MILK)}
              isInteger={true}
              keyboardType={
                Platform.OS === 'ios'
                  ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
                  : KEYBOARD_TYPE.NUMBER_PAD
              }
              inputAccessoryViewID="customInputAccessoryView"
              returnKeyType={NEXT_FIELD_TEXT.DONE}
              onFocus={() => {
                setType(CONTENT_TYPE.NUMBER);
                setAction({
                  dismiss: true,
                });
              }}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default AddAnimalForm;
