import fonts, { normalize } from '../../constants/theme/variables/customFont';
import colors from '../../constants/theme/variables/customColor';
import DeviceInfo from 'react-native-device-info';
import { Platform } from 'react-native';
import customFont from '../../constants/theme/variables/customFont';

export default {
  flexOne: {
    backgroundColor: colors.white,
    height: '14%',
    paddingTop: normalize(30),
  },
  row1: {
    flex: 1,
    flexDirection: 'row',
    // paddingTop: normalize(25),
    // paddingBottom: normalize(8),
    width: DeviceInfo.isTablet() ? '95%' : '90%',
    marginTop: DeviceInfo.isTablet() ? normalize(20) : 0,
    alignSelf: 'center',
    justifyContent: 'space-between',
  },
  row2: {
    flexDirection: 'row',
  },
  headerBottomBorder: {
    borderBottomWidth: normalize(1),
    borderBottomColor: colors.lightWhite,
  },
  pageSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  leftButtonView: {
    justifyContent: 'center',
    // paddingHorizontal: normalize(10),
    marginTop: normalize(2),
  },
  dropdownView: {
    paddingLeft: normalize(12),
    justifyContent: 'center',
    // paddingHorizontal: normalize(10),
    marginTop: normalize(2),
  },
  headerTitle: {
    justifyContent: 'center',
  },
  separateIconAndTitle: {
    marginLeft: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoIcon: { marginLeft: normalize(12) },
  titleText: {
    fontSize: normalize(16),
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.grey1,
  },
  iconsSections: {
    // flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // width: DeviceInfo.isTablet() ? normalize(80) : normalize(70),
  },
  verticalDotIconButton: {
    // paddingRight: normalize(5),
    paddingLeft: normalize(5),
    // marginLeft: normalize(5),
    justifyContent: 'center',
    height: '100%',
  },
  rightButtonView: {
    // paddingRight: normalize(20),
    paddingLeft: normalize(20),
    marginLeft: normalize(5),
    justifyContent: 'center',
    height: '100%',
  },
  subtitleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(10),
    lineHeight: normalize(15),
    color: colors.grey1,
    letterSpacing: 0.5,
    marginLeft: normalize(60),
    marginBottom: normalize(16),
  },
  grey1: colors.grey1,
  badgeContainer: {
    position: 'absolute',
    top: Platform.OS == 'ios' ? 15 : 20,
    right: -6,
    backgroundColor: colors.orangeColor,
    borderRadius: 10,
    minWidth: DeviceInfo.isTablet() ? 20 : 15,
    minHeight: DeviceInfo.isTablet() ? 20 : 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: DeviceInfo.isTablet() ? 10 : 8,
    fontWeight: 'bold',
  },
  customButtonStyle: {
    justifyContent: 'center',
    height: '100%',
  },
  customBtnTextStyle: {
    fontFamily: fonts.HelveticaNeueBold,
    color: colors.primaryMain,
    fontSize: normalize(14),
    // marginRight: normalize(20),
  },
  notificationContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    // marginHorizontal: 20,
    // paddingRight: 10,
    backgroundColor: 'white',
  },
  markAllReadText: {
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.grey1,
    fontSize: normalize(13),
    marginTop: normalize(3),
    // color:'red'
  },

  syncAgainBanner: {
    backgroundColor: colors.warning3,
    height: normalize(30),
    // height: normalize(30),
    justifyContent: 'center',
    alignItems: 'center',
    top: Platform.OS === 'android' ? normalize(18) : undefined,
  },
  syncAgainBannerText: {
    fontSize: normalize(12),
    lineHeight: normalize(24),
    textAlign: 'center',
  },
  warningView: {
    width: '100%',
    paddingVertical: normalize(7),
    backgroundColor: colors.warning5,
    alignItems: 'center',
  },
  warningViewText: {
    fontSize: normalize(12),
    textAlign: 'center',
    paddingHorizontal: normalize(12),
  },
  updateView: {
    width: '100%',
    paddingVertical: normalize(7),
    backgroundColor: colors.secondary4,
    alignItems: 'center',
  },
  updateViewText: {
    fontSize: normalize(12),
    textAlign: 'center',
    paddingHorizontal: normalize(12),
    color: colors.white,
    fontFamily: customFont.HelveticaNeueBold,
  },
  topbarHeight: {
    height: DeviceInfo.isTablet() ? '11%' : '14%',
  },
};
