import React, { useCallback } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect, useNavigation } from '@react-navigation/native';

//styles
import styles from './styles';
import { normalize } from '../../constants/theme/variables/customFont';
import colors from '../../constants/theme/variables/customColor';
import {
  BLACK_INFO_ICON,
  CHEVRON_DOWN_ICON,
  CHEVRON_LEFT_ICON,
  CROSS_ICON,
  NOTES_ICON,
  NOTIFICATION_BELL_ICON,
  RED_INFO_ICON,
} from '../../constants/AssetSVGConstants';

// reusable components
import VERTICAL_DOT_ICON from '../../components/common/VerticalDotIcon';

import { getNotificationsCountRequest } from '../../store/actions/notifications';

import ROUTE_CONSTANTS from '../../constants/RouteConstants';

import i18n from '../../localization/i18n';
import navigationService from '../../services/navigationService';

const TopBar = ({
  backButton,
  backButtonClick,
  crossIcon,
  crossClick,
  title,
  subtitle,
  infoIcon,
  onInfoIconClick,
  notificationIcon,
  notesIcon,
  onNotesClick,
  dropDown,
  dropDownClick,
  titleStyles,
  customHeaderStyle,
  showBottomBorder,
  verticalDotIcon,
  verticalDotClick,
  customButton,
  customClick,
  customLabel,
  customBtnDisabled,
  markAllRead,
  allRead,
  warningViewText,
  warningView = false,
  errorIcon,
  errorIconClick,
  customComponent = null,
}) => {
  //navigation
  const navigation = useNavigation();

  //api calling
  const dispatch = useDispatch();

  //redux states
  const notifications = useSelector(state => state.notifications);
  const dataSyncState = useSelector(state => state.dataSync);
  const update = useSelector(state => state.versioning);

  let { isSyncing, failure } = dataSyncState;
  //handlers
  const notificationClick = e => {
    navigation.navigate(ROUTE_CONSTANTS.NOTIFICATIONS);
  };

  useFocusEffect(
    useCallback(() => {
      dispatch(getNotificationsCountRequest());
    }, [dispatch]),
  );

  return (
    <SafeAreaView
      style={[
        styles.flexOne,
        customHeaderStyle,
        showBottomBorder ? styles.headerBottomBorder : null,
        navigationService.getCurrentNavigation() != ROUTE_CONSTANTS.DASHBOARD &&
        !isSyncing &&
        failure
          ? styles.topbarHeightSyncFailed
          : styles.topbarHeight,
        (warningView || update?.isUpdateAvailable) && {
          height: '20%',
        },
      ]}>
      {navigationService.getCurrentNavigation() != ROUTE_CONSTANTS.DASHBOARD &&
      !isSyncing &&
      failure ? (
        <View style={styles.syncAgainBanner}>
          <Text style={styles.syncAgainBannerText}>
            {i18n.t('syncAgainMsg')}
          </Text>
        </View>
      ) : null}
      {warningView && (
        <View style={styles.warningView}>
          <Text style={styles.warningViewText}>{warningViewText}</Text>
        </View>
      )}

      {update?.isUpdateAvailable && (
        <View style={styles.updateView}>
          <Text style={styles.updateViewText}>
            {i18n.t('buildReadyDescription')}
          </Text>
        </View>
      )}

      <View style={[styles.row1]}>
        <View style={styles.pageSection}>
          {backButton && (
            <TouchableOpacity
              onPress={backButtonClick}
              activeOpacity={1}
              style={styles.leftButtonView}>
              <CHEVRON_LEFT_ICON
                stroke={colors.grey1}
                strokeWidth={normalize(2)}
                width={normalize(24)}
                height={normalize(24)}
              />
            </TouchableOpacity>
          )}
          {crossIcon && (
            <TouchableOpacity
              onPress={crossClick}
              activeOpacity={1}
              style={styles.leftButtonView}>
              <CROSS_ICON
                stroke={colors.grey1}
                strokeWidth={styles.crossIconStrokeWidth}
                width={normalize(15)}
                height={normalize(15)}
              />
            </TouchableOpacity>
          )}

          <View
            style={[
              styles.headerTitle,
              (crossIcon || backButton) && styles.separateIconAndTitle,
            ]}>
            <View style={styles.titleContainer}>
              <Text
                onPress={() => dropDown && dropDownClick()}
                style={{ ...styles.titleText, ...titleStyles }}>
                {title}
              </Text>
              {infoIcon && (
                <TouchableOpacity
                  onPress={onInfoIconClick}
                  style={styles.infoIcon}>
                  <BLACK_INFO_ICON
                    fill={styles.grey1}
                    width={normalize(16)}
                    height={normalize(16)}
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {dropDown && (
            <TouchableOpacity
              onPress={dropDownClick}
              activeOpacity={1}
              style={styles.dropdownView}>
              <CHEVRON_DOWN_ICON
                // stroke={colors.grey1}
                strokeWidth={normalize(2)}
                width={normalize(15)}
                height={normalize(15)}
              />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.iconsSections}>
          {customComponent && customComponent}

          {errorIcon && (
            <TouchableOpacity
              activeOpacity={1}
              onPress={errorIconClick}
              style={styles.rightButtonView}>
              <RED_INFO_ICON
                width={normalize(20)}
                height={normalize(20)}
                fill={colors.error3}
              />
            </TouchableOpacity>
          )}
          {notificationIcon && (
            <TouchableOpacity
              activeOpacity={1}
              onPress={notificationClick}
              style={styles.rightButtonView}>
              <NOTIFICATION_BELL_ICON
                stroke={colors.grey1}
                strokeWidth={'0.3'}
                fill={colors.grey1}
                width={normalize(20)}
                height={normalize(20)}
              />
              {!!(notifications?.count > 0 || true) && (
                <View style={styles.badgeContainer}>
                  <Text style={styles.badgeText}>{notifications?.count}</Text>
                </View>
              )}
            </TouchableOpacity>
          )}
          {notesIcon && (
            <TouchableOpacity
              activeOpacity={1}
              onPress={onNotesClick}
              style={styles.rightButtonView}>
              <NOTES_ICON
                stroke={colors.primaryMain}
                strokeWidth={'0.3'}
                width={normalize(20)}
                height={normalize(20)}
              />
            </TouchableOpacity>
          )}
          {verticalDotIcon && (
            <TouchableOpacity
              activeOpacity={1}
              style={styles.verticalDotIconButton}
              onPress={verticalDotClick}>
              <VERTICAL_DOT_ICON selected={false} size={36} />
            </TouchableOpacity>
          )}
          {customButton && (
            <TouchableOpacity
              activeOpacity={1}
              onPress={customClick}
              disabled={customBtnDisabled}
              style={styles.customButtonStyle}>
              <Text style={styles.customBtnTextStyle}>{customLabel}</Text>
            </TouchableOpacity>
          )}
          {allRead && (
            <TouchableOpacity onPress={markAllRead}>
              {
                <View style={[styles.notificationContainer]}>
                  <Text style={styles.markAllReadText}>
                    {i18n.t('markRead')}
                  </Text>
                </View>
              }
            </TouchableOpacity>
          )}
        </View>
      </View>
      <View style={styles.row2}>
        {subtitle ? <Text style={styles.subtitleText}>{subtitle}</Text> : null}
      </View>
    </SafeAreaView>
  );
};
export default TopBar;
