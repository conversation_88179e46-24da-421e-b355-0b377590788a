import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';

export default {
  container: {
    flexDirection: 'column',
  },
  logoutIconContainer: {
    alignItems: 'center',
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(24),
    color: colors.grey1,
    alignSelf: 'center',
    marginHorizontal: normalize(35),
  },
  descriptionText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(16),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
    alignSelf: 'center',
    marginHorizontal: normalize(35),
    marginVertical: normalize(16),
  },
  exportLogIcon: {
    alignItems: 'center',
  },
};
