// modules
import React from 'react';
import { View } from 'react-native';
import { Text } from 'native-base';

// styles
import styles from './styles';

// common components
import BottomActionSheet from '../../common/BottomActionSheet';

const ExportLogAlert = props => {
  const { isOpen, onClose, onClick, data } = props;

  return (
    <BottomActionSheet isOpen={isOpen} onClose={onClose}>
      <View style={styles.container}>
        <View style={styles.exportLogIcon}>{data?.icon}</View>
        <Text style={styles.titleText}>{data?.heading}</Text>
        <Text style={styles.descriptionText}>{data?.description}</Text>
      </View>
    </BottomActionSheet>
  );
};

export default ExportLogAlert;
