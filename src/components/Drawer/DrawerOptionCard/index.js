import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Text } from 'native-base';

import styles from './styles';

const DrawerOptionCard = props => {
  let { name, icon, onPress, disabled, customTextStyle } = props;

  return (
    <TouchableOpacity
      style={styles.view}
      disabled={disabled}
      onPress={() => onPress()}>
      <View style={styles.firstColumn}>{icon}</View>
      <View style={styles.secondColumn}>
        <Text style={[styles.routeName, customTextStyle]}>{name}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default DrawerOptionCard;
