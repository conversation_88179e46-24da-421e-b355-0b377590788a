import DeviceInfo from 'react-native-device-info';
import customColor from '../../../constants/theme/variables/customColor';
import commonFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';

export default {
  view: {
    flexDirection: 'row',
    paddingVertical: normalize(13),
    width: '90%',
  },
  firstColumn: {
    width: DeviceInfo.isTablet() ? '15%' : '20%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  routeName: {
    lineHeight: normalize(15),
    fontSize: normalize(14),
    fontFamily: commonFont.HelveticaNeueMedium,
    color: customColor.grey1,
    paddingBottom: DeviceInfo.isTablet() ? normalize(0) : '1%',
  },
  selectedRouteName: {
    fontSize: normalize(14),
    fontFamily: commonFont.HelveticaNeueMedium,
    color: 'rgb(41,203,151)',
    paddingBottom: '1%',
  },
  icon: {
    fontSize: normalize(24),
    color: 'rgb(80,45,127)',
    paddingRight: normalize(10),
  },
  selectedIcon: {
    fontSize: normalize(26),
    color: 'rgb(41,203,151)',
    paddingRight: normalize(10),
  },
  secondColumn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
};
