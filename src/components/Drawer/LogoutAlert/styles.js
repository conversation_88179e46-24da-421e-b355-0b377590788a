import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';

export default {
  flexOne: { flex: 1 },
  container: {
    flexDirection: 'column',
    marginVertical: normalize(20),
    alignItems: 'center',
  },
  logoutIconContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    marginVertical: normalize(16),
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(24),
    color: colors.grey1,
    alignSelf: 'center',
    marginHorizontal: normalize(35),
    textAlign: 'center',
  },
  optionsContainer: {
    // maxWidth: 460,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: normalize(29),
    backgroundColor: 'pink',
    marginBottom: normalize(39),
    // marginHorizontal: normalize(20),
  },
  leftButtonsStyle: {
    width: normalize(158),
    height: normalize(52),
    marginHorizontal: normalize(5),
    alignSelf: 'flex-end',
    // flex: 1,
  },
  rightButtonsStyle: {
    width: normalize(158),
    height: normalize(52),
    marginHorizontal: normalize(5),
    alignSelf: 'flex-start',
    // flex: 1,
  },
  errorButtonsStyle: {
    backgroundColor: colors.error4,
  },
};
