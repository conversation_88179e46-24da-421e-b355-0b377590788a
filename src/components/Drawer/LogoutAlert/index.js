// modules
import React from 'react';
import { View } from 'react-native';
import { Text } from 'native-base';

// styles
import styles from './styles';

// common components
import BottomActionSheet from '../../common/BottomActionSheet';
import FormButton from '../../common/FormButton';

// translations
import i18n from '../../../localization/i18n';

import { BUTTON_TYPE } from '../../../constants/FormConstants';

const LogoutAlert = props => {
  const { isOpen, onClose, onClick, data } = props;

  return (
    <BottomActionSheet isOpen={isOpen} onClose={onClose}>
      <View style={styles.container}>
        <View style={styles.logoutIconContainer}>{data?.icon}</View>
        <Text style={styles.titleText}>{data?.description}</Text>
        <View style={styles.optionsContainer}>
          <View style={styles.flexOne}>
            <FormButton
              type={BUTTON_TYPE.SECONDARY}
              label={i18n.t('no')}
              onPress={onClose}
              customButtonStyle={styles.leftButtonsStyle}
            />
          </View>
          <View style={styles.flexOne}>
            <FormButton
              type={BUTTON_TYPE.PRIMARY}
              label={i18n.t('yes')}
              onPress={onClick}
              customButtonStyle={[
                styles.rightButtonsStyle,
                data?.error ? styles.errorButtonsStyle : {},
              ]}
            />
          </View>
        </View>
      </View>
    </BottomActionSheet>
  );
};

export default LogoutAlert;
