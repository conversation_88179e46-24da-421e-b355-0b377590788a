// modules
import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import CustomBottomSheet from '../../common/CustomBottomSheet';
import SelectedField from '../../common/SelectField';

// constants
import { BOTTOM_SHEET_TYPE } from '../../../constants/FormConstants';

// localization
import i18n from '../../../localization/i18n';

// styling constants
import customColor from '../../../constants/theme/variables/customColor';
import { normalize } from '../../../constants/theme/variables/customFont';

// models
import { toolNotebookTrailModel } from '../../../models/noteBook';

// actions
import { updateNoteBookTrail } from '../../../store/actions/notebook';

const ToolTrail = () => {
  const dispatch = useDispatch();

  const countryTools = useSelector(state => state.userPreferences.countryTools);
  const notebookTrailInfo = useSelector(
    state => state.noteBook?.notebookTrailInfo,
  );

  const [showToolSheet, setShowToolSheet] = useState(null);

  const _handleSelectTool = tool => {
    const model = toolNotebookTrailModel(tool);

    const payload = {
      ...notebookTrailInfo,
      tool: model,
    };

    dispatch(updateNoteBookTrail(payload));
  };

  return (
    <View style={styles.container}>
      <SelectedField
        disabled={false}
        label={i18n.t('filterToolsText')}
        value={notebookTrailInfo?.tool?.name}
        placeholder={i18n.t('selectOne')}
        handleSelect={() => {
          setShowToolSheet(true);
        }}
        customStyles={styles.customInputStyle}
        customLabelStyle={styles.customFieldLabel}
      />
      <CustomBottomSheet
        type={BOTTOM_SHEET_TYPE.CATEGORY_TOOLS}
        openBottomSheet={showToolSheet}
        hideBottomSheet={() => setShowToolSheet(false)}
        selectLabel={i18n.t('selectTool')}
        data={countryTools}
        onChange={_handleSelectTool}
        customBottomSheetSafeStyles={styles.customBottomSheetSafeStyles}
        customListStyles={styles.listStyles}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: normalize(20),
    marginVertical: normalize(10),
    justifyContent: 'center',
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: customColor.grey1,
    marginBottom: normalize(10),
  },
  customInputStyle: {
    backgroundColor: customColor.white,
  },
  customBottomSheetSafeStyles: {
    height: null,
  },
  listStyles: {
    height: '100%',
  },
});

export default ToolTrail;
