// modules
import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styling constants
import customColor from '../../../constants/theme/variables/customColor';
import { normalize } from '../../../constants/theme/variables/customFont';

// localization
import i18n from '../../../localization/i18n';

// components
import CustomBottomSheet from '../../common/CustomBottomSheet';

// constants
import { BOTTOM_SHEET_TYPE } from '../../../constants/FormConstants';

// actions
import { getAllAccountsRequest } from '../../../store/actions/accounts';
import { getAllAccountSitesRequest } from '../../../store/actions/site';
import { updateNoteBookTrail } from '../../../store/actions/notebook';

// models
import { accountNotebookTrailModel } from '../../../models/noteBook';

const AccountTrail = () => {
  const dispatch = useDispatch();

  const generalAccountState = useSelector(
    state => state.accounts.generalAccountState,
  );
  const notebookTrailInfo = useSelector(
    state => state.noteBook?.notebookTrailInfo,
  );

  useEffect(() => {
    dispatch(getAllAccountsRequest());
  }, []);

  const _handleSelectAccount = account => {
    const model = accountNotebookTrailModel(account);

    const payload = {
      ...notebookTrailInfo,
      account: model,
      site: null,
      visit: null,
    };

    dispatch(updateNoteBookTrail(payload));

    dispatch(getAllAccountSitesRequest(model));
  };

  return (
    <View style={styles.container}>
      <CustomBottomSheet
        data={generalAccountState.accounts}
        value={
          notebookTrailInfo?.account?.accountId ||
          notebookTrailInfo?.account?.localAccountId
        }
        onChange={_handleSelectAccount}
        type={BOTTOM_SHEET_TYPE.CUSTOMER_PROSPECT}
        label={i18n.t('accounts')}
        selectLabel={i18n.t('selectCustomerProspect')}
        placeholder={i18n.t('selectOne')}
        searchPlaceHolder={i18n.t('search')}
        isLoading={generalAccountState.isLoading}
        customLabelStyle={styles.customFieldLabel}
        customInputStyle={styles.customInputStyle}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: normalize(20),
    justifyContent: 'center',
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: customColor.grey1,
    marginBottom: normalize(10),
  },
  customInputStyle: {
    backgroundColor: customColor.white,
  },
});

export default AccountTrail;
