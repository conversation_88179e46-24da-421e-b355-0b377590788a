// modules
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import CustomBottomSheet from '../../common/CustomBottomSheet';

// constants
import { BOTTOM_SHEET_TYPE } from '../../../constants/FormConstants';

// localization
import i18n from '../../../localization/i18n';

// styling constants
import customColor from '../../../constants/theme/variables/customColor';
import { normalize } from '../../../constants/theme/variables/customFont';

// models
import { siteNotebookTrailModel } from '../../../models/noteBook';

// actions
import { updateNoteBookTrail } from '../../../store/actions/notebook';
import { getAllSiteVisitsRequest } from '../../../store/actions/visit';

const SiteTrail = () => {
  const dispatch = useDispatch();

  const generalSiteState = useSelector(state => state.site.generalSiteState);
  const notebookTrailInfo = useSelector(
    state => state.noteBook?.notebookTrailInfo,
  );

  const _handleSelectSite = site => {
    const model = siteNotebookTrailModel(site);

    const payload = {
      ...notebookTrailInfo,
      site: model,
      visit: null,
    };

    dispatch(updateNoteBookTrail(payload));

    dispatch(getAllSiteVisitsRequest(model));
  };

  return (
    <View style={styles.container}>
      <CustomBottomSheet
        data={
          notebookTrailInfo?.account?.accountId ||
          notebookTrailInfo?.account?.localAccountId
            ? generalSiteState.sites
            : []
        }
        value={
          notebookTrailInfo?.site?.siteId ||
          notebookTrailInfo?.site?.localSiteId
        }
        onChange={_handleSelectSite}
        type={BOTTOM_SHEET_TYPE.IMAGE_LIST_ITEM}
        label={i18n.t('site')}
        selectLabel={i18n.t('selectSite')}
        placeholder={i18n.t('selectOne')}
        searchPlaceHolder={i18n.t('search')}
        isLoading={generalSiteState.isLoading}
        customLabelStyle={styles.customFieldLabel}
        customInputStyle={styles.customInputStyle}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: normalize(20),
    marginVertical: normalize(10),
    justifyContent: 'center',
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: customColor.grey1,
    marginBottom: normalize(10),
  },
  customInputStyle: {
    backgroundColor: customColor.white,
  },
});

export default SiteTrail;
