// modules
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import CustomBottomSheet from '../../common/CustomBottomSheet';

// constants
import { BOTTOM_SHEET_TYPE } from '../../../constants/FormConstants';

// localization
import i18n from '../../../localization/i18n';

// styling constants
import { normalize } from '../../../constants/theme/variables/customFont';
import customColor from '../../../constants/theme/variables/customColor';

// models
import { visitNotebookTrailModel } from '../../../models/noteBook';

// actions
import { updateNoteBookTrail } from '../../../store/actions/notebook';

const VisitTrail = () => {
  const dispatch = useDispatch();

  const notebookTrailInfo = useSelector(
    state => state.noteBook?.notebookTrailInfo,
  );
  const generalVisitState = useSelector(state => state.visit.generalVisitState);

  const _handleSelectVisit = visit => {
    const model = visitNotebookTrailModel(visit);

    const payload = {
      ...notebookTrailInfo,
      visit: model,
    };

    dispatch(updateNoteBookTrail(payload));
  };

  return (
    <View style={styles.container}>
      <CustomBottomSheet
        data={
          notebookTrailInfo?.site?.siteId ||
          notebookTrailInfo?.site?.localSiteId
            ? generalVisitState.visits
            : []
        }
        value={
          notebookTrailInfo?.visit?.visitId ||
          notebookTrailInfo?.visit?.localVisitId
        }
        onChange={_handleSelectVisit}
        type={BOTTOM_SHEET_TYPE.IMAGE_LIST_ITEM}
        label={i18n.t('visitName')}
        selectLabel={i18n.t('selectVisit')}
        placeholder={i18n.t('selectOne')}
        searchPlaceHolder={i18n.t('search')}
        isLoading={generalVisitState.isLoading}
        customLabelStyle={styles.customFieldLabel}
        customInputStyle={styles.customInputStyle}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: normalize(20),
    marginVertical: normalize(10),
    justifyContent: 'center',
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: customColor.grey1,
    marginBottom: normalize(10),
    textTransform: 'capitalize',
  },
  customInputStyle: {
    backgroundColor: customColor.white,
  },
});

export default VisitTrail;
