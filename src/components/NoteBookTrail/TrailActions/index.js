// modules
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// components
import FormButton from '../../common/FormButton';

// constants
import { BUTTON_TYPE } from '../../../constants/FormConstants';

// localization
import i18n from '../../../localization/i18n';

// styling constants
import { normalize } from '../../../constants/theme/variables/customFont';
import customColor from '../../../constants/theme/variables/customColor';

// actions
import {
  addNewNoteBookTrailRequest,
  clearNoteBookTrail,
  clearTrailFromNoteBookData,
} from '../../../store/actions/notebook';

const TrailActions = () => {
  const { pop } = useNavigation();

  const dispatch = useDispatch();

  const notebookTrailInfo = useSelector(
    state => state.noteBook?.notebookTrailInfo,
  );
  const isNotebookTrailFormDirty = useSelector(
    state => state.noteBook?.isNotebookTrailFormDirty,
  );

  const _handleAddTrail = () => {
    dispatch(addNewNoteBookTrailRequest(notebookTrailInfo));
    pop();
  };

  const _handleRemoveTrail = () => {
    dispatch(clearNoteBookTrail());

    dispatch(clearTrailFromNoteBookData());
  };

  return (
    <>
      <FormButton
        type={BUTTON_TYPE.PRIMARY}
        label={i18n.t('continue')}
        onPress={_handleAddTrail}
        disabled={!notebookTrailInfo || !isNotebookTrailFormDirty}
      />

      <View style={styles.gap} />

      {notebookTrailInfo && (
        <FormButton
          type={BUTTON_TYPE.SECONDARY}
          label={i18n.t('reset')}
          onPress={_handleRemoveTrail}
          customButtonStyle={styles.customButtonStyle}
          disabled={!notebookTrailInfo}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  gap: {
    height: normalize(10),
  },
  customButtonStyle: {
    backgroundColor: customColor.transparent,
    maxWidth: normalize(200),
    alignSelf: 'center',
    height: normalize(40),
    borderWidth: 0,
  },
});

export default TrailActions;
