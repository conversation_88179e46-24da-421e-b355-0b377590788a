import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import { Dimensions } from 'react-native';
import DeviceInfo from 'react-native-device-info';
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default {
  cardContainerWhenItem: {
    width: SCREEN_WIDTH * 0.77,
    height: normalize(185),
    // justifyContent: 'center',
    padding: normalize(16),
    backgroundColor: colors.actionCardColor,
    borderRadius: normalize(12),
    marginLeft: normalize(20),
  },
  svgContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: normalize(16),
    // alignItems: 'center',
  },
  noteSvgWhenItem: { width: normalize(30), height: normalize(30) },
  actionHeading: {
    // width: '30%',
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    letterSpacing: 0.2,
    color: colors.actionHeadingColor,
    lineHeight: normalize(18),
    marginBottom: normalize(5),
  },
  actionTextWidth: { width: '100%' },
  bottomButtonContainer: {
    flexDirection: 'row',
    marginTop: normalize(0),
    position: 'absolute',
    bottom: normalize(16),
    left: normalize(16),
  },
  actionButtonContainer: {
    width: normalize(60),
    height: normalize(25),
    borderRadius: normalize(4),
    backgroundColor: colors.secondary2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonText: {
    fontFamily: customFont.RobotoBold,
    color: colors.white,
    fontSize: normalize(11),
    letterSpacing: 0.2,
  },
  timeButtonContainer: {
    flexDirection: 'row',
    width: normalize(130),
    height: normalize(25),
    borderRadius: normalize(4),
    backgroundColor: colors.actionTimeBackgroundColor,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: normalize(8),
  },
  timeButtonText: {
    fontFamily: customFont.RobotoMedium,
    color: colors.screenBackgroundColor3,
    fontSize: normalize(11),
    letterSpacing: 0.2,
    marginLeft: normalize(3),
  },

  cardContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: normalize(16),
    width: DeviceInfo.isTablet() ? '95%' : '90%', //carousel
    backgroundColor: colors.actionCardColor,
    borderRadius: normalize(12),
    alignSelf: 'center',
    // marginHorizontal: normalize(20), //carousel
    height: normalize(130),
  },
  noteSvg: { width: normalize(80), height: normalize(80) },
  marginLeft: { marginLeft: normalize(12), flex: 1 },
  actionSubtext: {
    // width: '32%',
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(12),
    // lineHeight: normalize(14),
    letterSpacing: 0.2,
    color: colors.actionHeadingColor,
  },
};
