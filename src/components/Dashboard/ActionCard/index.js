// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

//styles
import styles from './styles';
import { normalize } from '../../../constants/theme/variables/customFont';

//constants
import {
  CLOCK,
  DASHBOARD_NOTE,
  FAVOURITE_ICON,
  FILLED_FAVOURITE_ICON,
} from '../../../constants/AssetSVGConstants';
import colors from '../../../constants/theme/variables/customColor';

//helpers
import { renderVisitDate } from '../../../helpers/dashboardHelper';
import { truncateString } from '../../../helpers/alphaNumericHelper';

//localization
import i18n from '../../../localization/i18n';

export const ActionCard = props => {
  const { item, index, onPress = () => {} } = props;
  return (
    <TouchableOpacity
      style={styles.cardContainerWhenItem}
      onPress={onPress}
      activeOpacity={0.8}>
      <View style={styles.svgContainer}>
        <DASHBOARD_NOTE {...styles.noteSvgWhenItem} />
        {item?.favourite === 1 ? (
          <FILLED_FAVOURITE_ICON
            style={{ color: colors.favoriteColor }}
            width={normalize(24)}
            height={normalize(24)}
          />
        ) : (
          <FAVOURITE_ICON width={normalize(24)} height={normalize(24)} />
        )}
      </View>

      <View>
        <Text
          numberOfLines={1}
          style={[styles.actionHeading, styles.actionTextWidth]}
          ellipsizeMode="tail">
          {/* {item?.title} */}
          {item?.title || i18n.t('Untitled')}
        </Text>
        <Text
          numberOfLines={2}
          style={[styles.actionSubtext, styles.actionTextWidth]}>
          {truncateString(item?.note, 50)}
        </Text>
      </View>

      <View style={styles.bottomButtonContainer}>
        <View style={styles.actionButtonContainer}>
          <Text style={styles.actionButtonText}>{i18n.t('actionCapital')}</Text>
        </View>

        <View style={styles.timeButtonContainer}>
          <CLOCK />
          <Text style={styles.timeButtonText}>
            {item?.actionNotificationDateTimeUtc &&
              renderVisitDate(item?.actionNotificationDateTimeUtc)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export const EmptyActionCard = props => {
  return (
    <View style={styles.cardContainer}>
      <View>
        <DASHBOARD_NOTE {...styles.noteSvg} />
      </View>

      <View style={styles.marginLeft}>
        <Text style={[styles.actionHeading]}>
          {i18n.t('noUpcomingActions')}
        </Text>
        <Text style={[styles.actionSubtext]}>{i18n.t('noActionText')}</Text>
      </View>
    </View>
  );
};
