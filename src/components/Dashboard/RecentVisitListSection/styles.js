import { Dimensions } from 'react-native';
import DeviceInfo from 'react-native-device-info';
const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default {
  recentVisitCustomStyle: {
    width: SCREEN_WIDTH * 0.9,
  },
  listSeparatorStyle: {
    marginLeft: 20,
  },
  listHeaderStyle: {
    marginLeft: DeviceInfo.isTablet()
      ? SCREEN_WIDTH * 0.025
      : SCREEN_WIDTH * 0.05,
  },
  listFooterStyle: {
    marginRight: DeviceInfo.isTablet()
      ? SCREEN_WIDTH * 0.035
      : SCREEN_WIDTH * 0.07,
  },
};
