// modules
import React from 'react';
import { View, FlatList } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// localization
import i18n from '../../../localization/i18n';

// styles
import styles from './styles';

// components
import SectionLabel from '../SectionLabel';
import RecentVisitListItem from '../../Accounts/RecentVisitListItem';
import EmptyListComponent from '../EmptyListComponent';

// constants
import { NO_VISITS } from '../../../constants/AssetSVGConstants';
import ROUTE_CONSTANTS from '../../../constants/RouteConstants';

// helpers
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';

// actions
import { getSelectedVisitDashboardRequest } from '../../../store/actions/visit';

const RecentVisitListSection = () => {
  const { navigate } = useNavigation();
  const dispatch = useDispatch();

  const recentInProgressVisits = useSelector(
    state => state.dashboard.recentInProgressVisits,
  );

  const onViewAllRecentVisitsPress = () => {
    navigate(ROUTE_CONSTANTS.HISTORY_STACK, {});
  };

  const _handleRecentVisitItemPress = item => {
    if (stringIsEmpty(item?.accountTitle)) {
      dispatch(
        getSelectedVisitDashboardRequest({
          id: item.id,
          localId: item.localId,
        }),
      );
    }
  };

  const _renderRecentVisitListItem = ({ item }) => (
    <RecentVisitListItem
      item={item}
      shouldShowCustomer={true}
      onPress={_handleRecentVisitItemPress}
      customContainerStyle={styles.recentVisitCustomStyle}
    />
  );

  const _renderListEmptyComponent = () => (
    <EmptyListComponent IMAGE_SVG={NO_VISITS} text={i18n.t('noRecentVisits')} />
  );

  const _renderItemSeparator = () => <View style={styles.listSeparatorStyle} />;

  const _renderListHeader = () => <View style={styles.listHeaderStyle} />;

  const _renderListFooter = () => <View style={styles.listFooterStyle} />;

  return (
    <>
      <SectionLabel
        title={i18n.t('recentVisit')}
        onViewAllPress={onViewAllRecentVisitsPress}
      />

      <FlatList
        data={recentInProgressVisits}
        keyExtractor={(item, index) => 'visit_history_' + index}
        renderItem={_renderRecentVisitListItem}
        ListEmptyComponent={_renderListEmptyComponent}
        ItemSeparatorComponent={_renderItemSeparator}
        ListHeaderComponent={_renderListHeader}
        ListFooterComponent={_renderListFooter}
        numColumns={1}
        horizontal
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      />
    </>
  );
};

export default RecentVisitListSection;
