import { normalize } from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import { Dimensions } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default {
  carouselPaginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  carouselSelectedPaginationDot: {
    width: normalize(12),
    height: normalize(12),
    borderRadius: normalize(50),
    marginHorizontal: normalize(5),
    backgroundColor: colors.secondary2,
  },
  carouselUnselectedPaginationDot: {
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(50),
    marginHorizontal: normalize(5),
    backgroundColor: colors.grey3,
  },
  actionCardCarouselContainer: {
    marginTop: normalize(12),
    alignSelf: 'center',
    width: '98%',
  },
  actionCardCarouselStyle: { width: '100%' },
  carouselHeight: normalize(200),
  carouselWidth: SCREEN_WIDTH * 0.82,
};
