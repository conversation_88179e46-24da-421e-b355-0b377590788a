// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

//styles
import styles from './styles';

//localization
import i18n from '../../../localization/i18n';

// reusable components
import UserAvatar from '../../common/UserAvatar';

//helpers
import { getAvatarFallbackText } from '../../../helpers/genericHelper';
import { getImage } from '../../../helpers/dashboardHelper';

import { ACCOUNT_TYPE } from '../../../constants/AppConstants';

const ContactCard = props => {
  const { item, navigateToSites } = props;

  return (
    <TouchableOpacity
      style={styles.pinnedContactContainer}
      onPress={() => navigateToSites(item)}>
      <View style={styles.pinnedContactSubContainer(item?.accountType)}>
        {/* <Image style={styles.pinnedContactImage} /> */}
        <View style={styles.avatarView}>
          <UserAvatar
            imageUri={getImage(item)}
            size={42}
            fallback={getAvatarFallbackText(item?.value)}
          />
        </View>
        <View style={{ alignItems: 'center' }}>
          <Text
            style={styles.pinnedContactName}
            numberOfLines={1}
            ellipsizeMode="middle">
            {item?.value?.split(' ').slice(0, -1).join(' ') || ''}
          </Text>
          <Text
            style={styles.pinnedContactName}
            numberOfLines={1}
            ellipsizeMode="middle">
            {item?.value?.split(' ').slice(-1).join(' ') || ''}
          </Text>
          <View style={styles.pinnedContactRoleView(item?.accountType)}>
            <Text style={styles.pinnedContactRole}>
              {item?.accountType === ACCOUNT_TYPE.CUSTOMER
                ? i18n.t('customer')
                : i18n.t('prospect')}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ContactCard;
