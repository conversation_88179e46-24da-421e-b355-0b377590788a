import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import { ACCOUNT_TYPE } from '../../../constants/AppConstants';

export default {
  pinnedContactContainer: {
    marginTop: normalize(14),
    // paddingLeft: normalize(20),
  },
  pinnedContactSubContainer: accountType => ({
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: normalize(130),
    minHeight: normalize(100),
    backgroundColor:
      accountType === ACCOUNT_TYPE.CUSTOMER
        ? colors.customerColor
        : colors.prospectColor,
    padding: normalize(10),
    borderRadius: normalize(6),
    borderColor: colors.white,
    borderWidth: normalize(5),
  }),
  avatarView: {
    marginBottom: normalize(8),
    justifyContent: 'center',
  },
  pinnedContactName: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    letterSpacing: 0.5,
    color: colors.grey1,
    textAlign: 'center',
  },
  pinnedContactRoleView: accountType => ({
    marginTop: normalize(8),
    backgroundColor:
      accountType === ACCOUNT_TYPE.CUSTOMER
        ? colors.customerTagColor
        : colors.prospectTagColor,
    minWidth: normalize(35),
    paddingHorizontal: normalize(5),
    paddingVertical: normalize(2),
    borderRadius: normalize(4),
  }),
  pinnedContactRole: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    letterSpacing: 0.5,
    color: colors.white,
  },
};
