import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import DeviceInfo from 'react-native-device-info';

export default {
  newRowItemHeadings: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: normalize(24),
    // paddingHorizontal: normalize(20),
    alignItems: 'center',
    width: DeviceInfo.isTablet() ? '95%' : '90%',
    alignSelf: 'center',
  },
  viewAllText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    letterSpacing: 0.5,
    color: colors.syncButtonColor,
  },
  componentDescription: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(12),
    letterSpacing: 0.4,
    color: colors.alphabetIndex,
  },
};
