// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

//styles
import styles from './styles';

//localization
import i18n from '../../../localization/i18n';

const SectionLabel = props => {
  const { title, onViewAllPress } = props;
  return (
    <>
      <View style={styles.newRowItemHeadings}>
        <Text style={styles.componentDescription}>{title}</Text>
        <TouchableOpacity style={{}} onPress={onViewAllPress}>
          <Text style={styles.viewAllText}>{i18n.t('viewAll')}</Text>
        </TouchableOpacity>
      </View>
    </>
  );
};

export default SectionLabel;
