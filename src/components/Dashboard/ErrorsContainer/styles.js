import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';

export default {
  container: {
    flexDirection: 'column',
  },
  modalStyle: {
    margin: 0,
  },
  bottomSheetSafeArea: {
    width: '100%',
    height: normalize(512),
    backgroundColor: colors.white,
    bottom: normalize(0),
    position: 'absolute',
    borderTopRightRadius: normalize(30),
    borderTopLeftRadius: normalize(30),
  },
  topDraggerBarParent: {
    justifyContent: 'center',
    alignSelf: 'center',
    height: normalize(30),
  },
  topDraggerBar: {
    height: normalize(7),
    width: normalize(45),
    borderRadius: normalize(40),
    backgroundColor: colors.grey3,
    marginTop: normalize(3),
    marginBottom: normalize(3),
  },
  infoIconContainer: {
    alignItems: 'center',
    marginTop: normalize(8),
  },
  redIconCircle: {
    width: normalize(48),
    height: normalize(48),
    backgroundColor: colors.favoriteColor,
    borderColor: colors.syncErrorIconBorderColor,
    borderWidth: normalize(2),
    borderRadius: normalize(24),
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(24),
    textAlign: 'center',
    color: colors.grey1,
    marginTop: normalize(12),
    marginBottom: normalize(20),
  },
  flatlist: {
    width: '100%',
    height: '70%',
    paddingHorizontal: normalize(24),
  },
  flatlistContainer: {
    width: '100%',
  },
  itemContainer: {
    backgroundColor: colors.grey23,
    marginBottom: normalize(8),
    borderRadius: normalize(8),
    paddingHorizontal: normalize(12),
  },
  itemText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(15),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.grey1,
    marginVertical: normalize(12),
  },
  emptyListContainer: {
    flex: 1,
    alignItems: 'center',
    marginTop: normalize(40),
  },
  infoIconStyles: {
    width: normalize(24),
    height: normalize(24),
    fill: colors.whiteInfoIconColor
  },
};
