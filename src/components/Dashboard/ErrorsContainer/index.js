// modules
import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  View,
  Text,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import Modal from 'react-native-modal';

// localization
import i18n from '../../../localization/i18n';

// styles
import styles from './styles';

// constants
import { RED_INFO_ICON } from '../../../constants/AssetSVGConstants';

const ErrorsContainer = props => {
  const { showContainer = false, data, onClose, openEditScreen } = props;
  const [list, setList] = useState(data);

  useEffect(() => {
    setList(data);
  }, [data]);

  const handleCancel = () => {
    onClose();
  };

  const renderItem = ({ item }) => {
    return (
      <TouchableOpacity
        key={item.id}
        onPress={() => {
          openEditScreen(item);
          handleCancel();
        }}>
        <View style={styles.itemContainer}>
          <Text style={styles.itemText}>
            {item.accountName + ': '}{item.message}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <View style={styles.container}>
        <SafeAreaView>
          <Modal
            testID={'dashboard-error-container'}
            isVisible={showContainer}
            style={styles.modalStyle}
            onBackdropPress={handleCancel}
            onBackButtonPress={handleCancel}
            hasBackdrop={true}
            avoidKeyboard={true}>
            <SafeAreaView style={styles.bottomSheetSafeArea}>
              <View style={styles.topDraggerBarParent}>
                <View style={styles.topDraggerBar} />
              </View>
              <View style={styles.infoIconContainer}>
                <View style={styles.redIconCircle}>
                  <RED_INFO_ICON {...styles.infoIconStyles} />
                </View>
              </View>

              <Text style={styles.titleText}>{i18n.t('syncErrors')}</Text>

              <View>
                {list.length > 0 && (
                  <FlatList
                    style={styles.flatlist}
                    contentContainerStyle={styles.flatlistContainer}
                    data={list.length > 0 ? list : []}
                    showsVerticalScrollIndicator={false}
                    renderItem={renderItem}
                  />
                )}
              </View>
              {list.length == 0 && (
                <View style={styles.emptyListContainer}>
                  <Text>{i18n.t('noSyncErrors')}</Text>
                </View>
              )}
            </SafeAreaView>
          </Modal>
        </SafeAreaView>
      </View>
    </>
  );
};

export default ErrorsContainer;
