import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import { Dimensions } from 'react-native';
import DeviceInfo from 'react-native-device-info';
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default {
  emptyListContainer: {
    marginTop: normalize(14),
    width: DeviceInfo.isTablet() ? SCREEN_WIDTH * 0.95 : SCREEN_WIDTH * 0.9,
    alignSelf: 'center',
  },
  emptyListSubContainer: {
    height: normalize(160),
    backgroundColor: colors.white,
    // marginHorizontal: normalize(20),
    borderRadius: normalize(12),
  },
  emptyListContentAlignment: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  emptyListText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    letterSpacing: 0.5,
    color: colors.alphabetIndex,
    marginVertical: normalize(12),
  },
};
