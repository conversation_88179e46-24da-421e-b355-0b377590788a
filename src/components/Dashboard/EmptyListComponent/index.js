// modules
import React from 'react';
import { View, Text } from 'react-native';

//styles
import styles from './styles';
import { normalize } from '../../../constants/theme/variables/customFont';

const EmptyListComponent = ({ IMAGE_SVG, text }) => {
  return (
    <View style={styles.emptyListContainer}>
      <View style={styles.emptyListSubContainer}>
        <View style={styles.emptyListContentAlignment}>
          <IMAGE_SVG width={normalize(85)} height={normalize(72)} />
          <Text style={styles.emptyListText}>{text}</Text>
        </View>
      </View>
    </View>
  );
};

export default EmptyListComponent;
