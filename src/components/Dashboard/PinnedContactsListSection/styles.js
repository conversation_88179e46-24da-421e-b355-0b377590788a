import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import { Dimensions } from 'react-native';
import DeviceInfo from 'react-native-device-info';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default {
  componentDescription: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(12),
    letterSpacing: 0.4,
    color: colors.alphabetIndex,
  },
  pinnedContactPlacement: {
    marginTop: normalize(22),
    width: DeviceInfo.isTablet() ? '95%' : '90%',
    alignSelf: 'center',
  },
  listSeparatorStyle: {
    marginLeft: 20,
  },
  listHeaderStyle: {
    marginLeft: DeviceInfo.isTablet()
      ? SCREEN_WIDTH * 0.025
      : SCREEN_WIDTH * 0.05,
  },
  listFooterStyle: {
    marginRight: DeviceInfo.isTablet()
      ? SCREEN_WIDTH * 0.035
      : SCREEN_WIDTH * 0.07,
  },
  fullWidth: { width: '100%' },
};
