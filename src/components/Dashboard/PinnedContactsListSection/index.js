// modules
import React from 'react';
import { View, FlatList } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// localization
import i18n from '../../../localization/i18n';

// styles
import styles from './styles';

// components
import EmptyListComponent from '../EmptyListComponent';
import ContactCard from '../ContactCard';
import SectionLabel from '../SectionLabel';

// constants
import { NO_PENS_ICON } from '../../../constants/AssetSVGConstants';
import ROUTE_CONSTANTS from '../../../constants/RouteConstants';
import { ACCOUNT_TYPE } from '../../../constants/AppConstants';

import { getAccountRequest } from '../../../store/actions/accounts';

import { accountFilterModel } from '../../../models/account';

const PinnedContactsListSection = () => {
  const { navigate } = useNavigation();
  const dispatch = useDispatch();

  const pinnedContacts = useSelector(state => state.dashboard.pinnedContacts);

  const onViewAllPress = () => {
    dispatch(
      getAccountRequest(
        accountFilterModel('', 3, { startDate: '', endDate: '' }, 1),
      ),
    );
    navigate(ROUTE_CONSTANTS.ACCOUNTS_STACK, {
      selectedTab: 3,
    });
  };

  const navigateToSites = data => {
    const navigationPayload = {
      id: data.localId,
      data: data,
      type:
        data.accountType === ACCOUNT_TYPE.PROSPECT
          ? ACCOUNT_TYPE.PROSPECT
          : ACCOUNT_TYPE.CUSTOMER,
    };

    navigate(ROUTE_CONSTANTS.SITES, navigationPayload);
  };

  const _renderContactItem = ({ item, index }) => (
    <ContactCard item={item} index={index} navigateToSites={navigateToSites} />
  );

  const _renderItemSeparator = () => <View style={styles.listSeparatorStyle} />;

  const _renderListHeader = () => <View style={styles.listHeaderStyle} />;

  const _renderListFooter = () => <View style={styles.listFooterStyle} />;

  const _renderEmptyListComponent = () => (
    <EmptyListComponent
      IMAGE_SVG={NO_PENS_ICON}
      text={i18n.t('noPinnedContacts')}
    />
  );

  return (
    <>
      <SectionLabel
        title={i18n.t('pinnedContacts')}
        onViewAllPress={onViewAllPress}
      />

      <View style={styles.fullWidth}>
        <FlatList
          data={pinnedContacts}
          horizontal
          keyExtractor={(item, index) => 'pinned_contact_' + index}
          showsHorizontalScrollIndicator={false}
          renderItem={_renderContactItem}
          ListEmptyComponent={_renderEmptyListComponent}
          ItemSeparatorComponent={_renderItemSeparator}
          ListHeaderComponent={_renderListHeader}
          ListFooterComponent={_renderListFooter}
        />
      </View>
    </>
  );
};

export default PinnedContactsListSection;
