// modules
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// components
import TopBar from '../../TopBar';
import ErrorsContainer from '../ErrorsContainer';
import CustomTopBarSyncingLoader from '../../CustomTopBarSyncingLoader';

// localization
import i18n from '../../../localization/i18n';

// styles
import styles from './styles';

// constants
import ROUTE_CONSTANTS from '../../../constants/RouteConstants';
import { ACCOUNT_TYPE, ENTITY_TYPE } from '../../../constants/AppConstants';

// actions
import { getCustomerByLocalOrServerIdRequest } from '../../../store/actions/customer';
import { getProspectByLocalOrServerIdRequest } from '../../../store/actions/prospect';
import { getSelectedVisitRequest } from '../../../store/actions/visit';

// helpers
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';

const DashboardTopBar = () => {
  const { navigate } = useNavigation();
  const dispatch = useDispatch();

  const syncErrors = useSelector(state => state.dashboard.syncErrors);

  const [showErrorsBottomSheet, setShowErrorsBottomSheet] = useState(false);

  const openErrorsBottomSheet = () => {
    setShowErrorsBottomSheet(true);
  };

  const closeErrorsBottomSheet = () => {
    setShowErrorsBottomSheet(false);
  };

  const navigateToNoteBook = () => navigate?.(ROUTE_CONSTANTS.NOTE_BOOK_LIST);

  const openEditScreen = item => {
    const { entityType } = item;

    if (
      entityType === ENTITY_TYPE.CUSTOMER ||
      entityType === ENTITY_TYPE.PROSPECT
    ) {
      const routingParams = {
        id: item.localId,
        update: true,
        type: entityType,
      };

      navigate(ROUTE_CONSTANTS.ADD_EDIT_ACCOUNT, routingParams);
    } else if (entityType === ENTITY_TYPE.SITE) {
      const model = {
        id: item.accountId,
        localId: item.localAccountId,
      };

      if (item.type === ACCOUNT_TYPE.CUSTOMER) {
        dispatch(getCustomerByLocalOrServerIdRequest(model));
      } else {
        dispatch(getProspectByLocalOrServerIdRequest(model));
      }

      const siteDataPayload = {
        type: item.type,
        id: item.localId,
        update: true,
        accountId: item.accountId,
        localAccountId: item.localAccountId,
      };

      navigate?.(ROUTE_CONSTANTS.ADD_UPDATE_SITE, siteDataPayload);
    } else {
      if (stringIsEmpty(item?.accountTitle)) {
        dispatch(
          getSelectedVisitRequest({ id: item.id, localId: item.localId }),
        );
      }
    }
  };

  return (
    <>
      <TopBar
        notificationIcon
        title={i18n.t('home')}
        notesIcon
        onNotesClick={navigateToNoteBook}
        customHeaderStyle={styles.headerStyle}
        titleStyles={styles.titleText}
        errorIcon={syncErrors.length > 0}
        errorIconClick={openErrorsBottomSheet}
        customComponent={<CustomTopBarSyncingLoader />}
      />

      <ErrorsContainer
        showContainer={showErrorsBottomSheet}
        data={syncErrors}
        onClose={closeErrorsBottomSheet}
        openEditScreen={openEditScreen}
      />
    </>
  );
};

export default DashboardTopBar;
