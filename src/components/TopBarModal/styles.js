import fonts, { normalize } from '../../constants/theme/variables/customFont';
import colors from '../../constants/theme/variables/customColor';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';

export default {
  container: {
    backgroundColor: colors.white,
    zIndex: 2,
    top:
      Platform.OS == 'ios'
        ? DeviceInfo.isTablet()
          ? normalize(50)
          : normalize(80)
        : normalize(30),

    borderWidth: normalize(1),
    borderColor: colors.grey10,
    elevation: 2,
    borderRadius: normalize(4),
    position: 'absolute',
    paddingVertical: normalize(5),
    // width: '55%',
    shadowColor: colors.grey2,
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.36,
    shadowRadius: 6.68,
    elevation: 11,
  },
  button: {
    paddingLeft: normalize(5),
    paddingVertical: normalize(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: normalize(10),
    alignItems: 'center',
    width: '100%',
  },
  text: {
    fontSize: normalize(14),
    paddingLeft: normalize(10),
    paddingRight: normalize(10),
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '400',
    color: colors.grey1,
  },
};
