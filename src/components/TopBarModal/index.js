import React from 'react';
import Modal from 'react-native-modal';
import { TouchableOpacity, Text, View } from 'react-native';

//styles
import styles from './styles';
import colors from '../../constants/theme/variables/customColor';

//language
import i18n from '../../localization/i18n';

//constants
import { TICK_ICON } from '../../constants/AssetSVGConstants';
import { ACCOUNT_TYPE } from '../../constants/AppConstants';

const TopbarModal = ({ isVisible, cancelModal, selected, onPress }) => {
  return (
    <Modal
      testID={'modal'}
      isVisible={isVisible}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      onBackdropPress={cancelModal}
      onBackButtonPress={cancelModal}
      animationInTiming={10}
      animationOutTiming={10}
      hasBackdrop={true}
      backdropColor={colors.white}
      backdropOpacity={0.1}
      avoidKeyboard={true}>
      <View style={styles.container}>
        {selected !== ACCOUNT_TYPE.CUSTOMER && (
          <TouchableOpacity
            onPress={selected === ACCOUNT_TYPE.CUSTOMER ? cancelModal : onPress}
            style={styles.button}>
            <Text style={styles.text}>
              {i18n.t('switchTo')} {i18n.t('customers')}
            </Text>
            {selected === ACCOUNT_TYPE.CUSTOMER && <TICK_ICON />}
          </TouchableOpacity>
        )}
        {selected !== ACCOUNT_TYPE.PROSPECT && (
          <TouchableOpacity
            style={styles.button}
            onPress={
              selected === ACCOUNT_TYPE.PROSPECT ? cancelModal : onPress
            }>
            <Text style={styles.text}>
              {' '}
              {i18n.t('switchTo')} {i18n.t('prospects')}
            </Text>
            {selected === ACCOUNT_TYPE.PROSPECT && <TICK_ICON />}
          </TouchableOpacity>
        )}
      </View>
    </Modal>
  );
};

export default TopbarModal;
