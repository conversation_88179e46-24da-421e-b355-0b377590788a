import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';

export default {
  flexOne: { flex: 1 },
  container: {
    flexDirection: 'column',
    marginBottom: normalize(20),
    alignItems: 'center',
  },
  iconContainer: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  titleText: {
    fontFamily: fonts.HelveticaNeueMedium,
    fontWeight: '500',
    fontSize: normalize(16),
    lineHeight: normalize(24),
    color: colors.grey1,
    alignSelf: 'center',
    marginHorizontal: normalize(35),
    textAlign: 'center',
    marginBottom: normalize(10),
  },
  subTitleText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    color: colors.grey1,
    alignSelf: 'center',
    marginHorizontal: normalize(35),
    textAlign: 'center',
  },
  optionsContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: normalize(29),
    backgroundColor: 'pink',
    marginBottom: normalize(39),
  },
  leftButtonsStyle: {
    width: normalize(158),
    height: normalize(52),
    marginHorizontal: normalize(5),
    alignSelf: 'flex-end',
  },
  rightButtonsStyle: {
    width: normalize(158),
    height: normalize(52),
    marginHorizontal: normalize(5),
    alignSelf: 'flex-start',
  },
  errorButtonsStyle: {
    backgroundColor: colors.error4,
  },
};
