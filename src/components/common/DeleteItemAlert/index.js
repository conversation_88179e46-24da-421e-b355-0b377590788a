// modules
import React from 'react';
import { View } from 'react-native';
import { Text } from 'native-base';

// styles
import styles from './styles';

// common components
import BottomActionSheet from '../../common/BottomActionSheet';
import FormButton from '../../common/FormButton';

// translations
import i18n from '../../../localization/i18n';

import { BUTTON_TYPE } from '../../../constants/FormConstants';
import { DELETE_ALERT_ICON } from '../../../constants/AssetSVGConstants';
import { normalize } from '../../../constants/theme/variables/customFont';

const DeleteItemAlert = props => {
  const { isOpen, onClose, onClick } = props;

  return (
    <BottomActionSheet isOpen={isOpen} onClose={onClose}>
      <View style={styles.container}>
        <View style={styles.iconContainer}>
          <DELETE_ALERT_ICON width={normalize(100)} height={normalize(100)} />
        </View>
        <Text style={styles.titleText}>{i18n.t('permanentDelete')}</Text>
        <Text style={styles.subTitleText}>{i18n.t('deleteConfirmation')}</Text>
        <View style={styles.optionsContainer}>
          <View style={styles.flexOne}>
            <FormButton
              type={BUTTON_TYPE.SECONDARY}
              label={i18n.t('cancel')}
              onPress={onClose}
              customButtonStyle={styles.leftButtonsStyle}
            />
          </View>
          <View style={styles.flexOne}>
            <FormButton
              type={BUTTON_TYPE.PRIMARY}
              label={i18n.t('delete')}
              onPress={onClick}
              customButtonStyle={[
                styles.rightButtonsStyle,
                styles.errorButtonsStyle,
              ]}
            />
          </View>
        </View>
      </View>
    </BottomActionSheet>
  );
};

export default DeleteItemAlert;
