// modules
import React from 'react';
import { View } from 'react-native';
import { Text, Input } from 'native-base';
import { getNumberFormatSettings } from 'react-native-localize';

// styles
import styles from './styles';

// translations
import i18n from '../../../localization/i18n';

// constants
import colors from '../../../constants/theme/variables/customColor';
import { INPUT_TYPE, KEYBOARD_TYPE } from '../../../constants/FormConstants';
import { NUMBER_DEFAULT_MAX_LENGTH } from '../../../constants/AppConstants';

// helpers
import {
  checkIntegerAndDecimalValidation,
  countCharacters,
  removeCurrencyFromInput,
  removeNumberCommas,
  removeStringCommas,
  validateNumber,
} from '../../../helpers/alphaNumericHelper';

const NumberFormInputEn = props => {
  const {
    label,
    unit,
    keyboardType,
    placeholder,
    disabled,
    maxLength,
    required,
    isInteger,
    minValue,
    maxValue,
    reference,
    onSubmitEditing,
    skipBlurValidation,
    decimalPoints,
    returnKeyType,
    textAlign,
    isEditable,
    hideLabel,
    hideUnit,
    hasCommas,
    currency,
    blurOnSubmit,
    onContentSizeChange,
    inputAccessoryViewID,
    isNegative,
    selectTextOnFocus = true,
  } = props;
  const {
    customContainerStyle,
    customLabelStyle,
    customInputStyle,
    customInputContainerStyle,
    customUnitStyle,
  } = props;
  const { value, error, onChange, onBlur, onFocus } = props;

  // added extra param for the case forcefully called onBlur function
  const { forceOnBlur } = props;

  const onValueChange = v => {
    const regionSettings = getNumberFormatSettings();

    const isValid = checkIntegerAndDecimalValidation(
      v === '' || v === null ? '0' : v,
      isInteger,
      minValue,
      maxValue,
      decimalPoints,
      hasCommas,
      currency,
      isNegative,
      regionSettings,
    );
    if (isValid === true) {
      if (currency) {
        v = removeCurrencyFromInput(v, currency);
      }
      if (onChange) {
        if (hasCommas) {
          v = removeNumberCommas(v, isInteger);
          v = v?.toString() || '';
        }

        if (v?.length > 1 && countCharacters(v, '0') == v?.length) {
          return;
        }

        onChange(v);
      }
    }
  };

  const validateNumberInputOnBlur = e => {
    if (value != '' && value != null) {
      let tempVal = value;
      if (currency) {
        tempVal = removeCurrencyFromInput(tempVal, currency);
      }
      if (hasCommas) {
        tempVal = removeStringCommas(tempVal);
      }

      let isValid = validateNumber(tempVal, isInteger);
      if (isValid === true) {
        onBlur && onBlur(e);
      } else if (skipBlurValidation) {
        onBlur && onBlur(e);
      } else {
        onChange && onChange('');
      }
    } else if (skipBlurValidation) {
      onBlur && onBlur(e);
    }

    // extra param to give onBlur if value is null or empty
    if (forceOnBlur) {
      onBlur && onBlur(e);
    }
  };

  const renderNewRequiredDesign = () => {
    return (
      <>
        <Text style={[styles.labelNew, customLabelStyle]}>{label}</Text>
        {required && (
          <Text style={styles.starSignNewStyle}>{i18n.t('starSign')}</Text>
        )}
      </>
    );
  };

  return (
    <View style={[styles.container, customContainerStyle]}>
      {!hideLabel && (
        <View style={styles.labelContainerStyle}>
          {renderNewRequiredDesign()}
        </View>
      )}
      <View style={styles.inputRow}>
        <View
          style={[
            error ? styles.errorInputContainer : styles.inputContainer,
            disabled && styles.disabledInputStyle,
            customInputContainerStyle,
          ]}>
          <Input
            autoCapitalize="words"
            selectTextOnFocus={selectTextOnFocus}
            keyboardType={keyboardType || KEYBOARD_TYPE.NUMBER}
            variant="unstyled"
            ref={reference}
            isDisabled={disabled}
            maxLength={maxLength || NUMBER_DEFAULT_MAX_LENGTH}
            type={INPUT_TYPE.TEXT}
            placeholder={placeholder}
            placeholderTextColor={colors.grey2}
            value={currency ? currency + value : value}
            returnKeyType={returnKeyType || 'done'}
            onChangeText={onValueChange}
            onBlur={e => {
              e.persist = () => {};
              validateNumberInputOnBlur(e);
            }}
            onFocus={onFocus}
            style={[styles.inputStyle, customInputStyle]}
            textAlign={textAlign || 'center'}
            onSubmitEditing={() => {
              onSubmitEditing?.();
            }}
            onContentSizeChange={onContentSizeChange}
            blurOnSubmit={blurOnSubmit}
            // isReadOnly={!isEditable}
            inputAccessoryViewID={inputAccessoryViewID}
          />
        </View>
        {!hideUnit && (
          <Text style={[styles.unitTextStyle, customUnitStyle]}>{unit}</Text>
        )}
      </View>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

export default NumberFormInputEn;
