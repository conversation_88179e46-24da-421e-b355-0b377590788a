import React, { useEffect, useState } from 'react';

import { View, Text, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// localization
import i18n from '../../localization/i18n';

// reusable components
import RadioButton from '../../components/common/RadioButton';

//constants
import { CHEVRON_RIGHT_ICON } from '../../constants/AssetSVGConstants';
import { normalize } from '../../constants/theme/variables/customFont';
import colors from '../../constants/theme/variables/customColor';
import ROUTE_CONSTANTS from '../../constants/RouteConstants';

// helper
import {
  separateStringIntoCamelCase,
  stringIsEmpty,
} from '../../helpers/alphaNumericHelper';

// actions
import { updateUserPreferencesRequest } from '../../store/actions/userPreferences';
import { getEnumRequest } from '../../store/actions/enum';

const AppSettingList = props => {
  const { navigation } = props;
  const dispatch = useDispatch();

  const userData = useSelector(
    state => state?.userPreferences?.userPreferences,
  );
  let { bcsPointScale, unitOfMeasureKeys } = useSelector(
    state => state?.enums?.simpleEnum,
  );
  let { brandList } = useSelector(state => state?.enums?.enum);

  const [bcsPointScaling, setBcsPointScaling] = useState([]);
  const [brandListing, setBrandListing] = useState([]);

  useEffect(() => {
    if (!stringIsEmpty(bcsPointScale)) {
      let localBcsPointScale = bcsPointScale?.map(a => {
        let value = separateStringIntoCamelCase(a);
        return {
          key: a,
          value: value,
        };
      });
      setBcsPointScaling(localBcsPointScale);
    }
  }, [bcsPointScale]);

  useEffect(() => {
    if (!stringIsEmpty(brandList)) {
      let localBrandListing = brandList.map(a => {
        let value = separateStringIntoCamelCase(a?.value);
        return {
          key: a?.key,
          value: a?.value,
        };
      });
      setBrandListing(localBrandListing);
    }
  }, [brandList]);

  useEffect(() => {
    dispatch(getEnumRequest());
  }, []);

  const checkUnitOfMeasure = item => {
    if (!stringIsEmpty(userData?.unitOfMeasure)) {
      if (userData?.unitOfMeasure == item) {
        return true;
      } else return false;
    }
    return false;
  };

  const setUnitOfMeasure = item => {
    let selectedPreferences = { ...userData };
    selectedPreferences.unitOfMeasure = item;
    updateUserPreferences(selectedPreferences);
  };

  const checkBodyConditionScoreScale = item => {
    if (!stringIsEmpty(userData?.bcsPointScale)) {
      if (userData?.bcsPointScale == item) {
        return true;
      } else return false;
    }
    return false;
  };

  const setBodyConditionScoreScale = item => {
    let selectedPreferences = { ...userData };
    selectedPreferences.bcsPointScale = item;
    updateUserPreferences(selectedPreferences);
  };

  const checkBrandPlaceholder = item => {
    if (!stringIsEmpty(userData?.brandList)) {
      if (userData?.brandList.includes(item)) {
        return true;
      } else return false;
    }
    return false;
  };

  const setBrandPlaceholder = item => {
    let selectedPreferences = { ...userData };
    selectedPreferences.brandList = [];
    selectedPreferences.brandList = [item];
    updateUserPreferences(selectedPreferences);
  };

  const setCurrencySelected = item => {
    navigation.goBack();
    let selectedPreferences = { ...userData };
    selectedPreferences.selectedCurrency = item;
    updateUserPreferences(selectedPreferences);
  };

  const updateUserPreferences = data => {
    let updatedData = { ...data, newUpdated: true };
    dispatch(updateUserPreferencesRequest(updatedData));
  };

  return (
    <>
      <View style={styles.mainContainer}>
        <View>
          <Text style={styles.unitHeader}>{i18n.t('unitOfMeasure')}</Text>
        </View>
        <View>
          <Text style={styles.unitDescription}>
            {i18n.t('unitOfMeasureDescription')}
          </Text>
        </View>

        <View style={styles.radioButton}>
          {unitOfMeasureKeys?.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.buttonView}
              onPress={() => setUnitOfMeasure(item)}>
              <RadioButton isSelected={checkUnitOfMeasure(item)} />
              <Text style={styles.buttonText}>{i18n.t(item)}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.mainContainer}>
        <View>
          <Text style={styles.unitHeader}>
            {i18n.t('bodyConditionScoreScale')}
          </Text>
        </View>
        <View style={styles.radioButton}>
          {bcsPointScaling?.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.buttonView}
              onPress={() => setBodyConditionScoreScale(item?.key)}>
              <RadioButton
                isSelected={checkBodyConditionScoreScale(item?.key)}
              />
              <Text style={styles.buttonText}>{i18n.t(item?.key)}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.mainContainer}>
        <View>
          <Text style={styles.unitHeader}>{i18n.t('brandPlaceholder')}</Text>
        </View>
        <View style={styles.radioButton}>
          {brandListing?.map((item, index) => {
            return (
              <TouchableOpacity
                key={index}
                style={styles.buttonView}
                onPress={() => setBrandPlaceholder(item?.key)}>
                <RadioButton isSelected={checkBrandPlaceholder(item?.key)} />
                <Text style={styles.buttonText}>{item?.value}</Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>

      <View style={styles.mainContainer}>
        <View>
          <Text style={styles.unitHeader}>{i18n.t('currency')}</Text>
        </View>
        <View style={styles.radioButton}>
          <TouchableOpacity
            style={styles.currencyButtonView}
            onPress={() =>
              navigation.navigate(ROUTE_CONSTANTS.CURRENCY, {
                setCurrencySelected,
                selectedCurrency: userData?.selectedCurrency,
              })
            }>
            <View>
              <Text>{userData?.selectedCurrency}</Text>
            </View>

            <View>
              <CHEVRON_RIGHT_ICON
                stroke={colors.grey1}
                strokeWidth={normalize(2)}
                width={normalize(15)}
                height={normalize(15)}
              />
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
};

export default AppSettingList;
