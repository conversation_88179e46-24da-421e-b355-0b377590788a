import customFont, {
  normalize,
} from '../../constants/theme/variables/customFont';
import colors from '../../constants/theme/variables/customColor';

export default {
  mainContainer: {
    padding: normalize(20),
    marginBottom: normalize(10),
    backgroundColor: colors.white,
  },
  unitHeader: {
    color: colors.primaryMain,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  unitDescription: {
    color: colors.black,
    fontSize: normalize(12),
    paddingTop: normalize(5),
    fontFamily: customFont.HelveticaNeueRegular,
    fontStyle: 'italic',
  },
  radioButton: {
    justifyContent: 'center',
  },
  buttonView: {
    paddingTop: normalize(8),
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonText: {
    paddingLeft: normalize(10),
  },

  currencyButtonView: {
    paddingTop: normalize(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  formInputView: {
    marginBottom: normalize(24),
  },
  customFieldLabel: {
    letterSpacing: 0.2,
    color: colors.grey1,
    // textTransform: 'capitalize',
    marginBottom: normalize(10),
  },
};
