// modules
import React from 'react';
import { View, Text, Keyboard, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

// styling constants
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import customColor from '../../../constants/theme/variables/customColor';

// helpers
import { dateHelper } from '../../../helpers/dateHelper';

// constants
import { DATE_FORMATS } from '../../../constants/AppConstants';

// localization
import i18n from '../../../localization/i18n';

const NoteBookCreationDate = () => {
  const createdDate =
    useSelector(state => state.noteBook?.notebookData?.createdDate) || null;

  if (!createdDate) return null;

  return (
    <View
      style={styles.flexOne}
      onTouchStart={() => {
        Keyboard.dismiss();
      }}>
      <View style={styles.dateView}>
        <Text style={styles.dateText}>
          {dateHelper.getFormattedDate(createdDate, DATE_FORMATS.dd_MMMM_yyyy)}
          {` ${i18n.t('at')} `}
          {dateHelper.getFormattedDate(createdDate, DATE_FORMATS.H_MM_a)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  flexOne: {
    flex: 1,
  },
  dateView: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: normalize(16),
  },
  dateText: {
    fontWeight: '400',
    lineHeight: normalize(10),
    fontFamily: customFont.HelveticaNeueRegular,
    fontSize: normalize(10),
    color: customColor.alphabetIndex,
  },
});

export default NoteBookCreationDate;
