import fonts, { normalize } from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import { Platform } from 'react-native';

export default {
  container: {
    backgroundColor: colors.white,
    zIndex: 2,
    top: Platform.OS == 'ios' ? normalize(170) : normalize(130),
    borderWidth: normalize(2),
    borderColor: colors.lightWhite,
    borderRadius: normalize(4),
    position: 'absolute',
    padding: normalize(4),
    width: '45%',
    shadowColor: colors.popoverShadowColor,
    shadowOffset: {
      width: 0,
      height: 15,
    },
    shadowOpacity: 0.35,
    shadowRadius: 20,
    elevation: 11,
  },
  button: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: normalize(10),
    alignItems: 'center',
  },
  text: {
    fontWeight: '400',
    lineHeight: normalize(18),
    fontSize: normalize(14),
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.popoverTextColor,
  },
  selectedOption: {
    fontSize: normalize(14),
    lineHeight: normalize(18),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueMedium,
    color: colors.primaryMain,
  },
  separator: {
    alignSelf: 'center',
    width: '90%',
    height: normalize(2),
    backgroundColor: colors.lightWhite,
  },
  iconStyle: {
    width: normalize(16),
    height: normalize(16),
  },
};
