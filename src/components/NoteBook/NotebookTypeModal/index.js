import React from 'react';
import Modal from 'react-native-modal';
import { TouchableOpacity, Text, View } from 'react-native';

//styles
import styles from './styles';
import colors from '../../../constants/theme/variables/customColor';

//constants
import {
  NOTEBOOK_ACTION_CATEGORY,
  NOTEBOOK_GENERAL_CATEGORY,
} from '../../../constants/AppConstants';

// icons
import { TICK_ICON, UN_TICK_ICON } from '../../../constants/AssetSVGConstants';

// localization
import i18n from '../../../localization/i18n';

const NotebookTypeModal = ({
  isVisible,
  cancelModal,
  selected,
  onSelectNotebookType,
}) => {
  return (
    <Modal
      testID={'modal'}
      isVisible={isVisible}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      onBackdropPress={cancelModal}
      onBackButtonPress={cancelModal}
      animationInTiming={10}
      animationOutTiming={10}
      hasBackdrop={true}
      backdropColor={colors.white}
      backdropOpacity={0.1}
      avoidKeyboard={true}>
      <View style={styles.container}>
        <TouchableOpacity
          onPress={() => {
            onSelectNotebookType(NOTEBOOK_GENERAL_CATEGORY);
          }}
          style={styles.button}>
          <Text
            style={
              selected === NOTEBOOK_GENERAL_CATEGORY
                ? styles.selectedOption
                : styles.text
            }>
            {i18n.t('general')}
          </Text>

          {selected === NOTEBOOK_GENERAL_CATEGORY ? (
            <TICK_ICON {...styles.iconStyle} />
          ) : (
            <UN_TICK_ICON {...styles.iconStyle} />
          )}
        </TouchableOpacity>

        <View style={styles.separator} />

        <TouchableOpacity
          onPress={() => {
            onSelectNotebookType(NOTEBOOK_ACTION_CATEGORY);
          }}
          style={styles.button}>
          <Text
            style={
              selected === NOTEBOOK_ACTION_CATEGORY
                ? styles.selectedOption
                : styles.text
            }>
            {i18n.t('action')}
          </Text>
          {selected === NOTEBOOK_ACTION_CATEGORY ? (
            <TICK_ICON {...styles.iconStyle} />
          ) : (
            <UN_TICK_ICON {...styles.iconStyle} />
          )}
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

export default NotebookTypeModal;
