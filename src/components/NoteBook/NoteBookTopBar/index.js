// modules
import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  BackHandler,
  View,
  Platform,
  StatusBar,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// components
import TopBar from '../../TopBar';
import { showToast } from '../../common/CustomToast';
import UnsavedNoteModal from '../UnsavedNoteModal';

// localization
import i18n from '../../../localization/i18n';

// constants
import customColor from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import { TOAST_TYPE } from '../../../constants/FormConstants';
import { NOTEBOOK_ACTION_CATEGORY } from '../../../constants/AppConstants';

// actions
import {
  clearNoteBookReducer,
  saveNewNoteBookRequest,
  updateExistingNoteBookRequest,
} from '../../../store/actions/notebook';

const NoteBookTopBar = ({ isDisabled = false, _handleCloseAlert }) => {
  const dispatch = useDispatch();
  const { pop } = useNavigation();

  const noteBookState = useSelector(state => state.noteBook?.notebookData);
  const notebookMedia = useSelector(state => state.noteBook.notebookMedia);

  const [showChangesModal, setShowChangesModal] = useState(false);

  const isNewNoteBook =
    noteBookState?.id || noteBookState?.noteId ? false : true;

  const isComment = noteBookState?.isComment;

  const showSaveButton =
    !isDisabled &&
    (noteBookState?.title?.length > 0 ||
      noteBookState?.note?.length > 0 ||
      notebookMedia?.images?.length > 0 ||
      notebookMedia?.videos?.length > 0 ||
      notebookMedia?.audios?.length > 0)
      ? true
      : false;

  useEffect(() => {
    BackHandler.addEventListener(
      'hardwareBackPress',
      _handleAndroidBackButtonPress,
    );

    return () => {
      dispatch(clearNoteBookReducer());

      BackHandler.removeEventListener(
        'hardwareBackPress',
        _handleAndroidBackButtonPress,
      );
    };
  }, []);

  const _handleAndroidBackButtonPress = () => {
    if (
      noteBookState?.category === NOTEBOOK_ACTION_CATEGORY &&
      !noteBookState?.actionNotificationDateTimeUtc
    ) {
      showToast(TOAST_TYPE.ERROR, i18n.t('requiredActionNoteDateTime'));
    } else {
      onBackClick();
    }
  };

  const onBackClick = () => {
    _handleCloseAlert();

    if (noteBookState?.isDirty) {
      setShowChangesModal(true);
    } else {
      requestAnimationFrame(() => pop());
    }
  };

  const _handleSavePress = () => {
    if (showSaveButton) {
      if (isNewNoteBook) {
        dispatch(saveNewNoteBookRequest());
      } else {
        const payload = {
          localId: noteBookState?.id,
          noteId: noteBookState?.noteId || noteBookState?.sv_id,
        };

        dispatch(updateExistingNoteBookRequest(payload));
      }

      setShowChangesModal(false);
    }
  };

  const _handleDiscardChanges = () => {
    _handleCancelModel();

    requestAnimationFrame(() => pop());
    // dispatch(clearNoteBookReducer());
  };

  const _handleCancelModel = () => setShowChangesModal(false);

  return (
    <View style={styles.container}>
      <TopBar
        backButton
        backButtonClick={onBackClick}
        titleStyles={styles.titleText}
        title={
          isComment
            ? i18n.t('viewComment')
            : isNewNoteBook
            ? i18n.t('newNotes')
            : i18n.t('editNote')
        }
        customButton={!isDisabled && showSaveButton}
        customClick={_handleSavePress}
        customBtnDisabled={false}
        customLabel={isNewNoteBook ? i18n.t('save') : i18n.t('update')}
        showBottomBorder={true}
        customHeaderStyle={styles.customHeaderStyle}
      />

      <UnsavedNoteModal
        isVisible={showChangesModal}
        cancelModal={_handleCancelModel}
        onDiscardPress={_handleDiscardChanges}
        onSavePress={_handleSavePress}
        disabledSaveButton={!showSaveButton}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: Platform.OS === 'android' ? StatusBar.currentHeight : null,
    height: normalize(70),
  },
  customHeaderStyle: {
    height: null,
    flex: 1,
  },
  titleText: {
    color: customColor.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
    letterSpacing: 0.5,
    marginLeft: -10,
  },
});

export default NoteBookTopBar;
