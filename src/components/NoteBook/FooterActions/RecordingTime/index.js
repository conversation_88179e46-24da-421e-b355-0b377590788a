// modules
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// icons
import { RECORDING_AUDIO_ICON } from '../../../../constants/AssetSVGConstants';

// styling constants
import customColor from '../../../../constants/theme/variables/customColor';
import { normalize } from '../../../../constants/theme/variables/customFont';

// actions
import { stopNoteBookRecording } from '../../../../store/actions/notebook';

// helpers
import { renderTime } from '../../../../helpers/noteBookHelper';

const NoteBookRecordingTime = ({ isAudioRecording = false }) => {
  const dispatch = useDispatch();

  const recordingTimer = useSelector(
    state => state.noteBook.audioRecordingTime,
  );

  const _handleStopRecordingPress = () => {
    dispatch(stopNoteBookRecording());
  };

  return (
    <View style={styles.container}>
      <View style={styles.onRecording}>
        <Text style={styles.textRecordTime}>{renderTime(recordingTimer)}</Text>
      </View>

      <TouchableOpacity
        disabled={!isAudioRecording}
        style={styles.stopRecordingBtn}
        onPress={_handleStopRecordingPress}
        activeOpacity={1}>
        <RECORDING_AUDIO_ICON />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: normalize(20),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  onRecording: {
    backgroundColor: customColor.primaryMain,
    padding: normalize(10),
    flex: 0.9,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: normalize(90),
    flexDirection: 'row',
  },
  textRecordTime: {
    color: customColor.white,
  },
  stopRecordingBtn: {},
});

export default NoteBookRecordingTime;
