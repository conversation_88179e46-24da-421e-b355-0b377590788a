// modules
import React from 'react';
import { TouchableOpacity, StyleSheet, Keyboard } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styling constants
import { normalize } from '../../../../constants/theme/variables/customFont';
import { showToast } from '../../../common/CustomToast';

// icons
import { CAMERA_ICON } from '../../../../constants/AssetSVGConstants';

// actions
import { takeNoteBookCameraPicture } from '../../../../store/actions/notebook';

import { TOAST_TYPE } from '../../../../constants/FormConstants';

import i18n from '../../../../localization/i18n';

const NoteBookMediaAction = ({ isDisabled, disableRichTextEditorKeyboard }) => {
  const dispatch = useDispatch();
  const notebookMedia = useSelector(state => state.noteBook?.notebookMedia);

  const _handleOpenCameraPress = async () => {
    disableRichTextEditorKeyboard();
    Keyboard.dismiss();
    if (notebookMedia?.images?.length > 1) {
      showToast(TOAST_TYPE.ERROR, i18n.t('mediaLimitExceeds'));
    } else {
      dispatch(takeNoteBookCameraPicture());
    }
  };

  return (
    <TouchableOpacity
      disabled={isDisabled}
      onPress={_handleOpenCameraPress}
      style={[styles.padding15, isDisabled && styles.disabledStyles]}>
      <CAMERA_ICON width={normalize(18)} height={normalize(18)} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  padding15: {
    padding: normalize(15),
  },
  disabledStyles: {
    opacity: 0.3,
  },
});

export default NoteBookMediaAction;
