// modules
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, Keyboard } from 'react-native';
import { useDispatch } from 'react-redux';

// icons
import { NOTEBOOK_ATTACHMENT_ICON } from '../../../../constants/AssetSVGConstants';

// styling constants
import customColor from '../../../../constants/theme/variables/customColor';
import { normalize } from '../../../../constants/theme/variables/customFont';

// components
import NotesImageUploader from '../../../common/NotesImageUploader';

// actions
import {
  addNoteBookImagesAttachment,
  addNoteBookVideosAttachment,
} from '../../../../store/actions/notebook';

// constants
import { PHOTO_CONSTANT } from '../../../../constants/AppConstants';

const NoteBookAttachmentAction = ({
  isDisabled,
  disableRichTextEditorKeyboard,
}) => {
  const dispatch = useDispatch();

  const [openMediaModal, setOpenMediaModal] = useState(false);

  const _handleAttachmentsPress = () => {
    disableRichTextEditorKeyboard();
    Keyboard.dismiss();
    // richText?.current?.blurContentEditor?.();
    setOpenMediaModal(true);
  };

  const _handleSelectMediaType = type => {
    setOpenMediaModal(false);

    if (type === PHOTO_CONSTANT) {
      dispatch(addNoteBookImagesAttachment());
    } else {
      dispatch(addNoteBookVideosAttachment());
    }
  };

  return (
    <>
      <TouchableOpacity
        disabled={isDisabled}
        onPress={_handleAttachmentsPress}
        style={[styles.padding15, isDisabled && styles.disabledStyles]}>
        <NOTEBOOK_ATTACHMENT_ICON {...styles.icon} />
      </TouchableOpacity>

      <NotesImageUploader
        onPress={_handleSelectMediaType}
        onClose={() => setOpenMediaModal(false)}
        isOpen={openMediaModal}
      />
    </>
  );
};

const styles = StyleSheet.create({
  padding15: {
    padding: normalize(15),
  },
  icon: {
    fill: customColor.grey1,
    width: normalize(18),
    height: normalize(18),
  },
  disabledStyles: {
    opacity: 0.3,
  },
});

export default NoteBookAttachmentAction;
