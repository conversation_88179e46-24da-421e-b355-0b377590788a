// modules
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { RichToolbar, actions } from 'react-native-pell-rich-editor';
import { useSelector } from 'react-redux';

// components
import NoteBookShareAction from './ShareAction';
import NoteBookMediaAction from './MediaAction';
import NoteBookAttachmentAction from './AttachmentAction';
import NoteBookAudioAction from './AudioAction';
import NoteBookRecordingTime from './RecordingTime';
import NotebookSignatureAction from './SignatureAction';

// styling constants
import customColor from '../../../constants/theme/variables/customColor';
import { normalize } from '../../../constants/theme/variables/customFont';

// constants
import { NOTEBOOK_TOOLBAR } from '../../../constants/AppConstants';

// icons
import { LIST_ICON } from '../../../constants/AssetSVGConstants';

const NoteBookFooterActions = ({
  richTextRef,
  isDisabled = false,
  disableRichTextEditorKeyboard,
}) => {
  const isAudioRecording = useSelector(
    state => state.noteBook.isAudioRecording,
  );

  return (
    <View
      style={[
        styles.richToolbarView,
        richTextRef?.current?.isKeyboardOpen ? styles.isKeyboardOpen : {},
      ]}>
      {isAudioRecording ? (
        <NoteBookRecordingTime isAudioRecording={isAudioRecording} />
      ) : (
        <RichToolbar
          editor={richTextRef}
          selectedIconTint={customColor.primaryMain}
          iconTint={customColor.grey1}
          iconSize={normalize(16)}
          style={styles.richTextToolbarStyle}
          flatContainerStyle={styles.richTextToolbarInnerStyle}
          actions={[
            NOTEBOOK_TOOLBAR.SHARE,
            NOTEBOOK_TOOLBAR.MEDIA,
            NOTEBOOK_TOOLBAR.ATTACHMENT,
            actions.insertBulletsList,
            NOTEBOOK_TOOLBAR.SIGNATURE,
            NOTEBOOK_TOOLBAR.AUDIO,
          ]}
          // map icons for self made actions
          iconMap={{
            [NOTEBOOK_TOOLBAR.SHARE]: () => (
              <NoteBookShareAction
                disableRichTextEditorKeyboard={disableRichTextEditorKeyboard}
              />
            ),

            [NOTEBOOK_TOOLBAR.MEDIA]: () => (
              <NoteBookMediaAction
                isDisabled={isDisabled}
                disableRichTextEditorKeyboard={disableRichTextEditorKeyboard}
              />
            ),

            [NOTEBOOK_TOOLBAR.ATTACHMENT]: () => (
              <NoteBookAttachmentAction
                isDisabled={isDisabled}
                disableRichTextEditorKeyboard={disableRichTextEditorKeyboard}
              />
            ),

            [actions.insertBulletsList]: () => (
              <LIST_ICON
                style={[styles.icon, isDisabled ? styles.disabledStyles : {}]}
                isDisabled={isDisabled}
              />
            ),

            [NOTEBOOK_TOOLBAR.SIGNATURE]: () => (
              <NotebookSignatureAction
                isDisabled={isDisabled}
                disableRichTextEditorKeyboard={disableRichTextEditorKeyboard}
              />
            ),

            [NOTEBOOK_TOOLBAR.AUDIO]: () => (
              <NoteBookAudioAction
                isDisabled={isDisabled}
                disableRichTextEditorKeyboard={disableRichTextEditorKeyboard}
              />
            ),
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  richToolbarView: {
    // paddingHorizontal: normalize(20),
    backgroundColor: customColor.white,
    // marginBottom: normalize(10),
  },
  richTextToolbarStyle: {
    backgroundColor: customColor.transparent,
  },
  richTextToolbarInnerStyle: {
    flex: 1,
    justifyContent: 'space-around',
  },
  icon: {
    width: normalize(18),
    height: normalize(18),
  },
  isKeyboardOpen: {
    paddingBottom: normalize(0),
  },
  disabledStyles: {
    opacity: 0.3,
  },
});

export default NoteBookFooterActions;
