// modules
import React from 'react';
import { Keyboard, TouchableOpacity, StyleSheet } from 'react-native';
import { useDispatch } from 'react-redux';

// icons
import { AUDIO_ICON } from '../../../../constants/AssetSVGConstants';

// styling constants
import { normalize } from '../../../../constants/theme/variables/customFont';

// actions
import { startNoteBookRecording } from '../../../../store/actions/notebook';

const NoteBookAudioAction = ({ isDisabled, disableRichTextEditorKeyboard }) => {
  const dispatch = useDispatch();

  const _handleStartRecordingPress = () => {
    disableRichTextEditorKeyboard();
    Keyboard.dismiss();
    //   richText?.current?.blurContentEditor?.();

    dispatch(startNoteBookRecording());
  };

  return (
    <TouchableOpacity
      disabled={isDisabled}
      style={[styles.padding15, isDisabled && styles.disabledStyles]}
      onPress={_handleStartRecordingPress}>
      <AUDIO_ICON {...styles.icon} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  padding15: {
    padding: normalize(15),
  },
  icon: {
    width: normalize(18),
    height: normalize(18),
  },
  disabledStyles: {
    opacity: 0.3,
  },
});

export default NoteBookAudioAction;
