// modules
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, Keyboard } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import SignatureComponent from '../../../common/SignatureComponent';
import { showToast } from '../../../../components/common/CustomToast';
// icons
import {
  SIGNATURE_ICON,
  SIGNATURE_ICON_SELECTED,
} from '../../../../constants/AssetSVGConstants';

// styling constants
import customColor from '../../../../constants/theme/variables/customColor';
import { normalize } from '../../../../constants/theme/variables/customFont';

// actions
import { saveNotebookSignature } from '../../../../store/actions/notebook';

// constants
import { TOAST_TYPE } from '../../../../constants/FormConstants';

import i18n from '../../../../localization/i18n';

const NotebookSignatureAction = ({
  isDisabled,
  disableRichTextEditorKeyboard,
}) => {
  const dispatch = useDispatch();
  const notebookMedia = useSelector(state => state.noteBook?.notebookMedia);

  const [isSignatureModalOpen, setIsSignatureModalOpen] = useState(false);

  const _handleSignaturePress = () => {
    disableRichTextEditorKeyboard();
    Keyboard.dismiss();

    if (notebookMedia?.images?.length > 1) {
      showToast(TOAST_TYPE.ERROR, i18n.t('mediaLimitExceeds'));
    } else {
      setIsSignatureModalOpen(true);
    }
  };

  const _handleSave = data => {
    setIsSignatureModalOpen(false);
    dispatch(saveNotebookSignature(data));
  };

  return (
    <>
      <TouchableOpacity
        disabled={isDisabled}
        onPress={_handleSignaturePress}
        style={[styles.padding15, isDisabled && styles.disabledStyles]}>
        {isSignatureModalOpen ? (
          <SIGNATURE_ICON_SELECTED {...styles.filledIcon} />
        ) : (
          <SIGNATURE_ICON {...styles.icon} />
        )}
      </TouchableOpacity>

      {isSignatureModalOpen && (
        <SignatureComponent
          onSave={_handleSave}
          onClose={() => setIsSignatureModalOpen(false)}
          isOpen={isSignatureModalOpen}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  padding15: {
    padding: normalize(15),
  },
  icon: {
    fill: customColor.grey1,
    width: normalize(18),
    height: normalize(18),
  },
  filledIcon: {
    fill: customColor.grey1,
    width: normalize(28),
    height: normalize(28),
  },
  disabledStyles: {
    opacity: 0.3,
  },
});

export default NotebookSignatureAction;
