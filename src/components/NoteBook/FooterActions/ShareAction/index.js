// modules
import React from 'react';
import { TouchableOpacity, ActivityIndicator, Keyboard } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// icons
import { SHARE_ICON } from '../../../../constants/AssetSVGConstants';

// styling constants
import { normalize } from '../../../../constants/theme/variables/customFont';

// actions
import { shareNoteBookViaEmailRequest } from '../../../../store/actions/notebook';
import { getNotebookMediaPath } from '../../../../helpers/noteBookHelper';

const NoteBookShareAction = ({ disableRichTextEditorKeyboard }) => {
  const dispatch = useDispatch();

  const shareLoading = useSelector(
    state => state.noteBook?.shareNotebookLoading,
  );
  const notebookMedia = useSelector(state => state.noteBook?.notebookMedia);
  const notebookTitle = useSelector(
    state => state.noteBook?.notebookData?.title,
  );
  const note = useSelector(state => state.noteBook?.notebookData?.note);

  const _handleShareNoteBookPress = async () => {
    disableRichTextEditorKeyboard();
    Keyboard.dismiss();

    const mediaAttachments = notebookMedia?.images?.map(media => {
      return getNotebookMediaPath(media);
    });
    const videoAttachments = notebookMedia?.videos?.map(media => {
      return getNotebookMediaPath(media);
    });
    const audioAttachments = notebookMedia?.audios?.map(media => media?.path);

    const payload = {
      title: notebookTitle || '',
      noteHTML: note || '',
      attachments: [
        ...mediaAttachments,
        ...audioAttachments,
        ...videoAttachments,
      ],
    };

    dispatch(shareNoteBookViaEmailRequest(payload));
  };

  return (
    <TouchableOpacity
      disabled={shareLoading}
      onPress={_handleShareNoteBookPress}
      hitSlop={{
        left: 30,
        right: 30,
        bottom: 40,
        top: 40,
      }}>
      {shareLoading ? (
        <ActivityIndicator size="small" />
      ) : (
        <SHARE_ICON width={normalize(18)} height={normalize(18)} />
      )}
    </TouchableOpacity>
  );
};

export default NoteBookShareAction;
