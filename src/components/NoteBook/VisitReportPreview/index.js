// modules
import React, { useState } from 'react';
import { TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { Popover, ScrollView } from 'native-base';

//components
import NoteItem from '../../Visits/VisitReport/Components/NotesListing/NoteItem';

// icons
import { NOTEBOOK_INFO_ICON } from '../../../constants/AssetSVGConstants';

// styling constants
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import customColor from '../../../constants/theme/variables/customColor';

const { width } = Dimensions.get('window');

const VisitReportPreview = ({ note }) => {
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);

  const showPreview = () => setIsPreviewVisible(true);

  const closeAlert = () => setIsPreviewVisible(false);

  return (
    <Popover
      isOpen={isPreviewVisible}
      onClose={closeAlert}
      trigger={triggerProps => (
        <TouchableOpacity {...triggerProps} onPress={showPreview}>
          <NOTEBOOK_INFO_ICON {...styles.noteBookInfoIcon} />
        </TouchableOpacity>
      )}>
      <Popover.Content style={styles.popoverStyle} overflow={'visible'}>
        <Popover.Body
          style={styles.popoverBodyStyle}
          rounded={'md'}
          shadow={'0'}>
          <ScrollView>
            <NoteItem
              key={'note_' + (note?.id || note?.localId)}
              note={note}
              isPreview={true}
            />
          </ScrollView>
        </Popover.Body>
      </Popover.Content>
    </Popover>
  );
};

const styles = StyleSheet.create({
  popoverStyle: {
    borderWidth: 1,
    borderColor: customColor.popoverBorderColor,
    backgroundColor: customColor.white,
    marginRight: normalize(20),
    top: normalize(5),
    // height: normalize(500)
  },
  popoverBodyStyle: {
    width: width - normalize(150),
    backgroundColor: customColor.white,
    shadowColor: customColor.popoverShadowColor,
    shadowOffset: {
      width: -2,
      height: 15,
    },
    shadowOpacity: 0.35,
    shadowRadius: 20,
    elevation: 7,
  },
  noteBookInfoIcon: {
    width: normalize(16),
    height: normalize(16),
    marginTop: normalize(4),
    marginLeft: normalize(8),
  },
  countText: {
    flex: 0,
  },
  infoContainer: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: normalize(5),
  },
  text: {
    flex: 1,
    fontSize: normalize(13),
    fontWeight: '400',
    lineHeight: normalize(20.8),
    letterSpacing: normalize(0.2),
    color: customColor.popoverTextColor,
    fontFamily: customFont.HelveticaNeueMedium,
  },
  separator: {
    alignSelf: 'center',
    width: '90%',
    height: normalize(1),
    backgroundColor: customColor.lightWhite,
  },
});

export default VisitReportPreview;
