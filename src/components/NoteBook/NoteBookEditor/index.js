// modules
import React, { useCallback, useEffect, useState } from 'react';
import { View, StyleSheet, TextInput } from 'react-native';
import { RichEditor } from 'react-native-pell-rich-editor';
import { useDispatch, useSelector } from 'react-redux';
import _ from 'lodash';

// localization
import i18n from '../../../localization/i18n';

import VisitReportPreview from '../VisitReportPreview';

// constants
import customColor from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import {
  NOTEBOOK_EDITOR_TEXT_LENGTH,
  NOTEBOOK_VISIT_REPORT_TEXT_LENGTH,
} from '../../../constants/AppConstants';

// helpers
import { convertToHtml } from '../../../helpers/noteBookHelper';
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';

// actions
import {
  updateNoteBookNoteHtml,
  updateNoteBookTitle,
} from '../../../store/actions/notebook';

const NoteBookEditor = ({
  richTextRef,
  isDisabled = false,
  setEditorFocus,
  handleCursorPosition,
}) => {
  const dispatch = useDispatch();

  const notebookData = useSelector(state => state.noteBook?.notebookData);

  const [title, setTitle] = useState(notebookData?.title);

  useEffect(() => {
    richTextRef?.current?.setFontSize(2);
  }, []);

  useEffect(() => {
    if (
      notebookData?.note &&
      richTextRef?.current?.props?.initialContentHTML?.length <= 0
    ) {
      richTextRef?.current?.setContentHTML(convertToHtml(notebookData?.note));
    }
  }, [notebookData?.note]);

  const _handleChangeTitle = text => {
    setTitle(text);

    updateTitleInReducerState(text);
  };

  // handle change in title with delay using lodash debounce
  const updateTitleInReducerState = useCallback(
    _.debounce(text => {
      dispatch(updateNoteBookTitle(text));
    }, 100),
    [],
  );

  // handle change rich text editor with delay using lodash debounce
  const _richTextChangeHandler = _.debounce(htmlText => {
    if (htmlText.length < NOTEBOOK_EDITOR_TEXT_LENGTH) {
      dispatch(updateNoteBookNoteHtml(htmlText));
    }
  }, 300);

  return (
    <View style={styles.noteEditorView}>
      <View style={styles.titleView}>
        <TextInput
          editable={!isDisabled}
          value={title || notebookData?.title}
          placeholder={i18n.t('Topic')}
          onChangeText={_handleChangeTitle}
          style={styles.editorTitle}
          placeholderTextColor={styles.placeholderColor}
          onSubmitEditing={() => richTextRef?.current?.focusContentEditor()}
          maxLength={150}
        />
        {!stringIsEmpty(notebookData?.note) &&
          notebookData?.note?.length >= NOTEBOOK_VISIT_REPORT_TEXT_LENGTH && (
            <View style={styles.breadcrumbSecondCol}>
              <VisitReportPreview note={notebookData} />
            </View>
          )}
      </View>
      <RichEditor
        ref={richTextRef}
        disabled={isDisabled}
        onChange={_richTextChangeHandler}
        initialContentHTML={convertToHtml(notebookData?.note)}
        style={styles.richTextEditorStyle}
        androidHardwareAccelerationDisabled={true}
        androidLayerType="hardware"
        pasteAsPlainText
        javaScriptEnabled={true}
        allowFileAccess={false}
        allowFileAccessFromFileURLs={false}
        onFocus={() => setEditorFocus(true)}
        onBlur={() => setEditorFocus(false)}
        useContainer={true}
        onCursorPosition={handleCursorPosition}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  noteEditorView: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    flex: 1,
  },
  titleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  editorTitle: {
    color: customColor.primaryMain,
    fontFamily: customFont.HelveticaNeueMedium,
    fontSize: normalize(18),
    maxWidth: '100%',
  },
  placeholderColor: customColor.grey3,
  richTextEditorStyle: {
    flex: 1,
    width: '100%',
    marginLeft: normalize(-4.5),
    fontFamily: customFont.HelveticaNeueRegular,
    fontSize: normalize(14),
  },

  breadcrumbSecondCol: {
    flex: 0.28,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-end',
  },
});

export default NoteBookEditor;
