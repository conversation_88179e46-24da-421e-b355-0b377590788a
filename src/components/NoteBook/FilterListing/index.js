// modules
import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, Text } from 'react-native';

import Capsules from '../../common/Capsule';

// styling constants
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import { getFilterCapsuleMeta } from '../../../helpers/noteBookHelper';

// localization
import i18n from '../../../localization/i18n';
import customColor from '../../../constants/theme/variables/customColor';

const NotesFilterList = ({ noteBookList, filters, onCrossClick }) => {
  const [data, setCapsuleData] = useState([]);

  useEffect(() => {
    let _data = getFilterCapsuleMeta(filters);
    setCapsuleData(_data);
  }, [
    filters.endDate,
    filters.filterAccounts,
    filters.filterSites,
    filters.filterTools,
    filters.filterTypes,
    filters.filterVisit,
    filters.pageNo,
    filters.startDate,
    filters.updatedStartDate,
    filters.updatedEndDate,
    filters.filterCreator,
    filters.filterNoteCommentType,
  ]);

  return (
    <View>
      {getFilterCapsuleMeta(filters).length > 0 ? (
        <View style={styles.filterResult}>
          <Text style={styles.filterResultText}>
            {noteBookList?.length > 0
              ? `${i18n.t('filterResult')} ${noteBookList?.length}`
              : null}
          </Text>
        </View>
      ) : (
        <></>
      )}
      {data.length > 0 ? (
        <ScrollView
          style={styles.scrollableView}
          horizontal={true}
          showsHorizontalScrollIndicator={false}>
          <View style={styles.containerView}>
            {data.map((d, index) => {
              return (
                <Capsules
                  key={'filterCapsule_' + index}
                  text={d.text}
                  subText={d.subText}
                />
              );
            })}
          </View>
        </ScrollView>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  scrollableView: {
    height: normalize(40),
  },
  containerView: {
    // width: '100%',
    flexDirection: 'row',
    marginTop: normalize(8),
    alignItems: 'center',
  },
  richTextToolbarInnerStyle: {
    flex: 1,
    justifyContent: 'space-around',
  },
  icon: {
    width: normalize(18),
    height: normalize(18),
  },
  isKeyboardOpen: {
    paddingBottom: normalize(0),
  },
  filterResult: {
    marginTop: normalize(10),
  },
  filterResultText: {
    fontSize: normalize(12),
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.alphabetIndex,
    lineHeight: normalize(21),
    letterSpacing: 0.25,
  },
});

export default NotesFilterList;
