// modules
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Popover } from 'native-base';

// icons
import { NOTEBOOK_INFO_ICON } from '../../../constants/AssetSVGConstants';

// styling constants
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import customColor from '../../../constants/theme/variables/customColor';

// localization
import i18n from '../../../localization/i18n';

const { width } = Dimensions.get('window');

const TrailInfoAlert = () => {
  const [showInfoAlert, setShowInfoAlert] = useState(false);

  const trailInfo = () => setShowInfoAlert(true);

  const closeAlert = () => setShowInfoAlert(false);

  return (
    <Popover
      isOpen={showInfoAlert}
      onClose={closeAlert}
      trigger={triggerProps => (
        <TouchableOpacity {...triggerProps} onPress={trailInfo}>
          <NOTEBOOK_INFO_ICON {...styles.noteBookInfoIcon} />
        </TouchableOpacity>
      )}>
      <Popover.Content style={styles.popoverStyle} overflow={'visible'}>
        <Popover.Body
          style={styles.popoverBodyStyle}
          rounded={'md'}
          shadow={'0'}>
          <View style={styles.infoContainer}>
            <Text style={[styles.text, styles.countText]}>{i18n.t('1')}. </Text>
            <Text style={styles.text}>{i18n.t('noteBookTrailInfo')}</Text>
          </View>

          {/* <View style={styles.separator} /> */}
          {/* 
          <View style={styles.infoContainer}>
            <Text style={[styles.text, styles.countText]}>{i18n.t('2')}. </Text>
            <Text style={styles.text}>{i18n.t('maxAttachmentsLimit')}</Text>
          </View> */}
        </Popover.Body>
      </Popover.Content>
    </Popover>
  );
};

const styles = StyleSheet.create({
  popoverStyle: {
    borderWidth: 1,
    borderColor: customColor.popoverBorderColor,
    backgroundColor: customColor.white,
    marginRight: normalize(20),
    top: normalize(5),
  },
  popoverBodyStyle: {
    width: width - normalize(150),
    backgroundColor: customColor.white,
    shadowColor: customColor.popoverShadowColor,
    shadowOffset: {
      width: -2,
      height: 15,
    },
    shadowOpacity: 0.35,
    shadowRadius: 20,
    elevation: 7,
  },
  noteBookInfoIcon: {
    width: normalize(16),
    height: normalize(16),
    marginTop: normalize(4),
    marginLeft: normalize(8),
  },
  countText: {
    flex: 0,
  },
  infoContainer: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: normalize(5),
  },
  text: {
    flex: 1,
    fontSize: normalize(13),
    fontWeight: '400',
    lineHeight: normalize(20.8),
    letterSpacing: normalize(0.2),
    color: customColor.popoverTextColor,
    fontFamily: customFont.HelveticaNeueMedium,
  },
  separator: {
    alignSelf: 'center',
    width: '90%',
    height: normalize(1),
    backgroundColor: customColor.lightWhite,
  },
});

export default TrailInfoAlert;
