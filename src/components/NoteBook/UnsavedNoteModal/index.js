// modules
import React from 'react';
import Modal from 'react-native-modal';
import { View, Text, StyleSheet, Dimensions, SafeAreaView } from 'react-native';

// styling constants
import customColor from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';

// localization
import i18n from '../../../localization/i18n';

// components
import FormButton from '../../common/FormButton';

// constants
import { BUTTON_TYPE } from '../../../constants/FormConstants';

const { width } = Dimensions.get('window');

const UnsavedNoteModal = ({
  isVisible = false,
  cancelModal,
  onDiscardPress,
  onSavePress,
  disabledSaveButton = false,
}) => {
  return (
    <Modal
      isVisible={isVisible}
      animationIn={'slideInUp'}
      animationOut={'slideOutDown'}
      onBackdropPress={cancelModal}
      onBackButtonPress={cancelModal}
      animationInTiming={400}
      animationOutTiming={600}
      hasBackdrop={true}
      backdropColor={customColor.black}
      backdropOpacity={0.34}
      style={styles.modalStyle}>
      <View style={styles.container}>
        <SafeAreaView style={{ flex: 1 }}>
          <View style={styles.topDraggerBarParent}>
            <View style={styles.topDraggerBar} />
          </View>

          <View style={styles.contentContainer}>
            <Text style={styles.titleText}>{i18n.t('unsavedNotes')}</Text>

            <Text style={styles.descText}>{i18n.t('discardNotes')}</Text>
          </View>

          <View style={styles.buttonContainer}>
            <FormButton
              type={BUTTON_TYPE.SECONDARY}
              label={i18n.t('discard')}
              onPress={onDiscardPress}
              customButtonStyle={styles.button}
              customButtonTextStyle={styles.buttonText}
            />
            <FormButton
              disabled={disabledSaveButton}
              type={BUTTON_TYPE.PRIMARY}
              label={i18n.t('save')}
              onPress={onSavePress}
              customButtonStyle={styles.button}
              customButtonTextStyle={styles.buttonText}
            />
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalStyle: {
    margin: 0,
  },
  container: {
    width,
    backgroundColor: customColor.white,
    zIndex: 2,
    position: 'absolute',
    bottom: 0,
    borderTopLeftRadius: normalize(30),
    borderTopRightRadius: normalize(30),
    paddingHorizontal: normalize(20),
    alignItems: 'center',
  },
  topDraggerBarParent: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: normalize(10),
  },
  topDraggerBar: {
    height: normalize(4),
    width: normalize(45),
    borderRadius: normalize(40),
    backgroundColor: customColor.grey3,
  },
  contentContainer: {
    marginHorizontal: normalize(20),
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: normalize(15),
  },
  buttonContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: customColor.white,
    marginBottom: normalize(15),
  },
  button: {
    borderRadius: normalize(6),
    width: '47%',
  },
  buttonText: {
    textTransform: 'uppercase',
  },
  titleText: {
    fontSize: normalize(18),
    fontWeight: '500',
    color: customColor.grey1,
    fontFamily: customFont.HelveticaNeueMedium,
    lineHeight: normalize(18),
  },
  descText: {
    textAlign: 'center',
    fontSize: normalize(15),
    fontWeight: '400',
    color: customColor.grey1,
    fontFamily: customFont.HelveticaNeueMedium,
    lineHeight: normalize(18),
    marginTop: normalize(10),
  },
});

export default UnsavedNoteModal;
