// modules
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Popover } from 'native-base';
import { useDispatch, useSelector } from 'react-redux';

// icons
import {
  CHEVRON_DOWN_ICON,
  TICK_ICON,
  UN_TICK_ICON,
} from '../../../constants/AssetSVGConstants';

// constants
import {
  NOTEBOOK_ACTION_CATEGORY,
  NOTEBOOK_GENERAL_CATEGORY,
} from '../../../constants/AppConstants';

// styling constants
import customColor from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';

// components
import ActionCategoryReminder from '../ActionCategoryReminder';

// actions
import { changeNoteBookCategory } from '../../../store/actions/notebook';

// localizations
import i18n from '../../../localization/i18n';

const NoteBookCategorySelection = ({ isDisabled = false }) => {
  const dispatch = useDispatch();

  const notebookCategory = useSelector(
    state => state.noteBook?.notebookData?.category,
  );

  const [showCategoryModal, setShowCategoryModal] = useState(false);

  const _handleChangeCategory = noteType => {
    dispatch(changeNoteBookCategory(noteType));
    setShowCategoryModal(false);
  };

  const categoryType =
    notebookCategory === NOTEBOOK_ACTION_CATEGORY
      ? i18n.t('action')
      : i18n.t('general');

  return (
    <View style={styles.noteTypeView}>
      <Popover
        isOpen={showCategoryModal}
        onClose={() => setShowCategoryModal(false)}
        trigger={triggerProps => (
          <TouchableOpacity
            {...triggerProps}
            disabled={isDisabled}
            onPress={() => setShowCategoryModal(true)}
            style={[styles.noteView, isDisabled && styles.disabledButton]}>
            <Text style={styles.typeText}>{categoryType}</Text>
            <CHEVRON_DOWN_ICON />
          </TouchableOpacity>
        )}>
        <Popover.Content style={styles.popoverStyle} overflow={'visible'}>
          <Popover.Body
            style={styles.popoverBodyStyle}
            rounded={'md'}
            shadow={'0'}>
            <TouchableOpacity
              disabled={isDisabled}
              onPress={() => _handleChangeCategory(NOTEBOOK_ACTION_CATEGORY)}
              style={styles.button}>
              <Text
                style={
                  notebookCategory === NOTEBOOK_ACTION_CATEGORY
                    ? styles.selectedOption
                    : styles.text
                }>
                {i18n.t('action')}
              </Text>
              {notebookCategory === NOTEBOOK_ACTION_CATEGORY ? (
                <TICK_ICON {...styles.iconStyle} />
              ) : (
                <UN_TICK_ICON {...styles.iconStyle} />
              )}
            </TouchableOpacity>

            <View style={styles.separator} />

            <TouchableOpacity
              disabled={isDisabled}
              onPress={() => _handleChangeCategory(NOTEBOOK_GENERAL_CATEGORY)}
              style={styles.button}>
              <Text
                style={
                  notebookCategory === NOTEBOOK_GENERAL_CATEGORY
                    ? styles.selectedOption
                    : styles.text
                }>
                {i18n.t('general')}
              </Text>

              {notebookCategory === NOTEBOOK_GENERAL_CATEGORY ? (
                <TICK_ICON {...styles.iconStyle} />
              ) : (
                <UN_TICK_ICON {...styles.iconStyle} />
              )}
            </TouchableOpacity>
          </Popover.Body>
        </Popover.Content>
      </Popover>

      <ActionCategoryReminder isDisabled={isDisabled} />
    </View>
  );
};

const styles = StyleSheet.create({
  noteTypeView: {
    marginVertical: normalize(10),
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  noteView: {
    width: normalize(120),
    paddingVertical: normalize(6),
    paddingLeft: normalize(5),
    paddingRight: normalize(10),
    borderWidth: 1,
    borderColor: customColor.lightWhite,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 4,
  },
  typeText: {
    fontWeight: '500',
    lineHight: normalize(16),
    letterSpacing: normalize(0.15),
    fontSize: normalize(14),
    color: customColor.grey1,
    fontFamily: customFont.HelveticaNeueMedium,
  },
  popoverStyle: {
    borderWidth: 1,
    borderColor: customColor.popoverBorderColor,
    backgroundColor: customColor.white,
    marginLeft: normalize(10),
    top: normalize(5),
  },
  arrowStyles: {
    backgroundColor: customColor.white,
    borderColor: customColor.popoverBorderColor,
  },
  popoverBodyStyle: {
    backgroundColor: customColor.white,
    shadowColor: customColor.popoverShadowColor,
    shadowOffset: {
      width: -2,
      height: 15,
    },
    shadowOpacity: 0.35,
    shadowRadius: 20,
    elevation: 7,
  },
  button: {
    width: 'auto',
    marginHorizontal: normalize(5),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: customColor.disablePrimaryButtonBackgroundColor,
  },
  text: {
    fontWeight: '400',
    lineHeight: normalize(18),
    fontSize: normalize(14),
    fontFamily: customFont.HelveticaNeueRegular,
    color: customColor.popoverTextColor,
  },
  selectedOption: {
    fontSize: normalize(14),
    lineHeight: normalize(18),
    fontWeight: '500',
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.primaryMain,
  },
  separator: {
    marginVertical: normalize(10),
    alignSelf: 'center',
    width: '100%',
    height: normalize(2),
    backgroundColor: customColor.lightWhite,
  },
  iconStyle: {
    marginLeft: normalize(30),
    width: normalize(16),
    height: normalize(16),
  },
});

export default NoteBookCategorySelection;
