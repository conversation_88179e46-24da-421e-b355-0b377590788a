// modules
import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// components
import TrailInfoAlert from '../TrailInfoAlert';

// localization
import i18n from '../../../localization/i18n';

// styling constants
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import customColor from '../../../constants/theme/variables/customColor';

// constants
import ROUTE_CONSTANTS from '../../../constants/RouteConstants';

// helpers
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';
import { trimString } from '../../../helpers/noteBookHelper';

// actions
import {
  editNoteBookTrailRequest,
  visitGetBySiteNameRequest,
} from '../../../store/actions/notebook';
import { getAllAccountSitesRequest } from '../../../store/actions/site';
import { getAllSiteVisitsRequest } from '../../../store/actions/visit';

const NoteBookHeaderTrail = ({ isDisabled = false }) => {
  const { navigate } = useNavigation();

  const dispatch = useDispatch();

  const {
    accountId = '',
    localAccountId = '',
    accountName = '',

    siteId = '',
    localSiteId = '',
    siteName = '',

    visitId = '',
    localVisitId = '',
    visitName = '',

    section = '',
    sectionTitle = '',
  } = useSelector(state => state.noteBook?.notebookData);
  const loadingTrailData = useSelector(
    state => state.noteBook?.loadingTrailData,
  );

  useEffect(() => {
    if (
      !stringIsEmpty(accountId) ||
      !stringIsEmpty(localAccountId) ||
      !stringIsEmpty(siteId) ||
      !stringIsEmpty(localSiteId) ||
      !stringIsEmpty(visitId) ||
      !stringIsEmpty(localVisitId)
    ) {
      const payload = {
        accountId,
        localAccountId,
        siteId,
        localSiteId,
        visitId,
        localVisitId,
      };
      dispatch(visitGetBySiteNameRequest(payload));
    }
  }, [accountId, localAccountId, siteId, localSiteId, visitId, localVisitId]);

  const renderTrail = () => {
    let value = '';
    let isTail = false;

    if (!stringIsEmpty(accountName)) {
      isTail = accountName.length > 10;
      value += `${trimString({ text: accountName })}`;
    }

    if (!stringIsEmpty(siteName)) {
      isTail = siteName.length > 10;
      value += ` | ${trimString({ text: siteName })}`;
    }

    if (!stringIsEmpty(visitName)) {
      isTail = visitName.length > 10;
      value += ` | ${trimString({ text: visitName })}`;
    }

    if (!stringIsEmpty(value)) {
      value = `${trimString({
        text: value,
        isTailEllipsize: isTail,
        allowedCharLength: 60,
      })}`;
      return value;
    } else {
      return value;
    }
  };

  const _handleAddTrailPress = () => {
    navigate(ROUTE_CONSTANTS.NOTE_BOOK_TRAIL);
  };

  const _handleEditTrailPress = () => {
    // getting sites to show in dropdown if trail has site selected
    if (accountId || localAccountId) {
      const account = {
        accountId,
        localAccountId,
      };
      dispatch(getAllAccountSitesRequest(account));
    }

    // getting visits to show in dropdown if trail has visit selected
    if (siteId || localSiteId) {
      const site = {
        siteId,
        localSiteId,
      };
      dispatch(getAllSiteVisitsRequest(site));
    }

    const notebookTrail = {
      account: {
        accountId,
        localAccountId,
      },
      site: {
        siteId,
        localSiteId,
      },
      visit: {
        visitId,
        localVisitId,
      },
      tool: {
        name: sectionTitle,
        toolId: section,
      },
    };

    dispatch(editNoteBookTrailRequest(notebookTrail));

    navigate(ROUTE_CONSTANTS.NOTE_BOOK_TRAIL, { isEditMode: true });
  };

  const title =
    trimString({
      text: sectionTitle || '',
      isTailEllipsize: sectionTitle?.length > 24,
      allowedCharLength: 24,
    }) || '';

  const hasTrail = renderTrail();

  return (
    <View style={styles.breadcrumb}>
      {loadingTrailData ? (
        <ActivityIndicator />
      ) : (
        <View>
          <View style={styles.breadcrumbFirstCol}>
            {hasTrail && (
              <Text style={styles.breadcrumbText}>{hasTrail && hasTrail}</Text>
            )}
            {!hasTrail && !title && (
              <Text
                style={[
                  styles.removeTrailBtn,
                  isDisabled && styles.disabledText,
                ]}
                onPress={_handleAddTrailPress}
                disabled={isDisabled}>
                {i18n.t('addTrail')}
              </Text>
            )}
          </View>

          <View style={styles.breadcrumbFirstCol}>
            {title && <Text style={styles.breadcrumbText}>{title} </Text>}

            {(hasTrail || title) && (
              <Text
                style={[
                  styles.removeTrailBtn,
                  isDisabled && styles.disabledText,
                ]}
                onPress={_handleEditTrailPress}
                disabled={isDisabled}>
                {i18n.t('editTrail')}
              </Text>
            )}
          </View>
        </View>
      )}

      <View style={styles.breadcrumbSecondCol}>
        {hasTrail && <TrailInfoAlert />}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  breadcrumb: {
    marginVertical: normalize(10),
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  breadcrumbFirstCol: {
    flex: 0.79,
    flexDirection: 'row',
    flexWrap: 'wrap',
    maxWidth: normalize(265),
  },
  breadcrumbSecondCol: {
    flex: 0.28,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-end',
  },
  breadcrumbText: {
    fontSize: normalize(13),
    lineHeight: normalize(20),
    fontFamily: customFont.HelveticaNeueMedium,
    color: customColor.grey1,
  },
  removeTrailBtn: {
    fontSize: normalize(13),
    fontWeight: '500',
    lineHeight: normalize(20),
    color: customColor.primaryMain,
    textDecorationLine: 'underline',
    fontFamily: customFont.HelveticaNeueMedium,
    textDecorationColor: customColor.primaryMain,
  },
  noteBookInfoIcon: {
    width: normalize(16),
    height: normalize(16),
    marginTop: normalize(4),
    marginLeft: normalize(8),
  },
  favoriteIcon: {
    width: normalize(24),
    height: normalize(24),
  },
  disabledText: {
    color: customColor.disablePrimaryButtonTextColor,
    textDecorationColor: customColor.disablePrimaryButtonTextColor,
  },
});

export default NoteBookHeaderTrail;
