// modules
import React from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';

// localization
import i18n from '../../../localization/i18n';

// constants
import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import customColor from '../../../constants/theme/variables/customColor';

import { CROSS_ICON } from '../../../constants/AssetSVGConstants';

const NoteBookEditorLimitAlert = ({ _handleCloseAlert }) => {
  const subMessageComponent = (
    <Text style={styles.subMessage}>{i18n.t('charactersAllowedInReport')}</Text>
  );

  const customCrossIcon = <CROSS_ICON {...styles.crossIcon} />;

  return (
    <View style={[styles.alert]}>
      <View style={[styles.toastContainer, styles.toastContainerStyles]}>
        <Text style={[styles.toastMessageText, styles.messageStyles]}>
          {i18n.t('noteCharacterLimitForVisitReport') + ' '}
          {subMessageComponent}
        </Text>
        <TouchableOpacity
          hitSlop={{ top: 20, left: 20, bottom: 10, right: 10 }}
          style={[styles.closeIconStyle, styles.closeButtonStyles]}
          onPress={_handleCloseAlert}>
          {customCrossIcon}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  messageStyles: {
    flex: 1,
    lineHeight: normalize(20),
    fontWeight: 'bold',
    color: customColor.alertMessageColor,
  },
  subMessage: {
    fontWeight: '400',
  },
  toastContainerStyles: {
    alignItems: 'center',
    width: '100%',
    backgroundColor: customColor.warning5,
    paddingHorizontal: normalize(0),
    borderRadius: normalize(5),
  },
  closeButtonStyles: {
    marginLeft: normalize(0),
    width: normalize(40),
    justifyContent: 'center',
    alignItems: 'center',
  },
  crossIcon: {
    width: normalize(12),
    height: normalize(12),
    stroke: customColor.alertMessageColor,
    strokeWidth: normalize(2),
    style: {
      fontWeight: 'bold',
    },
  },
  alert: {
    backgroundColor: customColor.white,
    width: Dimensions.get('window').width - 40,
    position: 'relative',
  },
  toastContainer: {
    alignSelf: 'center',
    flexDirection: 'row',
    width: '90%',
    paddingHorizontal: normalize(24),
    paddingVertical: normalize(11),
    borderRadius: normalize(4),
    backgroundColor: customColor.userAvatarBackground,
  },
  toastMessageText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(14),
    lineHeight: normalize(17),
    color: customColor.white,
    marginLeft: normalize(13),
    flex: 1,
  },
  infoIconContainer: {
    marginTop: normalize(0),
  },
  closeIconStyle: {
    marginLeft: normalize(32),
    marginTop: normalize(0),
  },
});

export default NoteBookEditorLimitAlert;
