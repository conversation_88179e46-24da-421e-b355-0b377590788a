// modules
import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import Media from '../../common/Media';
import FullscreenModal from '../../common/FullscreenModal';

// actions
import { deleteNoteBookMediaByIdRequest } from '../../../store/actions/s3Media';
import { deleteNoteMedia } from '../../../store/actions/notebook';

const NoteBookImagesSlider = ({ isDisabled }) => {
  const dispatch = useDispatch();

  const notebookLoader = useSelector(a => a.s3Media?.specificNotebookLoader);
  const notebookMedia = useSelector(state => state.noteBook.notebookMedia);

  const [isFullscreen, setFullscreen] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState({});

  const mediaList = notebookMedia
    ? [...notebookMedia?.images, ...notebookMedia?.videos]
    : [];

  if (mediaList?.length <= 0) return <></>;

  const deleteImage = (item, mediaType) => {
    if (!isDisabled) {
      // removing notebook media record from media table
      dispatch(deleteNoteBookMediaByIdRequest(item));

      const payload = {
        mediaType,
        mediaId: item.mediaId,
      };

      // filter deleted media items in notebook media reducer
      dispatch(deleteNoteMedia(payload));
    }
  };

  const onMediaItemClick = (item, mediaType) => {
    let obj = {};
    obj.item = item;
    obj.media = mediaType;
    setSelectedMedia(obj);
    setFullscreen(!isFullscreen);
  };

  return (
    <View style={styles.imageContainer}>
      {mediaList?.map((item, index) => (
        <Media
          key={index}
          item={item}
          onMediaItemClick={onMediaItemClick}
          deleteImage={deleteImage}
          loader={notebookLoader}
        />
      ))}

      <FullscreenModal
        isVisible={isFullscreen}
        cancelModal={() => setFullscreen(false)}
        selectedItem={selectedMedia}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
});

export default NoteBookImagesSlider;
