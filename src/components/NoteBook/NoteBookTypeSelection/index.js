// modules
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// constants
import {
  DATE_FORMATS,
  HIT_SLOP,
  NOTEBOOK_ACTION_CATEGORY,
} from '../../../constants/AppConstants';

// icons
import { TIMER_ICON } from '../../../constants/AssetSVGConstants';

// localization
import i18n from '../../../localization/i18n';

// styling constants
import customColor from '../../../constants/theme/variables/customColor';
import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';

// components
import BottomSheetNoteCalender from '../../common/CustomBottomSheet/BottomSheetNoteCalender';

// helpers
import { dateHelper } from '../../../helpers/dateHelper';

// actions
import { updateReminderTime } from '../../../store/actions/notebook';

const NotebookTypeSelection = () => {
  const dispatch = useDispatch();

  const notebookCategory = useSelector(
    state => state.noteBook?.notebookData?.category,
  );
  const actionNotificationDateTimeUtc = useSelector(
    state => state.noteBook?.notebookData?.actionNotificationDateTimeUtc,
  );

  const [showCalendar, setShowCalendar] = useState(false);

  const _handleUpdateReminderTime = time => {
    const timeStamp = dateHelper.getUnixTimestamp(time);

    dispatch(updateReminderTime(timeStamp));
  };

  /**
   * @description
   * checking if user has selected the action category for notebook
   * then we are displaying the option to select date and time from picker
   */
  if (notebookCategory === NOTEBOOK_ACTION_CATEGORY) {
    // return formatted date time for display
    const formattedDate = actionNotificationDateTimeUtc
      ? dateHelper.getFormattedDate(
          actionNotificationDateTimeUtc,
          DATE_FORMATS.MMM_dd_HH_mm,
        )
      : i18n.t('setReminder');

    return (
      <>
        <TouchableOpacity
          onPress={() => setShowCalendar(true)}
          hitSlop={HIT_SLOP}>
          <View style={styles.flexRow}>
            <TIMER_ICON {...styles.timerIcon} />
            <Text style={styles.reminderText}>{formattedDate}</Text>
          </View>
        </TouchableOpacity>

        <BottomSheetNoteCalender
          isVisible={showCalendar}
          setIsVisible={setShowCalendar}
          setTimeDate={_handleUpdateReminderTime}
        />
      </>
    );
  }

  return null;
};

const styles = StyleSheet.create({
  flexRow: {
    flexDirection: 'row',
  },
  reminderText: {
    fontSize: normalize(12),
    color: customColor.secondary2,
    marginLeft: normalize(6),
    fontFamily: customFont.HelveticaNeueMedium,
  },
  timerIcon: {
    fill: customColor.secondary2,
    width: normalize(16),
    height: normalize(16),
  },
});

export default NotebookTypeSelection;
