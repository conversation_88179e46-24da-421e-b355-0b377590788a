// modules
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

// components
import AudioPlayer from '../../common/AudioPlayer';

// actions
import {
  deleteRecording,
  pausePlayingAudio,
  startPlayingAudio,
} from '../../../store/actions/notebook';
import { deleteNoteBookMediaByIdRequest } from '../../../store/actions/s3Media';

const NoteBookAudioRecordings = ({ isDisabled }) => {
  const dispatch = useDispatch();

  const specificNotebookLoader = useSelector(
    state => state.s3Media?.specificNotebookLoader,
  );
  const currentAuthUserState = useSelector(state => state.authentication.user);

  const notebookMedia = useSelector(state => state.noteBook.notebookMedia);

  if (notebookMedia?.audios?.length <= 0) return null;

  const _handlePlayAudio = (audio, audioIndex) => {
    const payload = {
      file: audio,
      audioIndex,
    };

    dispatch(startPlayingAudio(payload));
  };

  const _handlePausePlayingAudio = (audio, audioIndex) => {
    const payload = {
      file: audio,
      audioIndex,
    };

    dispatch(pausePlayingAudio(payload));
  };

  const _handleDeleteRecording = (recording, recordingIndex) => {
    if (!isDisabled) {
      dispatch(deleteNoteBookMediaByIdRequest(recording));

      dispatch(deleteRecording(recordingIndex));
    }
  };

  return notebookMedia?.audios?.map((file, index) => {
    if (file?.path) {
      return (
        <AudioPlayer
          key={index}
          item={file}
          index={index}
          playAudio={_handlePlayAudio}
          pausePlayer={_handlePausePlayingAudio}
          deleteAudio={_handleDeleteRecording}
          loader={specificNotebookLoader}
          userId={currentAuthUserState?.email}
        />
      );
    }
  });
};

export default NoteBookAudioRecordings;
