// module
import { StyleSheet } from 'react-native';

// style's constants , methods
import fonts, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: normalize(8),
    marginVertical: normalize(6),
    // minHeight: normalize(137.27),
    backgroundColor: colors.white,
    paddingVertical: normalize(16),
    paddingHorizontal: normalize(12),
  },
  titleText: {
    flex: 1,
    // fontWeight: '500',
    letterSpacing: 0,
    fontStyle: 'normal',
    // textAlign: 'center',
    fontSize: normalize(15),
    // maxWidth: normalize(265),
    color: colors.primaryMain,
    fontFamily: fonts.HelveticaNeueMedium,
  },
  descriptionView: {
    marginTop: normalize(8),
    marginBottom: normalize(16),
  },
  descriptionText: {
    color: colors.grey17,
    fontSize: normalize(14),
    lineHeight: 22,
    fontFamily: fonts.HelveticaNeueRegular,
  },
  button: {
    // width: normalize(28),
    // height: normalize(28),
    // alignItems: 'flex-end',
  },
  reminderText: {
    fontSize: normalize(12),
    color: colors.secondary2,
    // textTransform: 'capitalize',
    marginHorizontal: normalize(6),
    fontFamily: fonts.HelveticaNeueMedium,
  },
  timeText: {
    fontStyle: 'normal',
    fontSize: normalize(10),
    color: colors.alphabetIndex,
    fontFamily: fonts.HelveticaNeueRegular,
  },
  attachmentView: {
    height: normalize(28),
    minWidth: normalize(48),
    borderRadius: normalize(6),
    paddingHorizontal: normalize(8),
    backgroundColor: colors.primaryDark1,
  },
  attachmentText: {
    color: colors.white,
    fontSize: normalize(15),
    marginRight: normalize(4),
    fontFamily: fonts.HelveticaNeueMedium,
  },
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  flexDir: { flexDirection: 'row' },
  rowItemsCenter: { flexDirection: 'row', alignItems: 'center' },
  rowJustifyBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  commentIcon: {
    // width: normalize(24),
    // height: normalize(24),
    marginRight: normalize(8),
  },
  reportText: {
    marginTop: normalize(5),
    fontWeight: '400',
    color: colors.alphabetIndex,
    fontSize: normalize(12),
    // lineHeight: normalize(20),
    fontFamily: fonts.HelveticaNeueRegular,
  },
  trashIcon: {
    // width: normalize(24),
    // height: normalize(24),
  },
  timerIcon: {
    fill: colors.secondary2,
    width: normalize(16),
    height: normalize(16),
  },
  favoriteIcon: {
    fill: colors.error3,
    width: normalize(24),
    height: normalize(24),
  },
  disabledButton: {
    opacity: 0.2,
  },
  creatorText: {
    marginTop: normalize(5),
    fontWeight: '400',
    color: colors.alphabetIndex,
    fontSize: normalize(12),
    lineHeight: normalize(20),
    fontFamily: fonts.HelveticaNeueRegular,
  },
  subHeadingContainer: {
    flexDirection: 'row',
    flex: 1,
    overflow: 'hidden',
  },
  flex: {
    flex: 1,
  },
  checkboxStyle: { marginRight: normalize(14) },
});

export default styles;
