// modules
import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  Pressable as Button,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { useDispatch } from 'react-redux';

// components
import { showAlertMsg } from '../../common/Alerts';
import { truncateString } from '../../../helpers/alphaNumericHelper';
import CheckBox from '../../common/CheckBox';

// styles
import styles from './styles';
import { normalize } from '../../../constants/theme/variables/customFont';

// constants
import {
  TIMER_ICON,
  ATTACHMENT_ICON,
  TRASH_ICON,
  COMMENT_NOTE,
} from '../../../constants/AssetSVGConstants';
import {
  DATE_FORMATS,
  NOTEBOOK_ACTION_CATEGORY,
} from '../../../constants/AppConstants';

// helpers
import { getFormattedDate } from '../../../helpers/dateHelper';
import { renderNoteListDate } from '../../../helpers/noteBookHelper';
import { removeMailAddressFromString } from '../../../helpers/genericHelper';

// localization
import i18n from '../../../localization/i18n';

// actions
import {
  confirmDeleteNoteFailure,
  confirmDeleteNoteRequest,
  deleteNoteLoading,
} from '../../../store/actions/notebook';

const NotebookListItems = ({
  note,
  title,
  description,
  attachmentsCount,
  actionNotificationDateTimeUtc,
  onPress,
  isDeletable,
  localId,
  noteId,
  isDeleting = false,
  showSelections,
  onSelectNote,
  isSelected,
  isFavorite,
  onPressFavorite,
}) => {
  const dispatch = useDispatch();

  const {
    createUser = '',
    updatedDate = '',
    createdDate = '',
    category = '',
    isComment,
    visitName,
  } = note;

  const _handleDeleteNote = () => {
    const payload = [{ noteId, localId }];

    dispatch(deleteNoteLoading(payload));

    const alertButtons = [
      {
        text: i18n.t('cancel'),
        onPress: () => dispatch(confirmDeleteNoteFailure()),
        style: 'default',
      },
      {
        text: i18n.t('delete'),
        onPress: () => dispatch(confirmDeleteNoteRequest()),
        style: 'destructive',
      },
    ];

    showAlertMsg(
      i18n.t('confirmation'),
      i18n.t('deleteNoteMessage'),
      alertButtons,
    );
  };

  const createdBy = `${note.businessName ? note.businessName + ' | ' : ''} ${
    createUser
      ? truncateString(removeMailAddressFromString(createUser), 10) + ' | '
      : ''
  }`;

  const timeToCreate = getFormattedDate(
    new Date(updatedDate || createdDate),
    DATE_FORMATS.MMM_dd_HH_mm,
  );

  return (
    <Button
      style={styles.container}
      onPress={() =>
        showSelections ? onSelectNote(note) : !isDeleting ? onPress() : null
      }>
      <View style={styles.rowJustifyBetween}>
        {isComment == 1 ? (
          <View
            style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <COMMENT_NOTE {...styles.commentIcon} />
            <Text style={styles.reportText}>
              {i18n.t('visitReport') + ': ' + visitName}
            </Text>
          </View>
        ) : (
          <View style={styles.flex}>
            <Text style={styles.titleText}>{title || i18n.t('Untitled')}</Text>
            <View style={styles.subHeadingContainer}>
              <Text numberOfLines={3}>
                <Text style={styles.creatorText}>{createdBy}</Text>
                <Text style={styles.creatorText}>{timeToCreate}</Text>
              </Text>
            </View>
          </View>
        )}

        <View style={styles.buttonsContainer}>
          {/* <Button onPress={onPressFavorite} style={styles.button}>
            {isFavorite ? (
              <UN_FAVOURITE_ICON
                {...styles.favoriteIcon}
              />
            ) : (
              <FAVOURITE_ICON width={normalize(28)} height={normalize(28)} />
            )}
          </Button> */}

          {showSelections ? (
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => onSelectNote(note)}>
              <View style={styles.checkboxStyle}>
                <CheckBox isSelected={isSelected} isRounded={true} />
              </View>
            </TouchableOpacity>
          ) : (
            <Button
              onPress={isDeletable ? _handleDeleteNote : null}
              disabled={isDeleting}
              style={[styles.button, !isDeletable && styles.disabledButton]}>
              {isDeleting ? (
                <ActivityIndicator />
              ) : (
                <TRASH_ICON {...styles.trashIcon} />
              )}
            </Button>
          )}
        </View>
      </View>

      <View
        style={StyleSheet.flatten([
          styles.rowJustifyBetween,
          styles.descriptionView,
        ])}>
        <Text numberOfLines={4} style={styles.descriptionText}>
          {description}
        </Text>
      </View>

      <View style={styles.rowJustifyBetween}>
        <View style={styles.rowItemsCenter}>
          {category == NOTEBOOK_ACTION_CATEGORY &&
          actionNotificationDateTimeUtc ? (
            <View style={styles.flexDir}>
              <TIMER_ICON {...styles.timerIcon} />
              <Text style={styles.reminderText}>
                {renderNoteListDate(actionNotificationDateTimeUtc)}
              </Text>
            </View>
          ) : null}
        </View>

        {attachmentsCount > 0 ? (
          <View
            style={StyleSheet.flatten([
              styles.rowCenter,
              styles.attachmentView,
            ])}>
            <Text style={styles.attachmentText}>{attachmentsCount}</Text>
            <ATTACHMENT_ICON width={normalize(16)} height={normalize(16)} />
          </View>
        ) : null}
      </View>
    </Button>
  );
};
export default NotebookListItems;
