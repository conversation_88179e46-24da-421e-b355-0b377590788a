// modules
import React, { useRef } from 'react';
import {
  View,
  FlatList,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useSelector } from 'react-redux';

// constants
import {
  NO_ACCOUNT_FOUND_ICON,
  NO_RESULT_FOUND_ICON,
} from '../../../constants/AssetSVGConstants';

// localization
import i18n from '../../../localization/i18n';

// helpers
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';

// components
import NotebookListItems from '../NoteBookListItems';
import EmptyListComponent from '../../common/EmptyListComponent';

// styles
import styles from './styles';

const NotebookList = ({
  isLoading,
  syncLoader,
  searchTerm,
  data,
  onRefresh,
  onPress,
  emptyListTitle,
  emptyListDescription,
  onEndReached,
  onEndReachedThreshold,
  loader,
  showSelections,
  isItemSelected,
  onSelectNote,
}) => {
  const flatListRef = useRef();

  const authenticatedUser = useSelector(state => state.authentication.user);

  const deletingNotes = useSelector(state => state.noteBook?.deletingNotes);

  const emptyComponent = () => {
    const isNotLoading = !isLoading && !syncLoader && !loader;
    if (stringIsEmpty(searchTerm) && isNotLoading) {
      return (
        <EmptyListComponent
          title={emptyListTitle}
          description={emptyListDescription}
          image={<NO_RESULT_FOUND_ICON />}
          button={false}
        />
      );
    } else if (isNotLoading) {
      return (
        <EmptyListComponent
          title={i18n.t('noResultShow')}
          description={i18n.t('noResultShowDescription')}
          image={<NO_ACCOUNT_FOUND_ICON />}
          button={false}
        />
      );
    } else {
      scrollToTop();
      return null;
    }
  };

  const scrollToTop = () =>
    flatListRef?.current?.scrollToOffset?.({ animated: false, y: 0 });

  const ListFooter = () =>
    isLoading ? (
      <View style={styles.footerLoaderView}>
        <ActivityIndicator />
      </View>
    ) : null;

  const _renderRefreshControl = (
    <RefreshControl
      refreshing={syncLoader || loader || isLoading}
      onRefresh={onRefresh}
    />
  );

  const renderItem = ({ item }) => {
    let mediaCount = 0;
    if (!stringIsEmpty(item?.mediaItems)) {
      mediaCount = JSON.parse(item?.mediaItems)?.length;
    }

    const isDeletable = item?.createUser === authenticatedUser?.email;

    const isNoteDeleting = deletingNotes?.find(
      note => note?.localId === item?.localId,
    );

    return (
      <NotebookListItems
        note={item}
        localId={item.localId}
        noteId={item.id}
        title={item.title}
        description={item.note}
        isFavorite={item.favourite}
        attachmentsCount={mediaCount}
        actionNotificationDateTimeUtc={item?.actionNotificationDateTimeUtc}
        onPress={() => onPress(item)}
        isDeletable={isDeletable}
        isDeleting={isNoteDeleting ? true : false}
        showSelections={showSelections}
        onSelectNote={onSelectNote}
        isSelected={isItemSelected(item)}
      />
    );
  };

  return (
    <FlatList
      data={data}
      ref={flatListRef}
      showsVerticalScrollIndicator={false}
      renderItem={renderItem}
      onEndReachedThreshold={onEndReachedThreshold}
      onEndReached={onEndReached}
      keyExtractor={item => item.localId}
      ListEmptyComponent={emptyComponent}
      contentContainerStyle={styles.contentFlatListContainer}
      refreshControl={_renderRefreshControl}
      ListFooterComponent={ListFooter}
    />
  );
};

export default NotebookList;
